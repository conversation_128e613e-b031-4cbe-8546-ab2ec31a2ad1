#!/usr/bin/env bash

CGO_ENABLED=1
GOOS=linux
rm ./TerraVirtualMachine
go build -gcflags='all=-N -l' -o ./TerraVirtualMachine
chmod a+x ./TerraVirtualMachine

target="test@10.18.13.116"
sshpass -p "Admin123" ssh -p 9222 ${target} "systemctl stop VirtualMachines.service"
sshpass -p "Admin123" ssh -p 9222 ${target} "killall dlv"
sshpass -p "Admin123" ssh -p 9222 ${target} "killall TerraVirtualMachine"
sshpass -p "Admin123" scp -P 9222 TerraVirtualMachine ${target}:/Volume1/@apps/VirtualMachines/bin/TerraVirtualMachine
sshpass -p "Admin123" scp -P 9222 /root/go/bin/dlv ${target}:/root/dlv
sshpass -p "Admin123" ssh -p 9222 ${target} "killall dlv;. /etc/profile;/root/dlv --listen=:2345 --headless=true --check-go-version=false --api-version=2 --accept-multiclient exec /Volume1/@apps/VirtualMachines/bin/TerraVirtualMachine debug"


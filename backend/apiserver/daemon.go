package main

import (
	"bufio"
	ctx "context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"

	"github.com/sevlyar/go-daemon"
)

type DaemonizedService interface {
	Start() (err error)
	Stop() (err error)
}

type asyncOperateResult struct {
	Error  string `json:"error,omitempty"`
	Output string `json:"output,omitempty"`
}

var (
	pipFileName       string
	daemonizedService DaemonizedService
)

type ServiceGenerator func() (service DaemonizedService, err error)

func ProcessDaemon(executeName string, serviceGenerator ServiceGenerator) {
	const (
		ValidArguesCount = 2
		StartCommand     = "start"
		StopCommand      = "stop"
		StatusCommand    = "status"
		DebugCommand     = "debug"
	)
	if len(os.Args) != ValidArguesCount {
		printUsage(executeName)
		return
	}

	workingPath := PidFile
	var err error
	var command = os.Args[1]
	var pidFileName = filepath.Join(workingPath, fmt.Sprintf("%s.pid", executeName))
	if pipFileName == "" {
		pipFileName = filepath.Join(workingPath, fmt.Sprintf("%s.pip", executeName))
	}

	context := &daemon.Context{
		PidFileName: pidFileName,
		PidFilePerm: daemon.FILE_PERM,
	}

	daemon.AddCommand(daemon.StringFlag(&command, StopCommand), syscall.SIGTERM, onStopDaemon)
	daemon.AddCommand(daemon.StringFlag(&command, StopCommand), syscall.SIGINT, onStopDaemon)

	switch command {
	case StopCommand:
		if process, err := context.Search(); err != nil || nil == process {
			fmt.Printf("%s is already stopped\n", executeName)
			return
		} else if !isRunning(process) {
			fmt.Printf("%s is already stopped (process %d not running)\n", executeName, process.Pid)
		} else {
			defer os.Remove(pipFileName)
			if err = createPipe(pipFileName); err != nil {
				fmt.Printf("open pipe fail: %s\n", err.Error())
				return
			}
			go daemon.SendCommands(process)

			msg, err := readPipe(pipFileName)
			if err != nil {
				fmt.Printf("stop %s fail: %s\n", executeName, err.Error())
			} else if msg != "" {
				fmt.Println(msg)
				fmt.Printf("stop %s success\n", executeName)
			} else {
				fmt.Printf("stop %s success\n", executeName)
			}
		}
		return
	case StatusCommand:
		process, err := context.Search()
		if err != nil || nil == process {
			fmt.Printf("%s is stopped\n", executeName)
		} else if !isRunning(process) {
			fmt.Printf("%s is stopped (pid %d)\n", executeName, process.Pid)
		} else {
			fmt.Printf("%s is running, current pid %d\n", executeName, process.Pid)
		}
		return
	case StartCommand:
		if process, err := context.Search(); err == nil && nil != process {
			if isRunning(process) {
				fmt.Printf("%s is already running\n", executeName)
				return
			}
		}
		if _, err = context.Reborn(); err != nil {
			fmt.Printf("create daemon fail:%s\n", err.Error())
			return
		}
		//parent or child
		if !daemon.WasReborn() {
			//parent
			msg, err := readMessageFromPipe(pipFileName)
			if err != nil {
				fmt.Printf("start %s fail: %s\n", executeName, err.Error())
			} else {
				fmt.Println(msg)
				fmt.Printf("%s started\n", executeName)
			}
			return
		} else {
			//child
			defer os.Remove(pidFileName)
			daemonizedService, err = serviceGenerator()
			if err != nil {
				log.Printf("generate service fail: %s", err.Error())
				notifyErrorToPipe(pipFileName, err.Error())
				return
			}
			err := daemonizedService.Start()
			if err != nil {
				log.Printf("start service fail: %s", err.Error())
				notifyErrorToPipe(pipFileName, err.Error())
			} else {
				notifyMessageToPipe(pipFileName, "VirtualMachines starting")
				daemon.ServeSignals()
			}
			return
		}
	case DebugCommand:
		daemonizedService, err = serviceGenerator()
		if err != nil {
			log.Printf("generate service fail: %s", err.Error())
			return
		}

		loadEnvFile(EnvFile)
		err := daemonizedService.Start()
		if err != nil {
			log.Printf("start service fail: %s", err.Error())
		}

		ctx, cancel := signal.NotifyContext(ctx.Background(), syscall.SIGINT, syscall.SIGTERM)
		defer cancel()

		select {
		case <-ctx.Done():
			log.Printf("debug end")
		}

	default:
		printUsage(executeName)
	}
}

func onStopDaemon(sig os.Signal) error {
	if nil == daemonizedService {
		log.Println("invalid daemon service")
		return daemon.ErrStop
	}
	if pipFileName == "" {
		log.Println("invalid pipe file")
		return daemon.ErrStop
	}
	err := daemonizedService.Stop()
	if err != nil {
		log.Printf("stop service fail: %s", err.Error())
		notifyErrorToPipe(pipFileName, err.Error())
	} else {
		notifyMessageToPipe(pipFileName, "VirtualMachines stopping")
	}
	return daemon.ErrStop
}

func readMessageFromPipe(pipeName string) (message string, err error) {
	defer os.Remove(pipeName)
	if err = createPipe(pipeName); err != nil {
		return
	}
	message, err = readPipe(pipeName)
	return
}

func createPipe(pipeName string) (err error) {
	const (
		PipeFilePerm = 0600
	)
	if _, err = os.Stat(pipeName); !os.IsNotExist(err) {
		os.Remove(pipeName)
	}
	if err = syscall.Mkfifo(pipeName, PipeFilePerm); err != nil {
		return
	}

	return
}

func readPipe(pipeName string) (message string, err error) {
	const (
		PipeFilePerm = 0600
	)
	var pipe *os.File
	pipe, err = os.OpenFile(pipeName, os.O_RDONLY, PipeFilePerm)
	if err != nil {
		return
	}
	defer pipe.Close()

	var data = make([]byte, 1<<10)
	var n int
	n, err = pipe.Read(data)
	if err != nil {
		return
	}
	var result asyncOperateResult
	if err = json.Unmarshal(data[:n], &result); err != nil {

		return "", fmt.Errorf("unmarshal fail: %s, data %s", err.Error(), data[:n])
	}
	if result.Error != "" {
		err = errors.New(result.Error)
	} else {
		message = result.Output
	}
	return
}

func notifyMessageToPipe(pipeName, message string) (err error) {
	const (
		PipeFilePerm = 0600
	)
	pip, err := os.OpenFile(pipeName, os.O_RDWR, PipeFilePerm)
	if err != nil {
		return
	}
	defer pip.Close()
	var result = asyncOperateResult{Output: message}
	data, err := json.MarshalIndent(result, "", " ")
	if err != nil {
		return
	}
	_, err = pip.Write(data)
	return
}

func notifyErrorToPipe(pipeName, message string) (err error) {
	const (
		PipeFilePerm = 0600
	)
	pip, err := os.OpenFile(pipeName, os.O_RDWR, PipeFilePerm)
	if err != nil {
		return
	}
	defer pip.Close()
	var result = asyncOperateResult{Error: message}
	data, err := json.MarshalIndent(result, "", " ")
	if err != nil {
		return
	}
	_, err = pip.Write(data)
	return
}

func getWorkingPath() (path string, err error) {
	executable, err := os.Executable()
	if err != nil {
		return "", err
	}
	return filepath.Abs(filepath.Dir(executable))
}

func isRunning(process *os.Process) bool {
	if nil == process {
		return false
	}
	if err := process.Signal(syscall.Signal(0)); err == nil {
		return true
	} else {
		return false
	}
}

func loadEnvFile(filepath string) error {
	file, err := os.Open(filepath)
	if err != nil {
		panic("env load failed")
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 拆分 KEY=VALUE
		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			continue // 或者报错
		}

		key := strings.TrimSpace(parts[0])
		val := strings.TrimSpace(parts[1])
		val = strings.Trim(val, `"'`) // 去掉包裹的引号

		_ = os.Setenv(key, val) // 设置环境变量
	}

	return scanner.Err()
}

func printUsage(executeName string) {
	fmt.Printf("Usage: %s [start|stop|status|debug]\n", executeName)
}

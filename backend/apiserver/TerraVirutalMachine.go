package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/http/httptest"
	"os"
	"runtime/debug"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/errors/gerror"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/golibrary/utils"
)

var (
	HttpPort    = "8833"
	HttpIp      = ""
	ExecuteName = "VirtualMachines"
	TosStdOut   = "/var/log/VMs/panic.log"
	PidFile     = "/var/run"
	SockFile    = "/var/api/VirtualMachines.sock"
	EnvFile     = "/etc/VMs/VMs.conf"
)

type MainService struct {
	stop   chan bool
	engine *gin.Engine
}

func (service *MainService) Start() (err error) {
	// gin
	gin.SetMode(gin.DebugMode)

	engine := gin.New()
	engine.MaxMultipartMemory = 8 << 20 // 限制文件最大上传容量(8M)
	engine.Use(gin.Recovery())

	// iam/log
	opts := &log.Options{
		Level:       "info",
		Format:      "console",
		EnableColor: true,
		OutputPaths: []string{TosStdOut, "stdout"},
	}
	log.Init(opts)
	defer log.Flush()

	stdOut, err := os.OpenFile(TosStdOut, os.O_TRUNC|os.O_RDWR|os.O_CREATE, 0640)
	if err != nil {
		log.Fatalf(err.Error())
	}
	defer stdOut.Close()
	os.Stdout = stdOut
	os.Stderr = stdOut

	gin.DefaultWriter = io.Writer(stdOut)
	gin.DefaultErrorWriter = io.Writer(stdOut)

	// 捕获panic
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf(string(debug.Stack()))
			err = fmt.Errorf("%v \nstack details in %s", r, TosStdOut)
		}
	}()

	if utils.Exists(SockFile) {
		os.Remove(SockFile)
	}

	// http router
	apiserver.InitController(engine)

	go service.runServer(engine)

	return nil
}

func (service *MainService) Stop() (err error) {
	// 获取虚拟机列表
	listReq, err := http.NewRequest("GET", "/VirtualMachines/v1/vms/list_instance", nil)
	if err != nil {
		return fmt.Errorf("failed to create list request: %s", err)
	}

	listResp := httptest.NewRecorder()
	service.engine.ServeHTTP(listResp, listReq)

	if listResp.Code != http.StatusOK {
		return fmt.Errorf("failed to list VMs: %s", listResp.Body.String())
	}

	var instanceResult dto.InstanceResult
	if err := json.Unmarshal(listResp.Body.Bytes(), &instanceResult); err != nil {
		return fmt.Errorf("failed to parse VM list: %v", err)
	}

	var runningVMs []string
	for _, vm := range instanceResult.Instances {
		if vm.Status == "running" {
			runningVMs = append(runningVMs, vm.GuestId)
		}
	}

	for _, guestID := range runningVMs {
		forceShutdownURL := fmt.Sprintf("/VirtualMachines/v1/vms/%s/force_poweroff", guestID)
		shutdownReq, err := http.NewRequest("POST", forceShutdownURL, nil)
		if err != nil {
			return fmt.Errorf("failed to create shutdown request for VM %s: %v", guestID, err)
		}

		shutdownResq := httptest.NewRecorder()
		service.engine.ServeHTTP(shutdownResq, shutdownReq)

		if shutdownResq.Code != http.StatusOK {
			return fmt.Errorf("failed to force shutdown VM %s: %s", guestID, shutdownResq.Body.String())
		}
		log.Infof("Successfully forced shutdown VM: %s", guestID)
	}

	service.stop <- true
	return
}

func createDaemon() (DaemonizedService, error) {
	s := &MainService{}
	s.stop = make(chan bool, 1)
	return s, nil
}

func Truncate(stdOut *os.File) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		fi, err := stdOut.Stat()
		if err == nil {
			return
		}
		if fi.Size() > 100*1024*1024 {
			_ = stdOut.Truncate(0)
			_ = stdOut.Sync()
		}
	}
}

func (service *MainService) runServer(engine *gin.Engine) error {
	_ = os.Remove(SockFile)
	go func() {
		if err := engine.Run(strings.Join([]string{HttpIp, HttpPort}, ":")); err != nil {
			log.Fatal(err.Error())
		}
	}()

	listener, err := net.Listen("unix", SockFile)
	if err != nil {
		return gerror.Wrap(err, "failed to listen unix socket")
	}
	defer listener.Close()
	defer os.Remove(SockFile)

	if err = os.Chmod(SockFile, 0o777); err != nil {
		return gerror.Wrap(err, "chmod socket file error")
	}

	// run http server
	srv := http.Server{Handler: engine}
	serverErrors := make(chan error, 1)
	go func() {
		serverErrors <- srv.Serve(listener)
	}()

	// wait for shutdown
	select {
	case err = <-serverErrors:
		return gerror.Wrap(err, "server error")
	case <-service.stop:
		shutDownCtx, shutDownCancel := context.WithTimeout(context.Background(), time.Second*3)
		defer shutDownCancel()
		err = srv.Shutdown(shutDownCtx)
		if err != nil {
			// log.Println("did not graceful shutdown completely")
			err = srv.Close()
		}
		if err != nil {
			return gerror.Wrap(err, "could not shutdown gracefully")
		}
		return nil
	}
}

func main() {
	ProcessDaemon(ExecuteName, createDaemon)
}

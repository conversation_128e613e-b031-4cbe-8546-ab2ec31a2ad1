package v1

import "time"

var VolumeTempDir = "/tmp"

const (
	MediaImagePath      = "media_images"
	SyncInterval        = 2 * time.Second
	SystemNameLinux     = "linux"
	SystemNameWindows   = "windows"
	AdminLinux          = "root"
	AdminWindows        = "Administrator"
	MetaFileSuffix      = "meta"
	USBModelXHCI        = "nec-xhci"
	USBModelNone        = ""
	DiskBusIDE          = "ide"
	DiskBusSCSI         = "scsi"
	DiskBusSATA         = "sata"
	DiskBusVirtio       = "virtio"
	NetworkModelRTL8139 = "rtl8139"
	NetworkModelE1000   = "e1000"
	NetworkModelVIRTIO  = "virtio"
	RemoteControlVNC    = "vnc"
	RemoteControlSPICE  = "spice"
	TabletBusNone       = ""
	TabletBusVIRTIO     = "virtio"
	TabletBusUSB        = "usb"
	DisplayDriverVGA    = "vga"
	DisplayDriverVmVga  = "vmvga"
	DisplayDriverCirrus = "cirrus"
	DisplayDriverQXL    = "qxl"
	DisplayDriverVirtIO = "virtio"
	DisplayDriverNone   = "none"
	LevelInvalid        = "invalid"
	LevelError          = "error"
	LevelWarn           = "warn"
	LevelInfo           = "info"
)

type PriorityEnum uint

const (
	PriorityLow = iota + 1
	PriorityLowMedium
	PriorityMedium
	PriorityHighMedium
	PriorityHigh
)

const (
	InstanceMediaOptionNone uint = iota
	InstanceMediaOptionImage
	InstanceMediaOptionNetwork
)

type InstanceNetworkMode int

const (
	NetworkModePrivate = iota
	NetworkModePlain
	NetworkModeMono
	NetworkModeShare
	NetworkModeVPC
)

type TemplateOperatingSystem int

const (
	TemplateOperatingSystemLinux = iota
	TemplateOperatingSystemWindows
	TemplateOperatingSystemInvalid
)

func (value TemplateOperatingSystem) ToString() string {
	switch value {
	case TemplateOperatingSystemLinux:
		return SystemNameLinux
	case TemplateOperatingSystemWindows:
		return SystemNameWindows
	default:
		return "invalid"
	}
}

type TemplateDiskDriver int

const (
	TemplateDiskDriverIDE = iota + 1
	TemplateDiskDriverSATA
	TemplateDiskDriverVirtio
	TemplateDiskDriverInvalid
)

func (value TemplateDiskDriver) ToString() string {
	switch value {
	case TemplateDiskDriverIDE:
		return DiskBusIDE
	case TemplateDiskDriverSATA:
		return DiskBusSATA
	case TemplateDiskDriverVirtio:
		return DiskBusVirtio
	default:
		return "invalid"
	}
}

type TemplateNetworkModel int

const (
	TemplateNetworkModelVirtIO = iota
	TemplateNetworkModelE1000
	TemplateNetworkModelRTL18139
	TemplateNetworkModelInvalid
)

func (value TemplateNetworkModel) ToString() string {
	switch value {
	case TemplateNetworkModelVirtIO:
		return NetworkModelVIRTIO
	case TemplateNetworkModelE1000:
		return NetworkModelE1000
	case TemplateNetworkModelRTL18139:
		return NetworkModelRTL8139
	default:
		return "invalid"
	}
}

type TemplateDisplayDriver int

const (
	TemplateDisplayDriverVGA = iota
	TemplateDisplayDriverVmvga
	TemplateDisplayDriverCirrus
	TemplateDisplayDriverVirtIO
	TemplateDisplayDriverQXL
	TemplateDisplayDriverNone
	TemplateDisplayDriverInvalid
)

func (value TemplateDisplayDriver) ToString() string {
	switch value {
	case TemplateDisplayDriverVGA:
		return DisplayDriverVGA
	case TemplateDisplayDriverVmvga:
		return DisplayDriverVmVga
	case TemplateDisplayDriverCirrus:
		return DisplayDriverCirrus
	case TemplateDisplayDriverVirtIO:
		return DisplayDriverVirtIO
	case TemplateDisplayDriverQXL:
		return DisplayDriverQXL
	case TemplateDisplayDriverNone:
		return DisplayDriverNone
	default:
		return "invalid"
	}
}

// machine type
type VMInstanceMachineType string

const (
	MachineTypePC  = "pc"
	MachineTypeQ35 = "q35"
)

func (value VMInstanceMachineType) ToString() string {
	switch value {
	case "1":
		return MachineTypePC
	case "2":
		return MachineTypeQ35
	default:
		return "invalid"
	}
}

type TemplateRemoteControl int

const (
	TemplateRemoteControlVNC = iota
	TemplateRemoteControlSPICE
	TemplateRemoteControlInvalid
)

func (value TemplateRemoteControl) ToString() string {
	switch value {
	case TemplateRemoteControlVNC:
		return RemoteControlVNC
	case TemplateRemoteControlSPICE:
		return RemoteControlSPICE
	default:
		return "invalid"
	}
}

type TemplateUSBModel int

const (
	TemplateUSBModelPIIX3 = iota // USB 2.0
	TemplateUSBModelXHCI         // USB 3.0
	TemplateUSBModelInvalid
)

func (value TemplateUSBModel) ToString() string {
	switch value {
	case TemplateUSBModelPIIX3:
		return USBModelNone
	case TemplateUSBModelXHCI:
		return USBModelXHCI
	default:
		return "invalid"
	}
}

type TemplateTabletModel int

const (
	TemplateTabletModelNone = iota
	TemplateTabletModelUSB
	TemplateTabletModelVirtIO
	TemplateTabletModelInvalid
)

func (value TemplateTabletModel) ToString() string {
	switch value {
	case TemplateTabletModelNone:
		return TabletBusNone
	case TemplateTabletModelUSB:
		return TabletBusUSB
	case TemplateTabletModelVirtIO:
		return TabletBusVIRTIO
	default:
		return "invalid"
	}
}

type TaskID uint64

var InValidTaskId = TaskID(0xffffffffffffffff)

type LogLevel int

const (
	LogLevelInvalid = iota
	LogLevelError
	LogLevelWarn
	LogLevelInfo
)

func (l LogLevel) ToString() string {
	switch l {
	case LogLevelInfo:
		return LevelInfo
	case LogLevelWarn:
		return LevelWarn
	case LogLevelError:
		return LevelError
	}
	return LevelInvalid
}

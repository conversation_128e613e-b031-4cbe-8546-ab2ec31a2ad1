package v1

import (
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
)

const (
	ClearLogMessage      = "Logs have been successfully cleared."
	VMEditMessage        = "The virtual machine [%s] has been edited: [%s]"
	VMEditMessageF       = "The virtual machine [%s] has been failed to edited: [%s]"
	VMEditSuccessMessage = "The virtual machine [%s] has been successfully edited."
	VMEditFailedMessage  = "The virtual machine [%s] has been failed to edit."
)

type LogList struct {
	metav1.ListMeta `json:"metadata,omitempty"` // 标准对象元数据

	Items []LogEntry `json:"items"`
}

type LogEntry struct {
	Category string   `json:"ategory"`
	Priority LogLevel `json:"priority"` //1 错误 2 警告 3 信息
	Time     int64    `json:"time"`
	Username string   `json:"username"`
	Section  string   `json:"section"`
	Key      string   `json:"key"`
	TrPairs  []TrPair `json:"tr_pairs"`
}

type TrPair struct {
	Variable string `json:"variable"`
	Value    string `json:"value"`
}

package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
)

const (
	VDiskVolumePath = "/@VMs_iSCSI/LUN/VDISK_BLUN"
	VDiskPrefix     = "/twm/live_cluster/vdisk/"
)

type VDisk struct {
	DevLimit       int64              `json:"dev_limit,omitempty"`       // 设备的限制（可能用于 QoS 资源管理）未知
	DevReservation int64              `json:"dev_reservation,omitempty"` // 预留的设备资源 未知
	DevWeight      int                `json:"dev_weight,omitempty"`      // 设备权重，用于调度或资源分配 未知
	Format         int                `json:"format,omitempty"`          // 磁盘格式（）未知
	GuestID        string             `json:"guest_id"`                  // 所在虚拟机 UUID
	IOPSEnable     bool               `json:"iops_enable"`               // 是否启用 IOPS（输入/输出操作每秒）限制
	IsDummy        bool               `json:"is_dummy,omitempty"`        // 是否为虚拟磁盘的占位符? 未知
	IsMetaDisk     bool               `json:"is_meta_disk,omitempty"`    // 是否是元数据磁盘?（非数据磁盘） 未知
	MinorID        int                `json:"minor_id"`                  // 设备的顺序 ID
	RepositoryID   string             `json:"repository_id"`             // 所在存储池 UUID
	Size           int64              `json:"size,omitempty"`            // 磁盘大小（以字节为单位）
	Type           int                `json:"type,omitempty"`            // 磁盘类型?（可能用于区分系统盘和数据盘） 未知
	Unmap          bool               `json:"unmap"`                     // 是否支持 UNMAP/TRIM 操作（用于回收未使用空间）
	LUNUUID        string             `json:"lun_uuid"`                  // LUN UUID
	VDiskMode      TemplateDiskDriver `json:"vdisk_mode"`                // 磁盘模式（1:IDE, 2:SATA, 3:Virtio）
	VDiskDevice    string             `json:"vdisk_device,omitempty"`    // 磁盘设备（如 /dev/sd*）
}

type VDiskList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items map[string]*VDisk `json:"items"` // 虚拟磁盘列表， 虚拟盘 UUID <-> 虚拟盘实体
}

// 添加结构体方法
func (v *VDisk) GetPath(ctx context.Context, getRepoPath func(context.Context, string) (string, error)) (string, error) {
	var symap sync.Map
	symap.LoadOrStore(v.RepositoryID, v.RepositoryID)

	repoPath, err := getRepoPath(ctx, v.RepositoryID)
	if err != nil {
		return "", fmt.Errorf("repoPath get failed: %w", err)
	}

	lunPath := filepath.Join(repoPath, v.LUNUUID)
	if _, err := os.Stat(lunPath); os.IsNotExist(err) {
		return "", fmt.Errorf("LUN folder not found: %s", lunPath)
	}

	files, err := os.ReadDir(lunPath)
	if err != nil {
		return "", fmt.Errorf("failed to read LUN folder: %w", err)
	}

	if len(files) != 1 {
		return "", fmt.Errorf("LUN folder contains %d files, expected 1", len(files))
	}

	return filepath.Join(lunPath, files[0].Name()), nil
}

// MarshalJSON 自定义序列化方法
func (vl *VDiskList) MarshalJSON() ([]byte, error) {
	type Alias VDiskList
	return json.Marshal(&struct {
		*Alias
		ListMeta metav1.ListMeta `json:",inline"`
	}{
		Alias:    (*Alias)(vl),
		ListMeta: vl.ListMeta,
	})
}

// UnmarshalJSON 自定义反序列化方法
func (vl *VDiskList) UnmarshalJSON(data []byte) error {
	type Alias VDiskList
	aux := &struct {
		*Alias
		ListMeta metav1.ListMeta `json:",inline"`
	}{
		Alias: (*Alias)(vl),
	}
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}
	vl.ListMeta = aux.ListMeta
	return nil
}

// 实现BinaryMarshaler接口
func (vl *VDiskList) MarshalBinary() ([]byte, error) {
	return json.Marshal(vl)
}

// 实现BinaryUnmarshaler接口
func (vl *VDiskList) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, vl)
}

package v1

import "gitlab.local/golibrary/errors"

// Validate validates that a network object is valid.
func (n *Network) Validate() error {
	if n.NetworkName == "" {
		return errors.New("network name is required")
	}
	if n.Type == EXTERNAL {
		// 外部网络
		if len(n.Interfaces) == 0 {
			return errors.New("external network requires at least one host interface")
		}
		if n.VLANID < 1 || n.VLANID > 4094 {
			return errors.New("invalid VLAN ID, must be between 1-4094")
		}
	} else if n.Type == PRIVATE {
		// 专用网络
		if n.HostID == "" || n.HostName == "" {
			return errors.New("private network requires host information")
		}
		if n.VLANID != 0 {
			return errors.New("private network cannot have VLAN ID")
		}
		if len(n.Interfaces) > 1 {
			return errors.New("private network requires only one host interface")
		}
		for _, ifaces := range n.Interfaces {
			if len(ifaces.Items) > 1 {
				return errors.New("private network requires only one host interface")
			}
		}
	} else {
		// 未知网络类型
		return errors.New("invalid network type")
	}
	return nil
}

package v1

import metav1 "github.com/marmotedu/component-base/pkg/meta/v1"

const (
	HostPrefix = "/twm/live_cluster/hosts/"
	// /twm/live_cluster/hosts/{host_id}/base_info
	HostBaseInfo = "base_info"
	HostIface    = "nics"
	HostSep      = "/"

	HostNameDefault   = "TNAS"
	HostIfaceIdPrefix = "00000000-0000-0000-0000"
)

type HostNetIface struct {
	Name              string `json:"name"`
	EnableSriov       bool   `json:"enable_sriov"`
	MaxAvailableVfNum int    `json:"max_available_vf_num"`
}

type HostNetIfaceList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items map[string]*HostNetIface `json:"items"`
}

type Host struct {
	HostID   string `json:"host_id"`   // 主机ID
	HostName string `json:"host_name"` // 主机名称
}

type HostList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items map[string]*Host `json:"items"`
}

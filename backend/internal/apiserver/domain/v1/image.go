package v1

import (
	"path"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
)

const (
	ImageDiskVolumePath = "/@VmsImage"
	ImageConf           = "image.conf"
	ImagePrefix         = "/twm/live_cluster/image/"
)

type ImageType string

var (
	ISO  ImageType = "iso"
	DISK ImageType = "disk"
)

type ImageState string

var (
	StateAvailable ImageState = "available"
	StateUploading ImageState = "Upload"
	StateCreatring ImageState = "Create"
	StateError     ImageState = "Error"
)

type Image struct {
	metav1.ObjectMeta `json:"metadata,omitempty"` // 标准对象元数据

	Name      string    `json:"name,omitempty"`      // 映像名称
	Type      ImageType `json:"type,omitempty"`      // 映像的类型
	RealFiles string    `json:"real_name,omitempty"` // 真实文件名
	FileSize  uint64    `json:"file_size,omitempty"` // 映像文件大小
	Repos     []string  `json:"repos,omitempty"`     // 映像的位置
}

func (img *Image) LocationImageFile(imgID string, locationVolume string) string {
	return path.Join(locationVolume, ImageDiskVolumePath, imgID, img.RealFiles)
}

type ImageList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items map[string]*Image `json:"items"` // 镜像列表， 镜像id <-> 镜像实体
}

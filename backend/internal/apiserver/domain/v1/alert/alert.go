package alert

// 状态等级枚举
type SystemSeverityLevel int

const (
	SeverityLevelHealthy = iota
	SeverityLevelRunning
	SeverityLevelWarning
	SeverityLevelError
	SeverityLevelInvalid
)

const (
	Healthy = "healthy"
	Running = "running"
	Warning = "warning"
	Error   = "error"
	Invalid = "invalid"
)

func (level SystemSeverityLevel) ToString() string {
	switch level {
	case SeverityLevelHealthy:
		return Healthy
	case SeverityLevelRunning:
		return Running
	case SeverityLevelWarning:
		return Warning
	case SeverityLevelError:
		return Error
	default:
		return Invalid
	}
}

type StatusEntry struct {
	Component    string              // 组件类型
	Severity     SystemSeverityLevel // 严重等级
	Message      string              // 状态描述（三字段描述:组件_等级_简要描述, 例如:storage_warning_ThresholdExceeded）,健康状态返回空
	WithAlarmIds []string
	NorAlarmIds  []string
}

type ClusterHealth struct {
	TotalStatus  map[string]*ComponentHealth // 各组件健康状态
	GlobalStatus SystemSeverityLevel         // 集群整体状态
	//StatusEntries []StatusEntry               // 原始状态条目
}

type ComponentHealth struct {
	Healthy   int            // 健康数量
	Running   int            // 虚拟机运行时数量
	Warnings  int            // 警告数量
	Errors    int            // 错误数量
	LatestMsg map[string]int // 最新消息统计（message->count）
}

type SystemAlert struct{}

func (s *SystemAlert) toComponentHealth(entries []StatusEntry) *ComponentHealth {
	health := &ComponentHealth{
		LatestMsg: make(map[string]int),
	}

	for _, entry := range entries {
		switch entry.Severity {
		case SeverityLevelHealthy:
			health.Healthy++
		case SeverityLevelRunning:
			health.Running++
		case SeverityLevelWarning:
			health.Warnings++
		case SeverityLevelError:
			health.Errors++
		}

		health.LatestMsg[entry.Message]++
	}

	return health
}

func (s *SystemAlert) statusEntryMaxServerityLevel(entries []StatusEntry) SystemSeverityLevel {
	var maxLevel SystemSeverityLevel
	for _, entry := range entries {
		if entry.Severity > maxLevel {
			maxLevel = entry.Severity
		}
	}

	return maxLevel
}

// func (s *SystemAlert) GetClusterHealth() (ClusterHealth, error) {
//  clusterHealth := ClusterHealth{
//      TotalStatus:  make(map[string]*ComponentHealth),
//      GlobalStatus: SeverityLevelHealthy,
//  }

//  maxWorkers := 6
//  sem := make(chan struct{}, maxWorkers)
//  ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)

//  resultChan := make(chan struct {
//      componentType string
//      entries       []StatusEntry
//      err           error
//  }, len(AlertRegister.handlers))
//  g, ctx := errgroup.WithContext(ctx)

//  for componentType := range AlertRegister.handlers {
//      ct := componentType

//      sem <- struct{}{}

//      g.Go(func() error {
//          defer func() { <-sem }()

//          select {
//          case <-ctx.Done():
//              return ctx.Err()
//          default:
//              entries, err := AlertRegister.ExecAlertHandler(ct)
//              resultChan <- struct {
//                  componentType string
//                  entries       []StatusEntry
//                  err           error
//              }{ct, entries, err}
//              return nil
//          }
//      })
//  }

//  go func() {
//      g.Wait()
//      close(resultChan)
//  }()

//  for {
//      select {
//      case <-ctx.Done():
//          log.Warnf("[Alert_Domain] component alert listing  timed out - %v", ctx.Err())
//          cancel()
//          return clusterHealth, ctx.Err()
//      case result, ok := <-resultChan:
//          if !ok {
//              cancel()
//              return clusterHealth, nil
//          }

//          if result.err != nil {
//              log.Warnf("[Alert_Domain] component alert listing for %s failed - %v",
//                  result.componentType, result.err)
//              continue
//          }

//          clusterHealth.TotalStatus[result.componentType] = toComponentHealth(result.entries)
//          clusterHealth.StatusEntries = append(clusterHealth.StatusEntries, result.entries...)
//          clusterHealth.GlobalStatus = max(
//              statusEntryMaxServerityLevel(result.entries),
//              clusterHealth.GlobalStatus,
//          )
//      }
//  }
// }

func (s *SystemAlert) GetClusterHealth() (ClusterHealth, error) {
	clusterHealth := ClusterHealth{
		TotalStatus:  make(map[string]*ComponentHealth),
		GlobalStatus: SeverityLevelHealthy,
	}

	for componentType := range AlertRegister.handlers {
		entries, err := AlertRegister.ExecAlertHandler(componentType)
		if err != nil {
			return clusterHealth, err
		}

		// 直接添加entries到TotalStatus和StatusEntries
		clusterHealth.TotalStatus[componentType] = s.toComponentHealth(entries)
		//clusterHealth.StatusEntries = append(clusterHealth.StatusEntries, entries...)

		// 更新全局状态
		clusterHealth.GlobalStatus = max(
			s.statusEntryMaxServerityLevel(entries),
			clusterHealth.GlobalStatus,
		)
	}

	return clusterHealth, nil
}

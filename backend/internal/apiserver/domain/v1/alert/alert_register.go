package alert

import "sync"

type alertHandler func() (StatusEntry, error)

// alertRegister 用于构建状态条目的构建器
type alertRegister struct {
	handlers map[string][]alertHandler
	hmut     sync.Mutex
}

func NewAlertRegister() *alertRegister {
	return &alertRegister{
		handlers: make(map[string][]alertHandler),
	}
}

func (r *alertRegister) AddAlertHandler(componentType string, handler alertHandler) *alertRegister {
	r.hmut.Lock()
	defer r.hmut.Unlock()

	r.handlers[componentType] = append(r.handlers[componentType], handler)
	return r
}

func (r *alertRegister) ExecAlertHandler(componentType string) ([]StatusEntry, error) {
	entires := make([]StatusEntry, 0)
	for _, handler := range r.handlers[componentType] {
		entry, err := handler()
		if err != nil {
			return nil, err
		}
		entires = append(entires, entry)
	}
	return entires, nil
}

var AlertRegister *alertRegister

func init() {
	AlertRegister = NewAlertRegister()
}

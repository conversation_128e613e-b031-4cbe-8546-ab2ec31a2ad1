package alert

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestStatusEntry(t *testing.T) {
	tests := []struct {
		name        string
		statusEntry StatusEntry
		wantValid   bool
	}{
		{
			name: "存储警告测试",
			statusEntry: StatusEntry{
				Component:  "storage",
				Severity:   SeverityLevelWarning,
				Message:    "storage_warning_ThresholdExceeded",
				DeviceUUID: []string{"device-001", "device-002"},
			},
			wantValid: true,
		},
		{
			name: "网络错误测试",
			statusEntry: StatusEntry{
				Component:  "network",
				Severity:   SeverityLevelError,
				Message:    "network_error_ConnectionLost",
				DeviceUUID: []string{"device-003"},
			},
			wantValid: true,
		},
		{
			name: "CPU 健康状态测试",
			statusEntry: StatusEntry{
				Component:  "cpu",
				Severity:   SeverityLevelHealthy,
				Message:    "", // 健康状态返回空
				DeviceUUID: []string{"device-004", "device-005", "device-006"},
			},
			wantValid: true,
		},
		{
			name: "内存运行状态测试",
			statusEntry: StatusEntry{
				Component:  "memory",
				Severity:   SeverityLevelRunning,
				Message:    "memory_running_HighUsage",
				DeviceUUID: []string{"device-007"},
			},
			wantValid: true,
		},
		{
			name: "磁盘无效状态测试",
			statusEntry: StatusEntry{
				Component:  "disk",
				Severity:   SeverityLevelInvalid,
				Message:    "disk_invalid_NotResponding",
				DeviceUUID: []string{"device-008", "device-009"},
			},
			wantValid: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 验证结构体字段
			assert.NotEmpty(t, tt.statusEntry.Component, "Component should not be empty")

			// 验证 Severity 是否在有效范围内
			assert.True(t, tt.statusEntry.Severity >= SeverityLevelHealthy && tt.statusEntry.Severity <= SeverityLevelInvalid,
				"Severity should be within valid range")

			// 验证 Message 格式（当不是健康状态时）
			if tt.statusEntry.Severity != SeverityLevelHealthy {
				assert.Contains(t, tt.statusEntry.Message, tt.statusEntry.Component,
					"Message should contain component name")
			}

			// 验证 DeviceUUID 不为空
			assert.NotEmpty(t, tt.statusEntry.DeviceUUID, "DeviceUUID should not be empty")
		})
	}
}

// Package v1
/**
* @Project : terravirtualmachine
* @File    : network.go
* @IDE     : GoLand
* <AUTHOR> j<PERSON><EMAIL>
* @Date    : 2025/1/20 17:10
**/

package v1

import (
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
)

// Network 表示网络
type Network struct {
	metav1.ObjectMeta `json:"metadata,omitempty"` // 标准对象元数据

	NetworkID     string                    `json:"network_id"`     // 网络的唯一标识符
	NetworkName   string                    `json:"name"`           //网络名称
	HostID        string                    `json:"host_id"`        // 主机的唯一标识符（专用网络所在的主机）
	HostName      string                    `json:"host_name"`      // 主机的名称（专用网络所在的主机）
	Interfaces    map[string]*InterfaceList `json:"interfaces"`     // 该网络关联的网络接口映射（key-主机ID，val-该主机上的网络接口列表）
	NumGuests     int                       `json:"num_guests"`     // 连接到此网络的虚拟机数量
	NumHosts      int                       `json:"num_hosts"`      // 该网络关联的主机数量
	NumInterfaces int                       `json:"num_interfaces"` // 该网络的网络接口数量
	Type          NetworkType               `json:"type"`           // 网络的类型，例如 “private” 或 “external”
	VLANID        int                       `json:"vlan_id"`        // 该网络的 VLAN 标识符
}

// Interface 表示网络接口
type Interface struct {
	Checked       bool            `json:"checked"`        // 是否选中该网络接口
	HostID        string          `json:"host_id"`        // 此网络接口所属主机的唯一标识符
	HostName      string          `json:"host_name"`      // 此网络接口所属主机的主机名称
	InterfaceID   string          `json:"interface_id"`   // 网络接口的唯一标识符
	InterfaceName string          `json:"interface_name"` // 网络接口的名称，例如 “ovs_eth0”
	IP            []string        `json:"ip"`             // 该网络接口的 IP 地址
	Mask          []string        `json:"mask"`           // 该网络接口的子网掩码
	SpeedMbs      int             `json:"speed_mbs"`      // 网络接口的速度（Mbps），-1表示未知
	Status        InterfaceStatus `json:"status"`         // 网络接口的状态，例如 “connected” 或 “disconnected”
}

// NetworkType 表示网络的类型
type NetworkType string

const (
	PRIVATE  NetworkType = "private"  // 专用网络
	EXTERNAL NetworkType = "external" // 外部网络
)

// InterfaceStatus 表示网络接口的状态
type InterfaceStatus string

const (
	CONNECTED    InterfaceStatus = "connected"    // 已连接
	DISCONNECTED InterfaceStatus = "disconnected" // 已断开
)

// NetworkList 表示网络列表
type NetworkList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items []*Network `json:"items"` // 网络列表
}

// InterfaceList 表示网络接口列表
type InterfaceList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items []*Interface `json:"items"` // 网络接口列表
}

// NetworkHost 表示网络模块所使用的主机信息
type NetworkHost struct {
	HostID string `json:"host_id"` // 主机的唯一标识符
}

// NetworkHostList 表示网络模块所使用的主机信息列表
type NetworkHostList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items []*NetworkHost `json:"items"` // 主机列表
}

// VNic 表示虚拟网络接口
type VNic struct {
	VNicID      string                     `json:"virtual_interface_id"` // 虚拟网络接口的唯一标识符
	GuestID     string                     `json:"guest_id"`             // 虚拟机的唯一标识符，表明该接口属于哪个虚拟机
	GuestName   string                     `json:"guest_name"`           // 虚拟机的名称
	MacAddress  string                     `json:"mac_address"`          // 虚拟机虚拟网卡mac地址，用于在网络中唯一标识该接口
	NetworkID   string                     `json:"network_id"`           // 虚拟网络接口所属的网络的唯一标识符
	NetworkName string                     `json:"network_name"`         // 虚拟网络接口所属的网络的名称
	VNicType    int                        `json:"vnic_type"`            // 虚拟网络接口的类型
	Running     map[string]*VNicruningList `json:"running"`              // 虚拟网络接口的运行信息
	PreferSRIOV bool                       `json:"sr_iov"`               //是否优先支持SR-IOV技术
}

// VNicruning 表示虚拟网络接口在特定主机上的运行时信息
type VNicruning struct {
	InterfaceID   string `json:"interface_id"`   // 虚拟网络接口所连接的物理网络接口的唯一标识符
	InterfaceName string `json:"interface_name"` // 虚拟网络接口所连接的物理网络接口的名称
	OVSPort       string `json:"ovs_port"`       // 虚拟网络接口所连接的 Open vSwitch 端口的名称
	UseVf         bool   `json:"use_vf"`         //是否使用VF
	VfPoolName    string `json:"vf_pool_name"`   //VF池名字
}

type VNicruningList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items []*VNicruning `json:"items"` // 虚拟机上的运行信息表
}

// VNicList 表示虚拟网络接口列表
type VNicList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items []*VNic `json:"items"` // 虚拟网络接口列表
}

func (v *VNic) OVSPort(hostId string, vnicId string) string {
	if vnicList, ok := v.Running[hostId]; ok {
		for _, vnic := range vnicList.Items {
			if vnic.InterfaceID == vnicId {
				return vnic.OVSPort
			}
		}
	}
	return ""
}

func (v VNicList) ListVNicIDs() []string {
	ids := make([]string, 0, len(v.Items))
	for _, vnic := range v.Items {
		ids = append(ids, vnic.VNicID)
	}
	return ids
}

package v1

import metav1 "github.com/marmotedu/component-base/pkg/meta/v1"

// file config
type VGuestConfig struct {
	AutoSwitch      int    `json:"auto_switch"`      // 是否自动切换
	Autorun         bool   `json:"autorun"`          // 是否自动运行
	BootFrom        string `json:"boot_from"`        // 启动方式，例如 "disk"
	CPUPassthru     bool   `json:"cpu_passthru"`     // CPU 直通
	CPUPinNum       int    `json:"cpu_pin_num"`      // CPU 绑定的核数
	CPUWeight       int    `json:"cpu_weight"`       // CPU 权重
	Desc            string `json:"desc"`             // 描述信息
	HypervEnlighten bool   `json:"hyperv_enlighten"` // 是否启用 Hyper-V 特性
	IsGeneralVM     bool   `json:"is_general_vm"`    // 是否为通用虚拟机
	KBLayout        string `json:"kb_layout"`        // 键盘布局
	MachineType     string `json:"machine_type"`     // 机器类型，例如 "pc"
	Name            string `json:"name"`             // 虚拟机名称
	SerialConsole   bool   `json:"serial_console"`   // 是否启用串行控制台
	USBVersion      int    `json:"usb_version"`      // USB 版本
	UseOVMF         bool   `json:"use_ovmf"`         // 是否使用 OVMF（UEFI）
	UTCOffset       int    `json:"utc_offset"`       // UTC 时间偏移量
	VCPUNum         int    `json:"vcpu_num"`         // 虚拟 CPU 数量
	VideoCard       string `json:"video_card"`       // 视频卡类型，例如 "vmvga"
	VRAMSize        int64  `json:"vram_size"`        // 虚拟机内存大小（MB）
}

type VNicConfig struct {
	PreferSriov bool   `json:"prefer_sriov"`        //
	VNicType    int    `json:"vnic_type,omitempty"` //磁盘状态类型
	Mac         string `json:"mac,omitempty"`       // Mac地址
	VNicUuid    string `json:"vnic_uuid,omitempty"` //vnic
}

type VDiskConfig struct {
	VDiskMode  int  `json:"vdisk_mode,omitempty"` // 磁盘控制模式
	IsMetaDisk bool `json:"is_meta_disk,omitempty"`
	IsDummy    bool `json:"is_dummy,omitempty"`
}

type VDiskConfigList struct {
	Items map[string]VDiskConfig // vdiskID -> vdisk
}

// backend store
type RepositoryPool struct {
	RepoId             string  `json:"repoId"`               //存储空间唯一标识
	Name               string  `json:"name"`                 //存储空间名字
	HostID             string  `json:"host_id"`              //主机唯一标识
	LocationVolume     string  `json:"location_volume"`      //存储空间所在volume
	FileSystem         string  `json:"file_system"`          //存储空间文件系统
	TotalSize          float64 `json:"total_size"`           //存储空间总容量
	HardLimit          float64 `json:"hard_limit"`           //空间不足阈值
	SoftLimit          float64 `json:"soft_limit"`           //空间不足百分比阈值
	EnableLowerNotify  bool    `json:"enable_lower_notify"`  //可用空间低于"空间不足"阈值时通知
	LastNotifyTreshold string  `json:"last_notify_treshold"` //最后通知阈值
}

type RepositoryPoolList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items map[string]*RepositoryPool `json:"items"` // 存储空间列表
}

const (
	//Repository directory name in Volume
	RepoPathInVolume = "/@Repository"

	//config file name
	VnicConf   = "vnic.conf"
	VdiskConf  = "vdisk.conf"
	VguestConf = "vguest.conf"

	//repository status
	StatusNormal       = "normal"
	StatusInsufficient = "insufficient"
)

package v1

import (
	"os"
	"strconv"
	"strings"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/golibrary/utils"
)

const (
	GuestEtcdPrefix = "/twm/live_cluster/guests/"
	UsbToolConfJson = "/etc/tos/config/storage_device.json"
	DeviceNotUse    = "umounted"
)

type GuestHardwareTemplate struct {
	OperatingSystem string `json:"operating_system"`
	Disk            string `json:"disk"`
	Display         string `json:"display"`
	Control         string `json:"control"`
	USB             string `json:"usb,omitempty"`
	Tablet          string `json:"tablet,omitempty"`
}

type GuestNetworkInterface struct {
	NetworkSource   string `json:"network_source"`
	HardwareAddress string `json:"hardware_address"`
	NetBus          string `json:"network_bus"`
}

type GuestStoreVDisk struct {
	Device string `json:"source_dev"`
	DevBus string `json:"device_bus"`
	NAA    string `json:"naa"` // 块设备标识符
	Idx    int    `json:"idx"` // 设备序号
	// IO 速率控制
	WriteSpeed   uint64 `json:"write_speed,omitempty"`
	WriteIOPS    uint64 `json:"write_iops,omitempty"`
	ReadSpeed    uint64 `json:"read_speed,omitempty"`
	ReadIOPS     uint64 `json:"read_iops,omitempty"`
	WriteIOPSMax uint64 `json:"write_iops_max,omitempty"`
	ReadIOPSMax  uint64 `json:"read_iops_max,omitempty"`
}

// 创建虚拟机配置
type GuestConfig struct {
	Name          string
	ID            string
	User          string
	Group         string
	Cores         uint
	Memory        uint
	AutoStart     int
	System        string
	MonitorPort   uint
	MonitorSecret string
	Created       bool
	Progress      uint

	Running            bool
	VDisks             []GuestStoreVDisk
	VNics              []GuestNetworkInterface
	NetworkAddress     string
	Ports              []uint
	AuthUser           string
	AuthSecret         string
	StorageUsb         []string // Bus:Device:vendorId:productId
	SystemVersion      string
	Initialized        bool
	RootLoginEnabled   bool
	DataPath           string
	QEMUAvailable      bool
	CloudInitAvailable bool
	BootImages         []string
	CreateTime         string
	AddressAllocation  string
	InternalAddress    string
	ExternalAddress    string
	ReceiveSpeed       uint64
	SendSpeed          uint64
	CPUPriority        PriorityEnum
	CPUPassThru        bool
	OSMachine          string
	OSBoot             string
	Template           *GuestHardwareTemplate

	// StoragePool        string             `json:"storage_pool"`
	// StorageVolumes     []string           `json:"storage_volumes"`
	// DiskBus            []string           `json:"disk"`
	// StorageMode        InstanceStorageMode `json:"storage_mode"`
	// NetworkMode        InstanceNetworkMode `json:"network_mode"`
	// Security           *SecurityPolicy     `json:"security,omitempty"`
}

type VMInstancePowerState int

const (
	VMInstancePowerStatePreparing = iota
	VMInstancePowerStateCraeting
	VMInstancePowerStateBooting
	VMInstancePowerStateRuning
	VMInstancePowerStateShutdown
	VMInstancePowerStateSaved
	VMInstancePowerStateInvalid
)

const (
	PowerStatePreparing = "preparing"
	PowerStateCraeting  = "creating"
	PowerStateBooting   = "booting"
	PowerStateRuning    = "running"
	PowerStateShutdown  = "shutdown"
	PowerStateSaved     = "saved"
)

func (value VMInstancePowerState) ToString() string {
	switch value {
	case VMInstancePowerStatePreparing:
		return PowerStatePreparing
	case VMInstancePowerStateBooting:
		return PowerStateBooting
	case VMInstancePowerStateCraeting:
		return PowerStateCraeting
	case VMInstancePowerStateRuning:
		return PowerStateRuning
	case VMInstancePowerStateSaved:
		return PowerStateSaved
	case VMInstancePowerStateShutdown:
		return PowerStateShutdown
	default:
		return "invalid"
	}
}

func (value *VMInstancePowerState) ToPowerState(isRunning bool) {
	if isRunning {
		*value = VMInstancePowerStateRuning
	} else {
		*value = VMInstancePowerStateShutdown
	}
}

type VMInstanceSystemState int

const (
	SystemStateNetworkException = iota
	SystemStateOfflineForHost
	SystemStateStorageSpaceFull
	SystemStateStorageDamage
	SystemStateStorageLose
	SystemStateStateInvalid
)

const (
	NetworkException = "VM_Warning_NetworkException"
	OfflineForHost   = "VM_Error_OfflineForHost"
	StorageSpaceFull = "VM_Error_StorageSpaceFull"
	StorageDamage    = "VM_Error_StorageDamage"
	StorageLose      = "VM_Error_StorageLose"
	StateInvalid     = "invalid"
)

func (value VMInstanceSystemState) ToString() string {
	switch value {
	case SystemStateNetworkException:
		return PowerStatePreparing
	case SystemStateOfflineForHost:
		return PowerStateBooting
	case SystemStateStorageSpaceFull:
		return PowerStateCraeting
	case SystemStateStorageDamage:
		return PowerStateRuning
	case SystemStateStorageLose:
		return PowerStateSaved
	case SystemStateStateInvalid:
		return PowerStateShutdown
	default:
		return "invalid"
	}
}

// VMConfig 结构体表示虚拟机的配置信息
type VMInstance struct {
	metav1.ObjectMeta `json:"metadata,omitempty"`

	AutoSwitch      int      `json:"auto_switch"`      // 是否自动切换
	Autorun         int      `json:"autorun"`          // 是否自动运行
	BootFrom        string   `json:"boot_from"`        // 启动方式，例如 "disk"
	CPUPassthru     bool     `json:"cpu_passthru"`     // CPU 直通
	CPUPinNum       int      `json:"cpu_pin_num"`      // CPU 绑定的核数
	CPUWeight       int      `json:"cpu_weight"`       // CPU 权重
	CreatingHost    string   `json:"creating_host"`    // 创建该 VM 的主机
	HostID          string   `json:"host_id"`          // 创建该 VM 的主机
	Desc            string   `json:"desc"`             // 描述信息
	HypervEnlighten bool     `json:"hyperv_enlighten"` // 是否启用 Hyper-V 特性
	IsGeneralVM     bool     `json:"is_general_vm"`    // 是否为通用虚拟机
	ISOImages       []string `json:"iso_images"`       // 挂载的 ISO 镜像
	KBLayout        string   `json:"kb_layout"`        // 键盘布局
	MachineType     string   `json:"machine_type"`     // 机器类型，例如 "pc"
	Name            string   `json:"name"`             // 虚拟机名称
	RepositoryID    string   `json:"repository_id"`    // 存储池 ID
	RepoName        string   `json:"repository_name"`  // 存储池名称
	SerialConsole   bool     `json:"serial_console"`   // 是否启用串行控制台
	State           string   `json:"state"`            // 当前状态，例如 "shutdown"
	StateValue      int      `json:"statevalue"`       // 状态值
	USBVersion      int      `json:"usb_version"`      // USB 版本
	USBs            []string `json:"usbs"`             // 连接的 USB 设备
	USBDeviceName   []string `json:"usb_device_name"`  // USB 设备名称
	UseOVMF         bool     `json:"use_ovmf"`         // 是否使用 OVMF（UEFI）
	UTCOffset       int      `json:"utc_offset"`       // UTC 时间偏移量
	VCPUNum         int      `json:"vcpu_num"`         // 虚拟 CPU 数量
	VDisks          []string `json:"vdisks"`           // 连接的虚拟磁盘
	VideoCard       string   `json:"video_card"`       // 视频卡类型，例如 "vmvga"
	VNics           []string `json:"vnics"`            // 网络接口卡 ID
	VRAMSize        int64    `json:"vram_size"`        // 虚拟机内存大小（MB）
	ShutdownReason  int      `json:"shutdown_reason"`  //
	// IsForcedShutdown bool     `json:"is_forced_shutdown"` // 是否是强制关机的
}

type VMInstanceList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items map[string]*VMInstance `json:"items"` // 镜像列表， 镜像id <-> 镜像实体
}

type VNCTokenConfig struct {
	TokenCfg string
	Port     string
	Url      string
}

func NewVNCTokenConfig(tokenCfg string, port string, url string) VNCTokenConfig {
	if !utils.Exists(tokenCfg) {
		os.Create(tokenCfg)
	}

	return VNCTokenConfig{
		TokenCfg: tokenCfg,
		Port:     port,
		Url:      url,
	}
}

func (v VNCTokenConfig) configFile() string {
	return v.TokenCfg
}

func (v VNCTokenConfig) configSeq() string {
	return ":  "
}

func (v VNCTokenConfig) VNCLinkUrlAndPort(guestID string) (string, int) {
	port, _ := strconv.Atoi(v.Port)

	return v.Url + guestID, port
}

func (v VNCTokenConfig) AddVNCLink(guestID string, ip string, port string) error {
	path := v.configFile()
	seq := v.configSeq()

	data := make(map[string]string)
	data[guestID] = strings.Join([]string{ip, port}, ":")
	if _, err := utils.WriteConfig(path, data, seq); err != nil {
		log.Info("add vnc config fail,will don't to link vm")
		return err
	}
	return nil
}

func (v VNCTokenConfig) RemoveVNCLink(guestID string) error {
	path := v.configFile()
	seq := v.configSeq()

	fd, err := os.CreateTemp(VolumeTempDir, "token.conf_*")
	if err != nil {
		log.Info("remove vnc link - create temp file fail")
		return err
	}

	_, data, err := utils.ParsConfig(path, seq)

	if err != nil {
		log.Info("read vnc config fail")
		return err
	}

	for id, _ := range data {
		if id == guestID {
			delete(data, id)
			break
		}
	}

	tempFile := fd.Name()
	if _, err := utils.WriteConfig(tempFile, data, seq); err != nil {
		log.Info("remove vnc link - will don't to link vm")
		return err
	}

	if err := os.Rename(tempFile, path); err != nil {
		log.Info("remove vnc link - rename temp file fail")
	}

	fd.Close()
	return nil
}

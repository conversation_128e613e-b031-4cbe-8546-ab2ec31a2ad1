package v1

// 状态等级枚举
type SystemSeverityLevel int

const (
	SeverityLevelHealthy = iota
	SeverityLevelRunning
	SeverityLevelWarning
	SeverityLevelError
	SeverityLevelInvalid
)

const (
	Healthy = "healthy"
	Running = "running"
	Warning = "warning"
	Error   = "error"
	Invalid = "invalid"
)

func (level SystemSeverityLevel) ToString() string {
	switch level {
	case SeverityLevelHealthy:
		return Healthy
	case SeverityLevelRunning:
		return Running
	case SeverityLevelWarning:
		return Warning
	case SeverityLevelError:
		return Error
	default:
		return Invalid
	}
}

type StatusEntry struct {
	Component  string              // 组件类型
	Severity   SystemSeverityLevel // 严重等级
	Message    string              // 状态描述（三字段描述:组件_等级_简要描述, 例如:storage_warning_ThresholdExceeded）,健康状态返回空
	DeviceUUID []string            // 设备UUID
}

type ClusterHealth struct {
	TotalStatus   map[string]*ComponentHealth // 各组件健康状态
	GlobalStatus  SystemSeverityLevel         // 集群整体状态
	StatusEntries []StatusEntry               // 原始状态条目
}

type ComponentHealth struct {
	Healthy   int            // 健康数量
	Running   int            // 运行中数量
	Warnings  int            // 警告数量
	Errors    int            // 错误数量
	LatestMsg map[string]int // 最新消息统计（message->count）
}

package v1

import metav1 "github.com/marmotedu/component-base/pkg/meta/v1"

type VMUsage struct {
	MemoryUsage     float64   `json:"memory_usage"`     // 内存使用率（百分比）
	Disk            []Disk    `json:"disk"`             // 磁盘使用信息列表
	Network         []Network `json:"network"`          // 网络使用信息列表
	VCPUUsage       float64   `json:"vcpu_usage"`       // 虚拟 CPU 使用率（百分比）
	TotalIOPS       int       `json:"total_iops"`       // 总 IOPS
	TotalThroughput float64   `json:"total_throughput"` // 总吞吐量（单位：KB/s）
	TotalIOLatency  int       `json:"total_io_latency"` // 总 I/O 延迟（单位：毫秒）
}

type Disk struct {
	metav1.ObjectMeta `json:"metadata,omitempty"` // 标准对象元数据

	VDiskID         string  `json:"vdisk_id"`          // 虚拟磁盘 ID
	ReadIOPS        float64 `json:"read_iops"`         // 读取 IOPS（每秒输入/输出操作数）
	WriteIOPS       float64 `json:"write_iops"`        // 写入 IOPS
	TotalIOPS       float64 `json:"total_iops"`        // 总 IOPS
	ReadThroughput  float64 `json:"read_throughput"`   // 读取吞吐量（单位：KB/s）
	WriteThroughput float64 `json:"write_throughput"`  // 写入吞吐量（单位：KB/s）
	TotalThroughput float64 `json:"total_throughput"`  // 总吞吐量（单位：KB/s）
	ReadAvgLatency  float64 `json:"read_avg_latency"`  // 读取平均延迟（单位：毫秒）
	WriteAvgLatency float64 `json:"write_avg_latency"` // 写入平均延迟（单位：毫秒）
	TotalIOLatency  float64 `json:"total_io_latency"`  // 总 I/O 延迟（单位：毫秒）
}

type Network struct {
	Device  string  `json:"device"`   // 网络设备名称
	RxBytes float64 `json:"rx_bytes"` // 接收字节数
	TxBytes float64 `json:"tx_bytes"` // 发送字节数
}

type VMUsageList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items map[string]*VMUsage `json:"items"` // 虚拟机使用信息列表， 虚拟机id <-> 虚拟机使用信息
}

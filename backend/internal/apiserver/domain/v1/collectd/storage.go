package v1

import metav1 "github.com/marmotedu/component-base/pkg/meta/v1"

type StorageInfo struct {
	VolumePath   string  `json:"volume_path"`    // 卷路径
	SizeFreeByte float64 `json:"size_free_byte"` // 空闲空间（字节）
	SizeUsedByte float64 `json:"size_used_byte"` // 已用空间（字节）
	PercentFree  float64 `json:"percent_free"`   // 空闲空间百分比
	PercentUsed  float64 `json:"percent_used"`   // 已用空间百分比
}

type StorageState struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Storage map[string]*StorageInfo `json:"storage"` // 存储设备名到详情的映射
}

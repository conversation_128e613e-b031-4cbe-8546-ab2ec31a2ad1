package v1

import "context"

// 统一消息协议
type CollectdMessage struct {
	Module    ModuleType  `json:"module"`   // 模块类型枚举
	Operation OpType      `json:"op"`       // 操作类型
	Resource  string      `json:"resource"` // 资源标识符
	Data      interface{} `json:"data"`     // 动态数据
	Version   int64       `json:"version"`  // 数据版本
}

// 模块类型枚举
type ModuleType string

const (
	ModuleCPU     ModuleType = "cpu"
	ModuleMemory  ModuleType = "memory"
	ModuleStorage ModuleType = "storage"
	// 扩展其他模块...
)

// 操作类型枚举
type OpType string

const (
	OpCreate OpType = "create"
	OpUpdate OpType = "update"
	OpDelete OpType = "delete"
)

type WatchConfig struct {
	ResourceType string // 资源类型，如"system_utilization"
	Namespace    string // 命名空间
	HostFilter   string // 新增主机IP字段
	Callbacks    WatchCallbacks
}

type WatchCallbacks struct {
	OnUpdate func(ctx context.Context, resourceID string, newData map[string]string)
	OnDelete func(ctx context.Context, resourceID string)
}

type WatchEvent struct {
	EventType  string // "create|update|delete"
	ResourceID string
	Data       map[string]string
}

package v1

import metav1 "github.com/marmotedu/component-base/pkg/meta/v1"

// 网络接口信息
type NetworkInterface struct {
	IP      string `json:"ip" redis:"ip"`             // IP地址（可能为空）
	Mask    string `json:"mask" redis:"mask"`         // 子网掩码
	Speed   int    `json:"speed" redis:"speed"`       // 速率(Mbps)，-1表示未知
	Status  string `json:"status" redis:"status"`     // 连接状态
	Type    string `json:"type" redis:"type"`         // 接口类型
	UseDHCP bool   `json:"use_dhcp" redis:"use_dhcp"` // DHCP状态
}

// 完整网络状态集合
type NetworkState struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Interfaces map[string]*NetworkInterface `json:"interfaces"` // 接口名到详情的映射
}

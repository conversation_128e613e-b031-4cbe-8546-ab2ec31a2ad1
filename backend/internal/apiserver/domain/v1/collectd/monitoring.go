// domain/monitoring/monitoring_service.go
package v1

import (
	"encoding/json"
	"strings"
	"time"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
)

// 系统利用率
type SystemUtilization struct {
	CPU     CPUInfo        `json:"cpu"`
	Memory  MemoryInfo     `json:"memory"`
	Time    TimeInfo       `json:"time"`
	Network NetworSyskInfo `json:"network"`
}

type CPUInfo struct {
	TotalUsage string             `json:"total_usage"` // 总 CPU 使用率（百分比）
	CPUs       map[string]CPULoad `json:"-"`
}

// 实现 json.Marshaler 接口
func (c CPUInfo) MarshalJSON() ([]byte, error) {
	// 创建一个临时 map 来存储所有字段
	temp := make(map[string]interface{})
	temp["total_usage"] = c.TotalUsage

	// 将 CPUs 字段的键值对添加到临时 map 中
	for key, value := range c.CPUs {
		temp[key] = value
	}

	// 将临时 map 序列化为 JSON
	return json.Marshal(temp)
}

// 实现 json.Unmarshaler 接口
func (c *CPUInfo) UnmarshalJSON(data []byte) error {
	// 创建一个临时 map 来存储原始数据
	var rawData map[string]json.RawMessage
	if err := json.Unmarshal(data, &rawData); err != nil {
		return err
	}

	// 解析 total_usage 字段
	if totalUsage, ok := rawData["total_usage"]; ok {
		if err := json.Unmarshal(totalUsage, &c.TotalUsage); err != nil {
			return err
		}
	}

	// 初始化 CPUs 字段
	c.CPUs = make(map[string]CPULoad)

	// 解析 cpu0、cpu1 等动态键值对
	for key, value := range rawData {
		if strings.HasPrefix(key, "cpu") {
			var cpuLoad CPULoad
			if err := json.Unmarshal(value, &cpuLoad); err != nil {
				return err
			}
			c.CPUs[key] = cpuLoad
		}
	}

	return nil
}

// CPULoad 单个 CPU 核心的负载信息
type CPULoad struct {
	UserLoad   string `json:"user_load"`   // 用户态负载（百分比）
	SystemLoad string `json:"system_load"` // 系统态负载（百分比）
	OtherLoad  string `json:"other_load"`  // 其他负载（百分比）
}

type MemoryInfo struct {
	Used        float64 `json:"used"`
	Cached      float64 `json:"cached"`
	Free        float64 `json:"free"`
	Buffered    float64 `json:"buffered"`
	SlabUnrecl  float64 `json:"slab_unrecl"`
	SlabRecl    float64 `json:"slab_recl"`
	Utilization string  `json:"utilization"` // 内存使用率（百分比）
}

type NetworSyskInfo struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Interfaces map[string]*InterfaceStats `json:"interfaces"`
}

type InterfaceStats struct {
	RX float64 `json:"rx"` // 原rx_bytes
	TX float64 `json:"tx"` // 原tx_bytes
}

// 自定义JSON解析
func (n *NetworSyskInfo) UnmarshalJSON(data []byte) error {
	return json.Unmarshal(data, &n.Interfaces)
}

// 自定义JSON序列化
func (n NetworSyskInfo) MarshalJSON() ([]byte, error) {
	return json.Marshal(n.Interfaces)
}

type TimeInfo struct {
	CurrentTime string  `json:"current_time"` // 保持字符串格式
	Uptime      float64 `json:"uptime"`
}

// 自定义时间类型处理Unix时间戳
type UnixTime time.Time

func (ut *UnixTime) UnmarshalJSON(b []byte) error {
	var ts float64
	if err := json.Unmarshal(b, &ts); err != nil {
		return err
	}
	*ut = UnixTime(time.Unix(0, int64(ts*1e9)))
	return nil
}

// 虚拟盘利用率
type VdiskUtilization struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	VirtualDisks map[string]*Stats `json:"-"` // Redis哈希存储结构
}

// 单块虚拟磁盘性能指标
type Stats struct {
	// 基础标识
	LunUUID string `json:"lun_uuid"` // 存储单元唯一标识
	LunName string `json:"lun_name"` // 存储单元名称

	// 队列状态
	QueueDepth         int `json:"queue_depth"`      // 当前队列深度
	OngoingCommandCnt  int `json:"ongoing_cmd_cnt"`  // 进行中命令数
	DeferredCommandCnt int `json:"deferred_cmd_cnt"` // 延迟命令数

	// 读操作指标（单位：字节/毫秒）
	ReadBytes      uint64 `json:"read_bytes"`        // 读取字节总量
	ReadIOPS       uint32 `json:"read_iops"`         // 每秒读操作数
	ReadThroughput uint64 `json:"read_throughput"`   // 读吞吐量（B/s）
	ReadAvgLatency uint32 `json:"read_avg_latency"`  // 平均读延迟（ms）
	ReadAvgCmdSize uint32 `json:"read_avg_cmd_size"` // 平均读命令大小

	// 写操作指标（单位：字节/毫秒）
	WriteBytes      uint64 `json:"write_bytes"`        // 写入字节总量
	WriteIOPS       uint32 `json:"write_iops"`         // 每秒写操作数
	WriteThroughput uint64 `json:"write_throughput"`   // 写吞吐量（B/s）
	WriteAvgLatency uint32 `json:"write_avg_latency"`  // 平均写延迟（ms）
	WriteAvgCmdSize uint32 `json:"write_avg_cmd_size"` // 平均写命令大小

	// 综合指标
	TotalIOPS       uint32 `json:"total_iops"`       // 总IOPS
	TotalThroughput uint64 `json:"total_throughput"` // 总吞吐量
	TotalCmdCount   uint64 `json:"total_cmd_count"`  // 历史累计命令总数
	TotalIOLatency  uint64 `json:"total_io_latency"` // 总I/O延迟时间

	// 网络传输指标（可选）
	RXAvgLatency uint32 `json:"rx_avg_latency"` // 接收平均延迟
	TXAvgLatency uint32 `json:"tx_avg_latency"` // 发送平均延迟
}

// 自定义反序列化逻辑
func (u *VdiskUtilization) UnmarshalJSON(data []byte) error {
	return json.Unmarshal(data, &u.VirtualDisks)
}

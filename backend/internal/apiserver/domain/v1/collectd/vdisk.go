package v1

import (
	"encoding/json"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
)

type VDiskUsage struct {
	Size float64 `json:"size"` // 磁盘总大小（字节）
	Used float64 `json:"used"` // 磁盘已使用大小（字节）
}

type VDiskSpec struct {
	Disks     map[string]*VDiskUsage `json:"-"`
	TotalSize float64                `json:"total_size"` // 磁盘总大小（字节）
	TotalUsed float64                `json:"total_used"` // 磁盘已使用大小（字节）
	RawDisks  json.RawMessage        `json:"-"`
}

type VDiskList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items map[string]*VDiskSpec `json:"-"`
}

// UnmarshalJSON 自定义反序列化逻辑
func (v *VDiskSpec) UnmarshalJSON(data []byte) error {
	// 先解析已知固定字段
	type Alias struct {
		TotalSize float64 `json:"total_size"`
		TotalUsed float64 `json:"total_used"`
	}
	var aux Alias
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}
	v.TotalSize = aux.TotalSize
	v.TotalUsed = aux.TotalUsed

	// 解析动态磁盘字段
	var raw map[string]json.RawMessage
	if err := json.Unmarshal(data, &raw); err != nil {
		return err
	}

	v.Disks = make(map[string]*VDiskUsage)
	for key, value := range raw {
		// 跳过已处理的固定字段
		if key == "total_size" || key == "total_used" {
			continue
		}

		var usage VDiskUsage
		if err := json.Unmarshal(value, &usage); err != nil {
			return err
		}
		v.Disks[key] = &usage
	}

	return nil
}

// 添加自定义序列化方法
func (v VDiskSpec) MarshalJSON() ([]byte, error) {
	// 创建临时结构体包含所有字段
	tmp := make(map[string]interface{})
	tmp["total_size"] = v.TotalSize
	tmp["total_used"] = v.TotalUsed

	// 添加磁盘数据
	for uuid, usage := range v.Disks {
		tmp[uuid] = map[string]interface{}{
			"size": usage.Size,
			"used": usage.Used,
		}
	}
	return json.Marshal(tmp)
}

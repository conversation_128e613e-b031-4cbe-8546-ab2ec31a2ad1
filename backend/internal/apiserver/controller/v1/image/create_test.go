package image

// import (
// 	"bytes"
// 	"crypto/md5"
// 	"crypto/sha256"
// 	"encoding/hex"
// 	"encoding/json"
// 	"fmt"
// 	"io"
// 	"math/rand"
// 	"net/http"
// 	"os"
// 	"os/exec"
// 	"path"
// 	"strconv"
// 	"testing"
// 	"time"

// 	"github.com/gin-gonic/gin"
// 	"github.com/gorilla/websocket"
// 	"github.com/marmotedu/iam/pkg/log"
// 	"github.com/stretchr/testify/assert"
// 	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
// 	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
// 	"gitlab.local/golibrary/resp"
// )

// var testFileDir = "./testDir/"

// type uploadClient struct {
// 	taskCreate   map[domain.TaskID]*dto.ImageCreate
// 	taskProgress map[domain.TaskID]chan *dto.ImageProgress
// 	taskConn     map[domain.TaskID]*websocket.Conn
// }

// func (up *uploadClient) uploadFile(t *testing.T, taskid domain.TaskID) error {
// 	filePath := path.Join(testFileDir, up.taskCreate[taskid].DiskFileName)
// 	go func() {
// 		// 打开文件
// 		file, err := os.Open(filePath)
// 		if err != nil {
// 			return
// 		}
// 		defer file.Close()

// 		info, err := os.Stat(filePath)
// 		if err != nil {
// 			return
// 		}

// 		blockSize := 64 * 1024
// 		totalBlocks := info.Size() / int64(blockSize)
// 		curBlockNum := 1
// 		buffer := make([]byte, blockSize) // 每次发送64KB

// 		if info.Size()%int64(blockSize) != 0 {
// 			totalBlocks += 1
// 		}

// 		var chunk dto.IamgefileChunk
// 		var offset int64

// 		offset = 0
// 		for i := 0; i < int(totalBlocks); i++ {
// 			n, err := file.ReadAt(buffer, offset)
// 			if err != nil && err.Error() != "EOF" {
// 				return
// 			}

// 			chunk.FileName = filePath
// 			chunk.FileSize = info.Size()
// 			chunk.ChunkData = string(buffer)
// 			chunk.BlockNumber = curBlockNum
// 			chunk.TotalBlocks = int(totalBlocks)
// 			chunk.BlockHash = calculateHash([]byte(chunk.ChunkData))
// 			chunk.ChunkOffset = offset

// 			by, err := json.Marshal(chunk)
// 			if err != nil {
// 				return
// 			}
// 			// 发送数据
// 			if n > 0 {
// 				err = up.taskConn[taskid].WriteMessage(websocket.BinaryMessage, by)
// 				if err != nil {
// 					return
// 				}
// 			}

// 			curBlockNum += 1
// 			offset += int64(blockSize)

// 			// 检测进度正确性
// 			upPro := <-up.taskProgress[taskid]
// 			progress := float64(i) / float64(totalBlocks) * 100

// 			log.Infof("taskid : %d,upload:%d name: %s,progress: %d,status: %s", taskid, int(progress+0.5), upPro.Name, upPro.Progress, upPro.Status)
// 			// assert.True(t, (int(progress+0.5) >= upPro.Progress))
// 		}

// 		up.taskConn[taskid].Close()
// 	}()

// 	return nil
// }

// // 计算文件块的 SHA-256 哈希值
// func calculateHash(data []byte) string {
// 	hash := sha256.New()
// 	hash.Write(data)
// 	return hex.EncodeToString(hash.Sum(nil))
// }

// func generateRandomFile(filePath string, size int) error {
// 	if _, err := os.Stat(path.Dir(filePath)); os.IsNotExist(err) {
// 		err := os.MkdirAll(path.Dir(filePath), os.ModePerm)
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	// 创建文件
// 	file, err := os.Create(filePath)
// 	if err != nil {
// 		return err
// 	}
// 	defer file.Close()

// 	// 使用一个简单的字符集生成随机数据
// 	charset := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
// 	rand.Seed(time.Now().UnixNano())

// 	// 缓冲区大小，可以根据需求调整
// 	bufferSize := 3 * 1024 * 1024 // 1MB

// 	// 创建缓冲区
// 	buffer := make([]byte, bufferSize)

// 	// 写入随机数据
// 	for i := 0; i < size/bufferSize; i++ {
// 		// 填充缓冲区
// 		for j := 0; j < bufferSize; j++ {
// 			buffer[j] = charset[rand.Intn(len(charset))]
// 		}

// 		// 写入缓冲区数据
// 		_, err := file.Write(buffer)
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	return nil
// }

// func fileEqual(f1 string, f2 string) bool {
// 	fd1, _ := os.Open(f1)
// 	hash1 := md5.New()
// 	io.Copy(hash1, fd1)
// 	md5Hash1 := hash1.Sum(nil)

// 	fd2, _ := os.Open(f2)
// 	hash2 := md5.New()
// 	io.Copy(hash2, fd2)
// 	md5Hash2 := hash2.Sum(nil)

// 	return string(md5Hash1) == string(md5Hash2)
// }

// func generateRepositoryPool(t *testing.T, locates []dto.ImageLocate) *domain.RepositoryPoolList {
// 	_ = exec.Command("etcdctl",
// 		"--endpoints=http://127.0.0.1:2379",
// 		"del", "/twm/live_cluster/", "--prefix").Run()

// 	pools := domain.RepositoryPoolList{}
// 	pools.ListMeta.TotalCount = int64(len(locates))
// 	pools.Items = make(map[string]*domain.RepositoryPool)

// 	for i := 0; i < len(locates); i++ {
// 		id := locates[i].RepoId

// 		pools.Items[id] = &domain.RepositoryPool{
// 			Name:           "pool_" + strconv.Itoa(i),
// 			LocationVolume: path.Join(os.Getenv("PWD"), "/Volume"+strconv.Itoa(i)),
// 		}
// 	}

// 	for id, rep := range pools.Items {
// 		jsonData, err := json.Marshal(rep)
// 		assert.NoError(t, err)
// 		etcddir := "/twm/live_cluster/repository/" + id
// 		err = exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
// 			"put", etcddir, string(jsonData)).Run()
// 		assert.NoError(t, err)
// 	}

// 	return &pools
// }

// func TestImageController_Create(t *testing.T) {
// 	go func() {
// 		imageController := getTestImageController(t)

// 		r := gin.Default()
// 		r.POST("/generate_upload_task", resp.WrapResp(imageController.GenerateUploadTask))
// 		r.GET("/upload_file/:taskid", resp.WrapResp(imageController.UploadFile))
// 		r.GET("/get_tasks_progress", resp.WrapResp(imageController.ListTaskProgress))
// 		r.GET("/list_images", resp.WrapResp(imageController.List))
// 		r.Run(":8686")
// 	}()
// 	time.Sleep(time.Second * 1)

// 	tests := []struct {
// 		name     string
// 		wantCode int
// 		filePath string
// 		data     dto.ImageCreate
// 		pools    *domain.RepositoryPoolList
// 		before   func(t *testing.T, filesize uint32) *uploadClient
// 		after    func(t *testing.T, pools *domain.RepositoryPoolList)
// 	}{
// 		{
// 			name:     "test_0",
// 			wantCode: http.StatusOK,
// 			pools: generateRepositoryPool(t, []dto.ImageLocate{
// 				{RepoId: "111-222-333"},
// 			}),
// 			data: dto.ImageCreate{
// 				Name:         "test_0",
// 				DiskFileName: "test_0.iso",
// 				Type:         domain.ISO,
// 				FileSize:     10 * 1024 * 1024,
// 				ImageRepos: []dto.ImageLocate{
// 					{RepoId: "111-222-333"},
// 				},
// 			},
// 			filePath: path.Join(testFileDir, "test_0.iso"),
// 			before: func(t *testing.T, filesize uint32) *uploadClient {
// 				err := generateRandomFile(path.Join(testFileDir, "test_0.iso"), int(filesize))
// 				assert.NoError(t, err)

// 				var client uploadClient
// 				client.taskConn = make(map[domain.TaskID]*websocket.Conn)
// 				client.taskCreate = make(map[domain.TaskID]*dto.ImageCreate)
// 				client.taskProgress = make(map[domain.TaskID]chan *dto.ImageProgress)

// 				return &client
// 			},
// 			after: func(t *testing.T, pools *domain.RepositoryPoolList) {

// 			},
// 		},
// 		{
// 			name:     "test_1",
// 			wantCode: http.StatusOK,
// 			pools: generateRepositoryPool(t, []dto.ImageLocate{
// 				{RepoId: "111-222-333"}, {RepoId: "222-333-444"}, {RepoId: "333-444-555"}, {RepoId: "444-555-666"},
// 			}),
// 			data: dto.ImageCreate{
// 				Name:         "test_1",
// 				DiskFileName: "test_1.iso",
// 				Type:         domain.ISO,
// 				FileSize:     200 * 1024 * 1024,
// 				ImageRepos: []dto.ImageLocate{
// 					{RepoId: "111-222-333"}, {RepoId: "222-333-444"}, {RepoId: "444-555-666"},
// 				},
// 			},
// 			filePath: path.Join(testFileDir, "test_1.iso"),
// 			before: func(t *testing.T, filesize uint32) *uploadClient {
// 				err := generateRandomFile(path.Join(testFileDir, "test_1.iso"), int(filesize))
// 				assert.NoError(t, err)

// 				var client uploadClient
// 				client.taskConn = make(map[domain.TaskID]*websocket.Conn)
// 				client.taskCreate = make(map[domain.TaskID]*dto.ImageCreate)
// 				client.taskProgress = make(map[domain.TaskID]chan *dto.ImageProgress)

// 				return &client
// 			},
// 			after: func(t *testing.T, pools *domain.RepositoryPoolList) {

// 			},
// 		},
// 	}

// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			var taskid domain.TaskID
// 			client := tt.before(t, tt.data.FileSize)

// 			// 生成一个任务id
// 			img, err := json.Marshal(tt.data)
// 			assert.NoError(t, err)

// 			resp, err := http.Post("http://localhost:8686/generate_upload_task", "application/json", bytes.NewBuffer(img))
// 			assert.NoError(t, err)
// 			respDecode := json.NewDecoder(resp.Body)
// 			respDecode.Decode(&taskid)

// 			client.taskCreate[taskid] = &tt.data
// 			if _, ok := client.taskProgress[taskid]; !ok {
// 				client.taskProgress[taskid] = make(chan *dto.ImageProgress, 3)
// 			}

// 			// 使用生成的任务id，上传文件
// 			url := fmt.Sprintf("ws://localhost:8686/upload_file/%d", taskid)
// 			conn, _, err := websocket.DefaultDialer.Dial(url, nil)
// 			assert.NoError(t, err)

// 			client.taskConn[taskid] = conn
// 			client.uploadFile(t, taskid)

// 			// 查询任务的进度
// 			var tasks dto.ImageListProgress

// 			for {
// 				resp, err = http.Get("http://localhost:8686/get_tasks_progress")
// 				assert.NoError(t, err)
// 				respDecode = json.NewDecoder(resp.Body)
// 				err = respDecode.Decode(&tasks)
// 				assert.NoError(t, err)

// 				var pro *dto.ImageProgress
// 				for idx, _ := range tasks.Items {
// 					pro = tasks.Items[idx]
// 					if pro.Taskid == taskid {
// 						if pro.Status == domain.StateError {
// 							t.Error("upload file has error")
// 							goto end
// 						}

// 						select {
// 						case client.taskProgress[taskid] <- pro:
// 						default:
// 						}
// 					}
// 				}

// 				var imgs dto.ImageListDto
// 				resp, err = http.Get("http://localhost:8686/list_images")
// 				assert.NoError(t, err)
// 				respDecode = json.NewDecoder(resp.Body)
// 				err = respDecode.Decode(&imgs)
// 				assert.NoError(t, err)

// 				for _, img := range imgs.Items {
// 					if img.Name == pro.Name && img.Status == domain.StateAvailable {
// 						for _, repoId := range img.ImageRepos {
// 							assert.True(t,
// 								fileEqual(path.Join(tt.pools.Items[repoId.RepoId].LocationVolume, domain.ImageDiskVolumePath, img.ImageId, img.Name+".iso"),
// 									path.Join(testFileDir, img.Name+".iso")))
// 						}
// 						goto end
// 					}
// 				}
// 			}

// 		end:
// 			tt.after(t, tt.pools)
// 		})
// 	}
// }

// func TestImageController_CreateForEdit(t *testing.T) {
// 	go func() {
// 		imageController := getTestImageController(t)

// 		r := gin.Default()
// 		r.POST("/generate_upload_task", resp.WrapResp(imageController.GenerateUploadTask))
// 		r.GET("/upload_file/:taskid", resp.WrapResp(imageController.UploadFile))
// 		r.GET("/get_tasks_progress", resp.WrapResp(imageController.ListTaskProgress))
// 		r.GET("/list_images", resp.WrapResp(imageController.List))
// 		r.Run(":8686")
// 	}()
// 	time.Sleep(time.Second * 1)

// 	tests := []struct {
// 		name     string
// 		wantCode int
// 		filePath string
// 		data     dto.ImageCreate
// 		pools    *domain.RepositoryPoolList
// 		before   func(t *testing.T, filesize uint32) *uploadClient
// 		after    func(t *testing.T, pools *domain.RepositoryPoolList)
// 	}{
// 		{
// 			name:     "test_1",
// 			wantCode: http.StatusOK,
// 			pools: generateRepositoryPool(t, []dto.ImageLocate{
// 				{RepoId: "111-222-333"}, {RepoId: "222-333-444"}, {RepoId: "333-444-555"}, {RepoId: "444-555-666"},
// 			}),
// 			data: dto.ImageCreate{
// 				Name:         "test_1",
// 				DiskFileName: "test_1.iso",
// 				Type:         domain.ISO,
// 				FileSize:     50 * 1024 * 1024,
// 				ImageRepos: []dto.ImageLocate{
// 					{RepoId: "111-222-333"}, {RepoId: "222-333-444"}, {RepoId: "444-555-666"},
// 				},
// 			},
// 			filePath: path.Join(testFileDir, "test_1.iso"),
// 			before: func(t *testing.T, filesize uint32) *uploadClient {
// 				err := generateRandomFile(path.Join(testFileDir, "test_1.iso"), int(filesize))
// 				assert.NoError(t, err)

// 				var client uploadClient
// 				client.taskConn = make(map[domain.TaskID]*websocket.Conn)
// 				client.taskCreate = make(map[domain.TaskID]*dto.ImageCreate)
// 				client.taskProgress = make(map[domain.TaskID]chan *dto.ImageProgress)

// 				return &client
// 			},
// 			after: func(t *testing.T, pools *domain.RepositoryPoolList) {

// 			},
// 		},
// 	}

// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			var taskid domain.TaskID
// 			client := tt.before(t, tt.data.FileSize)

// 			// 生成一个任务id
// 			img, err := json.Marshal(tt.data)
// 			assert.NoError(t, err)

// 			resp, err := http.Post("http://localhost:8686/generate_upload_task", "application/json", bytes.NewBuffer(img))
// 			assert.NoError(t, err)
// 			respDecode := json.NewDecoder(resp.Body)
// 			respDecode.Decode(&taskid)

// 			client.taskCreate[taskid] = &tt.data
// 			if _, ok := client.taskProgress[taskid]; !ok {
// 				client.taskProgress[taskid] = make(chan *dto.ImageProgress, 3)
// 			}

// 			// 使用生成的任务id，上传文件
// 			url := fmt.Sprintf("ws://localhost:8686/upload_file/%d", taskid)
// 			conn, _, err := websocket.DefaultDialer.Dial(url, nil)
// 			assert.NoError(t, err)

// 			client.taskConn[taskid] = conn
// 			client.uploadFile(t, taskid)

// 			// 查询任务的进度
// 			var tasks dto.ImageListProgress

// 			for {
// 				resp, err = http.Get("http://localhost:8686/get_tasks_progress")
// 				assert.NoError(t, err)
// 				respDecode = json.NewDecoder(resp.Body)
// 				err = respDecode.Decode(&tasks)
// 				assert.NoError(t, err)

// 				var pro *dto.ImageProgress
// 				for _, item := range tasks.Items {
// 					if item.Taskid == taskid {
// 						pro = item
// 					}
// 				}
// 				if pro.Status == domain.StateError {
// 					t.Error("upload file has error")
// 					goto end
// 				}

// 				select {
// 				case client.taskProgress[taskid] <- pro:
// 				default:
// 				}

// 				var imgs dto.ImageListDto
// 				resp, err = http.Get("http://localhost:8686/list_images")
// 				assert.NoError(t, err)
// 				respDecode = json.NewDecoder(resp.Body)
// 				err = respDecode.Decode(&imgs)
// 				assert.NoError(t, err)

// 				for _, img := range imgs.Items {
// 					if img.Name == pro.Name && img.Status == domain.StateAvailable {
// 						for _, repoId := range img.ImageRepos {
// 							assert.True(t,
// 								fileEqual(path.Join(tt.pools.Items[repoId.RepoId].LocationVolume, domain.ImageDiskVolumePath, img.ImageId, img.Name+".iso"),
// 									path.Join(testFileDir, img.Name+".iso")))
// 						}
// 						goto end
// 					}
// 				}
// 			}

// 		end:
// 			tt.after(t, tt.pools)
// 		})
// 	}
// }

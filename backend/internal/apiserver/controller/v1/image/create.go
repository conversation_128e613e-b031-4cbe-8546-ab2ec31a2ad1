package image

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/marmotedu/errors"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
)

func (Self *ImageController) GenerateUploadTask(ctx *gin.Context) (interface{}, error) {
	var imgCreate dto.ImageCreate
	if err := ctx.ShouldBindJSON(&imgCreate); err != nil {
		return nil, errors.WithCode(code.ErrInvalidJSON, err.Error())
	}

	taskid, err := Self.srv.Images().GenerateUploadTask(ctx, imgCreate)
	if err != nil {
		return nil, errors.WithCode(code.ErrInvalidJSON, err.<PERSON><PERSON>r())
	}

	log.Infof("createUploadTask a task: %v,task Id: %d", imgCreate, taskid)

	return struct {
		TaskId uint64 `json:"task_id"`
	}{TaskId: uint64(taskid)}, nil
}

func (Self *ImageController) UploadFile(ctx *gin.Context) (interface{}, error) {
	if taskid, ok := ctx.GetQuery("taskid"); !ok {
		log.L(ctx).Infof("query params fail")
		return nil, errors.WithCode(code.ErrUnknown, "query params fail")
	} else {
		taskid, err := strconv.ParseUint(taskid, 10, 64)
		if err != nil {
			log.L(ctx).Infof("parse uint fail")
			return nil, errors.WithCode(code.ErrUnknown, "parse uint fail")
		}

		Self.srv.Images().UploadFile(ctx, domain.TaskID(taskid))
	}

	return nil, nil
}

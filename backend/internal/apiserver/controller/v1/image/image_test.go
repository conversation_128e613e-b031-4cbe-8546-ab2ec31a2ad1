package image

import (
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store/etcd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store/libvirt"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

var testNumber = 15

func makeTestImageHost() []string {
	host := make([]string, 0)

	host = append(host, []string{"333-222-111", "444-555-666", "555-666-777", "888-999-111"}...)
	return host
}

func getHttpRequest(body io.Reader) (*gin.Context, *httptest.ResponseRecorder) {
	req, _ := http.NewRequest(http.MethodPut, "", body)
	resp := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(resp)
	ctx.Request = req
	return ctx, resp
}

func TestMain(m *testing.M) {
	err := os.Setenv("ETCDCTL_API", "3")
	if err != nil {
		panic(err)
	}
	os.Exit(m.Run())
}

func initLibvirt() store.LibvirtFactory {
	libvirtStore, err := libvirt.GetLibvirtFactoryOr(options.NewLibvirtOptions())
	if err != nil {
		panic(err)
	}
	return libvirtStore
}

func getTestImageController(t *testing.T) *ImageController {
	etcdStore := getTestEtcdStore(t)
	libvirtStore := initLibvirt()

	return NewImageController(etcdStore, libvirtStore)
}

func getTestEtcdStore(t *testing.T) store.Factory {
	opts := options.NewEtcdOptions()
	opts.Endpoints = []string{"http://127.0.0.1:2379"}
	opts.Namespace = etcd.KeyPrefix
	etcdStore, err := etcd.GetEtcdFactoryOr(opts, nil)
	assert.NoError(t, err)
	return etcdStore
}

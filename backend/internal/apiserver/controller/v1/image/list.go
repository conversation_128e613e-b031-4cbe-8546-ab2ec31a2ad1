package image

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/errors"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
)

func (Self *ImageController) List(ctx *gin.Context) (interface{}, error) {

	var opts metav1.ListOptions
	if err := ctx.ShouldBindQuery(&opts); err != nil {
		return nil, errors.WithCode(code.ErrBind, err.Error())
	}

	images, err := Self.srv.Images().List(ctx, opts)
	if err != nil {
		return nil, err
	}

	return images, nil
}

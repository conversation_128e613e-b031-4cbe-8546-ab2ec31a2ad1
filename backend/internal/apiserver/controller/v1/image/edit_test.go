package image

// import (
// 	"bytes"
// 	"encoding/json"
// 	"net/http"
// 	"net/http/httptest"
// 	"os"
// 	"os/exec"
// 	"path"
// 	"path/filepath"
// 	"sort"
// 	"testing"

// 	"github.com/gin-gonic/gin"
// 	"github.com/stretchr/testify/assert"
// 	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
// )

// func TestImageController_Edit(t *testing.T) {
// 	imageController := getTestImageController(t)
// 	volume := map[string]string{
// 		"volume0": "111-222-333",
// 		"volume1": "222-333-444",
// 		"volume2": "333-444-555",
// 		"volume3": "444-555-666",
// 	}
// 	tests := []struct {
// 		name   string
// 		before func(t *testing.T) *dto.ImageEdit
// 		after  func(t *testing.T)
// 	}{
// 		//删除volume0，添加volume2
// 		{
// 			name: "test_0",
// 			before: func(t *testing.T) *dto.ImageEdit {
// 				_ = exec.Command("etcdctl",
// 					"--endpoints=http://127.0.0.1:2379",
// 					"del", "/twm/live_cluster/", "--prefix").Run()
// 				// image_id : [volume0,volume1,volume3]
// 				// volume0: 111-222-333
// 				// volume1: 222-333-444
// 				// volume2: 333-444-555
// 				// volume3: 444-555-666
// 				TestImageController_CreateForEdit(t)

// 				var images dto.ImageListDto
// 				var edit dto.ImageEdit

// 				req, _ := http.NewRequest(http.MethodGet, "", nil)
// 				resp := httptest.NewRecorder()
// 				ctx, _ := gin.CreateTestContext(resp)
// 				ctx.Request = req
// 				imageController.List(ctx)
// 				json.Unmarshal(resp.Body.Bytes(), &images)

// 				//删除volume0，添加volume2
// 				for _, img := range images.Items {
// 					edit.Id = img.ImageId
// 					edit.Name = img.Name
// 					for _, repo := range img.ImageRepos {
// 						if repo.RepoId == volume["volume0"] {
// 							continue
// 						}
// 						edit.RepoId = append(edit.RepoId, repo.RepoId)
// 					}
// 					edit.RepoId = append(edit.RepoId, volume["volume2"])
// 				}

// 				return &edit
// 			},
// 			after: func(t *testing.T) {
// 				td := path.Join(os.Getenv("PWD"), testFileDir)
// 				tv := path.Join(os.Getenv("PWD"), "Volume*")
// 				os.RemoveAll(td)
// 				files, _ := filepath.Glob(tv)
// 				for _, file := range files {
// 					os.RemoveAll(file)
// 				}
// 			},
// 		},
// 		//删除volume0 volume1 volume3
// 		{
// 			name: "test_0",
// 			before: func(t *testing.T) *dto.ImageEdit {
// 				_ = exec.Command("etcdctl",
// 					"--endpoints=http://127.0.0.1:2379",
// 					"del", "/twm/live_cluster/", "--prefix").Run()
// 				// image_id : [volume0,volume1,volume3]
// 				// volume0: 111-222-333
// 				// volume1: 222-333-444
// 				// volume2: 333-444-555
// 				// volume3: 444-555-666
// 				TestImageController_CreateForEdit(t)

// 				var images dto.ImageListDto
// 				var edit dto.ImageEdit

// 				req, _ := http.NewRequest(http.MethodGet, "", nil)
// 				resp := httptest.NewRecorder()
// 				ctx, _ := gin.CreateTestContext(resp)
// 				ctx.Request = req
// 				imageController.List(ctx)
// 				json.Unmarshal(resp.Body.Bytes(), &images)

// 				//删除volume0 volume1 volume3
// 				for _, img := range images.Items {
// 					edit.Id = img.ImageId
// 					edit.Name = img.Name
// 					for _, repo := range img.ImageRepos {
// 						if repo.RepoId == volume["volume0"] ||
// 							repo.RepoId == volume["volume1"] ||
// 							repo.RepoId == volume["volume3"] {
// 							continue
// 						}
// 						edit.RepoId = append(edit.RepoId, repo.RepoId)
// 					}
// 					// edit.RepoId = append(edit.RepoId, volume["volume2"])
// 				}

// 				return &edit

// 			},
// 			after: func(t *testing.T) {
// 				td := path.Join(os.Getenv("PWD"), testFileDir)
// 				tv := path.Join(os.Getenv("PWD"), "Volume*")
// 				os.RemoveAll(td)
// 				files, _ := filepath.Glob(tv)
// 				for _, file := range files {
// 					os.RemoveAll(file)
// 				}
// 			},
// 		},
// 		//删除volume3 添加volume2
// 		{
// 			name: "test_0",
// 			before: func(t *testing.T) *dto.ImageEdit {
// 				_ = exec.Command("etcdctl",
// 					"--endpoints=http://127.0.0.1:2379",
// 					"del", "/twm/live_cluster/", "--prefix").Run()
// 				// image_id : [volume0,volume1,volume3]
// 				// volume0: 111-222-333
// 				// volume1: 222-333-444
// 				// volume2: 333-444-555
// 				// volume3: 444-555-666
// 				TestImageController_CreateForEdit(t)

// 				var images dto.ImageListDto
// 				var edit dto.ImageEdit

// 				req, _ := http.NewRequest(http.MethodGet, "", nil)
// 				resp := httptest.NewRecorder()
// 				ctx, _ := gin.CreateTestContext(resp)
// 				ctx.Request = req
// 				imageController.List(ctx)
// 				json.Unmarshal(resp.Body.Bytes(), &images)

// 				for _, img := range images.Items {
// 					edit.Id = img.ImageId
// 					edit.Name = img.Name
// 					for _, repo := range img.ImageRepos {
// 						if repo.RepoId == volume["volume3"] {
// 							continue
// 						}
// 						edit.RepoId = append(edit.RepoId, repo.RepoId)
// 					}
// 					edit.RepoId = append(edit.RepoId, volume["volume2"])
// 				}

// 				return &edit

// 			},
// 			after: func(t *testing.T) {
// 				td := path.Join(os.Getenv("PWD"), testFileDir)
// 				tv := path.Join(os.Getenv("PWD"), "Volume*")
// 				os.RemoveAll(td)
// 				files, _ := filepath.Glob(tv)
// 				for _, file := range files {
// 					os.RemoveAll(file)
// 				}
// 			},
// 		},
// 	}

// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			edit := tt.before(t)

// 			// 执行edit操作
// 			jsonData, _ := json.Marshal(edit)
// 			ctx, _ := getHttpRequest(bytes.NewBuffer(jsonData))
// 			imageController.EditImage(ctx)

// 			// 验证etcd的修改
// 			ctx, resp := getHttpRequest(nil)
// 			imageController.List(ctx)
// 			var images dto.ImageListDto
// 			json.Unmarshal(resp.Body.Bytes(), &images)

// 			imgFound := false
// 			for _, img := range images.Items {
// 				if img.ImageId == edit.Id {
// 					oriRepos := make([]string, 0)
// 					oriRepos = append(oriRepos, func() []string {
// 						t := make([]string, 0)
// 						for _, locate := range img.ImageRepos {
// 							t = append(t, locate.RepoId)
// 						}
// 						return t
// 					}()...)
// 					sort.Strings(oriRepos)
// 					sort.Strings(edit.RepoId)
// 					assert.Equal(t, oriRepos, edit.RepoId)
// 					imgFound = true
// 					break
// 				}
// 			}
// 			if !imgFound {
// 				assert.Equal(t, len(edit.RepoId) == 0, !imgFound)
// 			}

// 			// 验证文件是否被移除
// 			// ctx, resp := getHttpRequest(bytes.NewBuffer(jsonData))
// 			// TODO

// 			// 验证image.conf是否修改
// 			// TODO

// 			// 验证移动后的文件是否相等
// 			// TODO

// 			tt.after(t)
// 		})
// 	}
// }

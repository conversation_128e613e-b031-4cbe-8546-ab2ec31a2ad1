package image

import (
	"github.com/gin-gonic/gin"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
)

func (Self *ImageController) EditImage(ctx *gin.Context) (interface{}, error) {
	var img dto.ImageEdit

	if err := ctx.ShouldBindJSON(&img); err != nil {
		return nil, err
	}

	err := Self.srv.Images().EditImage(ctx, img)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

package image

import (
	"github.com/gin-gonic/gin"
	controller "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/controller/v1"
	service "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/golibrary/resp"
)

var _ controller.Controller = (*ImageController)(nil)

type ImageController struct {
	srv service.Service
}

func NewImageController(store store.Factory, libvirtStore store.LibvirtFactory) *ImageController {
	return &ImageController{srv: service.NewService(store, libvirtStore, nil)}
}

func (Self *ImageController) RegisterRoutes(images *gin.RouterGroup) {
	images.GET("/list_images", resp.WrapResp(Self.List))
	images.GET("/upload_file", resp.WrapResp(Self.UploadFile))
	images.PUT("/edit_image", resp.WrapResp(Self.EditImage))
	images.PUT("/delete_image", resp.WrapResp(Self.Delete))
	images.POST("/generate_upload_task", resp.WrapResp(Self.GenerateUploadTask))
}

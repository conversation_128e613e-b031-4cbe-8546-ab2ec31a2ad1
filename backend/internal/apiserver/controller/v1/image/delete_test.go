package image

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"github.com/stretchr/testify/assert"

	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
)

func TestImageController_Delete(t *testing.T) {
	tests := []struct {
		name   string
		before func(t *testing.T) domain.ImageList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.ImageList {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				images := domain.ImageList{}
				images.ListMeta.TotalCount = int64(testNumber)
				images.Items = make(map[string]*domain.Image)

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()

					images.Items[id] = &domain.Image{
						Name:      "ubuntu 22.01" + "-" + strconv.Itoa(i),
						Type:      domain.ISO,
						RealFiles: "test_0.iso" + "-" + strconv.Itoa(i),
						Repos:     makeTestImageHost(),
						FileSize:  32135324,
					}
				}

				for id, img := range images.Items {
					jsonData, err := json.Marshal(img)
					assert.NoError(t, err)
					etcddir := domain.ImagePrefix + id
					err = exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
						"put", etcddir, string(jsonData)).Run()
					assert.NoError(t, err)

				}

				return images
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", domain.ImagePrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantImages := tt.before(t)
			imageController := getTestImageController(t)

			for imgId, _ := range wantImages.Items {
				req, _ := http.NewRequest(http.MethodGet, fmt.Sprintf("/v1/images/%s", imgId), nil)
				resp := httptest.NewRecorder()
				ctx, _ := gin.CreateTestContext(resp)
				ctx.Request = req
				ctx.Params = gin.Params{
					gin.Param{
						Key:   "image_id",
						Value: imgId,
					},
				}

				imageController.Delete(ctx)
				assert.Equal(t, http.StatusOK, resp.Code)
			}

			var images dto.ImageListDto
			ctx, resp := getHttpRequest(nil)
			imageController.List(ctx)
			assert.Equal(t, http.StatusOK, resp.Code)
			json.Unmarshal(resp.Body.Bytes(), &images)

			assert.Empty(t, images.Items)
			defer tt.after(t)
		})
	}
}

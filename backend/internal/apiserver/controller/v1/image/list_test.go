package image

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
)

func TestImageController_List(t *testing.T) {
	tests := []struct {
		name   string
		before func(t *testing.T) domain.ImageList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.ImageList {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				images := domain.ImageList{}
				images.ListMeta.TotalCount = int64(testNumber)
				images.Items = make(map[string]*domain.Image)

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()

					images.Items[id] = &domain.Image{
						Name:      "ubuntu 22.01" + "-" + strconv.Itoa(i),
						Type:      domain.ISO,
						RealFiles: "test_0.iso" + "-" + strconv.Itoa(i),
						Repos:     makeTestImageHost(),
						FileSize:  32135324,
					}
				}

				for id, img := range images.Items {
					jsonData, err := json.Marshal(img)
					assert.NoError(t, err)
					etcddir := domain.ImagePrefix + id
					err = exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
						"put", etcddir, string(jsonData)).Run()
					assert.NoError(t, err)

				}

				return images
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", domain.ImagePrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantImages := tt.before(t)

			req, err := http.NewRequest(http.MethodGet, "/v1/images", nil)
			assert.NoError(t, err)

			resp := httptest.NewRecorder()

			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			imageController := getTestImageController(t)

			imageController.List(ctx)

			assert.Equal(t, http.StatusOK, resp.Code)
			var images dto.ImageListDto
			err = json.Unmarshal(resp.Body.Bytes(), &images)
			assert.NoError(t, err)
			assert.NoError(t, err)

			assert.Equal(t, wantImages.TotalCount, images.TotalCount)
			assert.Equal(t, len(wantImages.Items), len(images.Items))
			for id, wantImage := range wantImages.Items {
				notfund := true
				for _, image := range images.Items {
					if image.ImageId == id {
						notfund = false
						assert.Equal(t, wantImage.Name, image.Name)
						assert.Equal(t, wantImage.Type, image.Type)
						assert.Equal(t, wantImage.RealFiles, image.RealName)
						assert.Equal(t, wantImage.FileSize, image.FileSize)
						assert.Equal(t, domain.StateAvailable, image.Status)
						for idx, repoid := range wantImage.Repos {
							assert.Equal(t, repoid, image.ImageRepos[idx].RepoId)
						}
					}
				}
				assert.False(t, notfund)
			}

			defer tt.after(t)
		})
	}
}

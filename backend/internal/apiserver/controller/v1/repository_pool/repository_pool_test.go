package repository_pool

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store/etcd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store/libvirt"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

func TestMain(m *testing.M) {
	err := os.Setenv("ETCDCTL_API", "3")
	if err != nil {
		panic(err)
	}
	os.Exit(m.Run())
}

func getTestRepositoryController(t *testing.T) *RepositoryController {
	etcdStore := getTestEtcdStore(t)
	libvirtStore := getTestLibvirtStore(t)
	return NewRepositoryController(etcdStore, libvirtStore, nil)
}

func getTestEtcdStore(t *testing.T) store.Factory {
	opts := options.NewEtcdOptions()
	opts.Endpoints = []string{"http://127.0.0.1:2379"}
	opts.Namespace = etcd.KeyPrefix
	etcdStore, err := etcd.GetEtcdFactoryOr(opts, nil)
	assert.NoError(t, err)
	return etcdStore
}

func getTestLibvirtStore(t *testing.T) store.LibvirtFactory {
	libvirtStore, err := libvirt.GetLibvirtFactoryOr(options.NewLibvirtOptions())
	assert.NoError(t, err)
	return libvirtStore
}

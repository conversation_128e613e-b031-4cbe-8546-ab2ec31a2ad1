package repository_pool

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"os/exec"
	"testing"

	"github.com/gin-gonic/gin"
	v1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	vm "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/controller/v1/vm"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
)

// 测试创建存储池同时导入配置文件中的虚拟机等数据
func TestRepositoryController_CreateWithImportData(t *testing.T) {
	tests := []struct {
		name       string
		before     func(t *testing.T) *domain.RepositoryPool
		after      func(t *testing.T)
		vmInstance dto.VMInstance
	}{
		{
			name: "test1",
			before: func(t *testing.T) *domain.RepositoryPool {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/repository", "--prefix").Run()

				id := "3e3809fa-b48b-4e31-b7fc-f474fc2a9768"

				pool := &domain.RepositoryPool{
					RepoId:             id,
					Name:               "twmStorage",
					HostID:             "host",
					LocationVolume:     "/Volume2",
					TotalSize:          0.0,
					HardLimit:          0.0,
					SoftLimit:          0.0,
					EnableLowerNotify:  true,
					LastNotifyTreshold: "123456",
				}
				jsonData, err := json.Marshal(pool)
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", fmt.Sprintf("/twm/live_cluster/repository/%s", id), string(jsonData)).Run()
				assert.NoError(t, err)

				return pool
			},
			vmInstance: dto.VMInstance{
				AutoSwitch:      0,
				Autorun:         0,
				BootFrom:        "disk",
				CPUPassthru:     false,
				CPUPinNum:       2,
				CPUWeight:       256,
				Desc:            "",
				HypervEnlighten: false,
				IsGeneralVM:     true,
				ISOImages:       []string{"8f7ea322-4933-420b-84d6-06c05011a596", "8f7ea322-4933-420b-84d6-06c05011a596"},
				KBLayout:        "en-gb",
				MachineType:     "q35",
				Name:            "VmforRepoTest",
				RepoId:          "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
				SerialConsole:   true,
				Status:          domain.PowerStateRuning,
				USBVersion:      2,
				USBs:            []string{"002:003:058f:6387", "unmounted", "unmounted", "unmounted"},
				UseOVMF:         false,
				VCPUNum:         2,
				VDisks: []dto.VDiskInstance{
					{Name: "VmforRepoTest",
						Idx: 111},
					{Name: "VmforRepoTest",
						Idx: 222},
				},
				VideoCard: "cirrus",
				VNics: []dto.VNicInstance{
					{Mac: "11:11:11:11:11:11:"},
					{Mac: "22:22:22:22:22:22"},
					{Mac: "33:33:33:33:33:33"},
				},
				VRAMSize: 2048,
			},
			after: func(t *testing.T) {
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/repository/", "--prefix").Run()
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interfaces/", "--prefix").Run()
				assert.NoError(t, err)

				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/guests/", "--prefix").Run()
				assert.NoError(t, err)

				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/vdisk/", "--prefix").Run()
				assert.NoError(t, err)

				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantPool := tt.before(t)

			jsonData, err := json.Marshal(wantPool)
			if err != nil {

			}
			jsonDataVm, err := json.Marshal(tt.vmInstance)
			if err != nil {

			}

			//测试创建存储池时卷中存在数据且导入的场景
			reqVm, err := http.NewRequest(http.MethodPost, "/vms/create_vm_instance", bytes.NewBuffer(jsonDataVm))
			assert.NoError(t, err)

			respVm := httptest.NewRecorder()

			ctx, _ := gin.CreateTestContext(respVm)
			ctx.Request = reqVm
			vmTestController := vm.NewVMController(getTestEtcdStore(t), getTestLibvirtStore(t), nil)

			vmTestController.Create(ctx)
			//创建完成之后调用after清空etcd数据
			tt.after(t)
			//调用before重新生成存储池信息
			req, err := http.NewRequest(http.MethodPost, "/v1/repository/create_repo", bytes.NewBuffer(jsonData))
			assert.NoError(t, err)

			resp := httptest.NewRecorder()

			ctx, _ = gin.CreateTestContext(resp)
			ctx.Request = req

			//需要手动设置路由参数，因为在测试环境下，gin 并不会自动从请求路径中解析出路由参数
			ctx.Params = gin.Params{
				gin.Param{
					Key:   "needImportData",
					Value: "true",
				},
			}
			repoController := getTestRepositoryController(t)

			result, _ := repoController.Create(ctx)
			if repo, ok := result.(*domain.RepositoryPool); ok {
				assert.Equal(t, repo.Name, wantPool.Name)
				assert.Equal(t, repo.HostID, wantPool.HostID)
				assert.Equal(t, repo.LocationVolume, wantPool.LocationVolume)
				assert.Equal(t, repo.HardLimit, wantPool.HardLimit)
				assert.Equal(t, repo.SoftLimit, wantPool.SoftLimit)
				assert.Equal(t, repo.EnableLowerNotify, wantPool.EnableLowerNotify)
				assert.Equal(t, repo.LastNotifyTreshold, wantPool.LastNotifyTreshold)

				configPath := fmt.Sprintf("%v/@Repository", repo.LocationVolume)
				entries, err := os.ReadDir(configPath)
				if err != nil {
				}
				for _, entry := range entries {
					if entry.IsDir() && entry.Name() != "." && entry.Name() != ".." {
						entryPath := fmt.Sprintf("%v/%v", configPath, entry.Name())
						os.RemoveAll(entryPath)
					}
				}
			}
			assert.Equal(t, http.StatusOK, resp.Code)

			defer tt.after(t)
		})
	}
}

// 创建存储池但不导入卷中数据
func TestRepositoryController_CreateWithoutImportData(t *testing.T) {
	tests := []struct {
		name       string
		before     func(t *testing.T) *domain.RepositoryPool
		after      func(t *testing.T)
		vmInstance dto.VMInstance
	}{
		{
			name: "test1",
			before: func(t *testing.T) *domain.RepositoryPool {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/repository", "--prefix").Run()

				id := "3e3809fa-b48b-4e31-b7fc-f474fc2a9768"

				pool := &domain.RepositoryPool{
					RepoId:             id,
					Name:               "twmStorage",
					HostID:             "host",
					LocationVolume:     "/Volume2",
					TotalSize:          0.0,
					HardLimit:          0.0,
					SoftLimit:          0.0,
					EnableLowerNotify:  true,
					LastNotifyTreshold: "123456",
				}
				jsonData, err := json.Marshal(pool)
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", fmt.Sprintf("/twm/live_cluster/repository/%s", id), string(jsonData)).Run()
				assert.NoError(t, err)

				return pool
			},
			vmInstance: dto.VMInstance{
				AutoSwitch:      0,
				Autorun:         0,
				BootFrom:        "disk",
				CPUPassthru:     false,
				CPUPinNum:       2,
				CPUWeight:       256,
				Desc:            "",
				HypervEnlighten: false,
				IsGeneralVM:     true,
				ISOImages:       []string{"8f7ea322-4933-420b-84d6-06c05011a596", "8f7ea322-4933-420b-84d6-06c05011a596"},
				KBLayout:        "en-gb",
				MachineType:     "q35",
				Name:            "VmforRepoTest",
				RepoId:          "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
				SerialConsole:   true,
				Status:          domain.PowerStateRuning,
				USBVersion:      2,
				USBs:            []string{"002:003:058f:6387", "unmounted", "unmounted", "unmounted"},
				UseOVMF:         false,
				VCPUNum:         2,
				VDisks: []dto.VDiskInstance{
					{Name: "VmforRepoTest",
						Idx: 111},
					{Name: "VmforRepoTest",
						Idx: 222},
				},
				VideoCard: "cirrus",
				VNics: []dto.VNicInstance{
					{Mac: "11:11:11:11:11:11:"},
					{Mac: "22:22:22:22:22:22"},
					{Mac: "33:33:33:33:33:33"},
				},
				VRAMSize: 2048,
			},
			after: func(t *testing.T) {
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/repository/", "--prefix").Run()
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interfaces/", "--prefix").Run()
				assert.NoError(t, err)

				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/guests/", "--prefix").Run()
				assert.NoError(t, err)

				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/vdisk/", "--prefix").Run()
				assert.NoError(t, err)

				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantPool := tt.before(t)

			jsonData, err := json.Marshal(wantPool)
			if err != nil {

			}
			jsonDataVm, err := json.Marshal(tt.vmInstance)
			if err != nil {

			}
			reqVm, err := http.NewRequest(http.MethodPost, "/vms/create_vm_instance", bytes.NewBuffer(jsonDataVm))
			assert.NoError(t, err)

			respVm := httptest.NewRecorder()

			ctx, _ := gin.CreateTestContext(respVm)
			ctx.Request = reqVm
			vmTestController := vm.NewVMController(getTestEtcdStore(t), getTestLibvirtStore(t), nil)

			vmTestController.Create(ctx)

			//将所有虚拟机状态改为正在运行
			etcd := getTestEtcdStore(t)
			vmInstance, _ := etcd.VMs().List(ctx, v1.ListOptions{})
			for guestid, vm := range vmInstance.Items {
				vm.State = "running"
				err := etcd.VMs().Update(ctx, guestid, vm, v1.CreateOptions{})
				if err != nil {

				}
			}
			//创建完成之后调用after清空etcd数据
			tt.after(t)

			req, err := http.NewRequest(http.MethodPost, "/v1/repository/create_repo", bytes.NewBuffer(jsonData))
			assert.NoError(t, err)

			resp := httptest.NewRecorder()

			ctx, _ = gin.CreateTestContext(resp)
			ctx.Request = req

			//测试创建存储池存在数据但不需要导入的场景
			//需要手动设置路由参数，因为在测试环境下，gin 并不会自动从请求路径中解析出路由参数

			ctx.Params = gin.Params{
				gin.Param{
					Key:   "needImportData",
					Value: "false",
				},
			}

			repoController := getTestRepositoryController(t)

			result, _ := repoController.Create(ctx)
			if repo, ok := result.(*domain.RepositoryPool); ok {
				assert.Equal(t, repo.Name, wantPool.Name)
				assert.Equal(t, repo.HostID, wantPool.HostID)
				assert.Equal(t, repo.LocationVolume, wantPool.LocationVolume)
				assert.Equal(t, repo.HardLimit, wantPool.HardLimit)
				assert.Equal(t, repo.SoftLimit, wantPool.SoftLimit)
				assert.Equal(t, repo.EnableLowerNotify, wantPool.EnableLowerNotify)
				assert.Equal(t, repo.LastNotifyTreshold, wantPool.LastNotifyTreshold)
			}
			assert.Equal(t, http.StatusOK, resp.Code)
			defer tt.after(t)
		})
	}
}

// 创建存储池，卷中无数据情形
func TestRepositoryController_CreateWithoutDataInVolume(t *testing.T) {
	tests := []struct {
		name   string
		before func(t *testing.T) *domain.RepositoryPool
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) *domain.RepositoryPool {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/repository", "--prefix").Run()

				id := "3e3809fa-b48b-4e31-b7fc-f474fc2a9768"

				pool := &domain.RepositoryPool{
					RepoId:             id,
					Name:               "twmStorage",
					HostID:             "host",
					LocationVolume:     "/Volume2",
					TotalSize:          0.0,
					HardLimit:          0.0,
					SoftLimit:          0.0,
					EnableLowerNotify:  true,
					LastNotifyTreshold: "123456",
				}
				jsonData, err := json.Marshal(pool)
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", fmt.Sprintf("/twm/live_cluster/repository/%s", id), string(jsonData)).Run()
				assert.NoError(t, err)

				return pool
			},
			after: func(t *testing.T) {
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/repository/", "--prefix").Run()
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interfaces/", "--prefix").Run()
				assert.NoError(t, err)

				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/guests/", "--prefix").Run()
				assert.NoError(t, err)

				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/vdisk/", "--prefix").Run()
				assert.NoError(t, err)

				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantPool := tt.before(t)

			//先清空卷中配置文件
			configPath := fmt.Sprintf("%v/@Repository", wantPool.LocationVolume)
			entries, err := os.ReadDir(configPath)
			if err != nil {
			}
			for _, entry := range entries {
				if entry.IsDir() && entry.Name() != "." && entry.Name() != ".." {
					entryPath := fmt.Sprintf("%v/%v", configPath, entry.Name())
					os.RemoveAll(entryPath)
				}
			}

			jsonData, err := json.Marshal(wantPool)
			if err != nil {

			}
			req, err := http.NewRequest(http.MethodPost, "/v1/repository/create_repo", bytes.NewBuffer(jsonData))
			assert.NoError(t, err)

			resp := httptest.NewRecorder()

			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			//需要手动设置路由参数，因为在测试环境下，gin 并不会自动从请求路径中解析出路由参数
			ctx.Params = gin.Params{
				gin.Param{
					Key:   "needImportData",
					Value: "false",
				},
			}

			repoController := getTestRepositoryController(t)

			result, _ := repoController.Create(ctx)
			if repo, ok := result.(*domain.RepositoryPool); ok {
				assert.Equal(t, repo.Name, wantPool.Name)
				assert.Equal(t, repo.HostID, wantPool.HostID)
				assert.Equal(t, repo.LocationVolume, wantPool.LocationVolume)
				assert.Equal(t, repo.HardLimit, wantPool.HardLimit)
				assert.Equal(t, repo.SoftLimit, wantPool.SoftLimit)
				assert.Equal(t, repo.EnableLowerNotify, wantPool.EnableLowerNotify)
				assert.Equal(t, repo.LastNotifyTreshold, wantPool.LastNotifyTreshold)
			}
			assert.Equal(t, http.StatusOK, resp.Code)
			defer tt.after(t)
		})
	}
}

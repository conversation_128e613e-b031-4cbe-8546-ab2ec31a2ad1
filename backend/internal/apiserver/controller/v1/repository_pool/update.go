// Package network
/**
* @Project : terravirtualmachine
* @File    : repository_pool.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/3/5 16:00
**/

package repository_pool

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
	"gitlab.local/golibrary/errors"
)

func (Self *RepositoryController) Update(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Repository Pool Update function called")

	var repo domain.RepositoryPool

	if err := ctx.ShouldBindJSON(&repo); err != nil {
		core.WriteResponse(ctx, errors.WithCode(code.ErrBind, err.Error()), nil)
		return false, nil
	}

	if err := Self.srv.RepositoryPool().Update(ctx, repo.RepoId, &repo, metav1.UpdateOptions{}); err != nil {
		core.WriteResponse(ctx, err, nil)
		return false, nil
	}

	return repo, nil
}

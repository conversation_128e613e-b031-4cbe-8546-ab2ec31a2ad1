// Package network
/**
* @Project : terravirtualmachine
* @File    : repository_pool.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/3/5 16:00
**/

package repository_pool

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

func (Self *RepositoryController) WhetherExistData(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("WhetherExistData function called")

	var repo *domain.RepositoryPool

	if err := ctx.ShouldBindJSON(&repo); err != nil {
	}

	hostList, err := Self.srv.RepositoryPool().WhetherExistData(ctx, repo, metav1.GetOptions{})
	if err != nil {

	}

	return hostList, nil

}

package repository_pool

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	vm "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/controller/v1/vm"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
)

func TestRepositoryController_WhetherExistData(t *testing.T) {
	tests := []struct {
		name       string
		before     func(t *testing.T) domain.RepositoryPoolList
		after      func(t *testing.T)
		vmInstance dto.VMInstance
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.RepositoryPoolList {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/repository", "--prefix").Run()

				pools := domain.RepositoryPoolList{}
				pools.ListMeta.TotalCount = int64(1)
				pools.Items = make(map[string]*domain.RepositoryPool)

				id := "3e3809fa-b48b-4e31-b7fc-f474fc2a9768"

				pools.Items[id] = &domain.RepositoryPool{
					RepoId:             id,
					Name:               "twmStorage",
					HostID:             "host",
					LocationVolume:     "/Volume2",
					TotalSize:          0.0,
					HardLimit:          0.0,
					SoftLimit:          0.0,
					EnableLowerNotify:  true,
					LastNotifyTreshold: "",
				}
				jsonData, err := json.Marshal(pools.Items[id])
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", fmt.Sprintf("/twm/live_cluster/repository/%s", id), string(jsonData)).Run()
				assert.NoError(t, err)

				return pools
			},
			vmInstance: dto.VMInstance{
				AutoSwitch:      0,
				Autorun:         0,
				BootFrom:        "disk",
				CPUPassthru:     false,
				CPUPinNum:       2,
				CPUWeight:       256,
				Desc:            "",
				HypervEnlighten: false,
				IsGeneralVM:     true,
				ISOImages:       []string{"8f7ea322-4933-420b-84d6-06c05011a596", "8f7ea322-4933-420b-84d6-06c05011a596"},
				KBLayout:        "en-gb",
				MachineType:     "q35",
				Name:            "VmforRepoTest",
				RepoId:          "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
				SerialConsole:   true,
				Status:          domain.PowerStateRuning,
				USBVersion:      2,
				USBs:            []string{"002:003:058f:6387", "unmounted", "unmounted", "unmounted"},
				UseOVMF:         false,
				VCPUNum:         2,
				VDisks: []dto.VDiskInstance{
					{Name: "VmforRepoTest",
						Idx: 111},
					{Name: "VmforRepoTest",
						Idx: 222},
				},
				VideoCard: "cirrus",
				VNics: []dto.VNicInstance{
					{Mac: "11:11:11:11:11:11:"},
					{Mac: "22:22:22:22:22:22"},
					{Mac: "33:33:33:33:33:33"},
				},
				VRAMSize: 2048,
			},
			after: func(t *testing.T) {
				//清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/repository/", "--prefix").Run()
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interface/", "--prefix").Run()
				assert.NoError(t, err)

				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/guests/", "--prefix").Run()
				assert.NoError(t, err)

				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/vdisk/", "--prefix").Run()
				assert.NoError(t, err)

				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantPools := tt.before(t)

			for repoid, wantPool := range wantPools.Items {
				jsonData, err := json.Marshal(wantPool)
				if err != nil {

				}
				jsonDataVm, err := json.Marshal(tt.vmInstance)
				if err != nil {

				}
				reqVm, err := http.NewRequest(http.MethodPost, "/vms/create_vm_instance", bytes.NewBuffer(jsonDataVm))
				assert.NoError(t, err)

				respVm := httptest.NewRecorder()

				ctx, _ := gin.CreateTestContext(respVm)
				ctx.Request = reqVm
				vmTestController := vm.NewVMController(getTestEtcdStore(t), getTestLibvirtStore(t), nil)

				vmTestController.Create(ctx)

				req, err := http.NewRequest(http.MethodGet, "/v1/repository/whether_exist_data", bytes.NewBuffer(jsonData))
				assert.NoError(t, err)

				resp := httptest.NewRecorder()

				ctx, _ = gin.CreateTestContext(resp)
				ctx.Request = req

				//需要手动设置路由参数，因为在测试环境下，gin 并不会自动从请求路径中解析出路由参数
				ctx.Params = gin.Params{
					gin.Param{
						Key:   "repoId",
						Value: repoid,
					},
				}

				repoController := getTestRepositoryController(t)

				//var result dto.HostListDto
				result, _ := repoController.WhetherExistData(ctx)
				if listHosts, ok := result.(dto.RepoHostList); ok {
					assert.True(t, listHosts.HasData)
				}

				assert.Equal(t, http.StatusOK, resp.Code)
			}

			defer tt.after(t)
		})
	}
}

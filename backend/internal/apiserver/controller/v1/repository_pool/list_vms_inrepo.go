package repository_pool

// Package repository
/**
* @Project : terravirtualmachine
* @File    : repository_pool.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/3/5 16:00
**/

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
	"gitlab.local/golibrary/errors"
)

func (Self *RepositoryController) ListVmsInRepo(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Repository Pool ListAvailVolumes function called")
	var repo *domain.RepositoryPool

	if err := ctx.ShouldBindJSON(&repo); err != nil {
		core.WriteResponse(ctx, errors.WithCode(code.ErrBind, err.Error()), nil)
		return false, nil
	}
	mpVolumes, err := Self.srv.RepositoryPool().ListVmsInRepo(ctx, repo, metav1.GetOptions{})

	if err != nil {
		core.WriteResponse(ctx, err, mpVolumes)
		return false, nil
	}

	return mpVolumes, nil
}

package repository_pool

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

func TestRepositoryController_Get(t *testing.T) {
	testNumber := 6
	tests := []struct {
		name   string
		before func(t *testing.T) domain.RepositoryPoolList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.RepositoryPoolList {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/repository", "--prefix").Run()

				pools := domain.RepositoryPoolList{}
				pools.ListMeta.TotalCount = int64(testNumber)
				pools.Items = make(map[string]*domain.RepositoryPool)

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()

					pools.Items[id] = &domain.RepositoryPool{
						Name:               "twmStorage" + "-" + strconv.Itoa(i),
						HostID:             "/Volume" + strconv.Itoa(i),
						LocationVolume:     "/Volume" + strconv.Itoa(i),
						TotalSize:          0.0,
						HardLimit:          0.0,
						SoftLimit:          0.0,
						EnableLowerNotify:  true,
						LastNotifyTreshold: strconv.Itoa(100 * i),
					}
				}

				for id, pool := range pools.Items {
					jsonData, err := json.Marshal(pool)
					assert.NoError(t, err)
					err = exec.Command("etcdctl",
						"--endpoints=http://127.0.0.1:2379",
						"put", fmt.Sprintf("/twm/live_cluster/repository/%s", id), string(jsonData)).Run()
					assert.NoError(t, err)

				}

				return pools
			},
			after: func(t *testing.T) {
				// 清理测试数据
				// err := exec.Command("etcdctl",
				// 	"--endpoints=http://127.0.0.1:2379",
				// 	"del", "/twm/live_cluster/repository/", "--prefix").Run()
				// assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantPools := tt.before(t)

			for repoid, wantPool := range wantPools.Items {
				req, err := http.NewRequest(http.MethodGet, "/v1/repository/get_repo:"+repoid, nil)
				assert.NoError(t, err)

				resp := httptest.NewRecorder()

				ctx, _ := gin.CreateTestContext(resp)
				ctx.Request = req

				// 需要手动设置路由参数，因为在测试环境下，gin 并不会自动从请求路径中解析出路由参数
				ctx.Params = gin.Params{
					gin.Param{
						Key:   "repoId",
						Value: repoid,
					},
				}

				repoController := getTestRepositoryController(t)

				repoController.Get(ctx)

				assert.Equal(t, http.StatusOK, resp.Code)
				var repo domain.RepositoryPool
				err = json.Unmarshal(resp.Body.Bytes(), &repo)
				assert.NoError(t, err)

				assert.NoError(t, err)

				assert.Equal(t, wantPool.Name, repo.Name)
				assert.Equal(t, wantPool.HostID, repo.HostID)
				assert.Equal(t, wantPool.LocationVolume, repo.LocationVolume)
				assert.Equal(t, wantPool.HardLimit, repo.HardLimit)
				assert.Equal(t, wantPool.SoftLimit, repo.SoftLimit)
				assert.Equal(t, wantPool.EnableLowerNotify, repo.EnableLowerNotify)
				assert.Equal(t, wantPool.LastNotifyTreshold, repo.LastNotifyTreshold)
			}

			defer tt.after(t)
		})
	}
}

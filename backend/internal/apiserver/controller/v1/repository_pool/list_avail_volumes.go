package repository_pool

// Package repository
/**
* @Project : terravirtualmachine
* @File    : repository_pool.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/3/5 16:00
**/

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
)

func (Self *RepositoryController) ListAvailVolumes(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Repository Pool ListAvailVolumes function called")

	mpVolumes, err := Self.srv.RepositoryPool().ListAvailVolumes(ctx, metav1.GetOptions{})

	if err != nil {
		core.WriteResponse(ctx, err, mpVolumes)
		return false, nil
	}

	return mpVolumes, nil
}

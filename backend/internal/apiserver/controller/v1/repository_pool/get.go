package repository_pool

// Package network
/**
* @Project : terravirtualmachine
* @File    : repository_pool.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/3/5 16:00
**/
import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
)

func (Self *RepositoryController) Get(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Repository Pool Get function called")

	repo, err := Self.srv.RepositoryPool().Get(ctx, ctx.Param("repoId"), metav1.GetOptions{})
	if err != nil {
		core.WriteResponse(ctx, err, nil)
		return false, nil
	}

	return repo, nil

}

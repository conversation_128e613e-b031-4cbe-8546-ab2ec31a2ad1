package repository_pool

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
)

func TestListVolumes_List(t *testing.T) {

	//tt.before(t)

	req, err := http.NewRequest(http.MethodGet, "v1/repository/list_avail_volumes", nil)
	assert.NoError(t, err)

	resp := httptest.NewRecorder()

	ctx, _ := gin.CreateTestContext(resp)
	ctx.Request = req

	//需要手动设置路由参数，因为在测试环境下，gin 并不会自动从请求路径中解析出路由参数
	// ctx.Params = gin.Params{
	// 	gin.Param{
	// 		Key:   "network_id",
	// 		Value: tt.wantNetwork.NetworkID,
	// 	},
	// }

	repoController := getTestRepositoryController(t)

	mpVolumes, err := repoController.ListAvailVolumes(ctx)
	assert.NoError(t, err)
	core.WriteResponse(ctx, nil, mpVolumes)

	assert.Equal(t, http.StatusOK, resp.Code)

	err = json.Unmarshal(resp.Body.Bytes(), mpVolumes)
	assert.NoError(t, err)

}

package repository_pool

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"testing"

	"github.com/gin-gonic/gin"
	v1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

func TestRepositoryController_Update(t *testing.T) {
	tests := []struct {
		name     string
		before   func(t *testing.T) domain.RepositoryPool
		after    func(t *testing.T)
		wantRepo domain.RepositoryPool
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.RepositoryPool {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/repository", "--prefix").Run()

				id := "3e3809fa-b48b-4e31-b7fc-f474fc2a9768"

				pool := domain.RepositoryPool{
					RepoId:             id,
					Name:               "twmStorage",
					HostID:             "host",
					LocationVolume:     "/Volume2",
					TotalSize:          0.0,
					HardLimit:          0.0,
					SoftLimit:          0.0,
					EnableLowerNotify:  true,
					LastNotifyTreshold: "",
				}
				jsonData, err := json.Marshal(pool)
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", fmt.Sprintf("/twm/live_cluster/repository/%s", id), string(jsonData)).Run()
				assert.NoError(t, err)

				return pool
			},

			wantRepo: domain.RepositoryPool{
				RepoId:             "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
				Name:               "twmStorage",
				HostID:             "host",
				LocationVolume:     "/Volume2",
				TotalSize:          0.0,
				HardLimit:          0.0,
				SoftLimit:          0.0,
				EnableLowerNotify:  false,
				LastNotifyTreshold: "",
			},

			after: func(t *testing.T) {
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/repository/", "--prefix").Run()

				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			originRepo := tt.before(t)
			jsonData, err := json.Marshal(tt.wantRepo)
			if err != nil {

			}

			req, err := http.NewRequest(http.MethodGet, "/v1/repository/update_repo", bytes.NewBuffer(jsonData))
			assert.NoError(t, err)

			resp := httptest.NewRecorder()

			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			repoController := getTestRepositoryController(t)

			repoController.Update(ctx)

			etcd := getTestEtcdStore(t)
			repoUpdated, _ := etcd.RepositoryPool().Get(ctx, originRepo.RepoId, v1.GetOptions{})

			assert.Equal(t, repoUpdated.Name, tt.wantRepo.Name)
			assert.Equal(t, repoUpdated.HostID, tt.wantRepo.HostID)
			assert.Equal(t, repoUpdated.LocationVolume, tt.wantRepo.LocationVolume)
			assert.Equal(t, repoUpdated.HardLimit, tt.wantRepo.HardLimit)
			assert.Equal(t, repoUpdated.SoftLimit, tt.wantRepo.SoftLimit)
			assert.Equal(t, repoUpdated.EnableLowerNotify, tt.wantRepo.EnableLowerNotify)
			assert.Equal(t, repoUpdated.LastNotifyTreshold, tt.wantRepo.LastNotifyTreshold)

			assert.Equal(t, http.StatusOK, resp.Code)

			defer tt.after(t)
		})
	}
}

// Package network
/**
* @Project : terravirtualmachine
* @File    : repository_pool.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/3/5 16:00
**/

package repository_pool

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
	"gitlab.local/golibrary/errors"
)

func (Self *RepositoryController) List(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Repository Pool List function called")

	var opts metav1.ListOptions
	if err := ctx.ShouldBindQuery(&opts); err != nil {
		core.WriteResponse(ctx, errors.WithCode(code.ErrBind, err.Error()), nil)
		return false, nil
	}

	repos, err := Self.srv.RepositoryPool().List(ctx, metav1.ListOptions{})
	if err != nil {
		core.WriteResponse(ctx, err, nil)
		return false, nil
	}

	return repos, nil

}

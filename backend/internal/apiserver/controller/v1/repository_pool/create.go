// Package network
/**
* @Project : terravirtualmachine
* @File    : repository_pool.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/3/5 16:00
**/

package repository_pool

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/golibrary/errors"
)

func (Self *RepositoryController) Create(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Repository Pool Create function called")

	var repo *domain.RepositoryPool

	if err := ctx.ShouldBindJSON(&repo); err != nil {
		return nil, errors.WithCode(code.ErrInvalidJSON, err.Error())
	}
	var needImportData bool
	param := ctx.Param("need_import_data")
	if param == "true" {
		needImportData = true
	} else {
		needImportData = false
	}

	repo, err := Self.srv.RepositoryPool().Create(ctx, repo, needImportData, metav1.CreateOptions{})
	if err != nil {
		return nil, nil
	}
	log.Infof("repository creation success: reopoId:%v", repo.RepoId)

	return repo, nil
}

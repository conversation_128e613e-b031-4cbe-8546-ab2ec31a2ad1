// Package repository
/**
* @Project : terravirtualmachine
* @File    : repository_pool.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/3/5 16:00
**/

package repository_pool

import (
	"github.com/gin-gonic/gin"
	controller "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/controller/v1"
	service "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/golibrary/resp"
)

var _ controller.Controller = (*RepositoryController)(nil)

type RepositoryController struct {
	srv service.Service
}

func NewRepositoryController(store store.Factory, libvirtStore store.LibvirtFactory, redisStore store.RedisFactory) *RepositoryController {
	return &RepositoryController{srv: service.NewService(store, libvirtStore, redisStore)}
}

func (Self *RepositoryController) RegisterRoutes(repositorys *gin.RouterGroup) {
	repositorys.POST("/create_repo/:need_import_data", resp.WrapResp(Self.Create))            // 新增存储空间
	repositorys.GET("/get_repo/:repoId", resp.WrapResp(Self.Get))                             // 获取特定存储空间
	repositorys.GET("/list_repos", resp.WrapResp(Self.List))                                  // 获取所有存储空间
	repositorys.PUT("/update_repo", resp.WrapResp(Self.Update))                               // 修改特定存储空间的配置
	repositorys.DELETE("/delete_repo/:repoId/save/:needSaveData", resp.WrapResp(Self.Delete)) // 删除存储空间
	repositorys.GET("/list_avail_volumes", resp.WrapResp(Self.ListAvailVolumes))              // 获取可用的存储空间
	repositorys.POST("/whether_exist_data", resp.WrapResp(Self.WhetherExistData))             // 是否存在虚拟机映像等数据
	repositorys.GET("/check_vms_running/:repo_id", resp.WrapResp(Self.CheckVmsRunning))       // 是否有虚拟机正在运行
	repositorys.POST("/list_vms_inrepo", resp.WrapResp(Self.ListVmsInRepo))                   // 列出存储池下的虚拟机信息
}

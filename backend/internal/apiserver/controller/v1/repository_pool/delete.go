// Package repository
/**
* @Project : terravirtualmachine
* @File    : repository_pool.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/3/5 16:00
**/

package repository_pool

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
)

func (Self *RepositoryController) Delete(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Repository Pool Delete function called")

	var needSaveData bool
	param := ctx.Param("need_save_data")
	if param == "true" {
		needSaveData = true
	} else {
		needSaveData = false
	}

	repo, err := Self.srv.RepositoryPool().Delete(ctx, ctx.Param("repoId"), needSaveData, metav1.DeleteOptions{})
	if err != nil {

	}

	return repo, nil
}

package network

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

func TestNetworkController_Create(t *testing.T) {
	tests := []struct {
		name              string
		network           domain.Network
		wantCode          int
		wantBody          string
		wantNumHosts      int
		wantNumInterfaces int
		after             func(t *testing.T)
	}{
		{
			name: "空网络名称",
			network: domain.Network{
				Type:   "external",
				VLANID: 100,
			},
			wantCode: http.StatusBadRequest,
			wantBody: `{"code":100004,"message":"Validation failed"}`,
			after: func(t *testing.T) {

			},
		},
		{
			name: "外部网络无接口",
			network: domain.Network{
				ObjectMeta: metav1.ObjectMeta{
					Name: "test",
				},
				Type:   "external",
				VLANID: 100,
			},
			wantCode: http.StatusBadRequest,
			wantBody: `{"code":100004,"message":"Validation failed"}`,
			after: func(t *testing.T) {

			},
		},
		{
			name: "外部网络 VLAN ID 小于 1",
			network: domain.Network{
				ObjectMeta: metav1.ObjectMeta{
					Name: "test",
				},
				Type:   "external",
				VLANID: 0,
				Interfaces: map[string]*domain.InterfaceList{
					"host1": {
						ListMeta: metav1.ListMeta{
							TotalCount: 1,
						},
						Items: []*domain.Interface{
							{
								Checked:       true,
								HostID:        "host1",
								HostName:      "host1",
								InterfaceID:   "if1",
								InterfaceName: "eth0",
								IP:            []string{"***********"},
								Mask:          []string{"*************"},
								SpeedMbs:      100,
								Status:        domain.CONNECTED,
							},
						},
					},
				},
			},
			wantCode: http.StatusBadRequest,
			wantBody: `{"code":100004,"message":"Validation failed"}`,
			after: func(t *testing.T) {

			},
		},
		{
			name: "外部网络 VLAN ID 大于 4094",
			network: domain.Network{
				ObjectMeta: metav1.ObjectMeta{
					Name: "test",
				},
				Type:   "external",
				VLANID: 4095,
				Interfaces: map[string]*domain.InterfaceList{
					"host1": {
						ListMeta: metav1.ListMeta{
							TotalCount: 1,
						},
						Items: []*domain.Interface{
							{
								Checked:       true,
								HostID:        "host1",
								HostName:      "host1",
								InterfaceID:   "if1",
								InterfaceName: "eth0",
								IP:            []string{"***********"},
								Mask:          []string{"*************"},
								SpeedMbs:      100,
								Status:        domain.CONNECTED,
							},
						},
					},
				},
			},
			wantCode: http.StatusBadRequest,
			wantBody: `{"code":100004,"message":"Validation failed"}`,
			after: func(t *testing.T) {

			},
		},
		{
			name: "专用网络无主机信息",
			network: domain.Network{
				ObjectMeta: metav1.ObjectMeta{
					Name: "test",
				},
				Type:   "private",
				VLANID: 0,
			},
			wantCode: http.StatusBadRequest,
			wantBody: `{"code":100004,"message":"Validation failed"}`,
			after: func(t *testing.T) {

			},
		},
		{
			name: "专用网络有 VLAN ID",
			network: domain.Network{
				ObjectMeta: metav1.ObjectMeta{
					Name: "test",
				},
				HostID:   "host1",
				HostName: "host1",
				Type:     "private",
				VLANID:   100,
			},
			wantCode: http.StatusBadRequest,
			wantBody: `{"code":100004,"message":"Validation failed"}`,
			after: func(t *testing.T) {

			},
		},
		{
			name: "专用网络多个接口",
			network: domain.Network{
				ObjectMeta: metav1.ObjectMeta{
					Name: "test",
				},
				HostID:   "host1",
				HostName: "host1",
				Type:     "private",
				VLANID:   0,
				Interfaces: map[string]*domain.InterfaceList{
					"host1": {
						ListMeta: metav1.ListMeta{
							TotalCount: 2,
						},
						Items: []*domain.Interface{
							{
								Checked:       true,
								HostID:        "host1",
								HostName:      "host1",
								InterfaceID:   "if1",
								InterfaceName: "eth0",
								IP:            []string{"***********"},
								Mask:          []string{"*************"},
								SpeedMbs:      100,
								Status:        domain.CONNECTED,
							},
							{
								Checked:       true,
								HostID:        "host1",
								HostName:      "host1",
								InterfaceID:   "if2",
								InterfaceName: "eth1",
								IP:            []string{"***********"},
								Mask:          []string{"*************"},
								SpeedMbs:      100,
								Status:        domain.CONNECTED,
							},
						},
					},
				},
			},
			wantCode: http.StatusBadRequest,
			wantBody: `{"code":100004,"message":"Validation failed"}`,
			after: func(t *testing.T) {

			},
		},
		{
			name: "未知网络类型",
			network: domain.Network{
				ObjectMeta: metav1.ObjectMeta{
					Name: "test",
				},
				Type:   "unknown",
				VLANID: 0,
			},
			wantCode: http.StatusBadRequest,
			wantBody: `{"code":100004,"message":"Validation failed"}`,
			after: func(t *testing.T) {

			},
		},
		{
			name: "成功创建外部网络",
			network: domain.Network{
				ObjectMeta: metav1.ObjectMeta{
					Name: "ValidExternalNetwork",
				},
				Type:   "external",
				VLANID: 100,
				Interfaces: map[string]*domain.InterfaceList{
					"host1": {
						ListMeta: metav1.ListMeta{
							TotalCount: 1,
						},
						Items: []*domain.Interface{
							{
								Checked:       true,
								HostID:        "host1",
								HostName:      "host1",
								InterfaceID:   "if1",
								InterfaceName: "eth0",
								IP:            []string{"***********"},
								Mask:          []string{"*************"},
								SpeedMbs:      100,
								Status:        domain.CONNECTED,
							},
						},
					},
				},
			},
			wantCode:          http.StatusOK,
			wantBody:          "",
			wantNumHosts:      1,
			wantNumInterfaces: 1,
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				assert.NoError(t, err)
			},
		},
		{
			name: "成功创建专用网络",
			network: domain.Network{
				ObjectMeta: metav1.ObjectMeta{
					Name: "ValidPrivateNetwork",
				},
				Type:     "private",
				HostID:   "host1",
				HostName: "host1",
				Interfaces: map[string]*domain.InterfaceList{
					"host1": {
						ListMeta: metav1.ListMeta{
							TotalCount: 1,
						},
						Items: []*domain.Interface{
							{
								Checked:  true,
								HostID:   "host1",
								HostName: "host1",
							},
						},
					},
				},
			},
			wantCode:          http.StatusOK,
			wantBody:          "",
			wantNumHosts:      1,
			wantNumInterfaces: 1,
			after: func(t *testing.T) {
				// 清理测试数据
				// err := exec.Command("etcdctl",
				// 	"--endpoints=http://127.0.0.1:2379",
				// 	"del", "/twm/live_cluster/networks/", "--prefix").Run()
				// assert.NoError(t, err)
				// // 清理测试网桥
				// res, err := exec.Command("ovs-vsctl", "list-br").Output()
				// assert.NoError(t, err)
				// bridges := strings.Split(strings.TrimSpace(string(res)), "\n")
				// for _, bridge := range bridges {
				// 	if strings.HasPrefix(bridge, "ovs-private") {
				// 		err := ovs.DeleteBridge(bridge)
				// 		assert.NoError(t, err)
				// 	}
				// }
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reqBody, err := json.Marshal(tt.network)
			assert.NoError(t, err)
			req, err := http.NewRequest(http.MethodPost, "/v1/networks", bytes.NewBuffer(reqBody))
			if err != nil {
				t.Fatal(err)
			}
			req.Header.Set("Content-Type", "application/json")

			resp := httptest.NewRecorder()

			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			networkController := getTestNetworkController(t)

			networkController.Create(ctx)

			assert.Equal(t, tt.wantCode, resp.Code)
			if tt.wantBody != "" {
				assert.JSONEq(t, tt.wantBody, resp.Body.String())
			} else {
				var network domain.Network
				err = json.Unmarshal(resp.Body.Bytes(), &network)
				assert.NoError(t, err)

				assert.NotEmpty(t, network.NetworkID)
				if network.Type == domain.PRIVATE {
					assert.Equal(t, tt.network.HostID, network.HostID)
					assert.Equal(t, tt.network.HostName, network.HostName)
				}
				assert.Equal(t, tt.wantNumHosts, network.NumHosts)
				assert.Equal(t, tt.wantNumInterfaces, network.NumInterfaces)
				assert.Equal(t, tt.network.Type, network.Type)
				assert.Equal(t, tt.network.VLANID, network.VLANID)

				for hostID, wantInterfaceList := range tt.network.Interfaces {
					ifaceList, ok := network.Interfaces[hostID]
					assert.True(t, ok)
					assert.Equal(t, len(wantInterfaceList.Items), len(ifaceList.Items))
					for _, wantInterface := range wantInterfaceList.Items {
						foundInterface := false
						for _, iface := range ifaceList.Items {
							if tt.network.Type == domain.PRIVATE {
								foundInterface = true
								assert.Equal(t, wantInterface.Checked, iface.Checked)
								assert.Equal(t, wantInterface.HostID, iface.HostID)
								assert.Equal(t, wantInterface.HostName, iface.HostName)
								assert.NotEmpty(t, iface.InterfaceID)
								assert.True(t, strings.HasPrefix(iface.InterfaceName, "ovs_private"))
							} else if tt.network.Type == domain.EXTERNAL {
								if wantInterface.InterfaceID == iface.InterfaceID {
									foundInterface = true
									assert.Equal(t, wantInterface.Checked, iface.Checked)
									assert.Equal(t, wantInterface.HostID, iface.HostID)
									assert.Equal(t, wantInterface.HostName, iface.HostName)
									assert.Equal(t, wantInterface.InterfaceID, iface.InterfaceID)
									assert.Equal(t, wantInterface.InterfaceName, iface.InterfaceName)
									assert.Equal(t, wantInterface.IP, iface.IP)
									assert.Equal(t, wantInterface.Mask, iface.Mask)
									assert.Equal(t, wantInterface.SpeedMbs, iface.SpeedMbs)
									assert.Equal(t, wantInterface.Status, iface.Status)
								}
							} else {
								t.Fatalf("unknown network type: %s", tt.network.Type)
							}
						}
						assert.True(t, foundInterface)
					}
				}
			}

			defer tt.after(t)
		})
	}
}

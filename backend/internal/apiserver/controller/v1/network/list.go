// Package network
/**
* @Project : terravirtualmachine
* @File    : list.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/20 17:23
**/

package network

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
	"gitlab.local/golibrary/errors"
)

func (Self *NetworkController) List(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Network List function called")

	var opts metav1.ListOptions
	if err := ctx.ShouldBindQuery(&opts); err != nil {
		core.WriteResponse(ctx, errors.WithCode(code.ErrBind, err.Error()), nil)
		return false, nil
	}

	networks, err := Self.srv.Networks().List(ctx, opts)
	if err != nil {
		core.WriteResponse(ctx, err, nil)
		return false, nil
	}

	return networks, nil
}

// Package network
/**
* @Project : terravirtualmachine
* @File    : delete.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/20 17:24
**/

package network

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
)

func (Self *NetworkController) Delete(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Network Delete function called.")

	if err := Self.srv.Networks().Delete(ctx, ctx.Param("network_id"), metav1.DeleteOptions{}); err != nil {
		core.WriteResponse(ctx, err, nil)
		return false, nil
	}

	return nil, nil
}

// Package network
/**
* @Project : terravirtualmachine
* @File    : delete_collection.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/21 14:36
**/

package network

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
)

func (Self *NetworkController) DeleteCollection(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Network DeleteCollection function called")

	networkIDs := ctx.QueryArray("network_id")

	if err := Self.srv.Networks().DeleteCollection(ctx, networkIDs, metav1.DeleteOptions{}); err != nil {
		core.WriteResponse(ctx, err, nil)
		return false, err
	}

	return nil, nil
}

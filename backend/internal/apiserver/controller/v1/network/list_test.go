package network

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/marmotedu/component-base/pkg/json"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

func TestNetworkController_List(t *testing.T) {
	tests := []struct {
		name         string
		before       func(t *testing.T)
		after        func(t *testing.T)
		wantNetworks domain.NetworkList
	}{
		{
			name: "test1",
			before: func(t *testing.T) {
				// 1、准备 /twm/live_cluster/networks/{network_id} 测试数据
				// 1.1、清理
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				// 1.2、准备
				networks := domain.NetworkList{
					ListMeta: metav1.ListMeta{
						TotalCount: 1,
					},
					Items: []*domain.Network{
						{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "Default VM Network",
								CreatedAt: time.Now(),
								UpdatedAt: time.Now(),
							},
							NetworkID: "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
							HostID:    "",
							HostName:  "",
							Interfaces: map[string]*domain.InterfaceList{
								"9b5910c9-557c-42a7-b592-7996d606dcc0": {
									ListMeta: metav1.ListMeta{
										TotalCount: 2,
									},
									Items: []*domain.Interface{
										{
											Checked:     true,
											HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
											InterfaceID: "00000000-0000-0000-0000-9009d05e8271",
										},
										{
											Checked:     false,
											HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
											InterfaceID: "00000000-0000-0000-0000-9009d05e8272",
										},
									},
								},
							},
							NumGuests:     0,
							NumHosts:      1,
							NumInterfaces: 2,
							Type:          domain.EXTERNAL,
							VLANID:        0,
						},
					},
				} // end of networks
				for _, network := range networks.Items {
					jsonData, err := json.Marshal(network)
					assert.NoError(t, err)
					err = exec.Command("etcdctl",
						"--endpoints=http://127.0.0.1:2379",
						"put", "/twm/live_cluster/networks/"+network.NetworkID, string(jsonData)).Run()
					assert.NoError(t, err)
				}

				// 2、准备 /twm/live_cluster/hosts/{host_id}/interfaces/{interface_id} 测试数据
				// 2.1、清理
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/hosts/", "--prefix").Run()
				// 2.2、准备
				ifaces := []*domain.Interface{
					{
						Checked:       true,
						HostID:        "9b5910c9-557c-42a7-b592-7996d606dcc0",
						HostName:      "local",
						InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
						InterfaceName: "ovs_eth0",
						IP:            []string{"**********"},
						Mask:          []string{"*************"},
						SpeedMbs:      1000,
						Status:        domain.CONNECTED,
					},
					{
						Checked:       false,
						HostID:        "9b5910c9-557c-42a7-b592-7996d606dcc0",
						HostName:      "local",
						InterfaceID:   "00000000-0000-0000-0000-9009d05e8272",
						InterfaceName: "ovs_eth1",
						IP:            []string{"***************"},
						Mask:          []string{"*************"},
						SpeedMbs:      -1,
						Status:        domain.DISCONNECTED,
					},
				}
				for _, iface := range ifaces {
					jsonData, err := json.Marshal(iface)
					assert.NoError(t, err)
					err = exec.Command("etcdctl",
						"--endpoints=http://127.0.0.1:2379",
						"put", fmt.Sprintf("/twm/live_cluster/hosts/%s/interfaces/%s", iface.HostID, iface.InterfaceID), string(jsonData)).Run()
					assert.NoError(t, err)
				}

				// 3、准备 /twm/live_cluster/virtual_interfaces/{virtual_interface_id} 测试数据
				// 3.1、清理
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interfaces/", "--prefix").Run()
				// 3.2、准备
				vIfaces := []*domain.VNic{
					{
						VNicID:      "virtual_interface_id",
						GuestID:     "guest_id",
						GuestName:   "guest_name",
						MacAddress:  "00:00:00:00:00:00",
						NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
						NetworkName: "Default VM Network",
						Running: map[string]*domain.VNicruningList{
							"9b5910c9-557c-42a7-b592-7996d606dcc0": {
								ListMeta: metav1.ListMeta{
									TotalCount: 1,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
										InterfaceName: "ovs_eth0",
										OVSPort:       "viface01",
										UseVf:         false,
										VfPoolName:    "vf_pool_name",
									},
								},
							},
						},
					},
				}
				for _, vIface := range vIfaces {
					jsonData, err := json.Marshal(vIface)
					assert.NoError(t, err)
					err = exec.Command("etcdctl",
						"--endpoints=http://127.0.0.1:2379",
						"put", fmt.Sprintf("/twm/live_cluster/virtual_interfaces/%s", vIface.VNicID), string(jsonData)).Run()
					assert.NoError(t, err)
				}
			},
			after: func(t *testing.T) {
				// 1、清理 /twm/live_cluster/networks/{network_id} 测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				assert.NoError(t, err)
				// 2、清理 /twm/live_cluster/hosts/{host_id}/interfaces/{interface_id} 测试数据
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/hosts/", "--prefix").Run()
				assert.NoError(t, err)
				// 3、清理 /twm/live_cluster/virtual_interfaces/{virtual_interface_id} 测试数据
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interfaces/", "--prefix").Run()
				assert.NoError(t, err)
			},
			wantNetworks: domain.NetworkList{
				ListMeta: metav1.ListMeta{
					TotalCount: 1,
				},
				Items: []*domain.Network{
					{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "Default VM Network",
							CreatedAt: time.Now(),
							UpdatedAt: time.Now(),
						},
						NetworkID: "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
						HostID:    "",
						HostName:  "",
						Interfaces: map[string]*domain.InterfaceList{
							"9b5910c9-557c-42a7-b592-7996d606dcc0": {
								ListMeta: metav1.ListMeta{
									TotalCount: 2,
								},
								Items: []*domain.Interface{
									{
										Checked:       true,
										HostID:        "9b5910c9-557c-42a7-b592-7996d606dcc0",
										HostName:      "local",
										InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
										InterfaceName: "ovs_eth0",
										IP:            []string{"**********"},
										Mask:          []string{"*************"},
										SpeedMbs:      1000,
										Status:        domain.CONNECTED,
									},
									{
										Checked:       false,
										HostID:        "9b5910c9-557c-42a7-b592-7996d606dcc0",
										HostName:      "local",
										InterfaceID:   "00000000-0000-0000-0000-9009d05e8272",
										InterfaceName: "ovs_eth1",
										IP:            []string{"***************"},
										Mask:          []string{"*************"},
										SpeedMbs:      -1,
										Status:        domain.DISCONNECTED,
									},
								},
							},
						},
						NumGuests:     1,
						NumHosts:      1,
						NumInterfaces: 2,
						Type:          domain.EXTERNAL,
						VLANID:        0,
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.before(t)

			req, err := http.NewRequest(http.MethodGet, "/v1/networks", nil)
			assert.NoError(t, err)

			resp := httptest.NewRecorder()

			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			networkController := getTestNetworkController(t)

			networkController.List(ctx)

			assert.Equal(t, http.StatusOK, resp.Code)
			var networks domain.NetworkList
			err = json.Unmarshal(resp.Body.Bytes(), &networks)
			assert.NoError(t, err)

			// 补充测试逻辑
			assert.Equal(t, tt.wantNetworks.TotalCount, networks.TotalCount)
			assert.Equal(t, len(tt.wantNetworks.Items), len(networks.Items))

			for _, wantNetwork := range tt.wantNetworks.Items {
				foundNetwork := false
				for _, network := range networks.Items {
					if wantNetwork.NetworkID == network.NetworkID {
						foundNetwork = true

						// 断言网络的元数据
						assert.Equal(t, wantNetwork.Name, network.Name)

						// 断言网络的基本信息
						assert.Equal(t, wantNetwork.NetworkID, network.NetworkID)
						assert.Equal(t, wantNetwork.HostID, network.HostID)
						assert.Equal(t, wantNetwork.HostName, network.HostName)
						assert.Equal(t, wantNetwork.NumGuests, network.NumGuests)
						assert.Equal(t, wantNetwork.NumHosts, network.NumHosts)
						assert.Equal(t, wantNetwork.NumInterfaces, network.NumInterfaces)
						assert.Equal(t, wantNetwork.Type, network.Type)
						assert.Equal(t, wantNetwork.VLANID, network.VLANID)

						// 断言网络的网络接口信息
						assert.Equal(t, len(wantNetwork.Interfaces), len(network.Interfaces))
						for hostID, wantInterfaceList := range wantNetwork.Interfaces {
							ifaceList, ok := network.Interfaces[hostID]
							assert.True(t, ok)
							assert.Equal(t, wantInterfaceList.TotalCount, network.Interfaces[hostID].TotalCount)
							assert.Equal(t, len(wantInterfaceList.Items), len(ifaceList.Items))
							for _, wantInterface := range wantInterfaceList.Items {
								foundInterface := false
								for _, iface := range ifaceList.Items {
									if wantInterface.InterfaceID == iface.InterfaceID {
										foundInterface = true
										// 断言网络接口的详细信息
										assert.Equal(t, wantInterface.Checked, iface.Checked)
										assert.Equal(t, wantInterface.HostID, iface.HostID)
										assert.Equal(t, wantInterface.HostName, iface.HostName)
										assert.Equal(t, wantInterface.InterfaceID, iface.InterfaceID)
										assert.Equal(t, wantInterface.InterfaceName, iface.InterfaceName)
										assert.Equal(t, wantInterface.IP, iface.IP)
										assert.Equal(t, wantInterface.Mask, iface.Mask)
										assert.Equal(t, wantInterface.SpeedMbs, iface.SpeedMbs)
										assert.Equal(t, wantInterface.Status, iface.Status)
									}
								} // end of range ifaceList.Items
								assert.True(t, foundInterface)
							} // end of range wantInterfaceList.Items
						} // end of range wantNetwork.Interfaces
					}
				} // end of range networks.Items
				assert.True(t, foundNetwork)
			} // end of range wantNetworks.Items

			defer tt.after(t)
		})
	}
}

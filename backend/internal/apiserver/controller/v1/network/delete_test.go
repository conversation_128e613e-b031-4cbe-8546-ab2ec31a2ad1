package network

import (
	"net/http"
	"net/http/httptest"
	"os/exec"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/marmotedu/component-base/pkg/json"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	"github.com/xgfone/go-ovs"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

func TestNetworkController_Delete(t *testing.T) {
	tests := []struct {
		name   string
		before func(t *testing.T) *domain.Network
	}{
		{
			name: "删除外部网络",
			before: func(t *testing.T) *domain.Network {
				// 清理可能存在的脏数据
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				// 准备测试数据
				network := domain.Network{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "Default VM Network",
						CreatedAt: time.Now(),
						UpdatedAt: time.Now(),
					},
					NetworkID: "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
					HostID:    "",
					HostName:  "",
					Interfaces: map[string]*domain.InterfaceList{
						"9b5910c9-557c-42a7-b592-7996d606dcc0": {
							ListMeta: metav1.ListMeta{
								TotalCount: 2,
							},
							Items: []*domain.Interface{
								{
									Checked:     true,
									HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
									InterfaceID: "00000000-0000-0000-0000-9009d05e8271",
								},
								{
									Checked:     false,
									HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
									InterfaceID: "00000000-0000-0000-0000-9009d05e8272",
								},
							},
						},
					},
					NumGuests:     0,
					NumHosts:      1,
					NumInterfaces: 2,
					Type:          domain.EXTERNAL,
					VLANID:        0,
				}

				jsonData, err := json.Marshal(network)
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/networks/"+network.NetworkID, string(jsonData)).Run()
				assert.NoError(t, err)

				return &network
			},
		},
		{
			name: "删除专用网络",
			before: func(t *testing.T) *domain.Network {
				// 清理可能存在的脏数据
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				// 准备测试数据
				network := domain.Network{
					ObjectMeta: metav1.ObjectMeta{
						Name: "ValidPrivateNetwork",
					},
					Type:      "private",
					NetworkID: "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
					HostID:    "host1",
					HostName:  "host1",
					Interfaces: map[string]*domain.InterfaceList{
						"host1": {
							ListMeta: metav1.ListMeta{
								TotalCount: 1,
							},
							Items: []*domain.Interface{
								{
									Checked:       true,
									HostID:        "host1",
									HostName:      "host1",
									InterfaceID:   "iface1",
									InterfaceName: "ovs_private_aaa",
								},
							},
						},
					},
				}

				jsonData, err := json.Marshal(network)
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/networks/"+network.NetworkID, string(jsonData)).Run()
				assert.NoError(t, err)

				err = ovs.CreateBridge(network.Interfaces[network.HostID].Items[0].InterfaceName)
				assert.NoError(t, err)

				return &network
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantNetwork := tt.before(t)

			req, err := http.NewRequest(http.MethodDelete, "/v1/networks/"+wantNetwork.NetworkID, nil)
			assert.NoError(t, err)

			resp := httptest.NewRecorder()

			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			// 需要手动设置路由参数，因为在测试环境下，gin 并不会自动从请求路径中解析出路由参数
			ctx.Params = gin.Params{
				gin.Param{
					Key:   "network_id",
					Value: wantNetwork.NetworkID,
				},
			}

			networkController := getTestNetworkController(t)

			networkController.Delete(ctx)

			assert.Equal(t, http.StatusOK, resp.Code)
		})
	}
}

package network

import (
	"bytes"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/marmotedu/component-base/pkg/json"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

func TestNetworkController_Edit(t *testing.T) {
	tests := []struct {
		name        string
		before      func(t *testing.T)
		after       func(t *testing.T)
		wantNetwork *domain.Network
	}{
		{
			name: "test1",
			before: func(t *testing.T) {
				// 清理可能存在的脏数据
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				// 准备测试数据
				network := domain.Network{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "Default VM Network",
						CreatedAt: time.Now(),
						UpdatedAt: time.Now(),
					},
					NetworkID: "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
					HostID:    "",
					HostName:  "",
					Interfaces: map[string]*domain.InterfaceList{
						"9b5910c9-557c-42a7-b592-7996d606dcc0": {
							ListMeta: metav1.ListMeta{
								TotalCount: 2,
							},
							Items: []*domain.Interface{
								{
									Checked:     true,
									HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
									InterfaceID: "00000000-0000-0000-0000-9009d05e8271",
								},
								{
									Checked:     false,
									HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
									InterfaceID: "00000000-0000-0000-0000-9009d05e8272",
								},
							},
						},
					},
					NumGuests:     0,
					NumHosts:      1,
					NumInterfaces: 2,
					Type:          domain.EXTERNAL,
					VLANID:        0,
				}

				jsonData, err := json.Marshal(network)
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/networks/"+network.NetworkID, string(jsonData)).Run()
				assert.NoError(t, err)

				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/hosts/", "--prefix").Run()
				ifaces := []*domain.Interface{
					{
						Checked:       false,
						HostID:        "9b5910c9-557c-42a7-b592-7996d606dcc0",
						HostName:      "local",
						InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
						InterfaceName: "ovs_eth0",
						IP:            []string{"**********"},
						Mask:          []string{"*************"},
						SpeedMbs:      1000,
						Status:        domain.CONNECTED,
					},
					{
						Checked:       false,
						HostID:        "9b5910c9-557c-42a7-b592-7996d606dcc0",
						HostName:      "local",
						InterfaceID:   "00000000-0000-0000-0000-9009d05e8272",
						InterfaceName: "ovs_eth1",
						IP:            []string{"***************"},
						Mask:          []string{"*************"},
						SpeedMbs:      -1,
						Status:        domain.DISCONNECTED,
					},
				}
				for _, iface := range ifaces {
					jsonData, err := json.Marshal(iface)
					assert.NoError(t, err)
					err = exec.Command("etcdctl",
						"--endpoints=http://127.0.0.1:2379",
						"put", fmt.Sprintf("/twm/live_cluster/hosts/%s/interfaces/%s", iface.HostID, iface.InterfaceID), string(jsonData)).Run()
					assert.NoError(t, err)
				}
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/hosts/", "--prefix").Run()
				assert.NoError(t, err)
			},
			wantNetwork: &domain.Network{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "Edited Default VM Network",
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				},
				NetworkID: "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
				HostID:    "",
				HostName:  "",
				Interfaces: map[string]*domain.InterfaceList{
					"9b5910c9-557c-42a7-b592-7996d606dcc0": {
						ListMeta: metav1.ListMeta{
							TotalCount: 2,
						},
						Items: []*domain.Interface{
							{
								Checked:       false,
								HostID:        "9b5910c9-557c-42a7-b592-7996d606dcc0",
								HostName:      "local",
								InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
								InterfaceName: "ovs_eth0",
								IP:            []string{"**********"},
								Mask:          []string{"*************"},
								SpeedMbs:      1000,
								Status:        domain.CONNECTED,
							},
							{
								Checked:       true,
								HostID:        "9b5910c9-557c-42a7-b592-7996d606dcc0",
								HostName:      "local",
								InterfaceID:   "00000000-0000-0000-0000-9009d05e8272",
								InterfaceName: "ovs_eth1",
								IP:            []string{"***************"},
								Mask:          []string{"*************"},
								SpeedMbs:      -1,
								Status:        domain.DISCONNECTED,
							},
						},
					},
				},
				NumGuests:     0,
				NumHosts:      1,
				NumInterfaces: 2,
				Type:          domain.EXTERNAL,
				VLANID:        100,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.before(t)

			jsonData, err := json.Marshal(tt.wantNetwork)
			assert.NoError(t, err)

			req, err := http.NewRequest(http.MethodPut, "/v1/networks/"+tt.wantNetwork.NetworkID, bytes.NewBuffer(jsonData))
			assert.NoError(t, err)

			resp := httptest.NewRecorder()

			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			// 需要手动设置路由参数，因为在测试环境下，gin 并不会自动从请求路径中解析出路由参数
			ctx.Params = gin.Params{
				gin.Param{
					Key:   "network_id",
					Value: tt.wantNetwork.NetworkID,
				},
			}

			networkController := getTestNetworkController(t)

			networkController.Edit(ctx)

			assert.Equal(t, http.StatusOK, resp.Code)
			var network domain.Network
			err = json.Unmarshal(resp.Body.Bytes(), &network)
			assert.NoError(t, err)

			assert.Equal(t, tt.wantNetwork.NetworkID, network.NetworkID)
			assert.Equal(t, tt.wantNetwork.HostID, network.HostID)
			assert.Equal(t, tt.wantNetwork.HostName, network.HostName)
			assert.Equal(t, tt.wantNetwork.NumGuests, network.NumGuests)
			assert.Equal(t, tt.wantNetwork.NumHosts, network.NumHosts)
			assert.Equal(t, tt.wantNetwork.NumInterfaces, network.NumInterfaces)
			assert.Equal(t, tt.wantNetwork.Type, network.Type)
			assert.Equal(t, tt.wantNetwork.VLANID, network.VLANID)

			for hostID, wantInterfaceList := range tt.wantNetwork.Interfaces {
				ifaceList, ok := network.Interfaces[hostID]
				assert.True(t, ok)
				assert.Equal(t, len(wantInterfaceList.Items), len(ifaceList.Items))
				for _, wantInterface := range wantInterfaceList.Items {
					foundInterface := false
					for _, iface := range ifaceList.Items {
						if wantInterface.InterfaceID == iface.InterfaceID {
							foundInterface = true
							assert.Equal(t, wantInterface.Checked, iface.Checked)
							assert.Equal(t, wantInterface.HostID, iface.HostID)
							assert.Equal(t, wantInterface.HostName, iface.HostName)
							assert.Equal(t, wantInterface.InterfaceID, iface.InterfaceID)
							assert.Equal(t, wantInterface.InterfaceName, iface.InterfaceName)
							assert.Equal(t, wantInterface.IP, iface.IP)
							assert.Equal(t, wantInterface.Mask, iface.Mask)
							assert.Equal(t, wantInterface.SpeedMbs, iface.SpeedMbs)
							assert.Equal(t, wantInterface.Status, iface.Status)
						}
					}
					assert.True(t, foundInterface)
				}
			}

			defer tt.after(t)
		})
	}
}

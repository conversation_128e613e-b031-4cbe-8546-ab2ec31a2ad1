package network

import (
	"net/http"
	"net/http/httptest"
	"os/exec"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/marmotedu/component-base/pkg/json"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

func TestNetworkController_ListInterfaces(t *testing.T) {
	tests := []struct {
		name   string
		before func(t *testing.T) domain.InterfaceList
		after  func(t *testing.T)
	}{
		{
			name: "test",
			before: func(t *testing.T) domain.InterfaceList {
				// 清理可能存在的脏数据
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/hosts/", "--prefix").Run()
				// 准备测试数据
				iface := domain.Interface{
					HostID:        "host_id",
					HostName:      "host_name",
					InterfaceID:   "interface_id",
					InterfaceName: "ovs_eth0",
					IP:            []string{"127.0.0.1"},
					Mask:          []string{"*************"},
					SpeedMbs:      1000,
					Status:        "connected",
				}
				jsonData, err := json.Marshal(iface)
				assert.NoError(t, err)
				key := "/twm/live_cluster/hosts/host_id/interfaces/interface_id"
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", key, string(jsonData),
				).Run()
				assert.NoError(t, err)

				return domain.InterfaceList{
					ListMeta: metav1.ListMeta{
						TotalCount: 1,
					},
					Items: []*domain.Interface{&iface},
				}
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/hosts/", "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantBody := tt.before(t)

			req, err := http.NewRequest(http.MethodGet, "/v1/networks/interfaces", nil)
			assert.NoError(t, err)

			resp := httptest.NewRecorder()

			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			networkController := getTestNetworkController(t)

			networkController.ListInterfaces(ctx)

			assert.Equal(t, http.StatusOK, resp.Code)
			var body domain.InterfaceList
			err = json.Unmarshal(resp.Body.Bytes(), &body)
			assert.NoError(t, err)

			assert.Equal(t, wantBody.TotalCount, body.TotalCount)
			assert.Equal(t, len(wantBody.Items), len(body.Items))

			for _, wantIface := range wantBody.Items {
				foundIface := false
				for _, iface := range body.Items {
					if wantIface.InterfaceID == iface.InterfaceID {
						foundIface = true
						assert.Equal(t, wantIface.Checked, iface.Checked)
						assert.Equal(t, wantIface.HostID, iface.HostID)
						assert.Equal(t, wantIface.HostName, iface.HostName)
						assert.Equal(t, wantIface.InterfaceID, iface.InterfaceID)
						assert.Equal(t, wantIface.InterfaceName, iface.InterfaceName)
						assert.Equal(t, wantIface.IP, iface.IP)
						assert.Equal(t, wantIface.Mask, iface.Mask)
						assert.Equal(t, wantIface.SpeedMbs, iface.SpeedMbs)
						assert.Equal(t, wantIface.Status, iface.Status)
					}
				}
				assert.True(t, foundIface)
			}

			defer tt.after(t)
		})
	}
}

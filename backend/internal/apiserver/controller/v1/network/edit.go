// Package network
/**
* @Project : terravirtualmachine
* @File    : edit.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/20 17:23
**/

package network

import (
	"time"

	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
	"gitlab.local/golibrary/errors"
)

func (Self *NetworkController) Edit(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Network Edit function called.")

	var network domain.Network

	if err := ctx.ShouldBindJSON(&network); err != nil {
		core.WriteResponse(ctx, errors.WithCode(code.ErrBind, err.Error()), nil)

		return false, nil
	}

	getNetwork, err := Self.srv.Networks().Get(ctx, ctx.Param("network_id"), metav1.GetOptions{})
	if err != nil {
		core.WriteResponse(ctx, err, nil)

		return false, nil
	}

	Self.handleEdit(getNetwork, &network)

	if err := getNetwork.Validate(); err != nil {
		core.WriteResponse(ctx, errors.WithCode(code.ErrValidation, err.Error()), nil)

		return false, nil
	}

	// Save changed fields.
	if err := Self.srv.Networks().Update(ctx, getNetwork, metav1.UpdateOptions{}); err != nil {
		core.WriteResponse(ctx, err, nil)

		return false, nil
	}

	return network, nil
}

func (Self *NetworkController) handleEdit(getNetwork, network *domain.Network) {
	getNetwork.Name = network.Name
	if getNetwork.Type == domain.EXTERNAL {
		getNetwork.VLANID = network.VLANID
	}
	getNetwork.Interfaces = network.Interfaces
	getNetwork.UpdatedAt = time.Now()
}

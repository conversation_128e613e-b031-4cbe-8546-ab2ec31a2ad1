// Package network
/**
* @Project : terravirtualmachine
* @File    : create.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/20 17:22
**/

package network

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
	"gitlab.local/golibrary/errors"
)

func (Self *NetworkController) Create(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Network Create function called")

	var network domain.Network

	if err := ctx.ShouldBindJSON(&network); err != nil {
		core.WriteResponse(ctx, errors.WithCode(code.ErrBind, err.Error()), nil)
		return false, err
	}

	if err := network.Validate(); err != nil {
		core.WriteResponse(ctx, errors.WithCode(code.ErrValidation, err.Error()), nil)
		return false, err
	}

	if err := Self.srv.Networks().Create(ctx, &network, metav1.CreateOptions{}); err != nil {
		core.WriteResponse(ctx, err, nil)
		return false, err
	}

	return network, nil
}

// Package network
/**
* @Project : terravirtualmachine
* @File    : get.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/20 17:23
**/

package network

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
)

func (Self *NetworkController) Get(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Network Get function called")

	network, err := Self.srv.Networks().Get(ctx, ctx.Param("network_id"), metav1.GetOptions{})
	if err != nil {
		core.WriteResponse(ctx, err, nil)
		return false, nil
	}

	return network, nil
}

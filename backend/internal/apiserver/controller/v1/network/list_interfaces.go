// Package network
/**
* @Project : terravirtualmachine
* @File    : list_interfaces.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/2/6 09:53
**/

package network

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/errors"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
)

func (Self *NetworkController) ListInterfaces(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("Network ListInterfaces function called")

	var opts metav1.ListOptions
	if err := ctx.ShouldBindQuery(&opts); err != nil {
		core.WriteResponse(ctx, errors.WithCode(code.ErrBind, err.Error()), nil)
		return false, nil
	}

	interfaces, err := Self.srv.Networks().ListInterfaces(ctx, opts)
	if err != nil {
		core.WriteResponse(ctx, err, nil)
		return false, nil
	}

	return interfaces, nil
}

// Package network
/**
* @Project : terravirtualmachine
* @File    : network.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/20 17:01
**/

package network

import (
	"github.com/gin-gonic/gin"
	controller "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/controller/v1"
	service "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/golibrary/resp"
)

var _ controller.Controller = (*NetworkController)(nil)

type NetworkController struct {
	srv service.Service
}

func NewNetworkController(store store.Factory, libvirtStore store.LibvirtFactory) *NetworkController {
	return &NetworkController{srv: service.NewService(store, libvirtStore, nil)}
}

func (Self *NetworkController) RegisterRoutes(networks *gin.RouterGroup) {
	networks.POST("/create_network", resp.WrapResp(Self.Create))                               // 新增网络
	networks.GET("/list_interfaces", resp.WrapResp(Self.ListInterfaces))                       // 获取集群中所有主机的所有网络接口列表
	networks.GET("/list_networks", resp.WrapResp(Self.List))                                   // 获取所有网络的列表
	networks.GET("/get_network/:network_id", resp.WrapResp(Self.Get))                          // 获取特定网络的详细信息
	networks.PUT("/update_network", resp.WrapResp(Self.Edit))                                  // 修改特定网络的配置
	networks.DELETE("/delete_network/:network_id", resp.WrapResp(Self.Delete))                 // 删除特定网络
	networks.DELETE("/delete_networks_batch", resp.WrapResp(Self.DeleteCollection))            // 批量删除特定网络
	networks.GET("/lisit_vnics_onnetwork/:network_id", resp.WrapResp(Self.ListVNicsOnNetwork)) // 批量删除特定网络
}

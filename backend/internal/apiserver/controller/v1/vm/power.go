package vm

import (
	"github.com/gin-gonic/gin"
	"github.com/marmotedu/errors"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
)

func (v *VMController) Start(ctx *gin.Context) (interface{}, error) {
	guestId := ctx.Param("id")

	resp := make(chan error, 1)
	v.vmSrv.StartInstance(ctx, guestId, resp)
	err := <-resp

	if err != nil {
		return false, errors.WithCode(code.ErrVMCreate, err.Error())
	}

	return nil, nil
}

func (v *VMController) ForceShutDown(ctx *gin.Context) (interface{}, error) {
	guestId := ctx.Param("id")

	resp := make(chan error, 1)
	v.vmSrv.ForceShutDownInstance(ctx, guestId, resp)
	err := <-resp

	if err != nil {
		return false, errors.WithCode(code.ErrVMCreate, err.Error())
	}

	return nil, nil
}

func (v *VMController) ShutDown(ctx *gin.Context) (interface{}, error) {
	guestId := ctx.Param("id")

	resp := make(chan error, 1)
	v.vmSrv.ShutDownInstance(ctx, guestId, resp)
	err := <-resp

	if err != nil {
		return false, errors.WithCode(code.ErrVMCreate, err.Error())
	}

	return nil, nil
}

func (v *VMController) SoftReboot(ctx *gin.Context) (interface{}, error) {
	guestId := ctx.Param("id")

	resp := make(chan error, 1)
	v.vmSrv.SoftRebootInstance(ctx, guestId, resp)
	err := <-resp

	if err != nil {
		return false, errors.WithCode(code.ErrVMCreate, err.Error())
	}

	return nil, nil
}

func (v *VMController) Pause(ctx *gin.Context) (interface{}, error) {
	guestId := ctx.Param("id")

	resp := make(chan error, 1)
	v.vmSrv.PauseInstance(ctx, guestId, resp)
	err := <-resp

	if err != nil {
		return false, errors.WithCode(code.ErrVMCreate, err.Error())
	}

	return nil, nil
}

func (v *VMController) Unpause(ctx *gin.Context) (interface{}, error) {
	guestId := ctx.Param("id")

	resp := make(chan error, 1)
	v.vmSrv.UnpauseInstance(ctx, guestId, resp)
	err := <-resp

	if err != nil {
		return false, errors.WithCode(code.ErrVMCreate, err.Error())
	}

	return nil, nil
}

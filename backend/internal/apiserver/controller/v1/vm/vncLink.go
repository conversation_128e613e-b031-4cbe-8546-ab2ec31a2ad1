package vm

import (
	"github.com/gin-gonic/gin"
	"github.com/marmotedu/errors"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
)

func (v *VMController) VNCLink(ctx *gin.Context) (interface{}, error) {
	guestId := ctx.Param("id")

	resp := make(chan dto.InstanceResult, 1)
	v.vmSrv.VNCLinkUrl(ctx, guestId, resp)
	result := <-resp
	if result.Error != nil {
		return false, errors.WithCode(code.ErrVMCreate, result.Error.Error())
	}

	return result.VncLink, nil
}

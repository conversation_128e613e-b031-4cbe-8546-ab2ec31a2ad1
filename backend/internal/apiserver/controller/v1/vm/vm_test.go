package vm

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"os/exec"
	"path"
	"regexp"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	uuid "github.com/satori/go.uuid"
	"github.com/shirou/gopsutil/disk"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store/etcd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store/libvirt"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store/redis"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

func TestMain(m *testing.M) {
	err := os.Setenv("ETCDCTL_API", "3")
	if err != nil {
		panic(err)
	}
	os.Exit(m.Run())
}

func getTestVMController(t *testing.T) *VMController {
	etcdStore := getTestEtcdStore(t)
	libvirtStore := getTestLibvirtStore(t)
	redisStore := getTestRedisStore(t)
	return NewVMController(etcdStore, libvirtStore, redisStore)
}

func getTestEtcdStore(t *testing.T) store.Factory {
	opts := options.NewEtcdOptions()
	opts.Endpoints = []string{"http://127.0.0.1:2379"}
	opts.Namespace = etcd.KeyPrefix
	etcdStore, err := etcd.GetEtcdFactoryOr(opts, nil)
	assert.NoError(t, err)
	return etcdStore
}

func getTestLibvirtStore(t *testing.T) store.LibvirtFactory {
	libvirtStore, err := libvirt.GetLibvirtFactoryOr(options.NewLibvirtOptions())
	assert.NoError(t, err)
	return libvirtStore
}

func getTestRedisStore(t *testing.T) store.RedisFactory {
	redisStore, _ := redis.GetRedisFactoryOr(&options.RedisOptions{
		Address:        "127.0.0.1:6379",
		Namespace:      "collectd",
		RequestTimeout: 5,
		DB:             0,
	})
	return redisStore
}

func generateRepositoryPool(t *testing.T, locates []dto.ImageLocate, volume []string) *domain.RepositoryPoolList {
	pools := domain.RepositoryPoolList{}
	pools.ListMeta.TotalCount = int64(len(locates))
	pools.Items = make(map[string]*domain.RepositoryPool)

	for i := 0; i < len(volume) && i < len(locates); i++ {
		id := locates[i].RepoId

		pools.Items[id] = &domain.RepositoryPool{
			Name:           "pool_" + strconv.Itoa(i),
			LocationVolume: path.Join(volume[i]),
		}
	}

	for id, rep := range pools.Items {
		jsonData, err := json.Marshal(rep)
		assert.NoError(t, err)
		etcddir := "/twm/live_cluster/repository/" + id
		err = exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
			"put", etcddir, string(jsonData)).Run()
		assert.NoError(t, err)
	}

	return &pools
}

func generateNetwork(t *testing.T, nets []domain.Network) *domain.NetworkList {
	netsList := domain.NetworkList{}
	netsList.ListMeta.TotalCount = int64(len(nets))
	netsList.Items = make([]*domain.Network, 0)

	for _, net := range nets {
		jsonData, err := json.Marshal(net)
		assert.NoError(t, err)
		etcddir := "/twm/live_cluster/networks/" + net.NetworkID
		err = exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
			"put", etcddir, string(jsonData)).Run()
		assert.NoError(t, err)

		netsList.Items = append(netsList.Items, &net)
	}

	return &netsList
}

type testImageCrate struct {
	Name         string           `json:"name"`      // 映像名称
	DiskFileName string           `json:"real_name"` // 映像名称
	Type         domain.ImageType `json:"type"`      // 映像的类型
	FileSize     uint32           `json:"file_size"` // 文件的大小
	ImageRepos   []string         `json:"repos"`     // 映像存储位置
}

func generateImage(t *testing.T, imgIds []string, imgCreate []testImageCrate) error {
	for i := 0; i < len(imgIds) && i < len(imgCreate); i++ {
		jsonData, err := json.Marshal(imgCreate[i])
		assert.NoError(t, err)
		etcddir := domain.ImagePrefix + imgIds[i]
		err = exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
			"put", etcddir, string(jsonData)).Run()
		assert.NoError(t, err)
	}

	return nil
}
func TestVmController_ListResource(t *testing.T) {
	if req, err := http.NewRequest(http.MethodGet, "/list_resource", nil); err == nil {
		req.Header.Set("Content-Type", "application/json")
		resp := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(resp)
		ctx.Request = req

		vmCtr := getTestVMController(t)
		if res, err := vmCtr.ListResource(ctx); err == nil {
			r, _ := res.(dto.VMResource)
			fmt.Printf("CPU: %d,Mem: %d\n", r.MaxCpuNum, r.MaxRamSize)
			for _, usb := range r.Usbs {
				fmt.Printf("Usb ProductName: %s,Usb Version: %s,Usb ID: %s\n", usb.ProductName, usb.Version, usb.UsbId)
			}
		} else {
			t.Fatal(err)
		}
	} else {
		t.Fatal(err)
	}
}

func TestVmController_VMInstanceCreate(t *testing.T) {
	_ = exec.Command("etcdctl",
		"--endpoints=http://127.0.0.1:2379",
		"del", "/twm/live_cluster/", "--prefix").Run()
	_ = exec.Command("targetcli", "clearconfig", "true").Run()

	//创建存储池
	volumes := make([]string, 0)
	poolsId := make([]dto.ImageLocate, 0)
	rule, _ := regexp.Compile(`/Volume\d+$`)
	if dfs, err := disk.Partitions(false); err == nil {
		for _, item := range dfs {
			if !rule.MatchString(item.Mountpoint) {
				continue
			}

			volumes = append(volumes, item.Mountpoint)
			poolsId = append(poolsId, dto.ImageLocate{RepoId: uuid.Must(uuid.NewV4()).String()})
		}
	} else {
		t.Fatalf(err.Error())
	}

	generateRepositoryPool(t, poolsId, volumes)
	//创建网络
	nets := make([]domain.Network, 0)
	nets = append(nets, []domain.Network{
		domain.Network{
			ObjectMeta: metav1.ObjectMeta{
				Name: "ValidExternalNetwork",
			},
			Type:      "external",
			VLANID:    100,
			NetworkID: uuid.Must(uuid.NewV4()).String(),
			Interfaces: map[string]*domain.InterfaceList{
				"host1": {
					ListMeta: metav1.ListMeta{
						TotalCount: 1,
					},
					Items: []*domain.Interface{
						{
							Checked:       true,
							HostID:        "host1",
							HostName:      "host1",
							InterfaceID:   "if1",
							InterfaceName: "ovs_eth0",
							IP:            []string{"***********"},
							Mask:          []string{"*************"},
							SpeedMbs:      100,
							Status:        domain.CONNECTED,
						},
					},
				},
			},
		},
	}...)
	generateNetwork(t, nets)
	//创建镜像
	imgsId := make([]string, 0)
	imgs := make([]testImageCrate, 0)

	imgId := uuid.Must(uuid.NewV4()).String()
	imgsId = append(imgsId, imgId)
	imgs = append(imgs, testImageCrate{
		Name:         imgId,
		DiskFileName: imgId + ".iso",
		Type:         domain.ISO,
		FileSize:     200 * 1024 * 1024,
		ImageRepos:   []string{poolsId[0].RepoId},
	})

	imgId = uuid.Must(uuid.NewV4()).String()
	imgsId = append(imgsId, imgId)
	imgs = append(imgs, testImageCrate{
		Name:         imgId,
		DiskFileName: imgId + ".iso",
		Type:         domain.ISO,
		FileSize:     200 * 1024 * 1024,
		ImageRepos:   []string{poolsId[0].RepoId},
	})

	imgId = uuid.Must(uuid.NewV4()).String()
	imgsId = append(imgsId, imgId)
	imgs = append(imgs, testImageCrate{
		Name:         imgId,
		DiskFileName: imgId + ".iso",
		Type:         domain.ISO,
		FileSize:     200 * 1024 * 1024,
		ImageRepos:   []string{poolsId[0].RepoId},
	})
	generateImage(t, imgsId, imgs)
	//获取创建资源,组建请求数据
	vmCreateReqBody := dto.VMInstance{}
	if req, err := http.NewRequest(http.MethodGet, "/list_resource", nil); err == nil {
		req.Header.Set("Content-Type", "application/json")
		resp := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(resp)
		ctx.Request = req

		vmCtr := getTestVMController(t)
		if res, err := vmCtr.ListResource(ctx); err == nil {
			r, _ := res.(dto.VMResource)
			vmCreateReqBody.VCPUNum = r.MaxCpuNum
			vmCreateReqBody.VRAMSize = r.MaxRamSize
			vmCreateReqBody.VideoCard = r.VideoCards[0]
			for _, img := range r.Images {
				vmCreateReqBody.ISOImages = append(vmCreateReqBody.ISOImages, img.Id)
			}
			vmCreateReqBody.USBVersion = 3
			for _, usb := range r.Usbs {
				vmCreateReqBody.USBs = append(vmCreateReqBody.USBs, usb.UsbId)
				vmCreateReqBody.USBDeviceName = append(vmCreateReqBody.USBDeviceName, usb.ProductName)
			}
			vmCreateReqBody.RepoId = r.Repos[0].RepoId
			vmCreateReqBody.RepoName = r.Repos[0].Name
			vmCreateReqBody.RepoId = r.Repos[0].RepoId
			vmCreateReqBody.RepoName = r.Repos[0].Name
			for _, net := range r.NetWorks {
				vnic := dto.VNicInstance{}
				vnic.Mac = "22:10:b5:02:76:13"
				vnic.NetworkId = net.NetworkId
				vnic.PreferSriov = net.SupportSriov
				vnic.VNicType = 1

				vmCreateReqBody.VNics = append(vmCreateReqBody.VNics, vnic)
			}
			vmCreateReqBody.VDisks = append(vmCreateReqBody.VDisks, dto.VDiskInstance{
				VDiskMode:      2,
				Name:           "虚拟磁盘 1",
				Unmap:          false,
				IOPSEnable:     false,
				DevLimit:       0,
				DevReservation: 0,
				DevWeight:      0,
				VDiskSize:      1,
				Idx:            1,
			})
			vmCreateReqBody.VDisks = append(vmCreateReqBody.VDisks, dto.VDiskInstance{
				VDiskMode:      2,
				Name:           "虚拟磁盘 2",
				Unmap:          false,
				IOPSEnable:     false,
				DevLimit:       0,
				DevReservation: 0,
				DevWeight:      0,
				VDiskSize:      1,
				Idx:            2,
			})
		} else {
			t.Fatal(err)
		}
	} else {
		t.Fatal(err)
	}
	vmCreateReqBody.Autorun = 0
	vmCreateReqBody.BootFrom = "disk"
	vmCreateReqBody.Bios = "legacy"
	vmCreateReqBody.KBLayout = "Default"
	vmCreateReqBody.SerialConsole = true
	vmCreateReqBody.IsWindowsVM = false
	vmCreateReqBody.UseOVMF = false
	vmCreateReqBody.IsGeneralVM = true
	vmCreateReqBody.AutoSwitch = 0
	vmCreateReqBody.Name = "testVm"
	vmCreateReqBody.MachineType = "pc"
	vmCreateReqBody.CPUWeight = 256
	vmCreateReqBody.Desc = "test"
	vmCreateReqBody.CPUPassthru = true
	vmCreateReqBody.HypervEnlighten = true
	vmCreateReqBody.CPUPinNum = 0
	vmCreateReqBody.PoweronAfterCreate = false
	//创建虚拟机
	reqBody, err := json.Marshal(vmCreateReqBody)
	assert.NoError(t, err)
	fmt.Print(string(reqBody))
	if req, err := http.NewRequest(http.MethodPost, "/create_vm_instance", bytes.NewBuffer([]byte(reqBody))); err == nil {
		req.Header.Set("Content-Type", "application/json")
		resp := httptest.NewRecorder()
		ctx, _ := gin.CreateTestContext(resp)
		ctx.Request = req

		vmCtr := getTestVMController(t)
		if _, err := vmCtr.Create(ctx); err == nil {
		} else {
			t.Fatal(err)
		}
	} else {
		t.Fatal(err)
	}
}

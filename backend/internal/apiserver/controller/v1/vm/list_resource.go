package vm

import (
	"github.com/gin-gonic/gin"
	"github.com/marmotedu/errors"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
)

func (v *VMController) ListResource(ctx *gin.Context) (interface{}, error) {
	if v.vmSrv == nil {
		log.L(ctx).Info("vm service init failed")
		return false, errors.WithCode(code.ErrVMInit, "vm service init failed")
	}

	// 调用服务层创建虚拟机
	resp := make(chan dto.InstanceResult, 1)
	v.vmSrv.ListResource(ctx, resp)
	result := <-resp
	if result.Error != nil {
		return false, errors.WithCode(code.ErrVMCreate, result.Error.Error())
	}

	return result.Resource, nil
}

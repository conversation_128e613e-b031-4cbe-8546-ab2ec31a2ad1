package vm

import (
	"github.com/gin-gonic/gin"
	service "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/golibrary/resp"
)

type VMController struct {
	srv   service.Service
	vmSrv service.VMSrv
}

func NewVMController(store store.Factory, libvirtStore store.LibvirtFactory, redis store.RedisFactory) *VMController {
	vmCtr := VMController{}
	vmCtr.srv = service.NewService(store, libvirtStore, redis)
	vmCtr.vmSrv = vmCtr.srv.VMs()
	return &vmCtr
}

func (Self *VMController) RegisterRoutes(vms *gin.RouterGroup) {
	// 基本操作
	vms.POST("/create_vm_instance", resp.WrapResp(Self.Create)) // 创建虚拟机
	vms.GET("/list_instance", resp.WrapResp(Self.List))         // 获取虚拟机列表
	// vms.GET("/:id", v.Get)       // 获取虚拟机详情
	// vms.PUT("/:id", v.Edit)      // 更新虚拟机
	// vms.DELETE("/:id", v.Delete) // 删除虚拟机
	// 资源管理
	vms.GET("/list_resource", resp.WrapResp(Self.ListResource))
	// 电源管理
	vms.POST("/:id/poweron", resp.WrapResp(Self.Start))                // 启动虚拟机
	vms.POST("/:id/poweroff", resp.WrapResp(Self.ShutDown))            // 停止虚拟机
	vms.POST("/:id/reboot", resp.WrapResp(Self.SoftReboot))            // 重启虚拟机
	vms.POST("/:id/force_poweroff", resp.WrapResp(Self.ForceShutDown)) // 强制关机
	vms.POST("/:id/pause", resp.WrapResp(Self.Pause))                  // 暂停虚拟机
	vms.POST("/:id/resume", resp.WrapResp(Self.Unpause))               // 恢复虚拟机

	// // 高级操作
	// vms.POST("/:id/clone", v.Clone)     // 克隆虚拟机
	// vms.POST("/:id/import", v.Import)   // 导入虚拟机
	// vms.POST("/:id/export", v.Export)   // 导出虚拟机
	// vms.POST("/:id/migrate", v.Migrate) // 迁移虚拟机
	// vms.POST("/:id/connect", v.Connect) // 连接虚拟机

	// 连接管理
	vms.POST("/:id/link", resp.WrapResp(Self.VNCLink)) // 连接虚拟机
}

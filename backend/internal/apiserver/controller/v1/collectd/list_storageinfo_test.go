package collectd

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"sort"
	"testing"

	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
)

func TestCollectdController_ListSto(t *testing.T) {
	controller := getTestCollectdController(t)

	tests := []struct {
		name   string
		before func(t *testing.T) dto.StorageListDto
		after  func(t *testing.T)
	}{
		{
			name: "test_list_storageinfo",
			before: func(t *testing.T) dto.StorageListDto {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
				meta := metav1.ListMeta{
					TotalCount: 4,
				}
				storageData := domain.StorageState{

					Storage: map[string]*domain.StorageInfo{
						"VM Storage 1": {
							SizeFreeByte: 15119634432,
							SizeUsedByte: 27416375296,
							PercentFree:  35.206581115722656,
							PercentUsed:  64.793418884277344,
						},
						"VM Storage 2": {
							SizeFreeByte: 25697906688,
							SizeUsedByte: 79791067136,
							PercentFree:  23.933040618896484,
							PercentUsed:  76.066959381103516,
						},
						"VM Storage 3": {
							SizeFreeByte: 79791067136,
							SizeUsedByte: 25697906688,
							PercentFree:  60.066959381103516,
							PercentUsed:  39.933040618896484,
						},
						"VM Storage 4": {
							SizeFreeByte: 25697906688,
							SizeUsedByte: 79791067136,
							PercentFree:  23.933040618896484,
							PercentUsed:  76.066959381103516,
						},
					},
				}

				for storageName, storage := range storageData.Storage {
					jsonData, err := json.Marshal(storage)
					assert.NoError(t, err)
					cmd := exec.Command("redis-cli", "-n", "0", "HSET", "volume", storageName, string(jsonData))
					assert.NoError(t, cmd.Run())
				}

				return dto.StorageListDto{
					ListMeta: meta,
					Storage: []*dto.StorageDto{
						{
							Desc:        "info",
							HostName:    "TNAS-001",
							ID:          "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
							Name:        "VM Storage 1",
							Size:        storageData.Storage["VM Storage 1"].SizeFreeByte + storageData.Storage["VM Storage 1"].SizeUsedByte,
							Used:        storageData.Storage["VM Storage 1"].SizeUsedByte,
							Type:        "healthy",
							Status:      "none",
							PercentFree: storageData.Storage["VM Storage 1"].PercentFree,
							PercentUsed: storageData.Storage["VM Storage 1"].PercentUsed,
						},
						{
							Desc:        "info",
							HostName:    "TNAS-001",
							ID:          "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
							Name:        "VM Storage 2",
							Size:        storageData.Storage["VM Storage 2"].SizeFreeByte + storageData.Storage["VM Storage 2"].SizeUsedByte,
							Used:        storageData.Storage["VM Storage 2"].SizeUsedByte,
							Type:        "healthy",
							Status:      "none",
							PercentFree: storageData.Storage["VM Storage 2"].PercentFree,
							PercentUsed: storageData.Storage["VM Storage 2"].PercentUsed,
						},
						{
							Desc:        "info",
							HostName:    "TNAS-001",
							ID:          "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
							Name:        "VM Storage 3",
							Size:        storageData.Storage["VM Storage 3"].SizeFreeByte + storageData.Storage["VM Storage 3"].SizeUsedByte,
							Used:        storageData.Storage["VM Storage 3"].SizeUsedByte,
							Type:        "healthy",
							Status:      "none",
							PercentFree: storageData.Storage["VM Storage 3"].PercentFree,
							PercentUsed: storageData.Storage["VM Storage 3"].PercentUsed,
						},
						{
							Desc:        "info",
							HostName:    "TNAS-001",
							ID:          "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
							Name:        "VM Storage 4",
							Size:        storageData.Storage["VM Storage 4"].SizeFreeByte + storageData.Storage["VM Storage 4"].SizeUsedByte,
							Used:        storageData.Storage["VM Storage 4"].SizeUsedByte,
							Type:        "healthy",
							Status:      "none",
							PercentFree: storageData.Storage["VM Storage 4"].PercentFree,
							PercentUsed: storageData.Storage["VM Storage 4"].PercentUsed,
						},
					},
				}
			},
			after: func(t *testing.T) {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wantStorage := test.before(t)
			defer test.after(t)

			req, err := http.NewRequest("GET", "/v1/collectd/ov/storageinfo", nil)
			assert.NoError(t, err)

			resp := httptest.NewRecorder()

			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			storage, err := controller.ListStoInfo(ctx)
			assert.NoError(t, err)
			core.WriteResponse(ctx, nil, storage)

			assert.Equal(t, http.StatusOK, resp.Code)
			var actualStorage dto.StorageListDto
			err = json.Unmarshal(resp.Body.Bytes(), &actualStorage)
			assert.NoError(t, err)

			sort.Slice(actualStorage.Storage, func(i, j int) bool {
				return actualStorage.Storage[i].Name < actualStorage.Storage[j].Name
			})

			sort.Slice(wantStorage.Storage, func(i, j int) bool {
				return wantStorage.Storage[i].Name < wantStorage.Storage[j].Name
			})

			assert.Equal(t, wantStorage, actualStorage)
		})
	}
}

package collectd

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/golibrary/errors"
)

func (Self *CollectdController) ListStoInfo(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("[Collectd_Controller] StorageInfo ListInfo called")

	var opts metav1.ListOptions
	if err := ctx.ShouldBindQuery(&opts); err != nil {
		return false, errors.WithCode(code.ErrBind, err.Error())
	}

	storage, err := Self.srv.CollStorageInfo().List(ctx, opts)
	if err != nil {
		return false, err
	}

	return storage, err
}

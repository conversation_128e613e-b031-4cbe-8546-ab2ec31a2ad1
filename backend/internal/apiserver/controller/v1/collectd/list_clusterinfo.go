package collectd

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/golibrary/errors"
)

func (Self *CollectdController) ListClusterInfo(ctx *gin.Context) (interface{}, error) {
	log.L(ctx).Info("[Collectd_Controller] ClusterInfo List called")

	var opts metav1.ListOptions
	if err := ctx.ShouldBindQuery(&opts); err != nil {
		return false, errors.WithCode(code.ErrBind, err.Error())
	}

	ovClusterInfo, err := Self.srv.CollClusterInfo().ListOLD(ctx, opts)
	if err != nil {
		return false, err
	}

	return ovClusterInfo, err
}

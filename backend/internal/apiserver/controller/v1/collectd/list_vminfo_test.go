package collectd

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"sort"
	"testing"

	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
)

func TestCollectdController_ListVMInfo(t *testing.T) {
	controller := getTestCollectdController(t)

	tests := []struct {
		name   string
		before func(t *testing.T) dto.VmListDto
		after  func(t *testing.T)
	}{
		{
			name: "test_list_vminfo",
			before: func(t *testing.T) dto.VmListDto {
				// 清理测试数据
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()

				// 测试用例
				netData := domain.NetworkState{
					ListMeta: metav1.ListMeta{
						TotalCount: 3,
					},
					Interfaces: map[string]*domain.NetworkInterface{
						"br0": {
							IP:      "************",
							Mask:    "*********",
							Speed:   1000,
							Status:  "connected",
							Type:    "ether",
							UseDHCP: true,
						},
						"virbr0": {
							IP:      "*************",
							Mask:    "*********",
							Speed:   -1,
							Status:  "disconnected",
							Type:    "ether",
							UseDHCP: false,
						},
						"ens33": {
							IP:      "",
							Mask:    "",
							Speed:   1000,
							Status:  "connected",
							Type:    "ether",
							UseDHCP: false,
						},
					},
				}

				for netname, net := range netData.Interfaces {
					jsonData, err := json.Marshal(net)
					assert.NoError(t, err)
					cmd := exec.Command("redis-cli", "-n", "0", "HSET", "network", netname, string(jsonData))
					assert.NoError(t, cmd.Run())
				}

				vmData1 := domain.VMUsage{
					MemoryUsage: 30.5, // 内存使用率 30.5%
					VCPUUsage:   25.0, // CPU 使用率 25.0%
					Disk: []domain.Disk{
						{
							VDiskID:         "vdisk-001",
							ReadIOPS:        100,
							WriteIOPS:       50,
							TotalIOPS:       150,
							ReadThroughput:  1024.0,
							WriteThroughput: 512.0,
							TotalThroughput: 1536.0,
							ReadAvgLatency:  10,
							WriteAvgLatency: 20,
							TotalIOLatency:  30,
						},
						{
							VDiskID:         "vdisk-002",
							ReadIOPS:        200,
							WriteIOPS:       100,
							TotalIOPS:       300,
							ReadThroughput:  2048.0,
							WriteThroughput: 1024.0,
							TotalThroughput: 3072.0,
							ReadAvgLatency:  15,
							WriteAvgLatency: 25,
							TotalIOLatency:  40,
						},
					},
					Network: []domain.Network{
						{
							Device:  "virbr0",
							RxBytes: 1024.0,
							TxBytes: 512.0,
						},
						{
							Device:  "br0",
							RxBytes: 2048.0,
							TxBytes: 1024.0,
						},
					},
				}

				vmData2 := domain.VMUsage{
					MemoryUsage: 88.5, // 内存使用率 30.5%
					VCPUUsage:   25.0, // CPU 使用率 25.0%
					Disk: []domain.Disk{
						{
							VDiskID:         "vdisk-001",
							ReadIOPS:        100,
							WriteIOPS:       50,
							TotalIOPS:       150,
							ReadThroughput:  1024.0,
							WriteThroughput: 512.0,
							TotalThroughput: 1536.0,
							ReadAvgLatency:  10,
							WriteAvgLatency: 20,
							TotalIOLatency:  30,
						},
						{
							VDiskID:         "vdisk-002",
							ReadIOPS:        200,
							WriteIOPS:       100,
							TotalIOPS:       300,
							ReadThroughput:  2048.0,
							WriteThroughput: 1024.0,
							TotalThroughput: 3072.0,
							ReadAvgLatency:  15,
							WriteAvgLatency: 25,
							TotalIOLatency:  40,
						},
						{
							VDiskID:         "vdisk-003",
							ReadIOPS:        200,
							WriteIOPS:       100,
							TotalIOPS:       300,
							ReadThroughput:  2048.0,
							WriteThroughput: 1024.0,
							TotalThroughput: 3072.0,
							ReadAvgLatency:  15,
							WriteAvgLatency: 25,
							TotalIOLatency:  40,
						},
					},
					Network: []domain.Network{
						{
							Device:  "br0",
							RxBytes: 1024.0,
							TxBytes: 512.0,
						},
						{
							Device:  "eth1",
							RxBytes: 2048.0,
							TxBytes: 1024.0,
						},
					},
				}

				vmData3 := domain.VMUsage{
					MemoryUsage: 77.5, // 内存使用率 30.5%
					VCPUUsage:   25.0, // CPU 使用率 25.0%
					Disk: []domain.Disk{
						{
							VDiskID:         "vdisk-vda",
							ReadIOPS:        100,
							WriteIOPS:       50,
							TotalIOPS:       150,
							ReadThroughput:  1024.0,
							WriteThroughput: 5955.0,
							TotalThroughput: 1536.0,
							ReadAvgLatency:  10,
							WriteAvgLatency: 20,
							TotalIOLatency:  30,
						},
						{
							VDiskID:         "vdisk-vdb",
							ReadIOPS:        200,
							WriteIOPS:       100,
							TotalIOPS:       300,
							ReadThroughput:  2048.0,
							WriteThroughput: 1024.0,
							TotalThroughput: 3072.0,
							ReadAvgLatency:  15,
							WriteAvgLatency: 25,
							TotalIOLatency:  40,
						},
					},
					Network: []domain.Network{
						{
							Device:  "br0",
							RxBytes: 1024.0,
							TxBytes: 512.0,
						},
						{
							Device:  "eth1",
							RxBytes: 2048.0,
							TxBytes: 1024.0,
						},
					},
				}

				vmUsageList := domain.VMUsageList{
					ListMeta: metav1.ListMeta{
						TotalCount: 3,
					},
					Items: map[string]*domain.VMUsage{
						"vm-001": &vmData1,
						"vm-002": &vmData2,
						"vm-003": &vmData3,
					},
				}

				for vmname, vmData := range vmUsageList.Items {
					vminfoData, err := json.Marshal(vmData)
					assert.NoError(t, err)
					cmd := exec.Command("redis-cli", "-n", "0", "HSET", "vm_usage", vmname, string(vminfoData))
					assert.NoError(t, cmd.Run())
				}

				vmListDto := dto.VmListDto{
					ListMeta: metav1.ListMeta{
						TotalCount: vmUsageList.TotalCount,
					},
					VMs: make([]*dto.VmDto, 0, len(vmUsageList.Items)),
				}

				for vmID, vmUsage := range vmUsageList.Items {
					vmDto := &dto.VmDto{
						ObjectMeta: metav1.ObjectMeta{
							Name: vmID,
						},
						CPUUsage: vmUsage.VCPUUsage,
						RAMUsage: vmUsage.MemoryUsage,
						Status:   "unknown", // 默认状态
						VmType:   "unknown", // 默认类型
						Desc:     "info",    // 默认描述
						ID:       vmID,
						Name:     vmID,
						Disks:    make([]dto.VmDisk, 0, len(vmUsage.Disk)),
						NICs:     make([]dto.VmNIC, 0, len(vmUsage.Network)),
					}

					// 填充磁盘数据
					for Idx, disk := range vmUsage.Disk {
						vmDto.Disks = append(vmDto.Disks, dto.VmDisk{
							Idx:         Idx,
							VdiskID:     disk.VDiskID,
							RThroughput: disk.ReadThroughput,
							WThroughput: disk.WriteThroughput,
						})
					}

					findNetStatus := func(device string) string {
						cmd := exec.Command("redis-cli", "-n", "0", "HGET", "network", device)
						output, err := cmd.Output()
						if err != nil {
							return "unknown" // 默认状态
						}

						// 解析 JSON 数据
						var result map[string]interface{}
						if err := json.Unmarshal(output, &result); err != nil {
							return "unknown" // 默认状态
						}

						// 提取 status 字段
						status, ok := result["status"].(string)
						if !ok {
							return "unknown" // 默认状态
						}

						return status
					}

					// 填充网络接口数据
					for idx, nic := range vmUsage.Network {
						vmDto.NICs = append(vmDto.NICs, dto.VmNIC{
							Device:    fmt.Sprintf("eth%d", idx),
							HostNetIf: "eth0", // 默认主机侧接口
							Rx:        nic.RxBytes,
							Tx:        nic.TxBytes,
							VnicID:    "vnic0",                   // 使用设备名作为虚拟网卡ID
							Status:    findNetStatus(nic.Device), // 默认网络状态
						})
					}

					vmListDto.VMs = append(vmListDto.VMs, vmDto)
				}

				return vmListDto
			},
			after: func(t *testing.T) {
				//清理测试数据
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantVMDtos := tt.before(t)
			defer tt.after(t)

			req, err := http.NewRequest(http.MethodGet, "/v1/collectd/ov/vminfo", nil)
			assert.NoError(t, err)

			resp := httptest.NewRecorder()

			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			vm, err := controller.ListVMInfo(ctx)
			assert.NoError(t, err)
			core.WriteResponse(ctx, nil, vm)

			assert.Equal(t, http.StatusOK, resp.Code)
			var actualVMDtos dto.VmListDto
			err = json.Unmarshal(resp.Body.Bytes(), &actualVMDtos)
			assert.NoError(t, err)

			// 在比较前对 VMs 按 ID 排序
			sort.Slice(actualVMDtos.VMs, func(i, j int) bool {
				return actualVMDtos.VMs[i].ID < actualVMDtos.VMs[j].ID
			})
			sort.Slice(wantVMDtos.VMs, func(i, j int) bool {
				return wantVMDtos.VMs[i].ID < wantVMDtos.VMs[j].ID
			})
			assert.EqualValues(t, wantVMDtos, actualVMDtos)
		})
	}
}

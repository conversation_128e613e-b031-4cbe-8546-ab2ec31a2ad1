package collectd

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"testing"

	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
)

func TestCollectdController_ListClusterInfo(t *testing.T) {
	controller := getTestCollectdController(t)

	tests := []struct {
		name   string
		before func(t *testing.T) dto.ClusterStatusResponse
		after  func(t *testing.T)
	}{
		{
			name: "test_list_clusterinfo",
			before: func(t *testing.T) dto.ClusterStatusResponse {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()

				vmData1 := domain.VMUsage{
					MemoryUsage: 30.5, // 内存使用率 30.5%
					VCPUUsage:   25.0, // CPU 使用率 25.0%
					Disk: []domain.Disk{
						{
							VDiskID:         "vdisk-001",
							ReadIOPS:        100,
							WriteIOPS:       50,
							TotalIOPS:       150,
							ReadThroughput:  1024.0,
							WriteThroughput: 512.0,
							TotalThroughput: 1536.0,
							ReadAvgLatency:  10,
							WriteAvgLatency: 20,
							TotalIOLatency:  30,
						},
						{
							VDiskID:         "vdisk-002",
							ReadIOPS:        200,
							WriteIOPS:       100,
							TotalIOPS:       300,
							ReadThroughput:  2048.0,
							WriteThroughput: 1024.0,
							TotalThroughput: 3072.0,
							ReadAvgLatency:  15,
							WriteAvgLatency: 25,
							TotalIOLatency:  40,
						},
					},
					Network: []domain.Network{
						{
							Device:  "virbr0",
							RxBytes: 1024.0,
							TxBytes: 512.0,
						},
						{
							Device:  "br0",
							RxBytes: 2048.0,
							TxBytes: 1024.0,
						},
					},
				}

				vmData2 := domain.VMUsage{
					MemoryUsage: 88.5, // 内存使用率 30.5%
					VCPUUsage:   25.0, // CPU 使用率 25.0%
					Disk: []domain.Disk{
						{
							VDiskID:         "vdisk-001",
							ReadIOPS:        100,
							WriteIOPS:       50,
							TotalIOPS:       150,
							ReadThroughput:  1024.0,
							WriteThroughput: 512.0,
							TotalThroughput: 1536.0,
							ReadAvgLatency:  10,
							WriteAvgLatency: 20,
							TotalIOLatency:  30,
						},
						{
							VDiskID:         "vdisk-002",
							ReadIOPS:        200,
							WriteIOPS:       100,
							TotalIOPS:       300,
							ReadThroughput:  2048.0,
							WriteThroughput: 1024.0,
							TotalThroughput: 3072.0,
							ReadAvgLatency:  15,
							WriteAvgLatency: 25,
							TotalIOLatency:  40,
						},
						{
							VDiskID:         "vdisk-003",
							ReadIOPS:        200,
							WriteIOPS:       100,
							TotalIOPS:       300,
							ReadThroughput:  2048.0,
							WriteThroughput: 1024.0,
							TotalThroughput: 3072.0,
							ReadAvgLatency:  15,
							WriteAvgLatency: 25,
							TotalIOLatency:  40,
						},
					},
					Network: []domain.Network{
						{
							Device:  "virbr0",
							RxBytes: 1024.0,
							TxBytes: 512.0,
						},
						{
							Device:  "eth1",
							RxBytes: 2048.0,
							TxBytes: 1024.0,
						},
					},
				}

				vmData3 := domain.VMUsage{
					MemoryUsage: 77.5, // 内存使用率 30.5%
					VCPUUsage:   25.0, // CPU 使用率 25.0%
					Disk: []domain.Disk{
						{
							VDiskID:         "vdisk-vda",
							ReadIOPS:        100,
							WriteIOPS:       50,
							TotalIOPS:       150,
							ReadThroughput:  1024.0,
							WriteThroughput: 5955.0,
							TotalThroughput: 1536.0,
							ReadAvgLatency:  10,
							WriteAvgLatency: 20,
							TotalIOLatency:  30,
						},
						{
							VDiskID:         "vdisk-vdb",
							ReadIOPS:        200,
							WriteIOPS:       100,
							TotalIOPS:       300,
							ReadThroughput:  2048.0,
							WriteThroughput: 1024.0,
							TotalThroughput: 3072.0,
							ReadAvgLatency:  15,
							WriteAvgLatency: 25,
							TotalIOLatency:  40,
						},
					},
					Network: []domain.Network{
						{
							Device:  "virbr0",
							RxBytes: 1024.0,
							TxBytes: 512.0,
						},
						{
							Device:  "eth1",
							RxBytes: 2048.0,
							TxBytes: 1024.0,
						},
					},
				}

				vmUsageList := domain.VMUsageList{
					ListMeta: metav1.ListMeta{
						TotalCount: 3,
					},
					Items: map[string]*domain.VMUsage{
						"vm-001": &vmData1,
						"vm-002": &vmData2,
						"vm-003": &vmData3,
					},
				}

				for vmname, vmData := range vmUsageList.Items {
					vminfoData, err := json.Marshal(vmData)
					assert.NoError(t, err)
					cmd := exec.Command("redis-cli", "-n", "0", "HSET", "vm_usage", vmname, string(vminfoData))
					assert.NoError(t, cmd.Run())
				}

				storageData := domain.StorageState{

					Storage: map[string]*domain.StorageInfo{
						"VM Storage 1": {
							SizeFreeByte: 15119634432,
							SizeUsedByte: 27416375296,
							PercentFree:  5.206581115722656,
							PercentUsed:  94.793418884277344,
						},
						"VM Storage 2": {
							SizeFreeByte: 25697906688,
							SizeUsedByte: 79791067136,
							PercentFree:  23.933040618896484,
							PercentUsed:  76.066959381103516,
						},
						"VM Storage 3": {
							SizeFreeByte: 79791067136,
							SizeUsedByte: 25697906688,
							PercentFree:  60.066959381103516,
							PercentUsed:  39.933040618896484,
						},
						"VM Storage 4": {
							SizeFreeByte: 25697906688,
							SizeUsedByte: 79791067136,
							PercentFree:  23.933040618896484,
							PercentUsed:  76.066959381103516,
						},
					},
				}

				for storageName, storage := range storageData.Storage {
					jsonData, err := json.Marshal(storage)
					assert.NoError(t, err)
					cmd := exec.Command("redis-cli", "-n", "0", "HSET", "volume", storageName, string(jsonData))
					assert.NoError(t, cmd.Run())
				}

				netData := domain.NetworkState{
					ListMeta: metav1.ListMeta{
						TotalCount: 4,
					},
					Interfaces: map[string]*domain.NetworkInterface{
						"br0": {
							IP:      "************",
							Mask:    "*********",
							Speed:   1000,
							Status:  "connected",
							Type:    "ether",
							UseDHCP: true,
						},
						"virbr0": {
							IP:      "*************",
							Mask:    "*********",
							Speed:   -1,
							Status:  "disconnected",
							Type:    "ether",
							UseDHCP: false,
						},
						"ens33": {
							IP:      "",
							Mask:    "",
							Speed:   1000,
							Status:  "connected",
							Type:    "ether",
							UseDHCP: false,
						},
						"eth1": {
							IP:      "",
							Mask:    "",
							Speed:   1000,
							Status:  "disconnected",
							Type:    "ether",
							UseDHCP: false,
						},
					},
				}

				for netname, net := range netData.Interfaces {
					jsonData, err := json.Marshal(net)
					assert.NoError(t, err)
					cmd := exec.Command("redis-cli", "-n", "0", "HSET", "network", netname, string(jsonData))
					assert.NoError(t, cmd.Run())
				}

				return dto.ClusterStatusResponse{
					ClusterStatus: "warning",
					GuestSumm: dto.GuestSumm{
						Running: 3,
						Warning: 2,
						Healthy: 1,
					},
					HostSumm: dto.HostSumm{
						Warning: 0,
						Healthy: 1,
					},
					RepoSumm: dto.RepoSumm{
						Warning: 1,
						Healthy: 3,
					},
					Reasons: []dto.Reason{
						{
							Num:    2,
							Status: "guest_running_warning",
						},
						{
							Num:    1,
							Status: "repo_provision_warning",
						},
					},
				}
			},
			after: func(t *testing.T) {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wantClusterStatus := test.before(t)
			defer test.after(t)

			req, err := http.NewRequest(http.MethodGet, "/v1/collectd/ov/clusterinfo", nil)
			assert.NoError(t, err)

			resp := httptest.NewRecorder()
			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			clusterinfo, err := controller.ListClusterInfo(ctx)
			assert.NoError(t, err)
			core.WriteResponse(ctx, nil, clusterinfo)

			assert.Equal(t, http.StatusOK, resp.Code)
			var actualClusterStatus dto.ClusterStatusResponse
			err = json.Unmarshal(resp.Body.Bytes(), &actualClusterStatus)
			assert.NoError(t, err)

			assert.Equal(t, wantClusterStatus, actualClusterStatus)
		})
	}
}

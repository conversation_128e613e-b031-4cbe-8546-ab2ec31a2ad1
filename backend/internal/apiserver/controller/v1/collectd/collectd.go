package collectd

import (
	"github.com/gin-gonic/gin"
	controller "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/controller/v1"
	service "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/golibrary/resp"
)

var _ controller.Controller = (*CollectdController)(nil)

type CollectdController struct {
	srv service.CollService
}

func NewCollectdController(store store.RedisFactory, factory store.Factory) *CollectdController {
	return &CollectdController{srv: service.NewCollService(store, factory)}
}

func (Self *CollectdController) RegisterRoutes(Collectd *gin.RouterGroup) {
	Collectd.GET("/ov/hostinfo", resp.WrapResp(Self.ListHostInfo))       //获取主览页面主机相关信息
	Collectd.GET("/ov/vminfo", resp.WrapResp(Self.ListVMInfo))           //获取主览页面虚拟机相关信息
	Collectd.GET("/ov/storageinfo", resp.WrapResp(Self.ListStoInfo))     //获取主览页面存储相关信息
	Collectd.GET("/ov/clusterinfo", resp.WrapResp(Self.ListClusterInfo)) //获取主览页面集群相关信息, 包含警告,错误和健康状态
}

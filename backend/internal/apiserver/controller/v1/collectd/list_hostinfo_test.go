package collectd

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"sort"
	"testing"

	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
)

func TestCollectdController_ListHostInfo(t *testing.T) {
	controller := getTestCollectdController(t)

	tests := []struct {
		name   string
		before func(t *testing.T) dto.HostListDto
		after  func(t *testing.T)
	}{
		{
			name: "test_list_hostinfo",
			before: func(t *testing.T) dto.HostListDto {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()

				sysData := domain.SystemUtilization{
					CPU: domain.CPUInfo{
						TotalUsage: "6",
						CPUs: map[string]domain.CPULoad{
							"cpu-0": {UserLoad: "1.86", SystemLoad: "5.38", OtherLoad: "0.00"},
							"cpu-1": {UserLoad: "1.44", SystemLoad: "4.94", OtherLoad: "0.00"},
						},
					},
					Memory: domain.MemoryInfo{
						Used:        3363573760.0,
						Free:        118341632.0,
						Buffered:    1961984.0,
						Cached:      438493184.0,
						SlabRecl:    69468160.0,
						SlabUnrecl:  83816448.0,
						Utilization: "82",
					},
					Network: domain.NetworSyskInfo{
						Interfaces: map[string]*domain.InterfaceStats{
							"lo":      {RX: 86, TX: 86},
							"bond0":   {RX: 0, TX: 0},
							"wwan0":   {RX: 0, TX: 0},
							"wwan1":   {RX: 0, TX: 0},
							"gre0":    {RX: 0, TX: 0},
							"gretap0": {RX: 0, TX: 0},
							"erspan0": {RX: 0, TX: 0},
							"sit0":    {RX: 0, TX: 0},
							"ip6tnl0": {RX: 0, TX: 0},
							"eth0":    {RX: 11, TX: 1},
							"tunl0":   {RX: 0, TX: 0},
							"vnet1":   {RX: 0, TX: 1},
							"br0":     {RX: 2, TX: 1},
						},
					},
				}

				sysCpu, err := json.Marshal(sysData.CPU)
				assert.NoError(t, err)
				cmd := exec.Command("redis-cli", "-n", "0", "HSET", "system_utilization", "cpu", string(sysCpu))
				assert.NoError(t, cmd.Run())

				sysMem, err := json.Marshal(sysData.Memory)
				assert.NoError(t, err)
				cmd = exec.Command("redis-cli", "-n", "0", "HSET", "system_utilization", "memory", string(sysMem))
				assert.NoError(t, cmd.Run())

				sysNet, err := json.Marshal(sysData.Network)
				assert.NoError(t, err)
				cmd = exec.Command("redis-cli", "-n", "0", "HSET", "system_utilization", "network", string(sysNet))
				assert.NoError(t, cmd.Run())

				netData := domain.NetworkState{
					ListMeta: metav1.ListMeta{
						TotalCount: 3,
					},
					Interfaces: map[string]*domain.NetworkInterface{
						"br0": {
							IP:      "************",
							Mask:    "*********",
							Speed:   1000,
							Status:  "connected",
							Type:    "ether",
							UseDHCP: true,
						},
						"vnet1": {
							IP:      "*************",
							Mask:    "*********",
							Speed:   -1,
							Status:  "disconnected",
							Type:    "ether",
							UseDHCP: false,
						},
						"eth0": {
							IP:      "",
							Mask:    "",
							Speed:   1000,
							Status:  "connected",
							Type:    "ether",
							UseDHCP: false,
						},
					},
				}

				for netname, net := range netData.Interfaces {
					jsonData, err := json.Marshal(net)
					assert.NoError(t, err)
					cmd = exec.Command("redis-cli", "-n", "0", "HSET", "network", netname, string(jsonData))
					assert.NoError(t, cmd.Run())
				}

				return dto.HostListDto{
					ListMeta: metav1.ListMeta{
						TotalCount: 1,
					},
					Hosts: []*dto.HostDto{
						{
							ObjectMeta: metav1.ObjectMeta{
								Name: "TNAS-001",
							},
							CPUUsage: 6,
							Desc:     "Info",
							ID:       "9b5910c9-557c-42a7-b592-7996d606dcc0",
							Name:     "TNAS-001",
							RAMUsage: 82,
							Status:   "none",
							HostType: "healthy",
							NICs: []dto.HostNIC{
								{
									Device: "br0",
									Rx:     2,
									Tx:     1,
									Status: "connected",
									Type:   "ether",
								},
								{
									Device: "vnet1",
									Rx:     0,
									Tx:     1,
									Status: "disconnected",
									Type:   "ether",
								},
								{
									Device: "eth0",
									Rx:     11,
									Tx:     1,
									Status: "connected",
									Type:   "ether",
								},
							},
						},
					},
				}
			},
			after: func(t *testing.T) {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantHostDtos := tt.before(t)
			defer tt.after(t)

			req, err := http.NewRequest(http.MethodGet, "/v1/collectd/ov/hostinfo", nil)

			assert.NoError(t, err)

			resp := httptest.NewRecorder()

			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			hostinfo, err := controller.ListHostInfo(ctx)
			assert.NoError(t, err)
			core.WriteResponse(ctx, nil, hostinfo)

			assert.Equal(t, http.StatusOK, resp.Code)
			var actualHostDtos dto.HostListDto
			err = json.Unmarshal(resp.Body.Bytes(), &actualHostDtos)
			assert.NoError(t, err)

			// 对实际值和预期值的 NICs 进行排序
			for _, host := range actualHostDtos.Hosts {
				host.NICs = sortNICs(host.NICs)
			}
			for _, host := range wantHostDtos.Hosts {
				host.NICs = sortNICs(host.NICs)
			}

			assert.Equal(t, wantHostDtos, actualHostDtos)

		})
	}

}

// 辅助函数：对 NICs 按 Device 名称排序
func sortNICs(nics []dto.HostNIC) []dto.HostNIC {
	sort.Slice(nics, func(i, j int) bool {
		return nics[i].Device < nics[j].Device
	})
	return nics
}

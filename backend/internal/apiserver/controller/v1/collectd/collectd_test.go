package collectd

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store/etcd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store/redis"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

func getTestCollectdController(t *testing.T) *CollectdController {
	redisStore := getTestRedisStore(t)
	etcdStore := getTestEtcdStore(t)
	return NewCollectdController(redisStore, etcdStore)
}

func getTestRedisStore(t *testing.T) store.RedisFactory {
	opts := options.NewRedisOptions()
	opts.Address = "localhost:6379"
	opts.DB = 0
	//opts.Namespace = "collectd"
	redisStore, err := redis.GetRedisFactoryOr(opts)
	assert.NoError(t, err)
	return redisStore
}

func getTestEtcdStore(t *testing.T) store.Factory {
	opts := options.NewEtcdOptions()
	opts.Endpoints = []string{"http://127.0.0.1:2379"}
	opts.Namespace = etcd.KeyPrefix
	etcdStore, err := etcd.GetEtcdFactoryOr(opts, nil)
	assert.NoError(t, err)
	return etcdStore
}

package log

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

func TestExport(t *testing.T) {
	controller := getTestLogController(t)
	store := getTestEtcdStore(t)

	tests := []struct {
		name   string
		before func(t *testing.T)
		after  func(t *testing.T)
	}{
		{
			name: "export logs",
			before: func(t *testing.T) {
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/log/", "--prefix").Run()
				assert.NoError(t, err)

				mes1 := fmt.Sprintf(domain.VMEditMessage, "aa-001", "Virtual disks added")
				store.Logs().Create(context.Background(),
					&domain.LogEntry{
						Message:  mes1,
						Priority: 1,
						Time:     time.Now().Unix(),
					}, metav1.CreateOptions{})

				mes2 := fmt.Sprintf(domain.VMEditMessage, "aa-002", "Virtual disks deleted")
				store.Logs().Create(context.Background(),
					&domain.LogEntry{
						Message:  mes2,
						Priority: 2,
						Time:     time.Now().Unix(),
					}, metav1.CreateOptions{})

				mes3 := fmt.Sprintf(domain.VMEditMessage, "aa-003", "Virtual disks edited")
				store.Logs().Create(context.Background(),
					&domain.LogEntry{
						Message:  mes3,
						Priority: 3,
						Time:     time.Now().Unix(),
					}, metav1.CreateOptions{})
			},
			after: func(t *testing.T) {
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/log/", "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			test.before(t)
			defer test.after(t)

			req, err := http.NewRequest(http.MethodGet, "/v1/log/list", nil)
			assert.NoError(t, err)

			resp := httptest.NewRecorder()
			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			_, err = controller.Export(ctx)
			assert.NoError(t, err)
			assert.Equal(t, http.StatusOK, resp.Code)

		})
	}
}

package log

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
)

func TestClear(t *testing.T) {
	controller := getTestLogController(t)
	store := getTestEtcdStore(t)

	tests := []struct {
		name   string
		before func() *dto.LogResponseDto
		after  func()
	}{
		{
			name: "clear logs",
			before: func() *dto.LogResponseDto {
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/log/", "--prefix").Run()
				assert.NoError(t, err)

				mes1 := fmt.Sprintf(domain.VMEditMessage, "aa-001", "Virtual disks added")
				store.Logs().Create(context.Background(),
					&domain.LogEntry{
						Message:  mes1,
						Priority: 1,
						Time:     time.Now().Unix(),
					},
					metav1.CreateOptions{})

				mes2 := fmt.Sprintf(domain.VMEditMessage, "aa-002", "Virtual disks deleted")
				store.Logs().Create(context.Background(),
					&domain.LogEntry{
						Message:  mes2,
						Priority: 3,
						Time:     time.Now().Unix(),
					},
					metav1.CreateOptions{})

				mes3 := fmt.Sprintf(domain.VMEditFailedMessage, "tt-001")
				store.Logs().Create(context.Background(),
					&domain.LogEntry{
						Message:  mes3,
						Priority: 1,
						Time:     time.Now().Unix(),
					},
					metav1.CreateOptions{})

				mes4 := fmt.Sprintf(domain.VMEditSuccessMessage, "tt-002")
				store.Logs().Create(context.Background(),
					&domain.LogEntry{
						Message:  mes4,
						Priority: 3,
						Time:     time.Now().Unix(),
					},
					metav1.CreateOptions{})

				store.Logs().Clear(context.Background(), &domain.LogDel{
					Start: -1,
					End:   1,
				}, metav1.DeleteOptions{})

				store.Logs().Clear(context.Background(), &domain.LogDel{
					Start: -1,
					End:   2,
				}, metav1.DeleteOptions{})

				store.Logs().Clear(context.Background(), &domain.LogDel{
					Start: -1,
					End:   3,
				}, metav1.DeleteOptions{})

				// 构造对比数据
				return &dto.LogResponseDto{
					Logs: []dto.LogEntryDto{
						{
							Event: domain.ClearLogMessage,
							Level: "info",
							Time:  time.Now().Format("2006/01/02 15:04:05"),
						},
					},
					Total: 1,
				}
			},
			after: func() {
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/log/", "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wantData := test.before()
			defer test.after()

			req, err := http.NewRequest(http.MethodGet, "/v1/log/clear", nil)
			assert.NoError(t, err)

			resp := httptest.NewRecorder()
			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			controller.Clear(ctx)
			gotData, err := controller.List(ctx)
			assert.NoError(t, err)
			assert.Equal(t, wantData, gotData)
		})
	}
}

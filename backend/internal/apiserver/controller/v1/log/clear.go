package log

import (
	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/golibrary/errors"
)

func (Self *LogController) Clear(c *gin.Context) (interface{}, error) {
	log.L(c).Info("VMs Logs Clean called")

	var opts metav1.ListOptions
	if err := c.ShouldBindQuery(&opts); err != nil {
		return false, errors.WithCode(code.ErrBind, err.Error())
	}

	err := Self.srv.Clear(c.Request.Context(), opts)
	if err != nil {
		return false, err
	}

	return true, nil
}

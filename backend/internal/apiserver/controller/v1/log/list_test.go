package log

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os/exec"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/core"
)

func TestList(t *testing.T) {
	controller := getTestLogController(t)
	store := getTestEtcdStore(t)

	tests := []struct {
		name   string
		before func() dto.LogResponseDto
		after  func()
	}{
		{
			name: "test",
			before: func() dto.LogResponseDto {
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/log/", "--prefix").Run()
				assert.NoError(t, err)

				mes1 := fmt.Sprintf(domain.VMEditMessage, "aa-001", "Virtual disks added")
				store.Logs().Create(context.Background(),
					&domain.LogEntry{
						Message:  mes1,
						Priority: 1,
						Time:     time.Now().Unix(),
					},
					metav1.CreateOptions{})

				mes2 := fmt.Sprintf(domain.VMEditMessage, "aa-002", "Virtual disks deleted")
				store.Logs().Create(context.Background(),
					&domain.LogEntry{
						Message:  mes2,
						Priority: 3,
						Time:     time.Now().Unix(),
					},
					metav1.CreateOptions{})

				mes3 := fmt.Sprintf(domain.VMEditFailedMessage, "tt-001")
				store.Logs().Create(context.Background(),
					&domain.LogEntry{
						Message:  mes3,
						Priority: 1,
						Time:     time.Now().Unix(),
					},
					metav1.CreateOptions{})

				mes4 := fmt.Sprintf(domain.VMEditSuccessMessage, "tt-002")
				store.Logs().Create(context.Background(),
					&domain.LogEntry{
						Message:  mes4,
						Priority: 3,
						Time:     time.Now().Unix(),
					},
					metav1.CreateOptions{})

				store.Logs().Clear(context.Background(), &domain.LogDel{
					Start: -1,
					End:   3,
				}, metav1.DeleteOptions{})

				// 构造对比数据
				var logList dto.LogResponseDto
				logList.Logs = make([]dto.LogEntryDto, 0)

				messageDto1 := fmt.Sprintf(domain.VMEditFailedMessage, "tt-001")
				logList.Logs = append(logList.Logs, dto.LogEntryDto{
					Event: messageDto1,
					Level: "error",
					Time:  time.Now().Format("2006/01/02 15:04:05"),
				})

				messageDto2 := fmt.Sprintf(domain.VMEditSuccessMessage, "tt-002")
				logList.Logs = append(logList.Logs, dto.LogEntryDto{
					Event: messageDto2,
					Level: "info",
					Time:  time.Now().Format("2006/01/02 15:04:05"),
				})
				logList.Total = 2
				return logList
			},
			after: func() {
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/log/", "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wantData := test.before()
			defer test.after()

			req, err := http.NewRequest(http.MethodGet, "/v1/log/list", nil)
			assert.NoError(t, err)

			resp := httptest.NewRecorder()
			ctx, _ := gin.CreateTestContext(resp)
			ctx.Request = req

			loglist, err := controller.List(ctx)
			assert.NoError(t, err)
			core.WriteResponse(ctx, nil, loglist)

			assert.Equal(t, http.StatusOK, resp.Code)

			var actualData dto.LogResponseDto
			err = json.Unmarshal(resp.Body.Bytes(), &actualData)
			assert.NoError(t, err)

			assert.ElementsMatch(t, wantData.Logs, actualData.Logs)
			assert.Equal(t, wantData.Total, actualData.Total)
		})
	}
}

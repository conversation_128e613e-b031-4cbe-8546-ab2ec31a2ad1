package log

import (
	"github.com/gin-gonic/gin"
	controller "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/controller/v1"
	service "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/golibrary/resp"
)

var _ controller.Controller = (*LogController)(nil)

type LogController struct {
	srv service.LogSrv
}

func NewLogController(store store.Factory) *LogController {
	return &LogController{srv: service.NewService(store, nil, nil).Logs()}
}

func (Self *LogController) RegisterRoutes(Log *gin.RouterGroup) {
	Log.GET("/list", resp.WrapResp(Self.List))     //获取主览页面集群相关信息, 包含警告,错误和健康状态
	Log.GET("/export", resp.WrapResp(Self.Export)) //导出日志
	Log.GET("/clear", resp.WrapResp(Self.Clear))   //清理日志
}

package log

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store/etcd"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

func TestMain(m *testing.M) {
	err := os.Setenv("ETCDCTL_API", "3")
	if err != nil {
		panic(err)
	}
	os.Exit(m.Run())
}

func getTestLogController(t *testing.T) *LogController {
	etcdStore := getTestEtcdStore(t)
	return NewLogController(etcdStore)
}

func getTestEtcdStore(t *testing.T) store.Factory {
	opts := options.NewEtcdOptions()
	opts.Endpoints = []string{"http://127.0.0.1:2379"}
	opts.Namespace = etcd.KeyPrefix
	etcdStore, err := etcd.GetEtcdFactoryOr(opts, nil)
	assert.NoError(t, err)
	return etcdStore
}

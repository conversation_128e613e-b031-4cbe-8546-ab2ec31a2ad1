// Package v1
/**
* @Project : terravirtualmachine
* @File    : network.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/20 17:03
**/

package v1

import (
	"context"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	uuid "github.com/satori/go.uuid"
	"github.com/vishvananda/netlink"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/golibrary/errors"
)

type NetworkSrv interface {
	Create(ctx context.Context, network *domain.Network, opts metav1.CreateOptions) error
	Update(ctx context.Context, network *domain.Network, opts metav1.UpdateOptions) error
	Delete(ctx context.Context, networkID string, opts metav1.DeleteOptions) error
	DeleteCollection(ctx context.Context, networkIDs []string, opts metav1.DeleteOptions) error
	Get(ctx context.Context, networkID string, opts metav1.GetOptions) (*domain.Network, error)
	List(ctx context.Context, opts metav1.ListOptions) (*domain.NetworkList, error)
	ListInterfaces(ctx context.Context, opts metav1.ListOptions) (*domain.InterfaceList, error)
	ListVNicsOnNetwork(ctx context.Context, networkId string, opts metav1.ListInterface) (*dto.VNicsOnNetoworkDto, error)
}

func (n *networkService) Create(ctx context.Context, network *domain.Network, opts metav1.CreateOptions) error {
	network.NetworkID = uuid.Must(uuid.NewV4()).String()
	network.CreatedAt = time.Now()
	network.UpdatedAt = time.Now()
	network.NumHosts = len(network.Interfaces)
	for _, ifaces := range network.Interfaces {
		ifaces.TotalCount = int64(len(ifaces.Items))
		network.NumInterfaces += len(ifaces.Items)
	}

	err := n.store.Networks().Create(ctx, network, metav1.CreateOptions{})
	if err != nil {
		log.L(ctx).Errorf("create network to etcd storage failed: %s", err.Error())

		return errors.WithCode(code.ErrEtcd, err.Error())
	}

	return nil
}

func (n *networkService) Update(ctx context.Context, network *domain.Network, opts metav1.UpdateOptions) error {
	if err := n.store.Networks().Update(ctx, network, opts); err != nil {
		return errors.WithCode(code.ErrEtcd, err.Error())
	}

	return nil
}

func (n *networkService) Delete(ctx context.Context, networkID string, opts metav1.DeleteOptions) error {
	network, err := n.store.Networks().Delete(ctx, networkID, opts)
	if network == nil {

	}
	if err != nil {
		log.L(ctx).Errorf("delete network from etcd storage failed: %s", err.Error())

		return errors.WithCode(code.ErrEtcd, err.Error())
	}

	// if network.Type == domain.PRIVATE {
	// 	if err := ovs.DeleteBridge(network.Interfaces[network.HostID].Items[0].InterfaceName); err != nil {
	// 		log.L(ctx).Errorf("delete ovs bridge failed: %s", err.Error())

	// 		return errors.WithCode(code.ErrOVS, err.Error())
	// 	}
	// }

	return nil
}

func (n *networkService) DeleteCollection(ctx context.Context, networkIDs []string, opts metav1.DeleteOptions) error {
	for _, networkID := range networkIDs {
		if err := n.Delete(ctx, networkID, opts); err != nil {
			return err
		}
	}
	return nil
}

func (n *networkService) Get(ctx context.Context, networkID string, opts metav1.GetOptions) (*domain.Network, error) {
	network, err := n.store.Networks().Get(ctx, networkID, metav1.GetOptions{})
	if err != nil {
		log.L(ctx).Errorf("get network from etcd storage failed: %s", err.Error())

		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	wg := sync.WaitGroup{}
	errChan := make(chan error, 1)
	finished := make(chan bool, 1)

	for _, ifaces := range network.Interfaces {
		wg.Add(1)
		go func(ifaces *domain.InterfaceList) {
			defer wg.Done()
			for _, iface := range ifaces.Items {
				realIface, err := n.store.Networks().GetInterface(ctx, iface.HostID, iface.InterfaceID, metav1.GetOptions{})
				if err != nil {
					log.L(ctx).Errorf("get interface from etcd storage failed: %s", err.Error())
					errChan <- errors.WithCode(code.ErrEtcd, err.Error())
					return
				}

				// 更新网络接口信息
				iface.HostName = realIface.HostName
				iface.InterfaceName = realIface.InterfaceName
				iface.IP = realIface.IP
				iface.Mask = realIface.Mask
				iface.SpeedMbs = realIface.SpeedMbs
				iface.Status = realIface.Status
			}
		}(ifaces)
	}

	go func() {
		wg.Wait()
		close(finished)
	}()

	select {
	case <-finished:
	case err := <-errChan:
		return nil, err
	}

	return network, nil
}

func (n *networkService) List(ctx context.Context, opts metav1.ListOptions) (*domain.NetworkList, error) {
	// 1、查询当前集群中的网络列表
	networks, err := n.store.Networks().List(ctx, metav1.ListOptions{})
	if err != nil {
		log.L(ctx).Errorf("list networks from etcd storage failed: %s", err.Error())

		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	// 2、查询当前集群中的虚拟网络接口列表
	vIfaces, err := n.store.Networks().ListVNics(ctx, metav1.ListOptions{})
	if err != nil {
		log.L(ctx).Errorf("list virtual interfaces from etcd storage failed: %s", err.Error())

		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	// 3、查询每个网络被多少虚拟机使用
	wg := sync.WaitGroup{}
	errChan := make(chan error, 1)
	finished := make(chan bool, 1)

	for _, network := range networks.Items {
		wg.Add(1)
		go func(network *domain.Network) {
			defer wg.Done()

			mapGuests := make(map[string]bool)
			for _, vIface := range vIfaces.Items {
				if vIface.NetworkID == network.NetworkID {
					mapGuests[vIface.GuestID] = true
				}
			}
			numGuests := len(mapGuests)
			// 更新网络被虚拟机使用的数量
			network.NumGuests = numGuests

		}(network)
	}

	go func() {
		wg.Wait()
		close(finished)
	}()

	select {
	case <-finished:
	case err := <-errChan:
		return nil, err
	}

	log.L(ctx).Debugf("get %d networks from etcd storage.", networks.TotalCount)

	return networks, nil
}

func (n *networkService) ListInterfaces(ctx context.Context, opts metav1.ListOptions) (*domain.InterfaceList, error) {

	// 查询当前集群中的主机列表
	hosts, err := n.store.Host().List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Errorf("[Network_Service] Backend storage failed to list hosts")
		return nil, err
	}

	var ifaceList domain.InterfaceList
	for hostId, host := range hosts.Items {
		hostIfaces, err := n.store.Host().ListIfaceOfHost(ctx, hostId, metav1.ListOptions{})
		if err != nil {
			log.Errorf("[Network_Service] Backend storage failed to list interfaces of host - %s", hostId)
			return nil, err
		}

		for IfaceId, Iface := range hostIfaces.Items {
			var iface domain.Interface
			iface.HostID = hostId
			iface.HostName = host.HostName
			iface.InterfaceName = Iface.Name
			iface.InterfaceID = IfaceId

			links, err := netlink.LinkList()
			if err != nil {
				log.Errorf("[Network_Service] interface retrieval from netlink failed - %s", err.Error())
				return nil, err
			}

			var ovsMac string
			//获取IP和子网掩码
			for _, link := range links {
				if link.Attrs().Name == Iface.Name {
					if link.Attrs().OperState.String() != "DOWN" {
						iface.Status = domain.CONNECTED
					} else {
						iface.Status = domain.DISCONNECTED
					}

					addrs, err := netlink.AddrList(link, netlink.FAMILY_V4)
					if err != nil {
						log.Warnf("[Network_Service] ip information retrieval failed - %s", Iface.Name)
					}

					for _, addr := range addrs {
						iface.IP = append(iface.IP, addr.IP.String())
						iface.Mask = append(iface.Mask, addr.Mask.String())
					}

					ovsMac = link.Attrs().HardwareAddr.String()

				}
			}

			for _, link := range links {
				if (link.Attrs().HardwareAddr.String()) == string(ovsMac) && link.Attrs().Name != Iface.Name {
					// 获取网口速度
					iface.SpeedMbs, err = getIfaceSpeed(string(link.Attrs().Name))
					if err != nil {
						log.Warnf("[Network_Service] speed retrieval failed - %s", Iface.Name)
					}
				}
			}

			//加入返回的接口列表
			ifaceList.TotalCount++
			ifaceList.Items = append(ifaceList.Items, &iface)
		}

	}

	log.Info("[Network_Service] interfaces list success")
	return &ifaceList, nil
}

func (n *networkService) ListVNicsOnNetwork(ctx context.Context, networkId string, opts metav1.ListInterface) (*dto.VNicsOnNetoworkDto, error) {
	// 2、查询当前集群中的虚拟网络接口列表
	vIfaces, err := n.store.Networks().ListVNics(ctx, metav1.ListOptions{})
	if err != nil {
		log.L(ctx).Errorf("[Network_Service] list virtual interfaces from etcd storage failed: %s", err.Error())

		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	network, err := n.store.Networks().Get(ctx, networkId, metav1.GetOptions{})
	if err != nil {
		log.L(ctx).Errorf("[Network_Service] network retrieval failed - Backend storage failed to get %s", networkId)

		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	//获取redis数据
	redisData, err := n.redis.CollMonitoring().List(ctx, metav1.ListOptions{})
	if err != nil {
		log.L(ctx).Errorf("[Network_Service] system uitilization retrieval failed - Backend redis failed to get system info")

		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	var res dto.VNicsOnNetoworkDto

	for _, vIface := range vIfaces.Items {
		if vIface.NetworkID == networkId {
			var vnic dto.VNicOnNetoworkDto
			vnic.GuestName = vIface.GuestName
			vnic.GuestID = vIface.GuestID
			vnic.MacAddress = vIface.MacAddress
			vnic.PreferSRIOV = vIface.PreferSRIOV
			vnic.NetworkName = network.NetworkName
			vnic.VNicID = vIface.VNicID

			//获取虚拟机信息
			vm, err := n.store.VMs().Get(ctx, vnic.GuestID, metav1.GetOptions{})
			if err != nil {

			}

			//获取虚拟网卡序列号
			for i := 0; i < len(vm.VNics); i++ {
				if vm.VNics[i] == vnic.VNicID {
					vnic.SeqNumber = i + 1
				}
			}
			var sumRX float64 = 0.0
			var sumTX float64 = 0.0
			//获取接收发送速率
			for _, ifacelist := range network.Interfaces {
				for _, iface := range ifacelist.Items {
					sumRX += redisData.Network.Interfaces[iface.InterfaceName].RX
					sumTX += redisData.Network.Interfaces[iface.InterfaceName].TX
				}
			}

			vnic.Rx = uint64(sumRX)
			vnic.Tx = uint64(sumTX)

			res.TotalCount++
			res.Items = append(res.Items, &vnic)
		}
	}

	return &res, nil
}

func getIfaceSpeed(nicName string) (int, error) {
	speedFile := filepath.Join("/sys/class/net", nicName, "speed")
	data, err := os.ReadFile(speedFile)
	if err != nil {
		return -1, err
	}
	speed, err := strconv.Atoi(strings.TrimSpace(string(data)))
	if err != nil {
		return -1, err
	}
	return speed, err
}

type networkService struct {
	store store.Factory
	redis store.RedisFactory
}

var _ NetworkSrv = (*networkService)(nil)

func newNetworks(srv *service) *networkService {
	return &networkService{store: srv.store}
}

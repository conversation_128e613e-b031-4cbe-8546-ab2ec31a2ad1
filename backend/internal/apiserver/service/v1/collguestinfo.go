package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/golibrary/errors"
)

type CollVMInfoSrv interface {
	List(ctx context.Context, opts metav1.ListOptions) (*dto.VmListDto, error)
}

func (c *collVMInfo) List(ctx context.Context, opts metav1.ListOptions) (*dto.VmListDto, error) {
	vmEtcd, err := c.etcdStore.VMs().List(ctx, opts)
	if err != nil {
		log.L(ctx).Warnf("[Guest_Service] vmEtcd get failed - for etcd %s", err.Error())
		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	var vmListDto dto.VmListDto
	vmListDto.TotalCount = vmEtcd.GetTotalCount()
	vmListDto.VMs = make([]*dto.VmDto, 0)

	for guestID, _ := range vmEtcd.Items {
		vm, err := c.redisStore.CollVMUsage().Get(ctx, guestID, metav1.GetOptions{})
		if err != nil {
			log.L(ctx).Warnf("[Guest_Service] vmusage get failed - for redis %s", err.Error())
			return nil, errors.WithCode(code.ErrRedisCmd, err.Error())
		}
		guest := &dto.VmDto{
			ObjectMeta: metav1.ObjectMeta{
				Name: guestID,
			},
			CPUUsage: vm.VCPUUsage,
			Desc:     "info",
			ID:       guestID,
			Name:     c.getGuestName(ctx, guestID),
			RAMUsage: vm.MemoryUsage,
			Status:   c.getGuestStatus(ctx, guestID), //etcd中虚拟机状态

			Disks:  make([]dto.VmDisk, 0, len(vm.Disk)),
			NICs:   make([]dto.VmNIC, 0, len(vm.Network)),
			VmType: c.getGuestType(ctx, vm),
		}

		// 填充磁盘数据
		for idx, disk := range vm.Disk {
			guest.Disks = append(guest.Disks, dto.VmDisk{
				Idx:         idx,                  // 磁盘索引
				VdiskID:     disk.VDiskID,         // 虚拟磁盘ID
				RThroughput: disk.ReadThroughput,  // 读取吞吐量（KB/s）
				WThroughput: disk.WriteThroughput, // 写入吞吐量（KB/s）
			})
		}

		// 填充网络接口数据
		for idx, nic := range vm.Network {
			guest.NICs = append(guest.NICs, dto.VmNIC{
				Device:    fmt.Sprintf("eth%d", idx),        // 虚拟网络设备名
				HostNetIf: c.findHostInterface(nic.Device),  // 主机侧接口名
				Rx:        nic.RxBytes,                      // 接收字节数
				Tx:        nic.TxBytes,                      // 发送字节数
				VnicID:    c.findVNICID(nic.Device),         // 虚拟网卡ID
				Status:    c.findNicStatus(ctx, nic.Device), // 状态转换
			})
		}
		vmListDto.VMs = append(vmListDto.VMs, guest)
	}

	// 按 Device 字段升序排序
	sort.Slice(vmListDto.VMs, func(i, j int) bool {
		return vmListDto.VMs[i].Name < vmListDto.VMs[j].Name
	})

	return &vmListDto, nil
}

func (c *collVMInfo) getGuestType(ctx context.Context, vm *domain.VMUsage) string {
	for _, net := range vm.Network {
		connectStr, _ := c.redisStore.CollNetwork().Get(ctx, net.Device, metav1.GetOptions{})
		var networkStatus domain.NetworkInterface
		if err := json.Unmarshal([]byte(connectStr), &networkStatus); err == nil {
			if networkStatus.Status == "disconnected" {
				return "warning"
			}
		}
	}
	return "healthy"
}

func (c *collVMInfo) getGuestName(ctx context.Context, guestID string) string {
	vm, err := c.etcdStore.VMs().Get(ctx, guestID, metav1.GetOptions{})
	if err != nil {
		log.L(ctx).Errorf("[Guest_Service] guestname get failed - %s", err.Error())
		return "unknown"
	}
	return vm.Name
}

func (c *collVMInfo) getGuestStatus(ctx context.Context, guestID string) string {
	vm, err := c.etcdStore.VMs().Get(ctx, guestID, metav1.GetOptions{})
	if err != nil {
		log.L(ctx).Errorf("[Guest_Service] gueststate get failed - %s", err.Error())
		return "shutdown"
	}
	return vm.State
}

func (c *collVMInfo) findHostInterface(device string) string {
	// 这里需要根据etcd中确定主机侧接口名
	return "eth0"
}

func (c *collVMInfo) findVNICID(device string) string {
	// 这里需要根据etcd网络配置来确定虚拟网卡ID
	return "vnic0"
}

func (c *collVMInfo) findNicStatus(ctx context.Context, device string) string {
	collNet, err := c.redisStore.CollNetwork().Get(ctx, device, metav1.GetOptions{})
	if err != nil {
		log.L(ctx).Debugf("[Guest_Service] redis network hgetfield failed - %v", err)
		return "unknown" // 默认状态
	}

	// 解析 JSON 数据
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(collNet), &result); err != nil {
		log.L(ctx).Errorf("[Guest_Service] json unmarshal failed - failed to unmarshal network status json %v", err)
		return "unknown" // 默认状态
	}

	// 提取 status 字段
	status, ok := result["status"].(string)
	if !ok {
		log.L(ctx).Warnf("[Guest_Service] network status field - missing or invalid %v", result)
		return "unknown" // 默认状态
	}

	return status
}

type collVMInfo struct {
	redisStore store.RedisFactory
	etcdStore  store.Factory
}

func newCollVMInfo(srv *collService) CollVMInfoSrv {
	return &collVMInfo{
		redisStore: srv.redisStore,
		etcdStore:  srv.etcdStore,
	}
}

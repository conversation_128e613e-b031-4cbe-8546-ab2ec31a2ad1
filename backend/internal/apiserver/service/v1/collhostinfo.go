package v1

import (
	"context"
	"fmt"
	"os"
	"sort"
	"strconv"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/golibrary/errors"
)

type CollHostInfoSrv interface {
	List(ctx context.Context, opts metav1.ListOptions) (*dto.HostListDto, error)
}

func (ch *collHostInfoService) List(ctx context.Context, opts metav1.ListOptions) (*dto.HostListDto, error) {
	sysUtil, err := ch.redisStore.CollMonitoring().List(ctx, opts)
	if err != nil {
		log.L(ctx).Errorf("[Host_Service] systemutilization get failed - %s", err.Error())

		return nil, errors.WithCode(code.ErrRedisCmd, err.Error())
	}
	colNet, err := ch.redisStore.CollNetwork().List(ctx, opts)
	if err != nil {
		log.L(ctx).Errorf("[Host_Service] network get failed - %s", err.Error())

		return nil, errors.WithCode(code.ErrRedisCmd, err.Error())
	}

	var hostListDto dto.HostListDto
	hostListDto.TotalCount = 1 //此处需要etcd中的host来确定主机数量
	hostListDto.Hosts = make([]*dto.HostDto, 0)

	host := &dto.HostDto{
		Desc: "Info",
		Name: func() string {
			hostname, err := os.Hostname()
			if err != nil {
				return "unknown"
			}
			return hostname
		}(),

		// 从 system_utilization 获取的指标
		CPUUsage: parsePercentage(sysUtil.CPU.TotalUsage),     // 解析百分比字符串为整数
		RAMUsage: parsePercentage(sysUtil.Memory.Utilization), // 同上

		// 网络接口数据处理
		NICs:     buildNICs(sysUtil.Network.Interfaces, colNet.Interfaces), // 合并两个数据源
		Status:   "running",
		HostType: "healthy",
	}

	hostListDto.Hosts = append(hostListDto.Hosts, host)
	log.L(ctx).Infof("[Host_Service] hostlistdto = %v", hostListDto)
	return &hostListDto, nil
}

// 辅助函数：解析百分比字符串
func parsePercentage(s string) float64 {
	f, err := strconv.ParseFloat(s, 64)
	if err != nil {
		return 0
	}
	formatted, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", f), 64)
	return formatted
}

// 辅助函数：构建网络接口列表
func buildNICs(utilNetwork map[string]*domain.InterfaceStats, netConfig map[string]*domain.NetworkInterface) []dto.HostNIC {
	nics := make([]dto.HostNIC, 0)

	// 只处理配置过的网络接口
	for device, config := range netConfig {
		nic := dto.HostNIC{
			Device: device,
			Type:   config.Type,
			Status: config.Status,
		}

		// 添加流量数据
		if util, ok := utilNetwork[device]; ok {
			nic.Rx = int(util.RX)
			nic.Tx = int(util.TX)
		}

		nics = append(nics, nic)
	}

	// 按 Device 字段升序排序
	sort.Slice(nics, func(i, j int) bool {
		return nics[i].Device < nics[j].Device
	})
	return nics
}

type collHostInfoService struct {
	redisStore store.RedisFactory
	etcdStore  store.Factory
}

var _ CollHostInfoSrv = (*collHostInfoService)(nil)

func newCollHostInfo(srv *collService) *collHostInfoService {
	return &collHostInfoService{
		redisStore: srv.redisStore,
		etcdStore:  srv.etcdStore,
	}
}

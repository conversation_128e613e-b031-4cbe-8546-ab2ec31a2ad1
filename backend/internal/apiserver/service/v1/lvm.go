package v1

import (
	"context"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.local/TerraMaster/tos-modules/service/disk/lvm"
	"gitlab.local/golibrary/parselvm"

	"gitlab.local/golibrary/utils"
)

type PhysicalVolume struct {
	ID      string `json:"id"`
	Device  string `json:"device"`
	DevSize int    `json:"dev_size"` // 设备扇区大小
	PeStart int    `json:"pe_start"`
	PeCount int    `json:"pe_count"` // 设备PE总数量
}

type LogicalVolume struct {
	ID           string    `json:"id"`
	Tags         []string  `json:"tags"`
	CreationTime int       `json:"creation_time"` // 创建时间戳
	CreationHost string    `json:"creation_host"` // 创建主机
	SegmentCount int       `json:"segment_count"` // 段数量
	Segments     []Segment `json:"segment"`       // 段是逻辑卷的一个特定区域，它定义了卷的存储特性和布局
}

type Segment struct {
	StartExtent int    `json:"start_extent"`
	ExtentCount int    `json:"extent_count"` // 段中包含的扇区数量
	Type        string `json:"type"`         // 段的类型,目前用到的有striped、cache; striped表示该段是普通逻辑卷，cache表示该段是缓存逻辑卷
}

type VolumeGroup struct {
	ID              string                    `json:"id"`
	Format          string                    `json:"format"`
	Tags            []string                  `json:"tags"`             // 我们的标签，即uuid
	ExtentSize      int                       `json:"extent_size"`      // 每个pe块大小 默认8192
	PhysicalVolumes map[string]PhysicalVolume `json:"physical_volumes"` // vg中的pv
	LogicalVolumes  map[string]LogicalVolume  `json:"logical_volumes"`  // vg中的lv
}

const backUpDir = "/etc/lvm/backup"

const TosRaidCfg = "/etc/mdadm.conf"

var instance *Vgs

//var once sync.Once

type CachePoolGetter interface {
	Get(cmd *exec.Cmd) string
	Remove(key string)
	RemoveAll()
}

type VgsGetter interface {
	List() (map[string]lvm.VgInfo, error)
	OnePool(name string) (*lvm.VgInfo, error)
	GetLvInfo(blk string) *lvm.LvInfo
	SetLvTag(blk string, tag string) error
	BlockDevFromVg(vg string) ([]lvm.LvAttr, error)
	GetPvsFromVg(vg string) []string
	GetVgNameFromPvs(name string) bool
	GetVgTagsFromPvs(name string) string
	GetCachePool() lvm.CachePoolGetter
}

type Vgs struct {
	lock      sync.Mutex
	CachePool CachePool
}

type Cache struct {
	Cmd   string
	Value string
	Timer time.Time
}

type CachePool struct {
	List map[string]Cache
	Lock sync.Mutex
}

func (c *CachePool) Get(cmd *exec.Cmd) string {
	c.Lock.Lock()
	defer c.Lock.Unlock()
	id := cmd.String()
	cache, ok := c.List[id]
	if !ok || time.Now().After(cache.Timer) {
		value, err := cmd.Output()
		if err != nil {
			return ""
		}
		cache = Cache{
			Cmd:   cmd.String(),
			Value: string(value),
			Timer: time.Now().Add(60 * time.Second),
		}
		c.List[id] = cache
	}
	return cache.Value
}

func (c *CachePool) Remove(key string) {
	c.Lock.Lock()
	defer c.Lock.Unlock()
	delete(c.List, key)
}

func (c *CachePool) RemoveAll() {
	c.Lock.Lock()
	defer c.Lock.Unlock()
	c.List = make(map[string]Cache, 0)
}

func GetInstance() *Vgs {
	once.Do(func() {
		instance = &Vgs{
			CachePool: CachePool{
				List: make(map[string]Cache),
			},
		}
	})
	return instance
}

func (v *Vgs) GetCachePool() lvm.CachePoolGetter {
	return &v.CachePool
}

// List 获取所有存储池的信息
// 保留接口,更改接口的实现方式
func (v *Vgs) List() (map[string]lvm.VgInfo, error) {
	return v.getAllVgInfo()
}

// 获取所有卷组信息
func (v *Vgs) getAllVgInfo() (map[string]lvm.VgInfo, error) {
	result := make(map[string]lvm.VgInfo)
	vgsInfo := parselvm.NewVgsInstance()
	for vgName, _ := range vgsInfo {
		vgInfo, err := v.getOnePool(vgName)
		if err != nil {
			continue
		}
		result[vgName] = *vgInfo
	}
	return result, nil
}

// OnePool 获取一个存储池的信息
// 保留接口,更改接口的实现方式
func (v *Vgs) OnePool(name string) (*lvm.VgInfo, error) {
	return v.getOnePool(name)
}

// GetOnePool 从backup中获取blk的信息  vgName = vg0
func (v *Vgs) getOnePool(vgName string) (*lvm.VgInfo, error) {
	//v.lock.Lock()
	//defer v.lock.Unlock()
	result := &lvm.VgInfo{}
	if vgName == "" {
		return nil, errors.New("invalid vg name")
	}

	//vgs := NewVgsInstance()
	vgs := parselvm.NewVgsInstance()
	vgInfo, ok := vgs[vgName]
	if !ok {
		return nil, errors.New("invalid vg name")
	}
	vgPeCount := 0
	lvUsePeCount := 0
	lvCount := 0
	pvName := []string{}
	for _, pv := range vgInfo.PhysicalVolumes {
		if v.IsFlashCachePv(pv.Device) { // 这里需优化、
			continue
		}
		vgPeCount += pv.PeCount
		pvName = append(pvName, pv.Device)
	}
	for _, lv := range vgInfo.LogicalVolumes {
		lvCount += 1
		for _, segment := range lv.Segments {
			lvUsePeCount += segment.ExtentCount // 还有就是lv的大小，要拿创建缓存的情况对比一下，看会不会多算lv
		}
	}
	vgSize := float64(vgPeCount * vgInfo.ExtentSize / 2)
	vgFree := float64((vgPeCount - lvUsePeCount) * vgInfo.ExtentSize / 2)
	vgTags := ""
	if len(vgInfo.Tags) > 0 {
		vgTags = vgInfo.Tags[len(vgInfo.Tags)-1]
	} else {
		vgTags = vgInfo.ID
	}
	result = &lvm.VgInfo{
		VgName:  vgName,
		VgSize:  vgSize,
		VgFree:  vgFree,
		LvCount: lvCount,
		PvName:  pvName,
		VgTags:  vgTags,
	}
	return result, nil
}

// GetLvInfo
// return nil if null
// 保留接口,更改接口的实现方式
func (v *Vgs) GetLvInfo(blk string) *lvm.LvInfo {
	return v.GetLvInfoFromBackup(blk)
}

// GetLvInfoFromBackup
// return nil if null
func (v *Vgs) GetLvInfoFromBackup(blk string) *lvm.LvInfo {
	//v.lock.Lock()
	//defer v.lock.Unlock()
	result := &lvm.LvInfo{}
	vgName, lvName, err := ParseBlkPath(blk)
	if err != nil {
		return nil
	}
	vgs := parselvm.NewVgsInstance()
	lvInfo, ok := vgs[vgName].LogicalVolumes[lvName]
	if !ok {
		return nil
	}
	lvExtentSum := 0
	for _, v := range lvInfo.Segments {
		lvExtentSum += v.ExtentCount
	}
	lvSize := float64(lvExtentSum * 8192)
	lvTag := ""
	if len(lvInfo.Tags) > 0 {
		lvTag = lvInfo.Tags[len(lvInfo.Tags)-1]
	} else {
		lvTag = lvInfo.ID
	}
	result = &lvm.LvInfo{
		LvName: lvName,
		LvAttr: lvm.LvAttr{LvDmPath: blk},
		LvUUID: lvInfo.ID,
		LvSize: lvSize,
		LvTime: time.Unix(int64(lvInfo.CreationTime), 0),
		LvTags: lvInfo.Tags,
		LvTag:  lvTag,
		VgName: vgName,
	}
	return result
}

// blkpath : /dev/mapper/vg0-lv0  根据路径解析vg、lv名称
func ParseBlkPath(blkPath string) (vgName string, lvName string, err error) {
	blkName := filepath.Base(blkPath)
	re := regexp.MustCompile(`^(\w+)-(\w+)$`)
	matches := re.FindStringSubmatch(blkName)
	if matches == nil || len(matches) != 3 {
		err = fmt.Errorf("Invalid blkPath format")
		return "", "", err
	}
	return matches[1], matches[2], nil
}

// SetLvTag setting lvm Tag
func (v *Vgs) SetLvTag(blk string, tag string) error {
	lvm := v.GetLvInfoFromBackup(blk)
	if lvm == nil {
		return fmt.Errorf("invalid block device %s", blk)
	}
	for _, item := range lvm.LvTags {
		_, _ = utils.ShellExec("lvchange", "--deltag", item, blk)
	}
	_, err := utils.ShellExec("lvchange", "--addtag", tag, blk)
	return err
}

func (v *Vgs) BlockDevFromVg(vg string) ([]lvm.LvAttr, error) {
	result := make([]lvm.LvAttr, 0)
	v.lock.Lock()
	defer v.lock.Unlock()
	cmd := v.buildCmd("vgs", "-o", "lv_dm_path", vg)
	output := v.CachePool.Get(cmd)
	lines := regexp.MustCompile(`[\n\r]+`).Split(output, -1)
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		fp := strings.Split(line, "|")
		result = append(result, lvm.LvAttr{
			LvDmPath: fp[0],
		})
	}
	return result, nil
}

func (v *Vgs) buildCmd(cmd string, args ...string) *exec.Cmd {
	defaultArgs := []string{"--readonly", "--noheadings", "--unit=k", "--nosuffix", "--separator=|"}
	if len(args) == 0 {
		args = defaultArgs
	} else {
		args = append(args, defaultArgs...)
	}
	var ctx context.Context
	ctx, _ = context.WithTimeout(context.Background(), 60*time.Second)
	return exec.CommandContext(ctx, cmd, args...)
}

// lv_dm_path
// lv_name,lv_dm_path,lv_uuid,lv_size,lv_time,vg_name,lv_tags
func (v *Vgs) stringToLv(input string) *lvm.LvInfo {
	line := strings.TrimSpace(input)
	if line == "" {
		return nil
	}
	col := strings.Split(line, "|")
	if len(col) < 7 {
		return nil
	}
	lvSize, _ := strconv.ParseFloat(col[3], 64)
	//2022-06-02 19:31:40 +0800
	lvTime, _ := time.Parse("2006-01-02 15:04:05 07:00", col[4])
	tags := make([]string, 0)
	var tag string
	if col[6] = strings.TrimSpace(col[6]); col[6] != "" {
		tags = strings.Split(col[6], ",")
		tag = tags[len(tags)-1]
	}
	result := lvm.LvInfo{
		LvName: strings.TrimSpace(col[0]),
		LvAttr: lvm.LvAttr{LvDmPath: strings.TrimSpace(col[1])},
		LvUUID: strings.TrimSpace(col[2]),
		LvSize: lvSize,
		LvTime: lvTime,
		LvTags: tags,
		LvTag:  tag,
		VgName: strings.TrimSpace(col[5]),
	}
	return &result
}

// vg_name,vg_size,vg_free,lv_count,pv_name,vg_tags // # vgs -o vg_name,vg_size,vg_free,lv_count,pv_name,vg_tags --readonly --noheadings --unit=k --nosuffix   vg0 966332416.00     0   1 /dev/md0   00000001-**************-6cbfb50207ab-traid
func (v *Vgs) stringToPool(input string) map[string]lvm.VgInfo {
	result := make(map[string]lvm.VgInfo)
	lines := regexp.MustCompile(`[\n\r]+`).Split(input, -1)
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		fp := strings.Split(line, "|")
		if len(fp) < 6 {
			continue
		}
		// 排除缓存阵列
		if fp[4] != "" && v.IsFlashCachePv(fp[4]) {
			continue
		}
		pool, ok := result[fp[0]]
		if ok {
			pool.PvName = append(pool.PvName, fp[4])
		} else {
			pool = lvm.VgInfo{}
			pool.VgName = fp[0]
			pool.VgSize, _ = strconv.ParseFloat(fp[1], 64)
			pool.VgFree, _ = strconv.ParseFloat(fp[2], 64)
			pool.LvCount, _ = strconv.Atoi(fp[3])
			pool.PvName = []string{fp[4]}
			pool.VgTags = fp[5]
		}
		result[fp[0]] = pool
	}
	return result
}

// IsFlashCachePv 判断该pv是否是缓存pv
func (v *Vgs) IsFlashCachePv(pvName string) bool {
	file, err := os.ReadFile(TosRaidCfg)
	if err != nil {
		_, _ = utils.ShellExec("mdadm -Ds > /etc/mdadm.conf")
		return false
	}
	for _, line := range strings.Split(string(file), "\n") {
		if strings.Contains(line, pvName) && strings.Contains(line, "UTOSFC-X86-S64") {
			return true
		}
	}
	return false
}

func (v *Vgs) GetPvsFromVg(vg string) []string {
	result := []string{}
	vgsInfo := parselvm.NewVgsInstance()
	vgInfo := vgsInfo[vg]
	for _, pv := range vgInfo.PhysicalVolumes {
		result = append(result, pv.Device)
	}
	return result
}

// GetVgNameFromPvs 判断此阵列的Vgs是否真实存在
// name --raid name  as:/dev/md0
func (v *Vgs) GetVgNameFromPvs(name string) bool {
	cmd := v.buildCmd("pvs", "-o", "vg_name", name)
	output := v.CachePool.Get(cmd)
	vgs := strings.Split(output, "\n")
	if strings.TrimSpace(vgs[1]) == "" {
		v.CachePool.Remove(cmd.String())
		_, _ = utils.ShellExec("mdadm", "-S", name)
		return false
	}
	return true
}

func (v *Vgs) GetVgTagsFromPvs(name string) string {
	cmd := v.buildCmd("pvs", "-o", "vg_tags", name)
	output := v.CachePool.Get(cmd)
	return strings.TrimSpace(output)
}

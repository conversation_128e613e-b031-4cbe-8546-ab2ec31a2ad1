package v1

import (
	"context"
	"log"
	"os"
	"strings"

	"github.com/digitalocean/go-openvswitch/ovs"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	uuid "github.com/satori/go.uuid"
	"github.com/vishvananda/netlink"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store/etcd"
)

type hostNetIface struct {
}

type HostService struct {
	hostNetIface
	store  store.Factory
	hostId string
}

// host 自动初始化
func init() {
	srv := NewService(etcd.BackendEtcdStore, nil, nil)
	service := newHostService(srv.(*service))

	if err := service.hostInit(); err != nil {
		service.store.Host().Delete(context.Background(), "", metav1.DeleteOptions{})
		log.Fatalf("[Host_Service] host interface initing failed - %s", err.Error())
	}

	if err := service.ifaceInit(service.hostId, service.store); err != nil {
		service.store.Host().Delete(context.Background(), service.hostId, metav1.DeleteOptions{})
		log.Fatalf("[Host_Service] host interface initing failed - %s", err.Error())
	}

}

func newHostService(srv *service) *HostService {
	service := HostService{
		store: srv.store,
	}
	return &service
}

func (h *hostNetIface) ifaceInit(hostid string, store store.Factory) error {
	client := ovs.New()
	bridges, err := client.VSwitch.ListBridges()
	if err != nil {
		return err
	}

	for _, bridge := range bridges {
		hostIface := domain.HostNetIface{}
		hostIface.Name = bridge
		// 默认不支持
		hostIface.EnableSriov = false
		hostIface.MaxAvailableVfNum = 0

		link, err := netlink.LinkByName(bridge)
		if err != nil {
			return err
		}
		mac := strings.ReplaceAll(link.Attrs().HardwareAddr.String(), ":", "")
		ifaceId := strings.Join([]string{domain.HostIfaceIdPrefix, mac}, "-")
		if err = store.Host().IfaceCreate(context.Background(), hostid, ifaceId, &hostIface, metav1.CreateOptions{}); err != nil {
			return err
		}
	}

	return nil
}

func (h *HostService) hostInit() error {
	host := domain.Host{}
	host.HostID = uuid.Must(uuid.NewV4()).String()
	if hostName, err := os.Hostname(); err != nil {
		return err
	} else {
		host.HostName = hostName
	}

	lists, err := h.store.Host().List(context.Background(), metav1.ListOptions{})
	if err != nil {
		return err
	}

	if len(lists.Items) != 0 {
		return nil
	}

	if err := h.store.Host().Create(context.Background(), host.HostID, &host, metav1.CreateOptions{}); err != nil {
		return err
	}

	h.hostId = host.HostID
	return nil
}

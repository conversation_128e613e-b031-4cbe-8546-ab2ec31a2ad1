package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"strconv"
	"strings"
	"sync"
	"time"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	uuid "github.com/satori/go.uuid"
	"github.com/shirou/gopsutil/disk"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/mem"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg"
	"gitlab.local/golibrary/utils"
)

type VMSrv interface {
	GetInstance(ctx context.Context, id string, resp chan dto.InstanceResult)
	CreateInstance(ctx context.Context, ins dto.VMInstance, resp chan dto.InstanceResult)
	ListResource(ctx context.Context, resp chan dto.InstanceResult)
	ListInstance(ctx context.Context, resp chan dto.InstanceResult)
	// Delete(ctx context.Context, id string) error
	StartInstance(ctx context.Context, guestID string, resp chan error)
	ForceShutDownInstance(ctx context.Context, guestID string, resp chan error)
	ShutDownInstance(ctx context.Context, guestID string, resp chan error)
	SoftRebootInstance(ctx context.Context, guestID string, resp chan error)
	PauseInstance(ctx context.Context, guestID string, resp chan error)
	UnpauseInstance(ctx context.Context, guestID string, resp chan error)
	VNCLinkUrl(ctx context.Context, guestID string, resp chan dto.InstanceResult)
}

type RepositoryPoolService interface {
	CreateConfig(ctx context.Context, guestId string, repoId string, vguestCfg string, vdiskCfg string, vniCfg string) error
}

type VDiskService interface {
	Create(ctx context.Context, vdisk *dto.VMInstanceRequestEdit, opts metav1.CreateOptions) ([]string, string, error)
	Update(ctx context.Context, vdisk *dto.VMInstanceRequestEdit, opts metav1.UpdateOptions) (string, error)
	Sort(ctx context.Context, vdisk *dto.VMInstanceRequestEdit, opts metav1.UpdateOptions) error
	Delete(ctx context.Context, vdisk *dto.VMInstanceRequestEdit, opts metav1.DeleteOptions) error
	Recover(ctx context.Context, vdisk *domain.VDiskList, recoverUUID string, opts metav1.UpdateOptions) (string, error)
	Running(ctx context.Context, vdiskIDList []string) error
	Stop(ctx context.Context, vdiskIDList []string) error
	List(ctx context.Context, opts metav1.ListOptions) (*domain.VDiskList, error)
}

type VNicService interface {
	Create(ctx context.Context, vnic *dto.VMInstanceRequestEdit, opts metav1.CreateOptions) ([]string, string, error)
	Update(ctx context.Context, vnic *dto.VMInstanceRequestEdit, opts metav1.UpdateOptions) error
	Delete(ctx context.Context, vnic *dto.VMInstanceRequestEdit, opts metav1.DeleteOptions) error
	List(ctx context.Context, guestId string, opts metav1.ListOptions) (*domain.VNicList, error)
	Recover(ctx context.Context, vnicList *domain.VNicList, repositoryUUID string, opts metav1.UpdateOptions) error
	Run(ctx context.Context, vnics []string) ([]string, error)
	Stop(ctx context.Context, vnics []string) error
}

type InstanceCommandType int

const (
	InsCmdCreate = iota
	InsCmdGet
	InsCmdList
	InsCmdListResource
	InsCmdStart
	InsCmdForceShutDown
	InsCmdShutDown
	InsCmdSoftReboot
	InsCmdPause
	InsCmdUnpause
	InsCmdVNCLink
	InsCmdInvalid
)

var instanceCommandNames = []string{
	"Create",
	"Get",
	"List",
	"ListResource",
	"Start",
	"ForceShutDown",
	"ShutDown",
	"SoftReboot",
	"Pause",
	"VncLink",
	"Unpause",
}

func (c InstanceCommandType) toString() string {
	if c >= InsCmdInvalid {
		return "invalid"
	}
	return instanceCommandNames[c]
}

type instanceCommand struct {
	Ctx        context.Context
	Type       InstanceCommandType
	InstanceId string
	Ins        dto.VMInstance
	RepoId     string

	Vdisks []dto.VDiskInstance
	VNics  []dto.VNicInstance

	ErrorChan  chan error
	ResultChan chan dto.InstanceResult
}

const (
	SyncInterval    = 2 * time.Second
	ShutDownWait    = 2 * time.Second
	ShutDownOutTime = 5 * time.Minute
)

type vmInstanceService struct {
	libvirtStore store.LibvirtFactory
	store        store.Factory
	redis        store.RedisFactory
	vdiskService VDiskService
	vnicService  VNicService
	repoService  RepositoryPoolService

	powerMut       sync.Mutex
	vmPowerManager map[string]*pkg.SimpleRunner

	defaultTemplate domain.GuestHardwareTemplate
	routineRunner   *pkg.SimpleRunner

	commands chan instanceCommand

	vncCfg domain.VNCTokenConfig
}

//var _ VMSrv = (*vmInstanceService)(nil)

func newVmInstanceService(srv *service) (service *vmInstanceService) {
	if InsCmdInvalid != len(instanceCommandNames) {
		log.Infof("insufficient command names %d/%d", len(instanceCommandNames), InsCmdInvalid)
		return nil
	}

	const (
		DefaultQueueSize = 1 << 10
	)

	service = &vmInstanceService{}
	service.defaultTemplate = domain.GuestHardwareTemplate{
		OperatingSystem: domain.TemplateOperatingSystem(domain.TemplateOperatingSystemLinux).ToString(),
		Disk:            domain.TemplateDiskDriver(domain.TemplateDiskDriverVirtio).ToString(),
		Display:         domain.TemplateDisplayDriver(domain.TemplateDisplayDriverVGA).ToString(),
		Control:         domain.TemplateRemoteControl(domain.TemplateRemoteControlVNC).ToString(),
		USB:             domain.TemplateUSBModel(domain.TemplateUSBModelPIIX3).ToString(),
		Tablet:          domain.TemplateTabletModel(domain.TemplateTabletModelUSB).ToString(),
	}

	service.libvirtStore = srv.libvirtStore
	service.commands = make(chan instanceCommand, DefaultQueueSize)
	service.routineRunner = pkg.CreateSimpleRunner(service.routine)
	service.store = srv.store
	service.redis = srv.redis
	service.vdiskService = newVDisk(srv)
	service.vnicService = newVNicService(srv)
	service.repoService = newRepositoryPool(srv)
	service.vncCfg = domain.NewVNCTokenConfig(os.Getenv("TARGET_CFG"), os.Getenv("NOVNC_LISTEN_PORT"), os.Getenv("NOVNC_URI"))
	service.vmPowerManager = make(map[string]*pkg.SimpleRunner)
	service.routineRunner.Start()
	return service
}

func (s *vmInstanceService) routine(c pkg.RoutineController) {
	var syncTicker = time.NewTicker(SyncInterval)
	for !c.IsStopping() {
		select {
		case <-c.GetNotifyChannel():
			c.SetStopping()
		case cmd := <-s.commands:
			s.handleCommand(cmd)
		case <-syncTicker.C:
			// TODO
		}
	}
	c.NotifyExit()
	log.Infof("<instance> stopped")
}

func (s *vmInstanceService) GetInstance(ctx context.Context, id string, resp chan dto.InstanceResult) {
	cmd := instanceCommand{Ctx: ctx, Type: InsCmdGet, InstanceId: id, ResultChan: resp}
	s.commands <- cmd
}

func (s *vmInstanceService) ListInstance(ctx context.Context, resp chan dto.InstanceResult) {
	cmd := instanceCommand{Ctx: ctx, Type: InsCmdList, ResultChan: resp}
	s.commands <- cmd
}

func (s *vmInstanceService) CreateInstance(ctx context.Context, ins dto.VMInstance, resp chan dto.InstanceResult) {
	cmd := instanceCommand{Ctx: ctx, Type: InsCmdCreate, Ins: ins, ResultChan: resp}
	s.commands <- cmd
}

func (s *vmInstanceService) ListResource(ctx context.Context, resp chan dto.InstanceResult) {
	cmd := instanceCommand{Ctx: ctx, Type: InsCmdListResource, ResultChan: resp}
	s.commands <- cmd
}

func (s *vmInstanceService) StartInstance(ctx context.Context, guestID string, resp chan error) {
	cmd := instanceCommand{Ctx: ctx, Type: InsCmdStart, InstanceId: guestID, ErrorChan: resp}
	s.commands <- cmd
}

func (s *vmInstanceService) ForceShutDownInstance(ctx context.Context, guestID string, resp chan error) {
	cmd := instanceCommand{Ctx: ctx, Type: InsCmdForceShutDown, InstanceId: guestID, ErrorChan: resp}
	s.commands <- cmd
}

func (s *vmInstanceService) ShutDownInstance(ctx context.Context, guestID string, resp chan error) {
	cmd := instanceCommand{Ctx: ctx, Type: InsCmdShutDown, InstanceId: guestID, ErrorChan: resp}
	s.commands <- cmd
}

func (s *vmInstanceService) SoftRebootInstance(ctx context.Context, guestID string, resp chan error) {
	cmd := instanceCommand{Ctx: ctx, Type: InsCmdSoftReboot, InstanceId: guestID, ErrorChan: resp}
	s.commands <- cmd
}

func (s *vmInstanceService) PauseInstance(ctx context.Context, guestID string, resp chan error) {
	cmd := instanceCommand{Ctx: ctx, Type: InsCmdPause, InstanceId: guestID, ErrorChan: resp}
	s.commands <- cmd
}

func (s *vmInstanceService) UnpauseInstance(ctx context.Context, guestID string, resp chan error) {
	cmd := instanceCommand{Ctx: ctx, Type: InsCmdUnpause, InstanceId: guestID, ErrorChan: resp}
	s.commands <- cmd
}

func (s *vmInstanceService) VNCLinkUrl(ctx context.Context, guestID string, resp chan dto.InstanceResult) {
	cmd := instanceCommand{Ctx: ctx, Type: InsCmdVNCLink, InstanceId: guestID, ResultChan: resp}
	s.commands <- cmd
}

func (s *vmInstanceService) handleCommand(cmd instanceCommand) {
	var err error
	switch cmd.Type {
	case InsCmdCreate:
		err = s.handleCreateInstance(cmd.Ctx, cmd.Ins, cmd.ResultChan)
	case InsCmdGet:
		err = s.handleGetInstance(cmd.Ctx, cmd.InstanceId, cmd.ResultChan)
	case InsCmdList:
		err = s.handleListInstance(cmd.Ctx, cmd.ResultChan)
	case InsCmdListResource:
		err = s.handleListResource(cmd.Ctx, cmd.ResultChan)
	case InsCmdStart:
		err = s.handleStartInstance(cmd.Ctx, cmd.InstanceId, cmd.ErrorChan)
	case InsCmdForceShutDown:
		err = s.handleForceShutDownInstance(cmd.Ctx, cmd.InstanceId, cmd.ErrorChan)
	case InsCmdShutDown:
		err = s.handleShutDownInstance(cmd.Ctx, cmd.InstanceId, cmd.ErrorChan)
	case InsCmdSoftReboot:
		err = s.handleSoftRebootInstance(cmd.Ctx, cmd.InstanceId, cmd.ErrorChan)
	case InsCmdPause:
		err = s.handlePauseInstance(cmd.Ctx, cmd.InstanceId, cmd.ErrorChan)
	case InsCmdUnpause:
		err = s.handleUnpauseInstance(cmd.Ctx, cmd.InstanceId, cmd.ErrorChan)
	case InsCmdVNCLink:
		err = s.handleVNCLinkUrl(cmd.Ctx, cmd.InstanceId, cmd.ResultChan)
	default:
		log.Infof("<instance> unsupported command type %d", cmd.Type)
	}
	if err != nil {
		log.Infof("<instance> handle command %s fail: %s", cmd.Type.toString(), err.Error())
	}
}

func (s *vmInstanceService) backendVMInstanceToWebVM(vm *domain.VMInstance, GuestId string) (dto.VMInstance, error) {
	var ins dto.VMInstance

	memStat, _ := mem.VirtualMemory()
	ins.Autorun = vm.Autorun
	ins.BootFrom = vm.BootFrom
	ins.CPUPassthru = vm.CPUPassthru
	ins.CPUPinNum = vm.CPUPinNum
	ins.CPUWeight = vm.CPUWeight
	ins.Desc = vm.Desc
	ins.GuestId = GuestId
	ins.HostRamSize = int64(memStat.Total)
	ins.HypervEnlighten = vm.HypervEnlighten
	ins.IsGeneralVM = vm.IsGeneralVM
	ins.ISOImages = vm.ISOImages
	ins.KBLayout = vm.KBLayout
	ins.Name = vm.Name
	ins.RepoId = vm.RepositoryID
	ins.RepoName = vm.RepoName
	ins.USBDeviceName = vm.USBDeviceName
	ins.USBs = vm.USBs
	ins.UseOVMF = vm.UseOVMF
	ins.VCPUNum = vm.VCPUNum
	ins.VCPUUsage = 0
	ins.VideoCard = vm.VideoCard
	ins.VRAMSize = vm.VRAMSize
	ins.SnapNum = 0 // TODO
	ins.Status = vm.State

	if lists, err := s.store.Host().List(context.Background(), metav1.ListOptions{}); err != nil {
		log.Warnf("[VMs_Service] VMs listing failed - Backend storage failed to list hosts")
	} else {
		// TODO, 集群需要修改
		for _, list := range lists.Items {
			ins.HostId = list.HostID
			ins.HostName = list.HostName
			break
		}
	}

	if ins.Status != domain.PowerStateRuning {
		return ins, nil
	}

	if vmUsage, err := s.redis.CollVMUsage().Get(context.Background(), GuestId, metav1.GetOptions{}); err != nil {
		log.Warnf("[VMs_Service] VMs listing failed - Backend storage failed to get usage")
	} else {
		for _, disk := range vmUsage.Disk {
			ins.TotalIOPS += disk.TotalIOPS
			ins.TotalThroughput += disk.TotalThroughput
			ins.TotalIOLatency += disk.TotalIOLatency
		}

		for _, vnic := range vmUsage.Network {
			ins.TotalRxBytes += vnic.RxBytes
			ins.TotalTxBytes += vnic.TxBytes
		}

		ins.CpuUsage = vmUsage.VCPUUsage
		ins.MemUsage = vmUsage.MemoryUsage
	}

	return ins, nil
}

func (s *vmInstanceService) handleGetInstance(ctx context.Context, GuestId string, resp chan dto.InstanceResult) error {
	panic("no finish")
	// vm, err := s.store.VMs().Get(ctx, GuestId, metav1.GetOptions{})
	// if err != nil {
	// 	err := fmt.Errorf("invalid instance '%s'", GuestId)
	// 	resp <- dto.InstanceResult{Error: err}
	// 	return err
	// }

	// retIns := make([]dto.VMInstance, 0, 1)
	// ins, err := s.getInstancebyGuestId(vm, GuestId)
	// if err != nil {
	// 	err := fmt.Errorf("vdisk redis list err: '%s'", err.Error())
	// 	return ins, err
	// }

	// repos, err := s.store.RepositoryPool().List(ctx, metav1.ListOptions{})
	// if err != nil {
	// 	err := fmt.Errorf("repository pool list err: '%s'", err.Error())
	// 	return ins, err
	// }

	// usage, infoOk := usages.Items[GuestId]
	// for _, VDiskId := range vm.VDisks {
	// 	disk, ok := vdisks.Items[VDiskId]

	// 	if !ok || !infoOk {
	// 		err := fmt.Errorf("invalid instance '%s'", VDiskId)
	// 		return ins, err
	// 	}

	// 	var insVDisk dto.VDiskInstance
	// 	insVDisk.IsDummy = disk.IsDummy
	// 	insVDisk.IsMetaDisk = disk.IsMetaDisk
	// 	insVDisk.RepoName = repos.Items[disk.RepositoryID].Name
	// 	insVDisk.Size = disk.Size
	// 	insVDisk.Status = disk.GetVDiskStatus()
	// 	insVDisk.Unmap = disk.Unmap
	// 	insVDisk.Used = disk.UsedSize()
	// 	insVDisk.VDiskMode = disk.VDiskMode

	// 	// 虚拟磁盘速度相关参数
	// 	for _, diskUsage := range usage.Disk {
	// 		if diskUsage.VDiskID == VDiskId {
	// 			insVDisk.ReadIops = int(diskUsage.ReadIOPS)
	// 			insVDisk.ReadAvgLatency = diskUsage.ReadAvgLatency
	// 			insVDisk.ReadThroughPut = diskUsage.ReadThroughput

	// 			insVDisk.WriteIops = int(diskUsage.WriteIOPS)
	// 			insVDisk.WriteAvgLatency = diskUsage.WriteAvgLatency
	// 			insVDisk.WriteThroughPut = diskUsage.WriteThroughput
	// 		}
	// 	}
	// 	ins.VDisks = append(ins.VDisks, insVDisk)
	// }

	// vnics, err := s.store.Networks().ListVirtualInterfaces(ctx, metav1.ListOptions{})
	// if err != nil {
	// 	err := fmt.Errorf("vnics list err: '%s'", err.Error())
	// 	return ins, err
	// }

	// for _, vnicId := range vm.VNics {
	// 	var insVNic dto.VNicInstance
	// 	var found = false
	// 	for _, vnic := range vnics.Items {
	// 		if vnic.VirtualInterfaceID == vnicId {
	// 			found = true

	// 			insVNic.Mac = vnic.MacAddress
	// 			insVNic.NetworkName = vnic.NetworkName
	// 			insVNic.NicName = vnic.GuestName
	// 			insVNic.Running = true
	// 			insVNic.VNicType = vnic.VNicType
	// 			// TODO
	// 			insVNic.Status = "online"
	// 			break
	// 		}
	// 	}
	// 	if !found {
	// 		err := fmt.Errorf("invalid instance '%s'", vnicId)
	// 		return ins, err
	// 	}

	// 	// 虚拟网卡速度相关参数
	// 	for _, vnicInfo := range usage.Network {
	// 		if vnicInfo.Device == insVNic.NicName {
	// 			insVNic.Tx = int(vnicInfo.TxBytes)
	// 			insVNic.Rx = int(vnicInfo.RxBytes)
	// 		}
	// 	}
	// 	ins.VNics = append(ins.VNics, insVNic)
	// }
	// return ins, nil
	// 	resp <- dto.InstanceResult{Error: err}
	// 	return err
	// }
	// retIns = append(retIns, ins)
	// resp <- dto.InstanceResult{Instances: retIns}
	// return nil
}

func (s *vmInstanceService) handleListInstance(ctx context.Context, resp chan dto.InstanceResult) error {
	listInstance, err := s.store.VMs().List(ctx, metav1.ListOptions{})
	if err != nil {
		resp <- dto.InstanceResult{Error: err}
		return err
	}

	retIns := make([]dto.VMInstance, 0, len(listInstance.Items))
	for guestId, vm := range listInstance.Items {
		status, err := s.backendVMInstanceToWebVM(vm, guestId)
		if err != nil {
			resp <- dto.InstanceResult{Error: err}
			return err
		}

		retIns = append(retIns, status)
	}

	resp <- dto.InstanceResult{Error: nil, Instances: retIns}
	return nil
}

func (s *vmInstanceService) handleVNCLinkUrl(ctx context.Context, GuestId string, resp chan dto.InstanceResult) error {
	result := dto.InstanceResult{}
	result.VncLink.Url, result.VncLink.Port = s.vncCfg.VNCLinkUrlAndPort(GuestId)
	result.VncLink.Https = false

	result.Error = nil
	resp <- result
	return nil
}

func (s *vmInstanceService) backendVMInstanceToVdisk(ins *domain.VMInstance) ([]domain.GuestStoreVDisk, error) {
	// vdisk
	var cfgVdisk domain.GuestStoreVDisk
	configVDisks := make([]domain.GuestStoreVDisk, 0, len(ins.VDisks))
	err := s.vdiskService.Running(context.Background(), ins.VDisks)
	if err != nil {
		log.Info("vdisk running fail")
	}
	for _, vdiskId := range ins.VDisks {
		vdisk, err := s.store.VDisks().Get(context.Background(), vdiskId, metav1.GetOptions{})
		if err != nil {
			log.Infof("vdiskId: %s, get more detail info fail", vdiskId)
			return nil, err
		}
		// vdisk control
		cfgVdisk.DevBus = vdisk.VDiskMode.ToString()
		cfgVdisk.Device = vdisk.VDiskDevice
		cfgVdisk.NAA = vdisk.LUNUUID
		// vdisk order
		cfgVdisk.Idx = vdisk.MinorID
		// vdisk iops
		cfgVdisk.WriteIOPSMax = uint64(vdisk.DevLimit)
		cfgVdisk.WriteIOPS = uint64(vdisk.DevReservation)
		cfgVdisk.ReadIOPSMax = uint64(vdisk.DevLimit)
		cfgVdisk.ReadIOPS = uint64(vdisk.DevReservation)
		configVDisks = append(configVDisks, cfgVdisk)
	}
	return configVDisks, nil
}

func (s *vmInstanceService) backendVMInstanceToVNic(ins *domain.VMInstance) ([]domain.GuestNetworkInterface, error) {
	// vnic
	var cfgVNic domain.GuestNetworkInterface
	configVNics := make([]domain.GuestNetworkInterface, 0, len(ins.VNics))
	ifaces, err := s.vnicService.Run(context.Background(), ins.VNics)
	if err != nil {
		log.Info("ovs create interface fail")
		return nil, err
	}

	for idx, vnicId := range ins.VNics {
		vnic, err := s.store.VNics().Get(context.Background(), vnicId, metav1.GetOptions{})
		if err != nil {
			log.Infof("vnicId: %s, get more detail info fail", vnicId)
			return nil, err
		}
		// vnic Mac
		cfgVNic.HardwareAddress = vnic.MacAddress
		// vnic 型号
		cfgVNic.NetBus = domain.TemplateNetworkModel(domain.TemplateNetworkModel(vnic.VNicType)).ToString()
		// vnic 网络接口
		cfgVNic.NetworkSource = ifaces[idx]
		// vnic sr-iov TODO
		configVNics = append(configVNics, cfgVNic)
	}
	return configVNics, nil
}

func (s *vmInstanceService) backendVMInstanceToISOImage(ins *domain.VMInstance) ([]string, error) {
	// vnic
	imgList, err := s.store.Images().List(context.Background(), metav1.ListOptions{})
	if err != nil {
		return nil, err
	}
	pool, err := s.store.RepositoryPool().List(context.Background(), metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	configISOs := make([]string, 0, len(ins.ISOImages))
	for _, iso := range ins.ISOImages {
		if strings.EqualFold(iso, domain.DeviceNotUse) {
			continue
		}

		if _, info := os.Stat(iso); os.IsExist(info) {
			configISOs = append(configISOs, iso)
			continue
		}

		found := false
		isoImage, ok := imgList.Items[iso]
		if !ok {
			return nil, fmt.Errorf("image [%s] not exist", iso)
		}

		for _, repo := range isoImage.Repos {
			imgFilePath := isoImage.LocationImageFile(iso, pool.Items[repo].LocationVolume)
			if _, err := os.Stat(imgFilePath); err == nil {
				configISOs = append(configISOs, imgFilePath)
				found = true
				break
			}
		}
		if !found {
			return nil, fmt.Errorf("iso image not found")
		}
	}

	return configISOs, nil
}

func (s *vmInstanceService) backendVMInstanceToGuestConfig(ins *domain.VMInstance) (*domain.GuestConfig, error) {
	var config domain.GuestConfig
	var err error

	if nil == config.Template {
		config.Template = &s.defaultTemplate
		log.Infof("<instance> using default template for instance '%s'", ins.InstanceID)
	}

	config.Name = ins.InstanceID
	config.ID = ins.InstanceID
	// CPU
	config.Cores = uint(ins.VCPUNum)
	// CPU 兼容、直通模式
	config.CPUPassThru = ins.CPUPassthru
	// CPU保留线程 TODO
	// Mem
	config.Memory = uint(ins.VRAMSize)
	// video card
	config.Template.Display = ins.VideoCard
	// machine type
	config.OSMachine = ins.MachineType
	// priority
	config.CPUPriority = domain.PriorityEnum(ins.CPUWeight)
	// vdisk
	if config.VDisks, err = s.backendVMInstanceToVdisk(ins); err != nil {
		log.Info("backend to vdisk fail")
		return nil, err
	}
	// vnic
	if config.VNics, err = s.backendVMInstanceToVNic(ins); err != nil {
		log.Info("backend to vnic fail")
		return nil, err
	}
	// iso镜像
	if config.BootImages, err = s.backendVMInstanceToISOImage(ins); err != nil {
		log.Info("backend to image fail")
		return nil, err
	}
	// 自动启动
	config.AutoStart = ins.Autorun
	// 启动位置
	config.OSBoot = ins.BootFrom
	// 固件 TODO
	// 键盘布局 TODO
	// 串行端口 TODO
	// 虚拟USB控制器
	config.Template.USB = domain.TemplateUSBModel(domain.TemplateUSBModel(ins.USBVersion)).ToString()
	// USB设备
	copy(config.StorageUsb, ins.USBs)
	return &config, nil
}

func (s *vmInstanceService) webVDisksToBackendVMInstance(guestId string, ins dto.VMInstance) ([]string, string, error) {
	vdiskIds := make([]string, 0)
	addVdisks := make([]dto.VDiskAdd, 0, len(ins.VDisks))

	var add dto.VDiskAdd
	var repoCfg string
	edit := dto.VMInstanceRequestEdit{}
	edit.RepositoryID = ins.RepoId
	edit.GuestID = guestId
	edit.VdiskNum = len(ins.VDisks)
	edit.OrderChanged = false

	for _, vdisk := range ins.VDisks {
		add.DevLimit = vdisk.DevLimit
		add.DevReservation = vdisk.DevReservation
		add.DevWeight = vdisk.DevWeight
		add.IopsEnable = vdisk.IOPSEnable
		add.Unmap = vdisk.Unmap
		add.Name = vdisk.Name
		add.Type = "add"
		add.VdiskSize = vdisk.VDiskSize
		add.Idx = vdisk.Idx
		add.VdiskMode = vdisk.VDiskMode

		addVdisks = append(addVdisks, add)
	}

	edit.Disks.Add = addVdisks
	if ids, cfg, err := s.vdiskService.Create(context.Background(), &edit, metav1.CreateOptions{}); err == nil {
		repoCfg = cfg
		vdiskIds = append(vdiskIds, ids...)
	} else {
		return nil, "", err
	}

	return vdiskIds, repoCfg, nil
}

func (s *vmInstanceService) webVNicToBackendVMInstance(guestId string, ins dto.VMInstance) ([]string, string, error) {
	var add dto.VNicAdd
	var repoCfg string

	vnicIds := make([]string, 0)
	addVNics := make([]dto.VNicAdd, 0, len(ins.VNics))

	edit := dto.VMInstanceRequestEdit{}
	edit.RepositoryID = ins.RepoId
	edit.GuestID = guestId
	edit.VdiskNum = len(ins.VDisks)
	edit.OrderChanged = false

	for _, vnic := range ins.VNics {
		add.Mac = vnic.Mac
		add.NetworkID = vnic.NetworkId
		add.PreferSRIOV = vnic.PreferSriov
		add.Type = "add"
		addVNics = append(addVNics, add)
	}
	edit.VNics.Add = addVNics

	if ids, cfg, err := s.vnicService.Create(context.Background(), &edit, metav1.CreateOptions{}); err != nil {
		return nil, "", err
	} else {
		repoCfg = cfg
		vnicIds = append(vnicIds, ids...)
	}

	return vnicIds, repoCfg, nil
}

func (s *vmInstanceService) webVMInstanceToBackendVMInstance(guestID string, ins dto.VMInstance, vguestCfg *string, vniCfg *string, vdiskCfg *string) (*domain.VMInstance, error) {
	var vm domain.VMInstance

	vm.InstanceID = guestID
	vm.AutoSwitch = ins.AutoSwitch
	vm.Autorun = ins.Autorun
	vm.BootFrom = ins.BootFrom
	vm.CPUPassthru = ins.CPUPassthru
	vm.CPUPinNum = ins.CPUPinNum
	vm.CPUWeight = ins.CPUWeight
	vm.Desc = ins.Desc
	vm.HypervEnlighten = ins.HypervEnlighten
	vm.IsGeneralVM = ins.IsGeneralVM
	vm.KBLayout = ins.KBLayout
	vm.MachineType = domain.VMInstanceMachineType(ins.MachineType).ToString()
	vm.Name = ins.Name
	vm.SerialConsole = ins.SerialConsole
	vm.USBVersion = ins.USBVersion
	vm.UseOVMF = ins.UseOVMF
	vm.VRAMSize = ins.VRAMSize
	vm.VideoCard = ins.VideoCard

	vm.CreatingHost = ""
	vm.ISOImages = ins.ISOImages
	vm.RepositoryID = ins.RepoId
	vm.RepoName = ins.RepoName
	vm.StateValue = domain.VMInstancePowerStateShutdown
	vm.State = domain.VMInstancePowerState(vm.StateValue).ToString()
	vm.USBs = ins.USBs
	vm.VCPUNum = ins.VCPUNum
	vm.USBDeviceName = ins.USBDeviceName

	if cfg, err := json.Marshal(vm); err == nil {
		*vguestCfg = string(cfg)
	} else {
		log.Infof("%s transfer to cfg fail", vm.InstanceID)
	}

	// VDISK
	var err error
	var wg sync.WaitGroup
	wg.Add(2)
	go func() {
		// Vdisk
		vm.VDisks, *vdiskCfg, err = s.webVDisksToBackendVMInstance(guestID, ins)
		if err != nil {
			log.Infof("instance vdisks failed,error: %s", err.Error())
		}
		wg.Done()
	}()
	// VNic
	go func() {
		vm.VNics, *vniCfg, err = s.webVNicToBackendVMInstance(guestID, ins)
		if err != nil {
			log.Infof("instance vnic failed,error: %s", err.Error())
		}
		wg.Done()
	}()

	wg.Wait()
	return &vm, err
}

func (s *vmInstanceService) handleStartInstance(ctx context.Context, guestID string, resp chan error) error {
	// 查询etcd中数据
	ins, err := s.store.VMs().Get(ctx, guestID, metav1.GetOptions{})
	if err != nil {
		log.Info("start instance failed,vm not exist")
		resp <- err
		return err
	}

	// 转化backend store为Guest.config
	insConfig, err := s.backendVMInstanceToGuestConfig(ins)
	if err != nil {
		log.Info("don't get Guest Config from backend vm instance")
		resp <- err
		return err
	}

	// 创建libvirt虚拟机
	if s.libvirtStore.VMs().Exists(insConfig.ID) {
		if err := s.libvirtStore.VMs().DeleteInstance(ctx, insConfig.ID); err != nil {
			log.Infof("ID %s's had exist,remove failed", insConfig.ID)
			resp <- err
			return err
		}
	}

	if err := s.libvirtStore.VMs().CreateGuestInstance(ctx, insConfig); err != nil {
		log.Info("libvirt create fail")
		resp <- err
		return err
	}

	// 启动虚拟机
	if err := s.libvirtStore.VMs().StartInstance(ctx, guestID); err != nil {
		// 启动失败，关闭虚拟机
		s.libvirtStore.VMs().DeleteInstance(ctx, guestID)
		s.cleanSourceAfterShutdown(ctx, guestID)

		log.Info("libvirt start vm fail")
		resp <- err
		return err
	}

	// 虚拟机状态更新
	ins.State = domain.VMInstancePowerState(domain.VMInstancePowerStateRuning).ToString()
	ins.StateValue = domain.VMInstancePowerStateRuning

	if err := s.store.VMs().Update(ctx, guestID, ins, metav1.CreateOptions{}); err != nil {
		log.Info("update backend failed, vm not exist")
		resp <- err
		return err
	}

	// 添加VNC连接
	vncPort, err := s.libvirtStore.VMs().GraphicsLinkPort(ctx, guestID)
	if err != nil {
		log.Info("vm [%s] don't get graphics link port")
	}
	s.vncCfg.AddVNCLink(guestID, "127.0.0.1", strconv.Itoa(vncPort))

	resp <- nil
	return nil
}

func (s *vmInstanceService) cleanSourceAfterShutdown(ctx context.Context, guestID string) error {
	ins, err := s.store.VMs().Get(ctx, guestID, metav1.GetOptions{})
	if err != nil {
		log.Info("start instance failed,vm not exist")
		return err
	}

	ins.State = domain.VMInstancePowerState.ToString(domain.VMInstancePowerStateShutdown)
	ins.StateValue = int(domain.VMInstancePowerStateShutdown)
	if err := s.store.VMs().Update(ctx, guestID, ins, metav1.CreateOptions{}); err != nil {
		log.Errorf("[VM_Service] virutal machine cleanning failed - %s", guestID)
		return err
	}

	if err := s.vdiskService.Stop(ctx, ins.VDisks); err != nil {
		log.Errorf("[VM_Service] virutal machine cleanning failed - vdisk failed to stop %s", guestID)
		return err
	}

	if err := s.vnicService.Stop(ctx, ins.VNics); err != nil {
		log.Errorf("[VM_Service] virutal machine cleanning failed - Backend storage failed to update %s", guestID)
		return err
	}

	// 移除libvirt中虚拟机
	s.vncCfg.RemoveVNCLink(guestID)
	s.libvirtStore.VMs().DeleteInstance(ctx, guestID)
	return nil
}

func (s *vmInstanceService) handleForceShutDownInstance(ctx context.Context, guestID string, resp chan error) error {
	running, _ := s.libvirtStore.VMs().IsInstanceRunning(ctx, guestID)
	if !running {
		log.Warnf("[VM_Service] virutal machine force shutdownning failed - %s virtual machine is not running", guestID)
		resp <- nil
		return nil
	}

	if err := s.libvirtStore.VMs().StopInstance(ctx, guestID, false, true); err != nil {
		log.Errorf("[VM_Service] virutal machine force shutdownning failed - %s", err.Error())
		resp <- err
		return err
	}

	if err := s.cleanSourceAfterShutdown(ctx, guestID); err != nil {
		resp <- err
		return err
	}
	resp <- nil

	s.powerMut.Lock()
	if rtCtr, ok := s.vmPowerManager[guestID]; ok {
		rtCtr.SetStopping()
	}
	s.powerMut.Unlock()

	return nil
}

func (s *vmInstanceService) handleShutDownInstance(ctx context.Context, guestID string, resp chan error) error {
	running, _ := s.libvirtStore.VMs().IsInstanceRunning(ctx, guestID)
	if !running {
		log.Warnf("[VM_Service] virutal machine shutdownning failed - %s virtual machine is not running", guestID)
		resp <- nil
		return nil
	}

	s.powerMut.Lock()
	if rtCtr, ok := s.vmPowerManager[guestID]; ok {
		resp <- nil
		rtCtr.GetNotifyChannel() <- true

		s.powerMut.Unlock()
		return nil
	}
	s.vmPowerManager[guestID] = nil
	s.powerMut.Unlock()

	s.libvirtStore.VMs().StopInstance(ctx, guestID, false, false)

	rtCtr := pkg.CreateSimpleRunner(func(c pkg.RoutineController) {
		var waitTicker = time.NewTicker(ShutDownWait)
		var outTimeTicker = time.NewTicker(ShutDownOutTime)

		for !c.IsStopping() {
			select {
			case <-c.GetNotifyChannel():
				outTimeTicker.Stop()
				outTimeTicker = time.NewTicker(ShutDownOutTime)
			case <-outTimeTicker.C:
				c.SetStopping()
			case <-waitTicker.C:
				running, _ := s.libvirtStore.VMs().IsInstanceRunning(context.Background(), guestID)
				if running {
					continue
				}

				s.cleanSourceAfterShutdown(context.Background(), guestID)
				c.SetStopping()
			}
		}

		s.powerMut.Lock()
		delete(s.vmPowerManager, guestID)
		s.powerMut.Unlock()
	})

	s.vmPowerManager[guestID] = rtCtr
	rtCtr.Start()

	resp <- nil
	return nil
}

func (s *vmInstanceService) handleSoftRebootInstance(ctx context.Context, guestID string, resp chan error) error {
	running, _ := s.libvirtStore.VMs().IsInstanceRunning(ctx, guestID)
	if !running {
		log.Warnf("[VM_Service] virutal machine rebootting failed - %s virtual machine is not running", guestID)
		resp <- nil
		return nil
	}

	if err := s.libvirtStore.VMs().StopInstance(ctx, guestID, true, false); err != nil {
		log.Warnf("[VM_Service] virutal machine rebootting failed - %s", err.Error())
		resp <- err
		return err
	}

	s.powerMut.Lock()
	if rtCtr, ok := s.vmPowerManager[guestID]; ok {
		rtCtr.SetStopping()
	}
	s.powerMut.Unlock()

	resp <- nil
	return nil
}

func (s *vmInstanceService) handlePauseInstance(ctx context.Context, guestID string, resp chan error) error {
	running, _ := s.libvirtStore.VMs().IsInstanceRunning(ctx, guestID)
	if !running {
		log.Warnf("[VM_Service] virutal machine pausing failed - %s virtual machine is not running", guestID)
		resp <- nil
		return nil
	}

	if err := s.libvirtStore.VMs().PauseInstance(ctx, guestID); err != nil {
		log.Errorf("[VM_Service] virutal machine pausing failed - %s", err.Error())
		resp <- err
		return err
	}

	ins, err := s.store.VMs().Get(ctx, guestID, metav1.GetOptions{})
	if err != nil {
		log.Warnf("[VM_Service] virutal machine pausing failed - %s", guestID)
		resp <- nil
		return nil
	}

	ins.State = domain.VMInstancePowerState.ToString(domain.VMInstancePowerStateSaved)
	ins.StateValue = int(domain.VMInstancePowerStateSaved)
	if err := s.store.VMs().Update(ctx, guestID, ins, metav1.CreateOptions{}); err != nil {
		log.Warnf("[VM_Service] virutal machine cleanning failed - %s", guestID)
	}

	s.powerMut.Lock()
	if rtCtr, ok := s.vmPowerManager[guestID]; ok {
		rtCtr.SetStopping()
	}
	s.powerMut.Unlock()

	resp <- nil
	return nil
}

func (s *vmInstanceService) handleUnpauseInstance(ctx context.Context, guestID string, resp chan error) error {
	running, _ := s.libvirtStore.VMs().IsInstanceRunning(ctx, guestID)
	if !running {
		log.Warnf("[VM_Service] virutal machine resuming failed - %s virtual machine is not running", guestID)
		resp <- nil
		return nil
	}

	if err := s.libvirtStore.VMs().UnpauseInstance(ctx, guestID); err != nil {
		log.Errorf("[VM_Service] virutal machine resuming failed - %s", err.Error())
		resp <- err
		return err
	}

	ins, err := s.store.VMs().Get(ctx, guestID, metav1.GetOptions{})
	if err != nil {
		log.Warnf("[VM_Service] virutal machine resuming failed - %s", guestID)
		resp <- nil
		return nil
	}

	ins.State = domain.VMInstancePowerState.ToString(domain.VMInstancePowerStateRuning)
	ins.StateValue = int(domain.VMInstancePowerStateRuning)
	if err := s.store.VMs().Update(ctx, guestID, ins, metav1.CreateOptions{}); err != nil {
		log.Warnf("[VM_Service] virutal machine resuming failed - %s", guestID)
	}

	resp <- nil
	return nil
}

func (s *vmInstanceService) handleCreateInstance(ctx context.Context, ins dto.VMInstance, resp chan dto.InstanceResult) error {
	var result dto.InstanceResult
	var vguestCfg, vniCfg, vdiskCfg string

	guestId := uuid.Must(uuid.NewV4()).String()
	vm, err := s.webVMInstanceToBackendVMInstance(guestId, ins, &vguestCfg, &vniCfg, &vdiskCfg)
	if err != nil {
		result.Error = err
		resp <- result
		return err
	}

	// 存储后端创建虚拟机
	if err := s.store.VMs().Create(ctx, guestId, vm, metav1.CreateOptions{}); err != nil {
		result.Error = err
		resp <- result
		return err
	}

	log.Infof("<instance> new instance '%s'(id '%s') created", vm.Name, vm.InstanceID)

	// 写入后端存储
	if err := s.repoService.CreateConfig(ctx, guestId, ins.RepoId, vguestCfg, vdiskCfg, vniCfg); err != nil {
		log.Infof("%s write to %s repo cfg fail,err - %s", guestId, vm.RepositoryID, err.Error())
	}
	resp <- result
	return nil
}

func (s *vmInstanceService) usbResourceDevice(ctx context.Context) ([]dto.UsbResource, error) {
	buffer, err := utils.GetFileContent(domain.UsbToolConfJson, 0)
	if err != nil {
		log.Infof("list usb resource failed")
		return nil, err
	}

	var usbs []dto.UsbResource

	type Partitions struct {
		DEVNAME  string `json:"DEVNAME"`
		UUID     string `json:"UUID"`
		PARTUUID string `json:"PARTUUID"`
		LABEL    string `json:"LABEL"`
		TYPE     string `json:"TYPE"`
		Index    int    `json:"index"`
	}
	type UsbConfig struct {
		DEVNAME      string       `json:"DEVNAME"`
		PTTYPE       string       `json:"PTTYPE"`
		Index        int          `json:"index"`
		Partitions   []Partitions `json:"partitions"`
		IDSerial     string       `json:"IDSerial"`
		PTUUID       string       `json:"PTUUID"`
		Product      string       `json:"product"`
		Manufacturer string       `json:"manufacturer"`
		UUID         string       `json:"UUID"`
	}

	configs := make([]UsbConfig, 0)
	if err = json.Unmarshal(buffer, &configs); err != nil {
		log.Infof("list usb resource failed,not unmarshal file %s", domain.UsbToolConfJson)
		return nil, nil
	}

	for _, config := range configs {
		if !strings.Contains(config.Product, "usb") { // 不是usb设备
			continue
		}

		usb := dto.UsbResource{}
		usbSysDir := path.Dir(config.Product)
		// bus: /sys/devices/pci0000:00/0000:00:15.0/usb1/1-2/busnum
		usbBus := utils.GetFirstLine(path.Join(usbSysDir, "busnum"))
		// Device: /sys/devices/pci0000:00/0000:00:15.0/usb1/1-2/devnum
		usbDev := utils.GetFirstLine(path.Join(usbSysDir, "devnum"))
		// vendorId: /sys/devices/pci0000:00/0000:00:15.0/usb1/1-2/idVendor
		usbVendorId := utils.GetFirstLine(path.Join(usbSysDir, "idVendor"))
		// productId: /sys/devices/pci0000:00/0000:00:15.0/usb1/1-2/idProduct
		usbProductId := utils.GetFirstLine(path.Join(usbSysDir, "idProduct"))
		// Version: /sys/devices/pci0000:00/0000:00:15.0/usb1/1-2/version
		usb.Version = utils.GetFirstLine(path.Join(usbSysDir, "version"))
		usb.UsbId = strings.Join([]string{"", usbBus, usbDev, usbVendorId, usbProductId}, ":")
		usb.ProductName = strings.Join([]string{config.Manufacturer, utils.GetFirstLine(config.Product)}, " ")
		usb.Disable = false
		usbs = append(usbs, usb)
	}

	// 检查USB是否被使用
	if vmlist, err := s.store.VMs().List(ctx, metav1.ListOptions{}); err == nil {
		for i := 0; i < len(usbs); i++ {
			usbs[i].Disable = true
			usbs[i].DisableReason = dto.UsbResourceState.ToString(dto.UsbResourceListFail)
		}
	} else {
		for _, vm := range vmlist.Items {
			for _, vmusb := range vm.USBs {
				for i := 0; i < len(usbs); i++ {
					if vmusb == usbs[i].UsbId {
						usbs[i].Disable = true
						usbs[i].DisableReason = dto.UsbResourceState.ToString(dto.UsbResourceStateUseing)
					}
				}
			}
		}
	}

	return usbs, nil
}

func (s *vmInstanceService) netResourceDevice(ctx context.Context) ([]dto.NetResource, error) {
	nets, err := s.store.Networks().List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Infof("list net resource err: %s", err.Error())
		return nil, err
	}

	res := make([]dto.NetResource, 0, len(nets.Items))
	for _, net := range nets.Items {
		res = append(res, dto.NetResource{
			Name:         net.NetworkName,
			NetworkId:    net.NetworkID,
			SupportSriov: false,
		})
	}
	return res, nil
}

func (s *vmInstanceService) repoResourceDevice(ctx context.Context) ([]dto.RepoResource, error) {
	repos, err := s.store.RepositoryPool().List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Infof("list repository pool resource err: %s", err.Error())
		return nil, err
	}

	res := make([]dto.RepoResource, 0, len(repos.Items))
	for id, repo := range repos.Items {
		usage, err := disk.Usage(repo.LocationVolume)
		if err != nil {
			return nil, err
		}

		res = append(res, dto.RepoResource{
			Name:           repo.Name,
			FreeSize:       int64(usage.Free),
			RepoId:         id,
			VolumeSize:     int64(usage.Total),
			RepoFileSystem: repo.FileSystem,
			Status:         "",
		})
	}
	return res, nil
}

func (s *vmInstanceService) imgResourceDevice(ctx context.Context) ([]dto.ImageResource, error) {
	imgs, err := s.store.Images().List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Infof("list images resource err: %s", err.Error())
		return nil, err
	}

	res := make([]dto.ImageResource, 0, len(imgs.Items))
	for id, img := range imgs.Items {
		res = append(res, dto.ImageResource{
			Id:       id,
			Name:     img.Name,
			Status:   domain.StateAvailable,
			ISOType:  img.Type,
			UsedSize: int64(img.FileSize),
		})
	}
	return res, nil
}

func (s *vmInstanceService) handleListResource(ctx context.Context, resp chan dto.InstanceResult) (err error) {
	var res dto.InstanceResult
	memStat, _ := mem.VirtualMemory()
	res.Resource.MaxCpuNum, _ = cpu.Counts(false)
	res.Resource.MaxRamSize = int64(memStat.Total)
	res.Resource.Usbs, _ = s.usbResourceDevice(ctx)
	res.Resource.NetWorks, _ = s.netResourceDevice(ctx)
	res.Resource.Images, _ = s.imgResourceDevice(ctx)
	res.Resource.VideoCards = []string{"cirrus", "vga", "vmvga"}
	res.Resource.Repos, err = s.repoResourceDevice(ctx)
	res.Resource.Firmware = []string{"Legacy BIOS", "UEFI"}
	res.Resource.KBLayout = []string{"fr-be", "ar", "bepo", "cz", "da", "de", "de-ch", "en-gb", "en-us",
		"es", "et", "fo", "fr", "fr-be", "fr-ca", "fr-ch", "hr", "hu", "is",
		"it", "ja", "lt", "lv", "mk", "nl", "nl-be", "no", "pl", "pt", "pt-br",
		"ru", "sl", "sv", "th", "tr"}

	if err != nil {
		res.Error = err
		return err
	}

	res.Error = nil
	resp <- res
	return nil
}

// 用于存储池中虚拟机删除、恢复等操作
// 不可添加别的处理
type vmRepoService struct {
	store        store.Factory
	vdiskService VDiskService
	vnicService  VNicService
}

func newVmRepoService(srv *service) *vmRepoService {
	var service vmRepoService
	fmt.Println("srv: ", srv)
	service.store = srv.store
	service.vdiskService = newVDisk(srv)
	service.vnicService = newVNicService(srv)
	return &service
}

func (s *vmRepoService) ListVmStatus(ctx context.Context, vguestIds []string) (map[string]string, error) {
	vmList, err := s.store.VMs().List(ctx, metav1.ListOptions{})

	retVmStatus := make(map[string]string)
	if err != nil {
		log.Info("When check vm running, occuer err")
		return retVmStatus, err
	}

	for _, guestid := range vguestIds {
		if vm, ok := vmList.Items[guestid]; ok {
			if vm.State == domain.PowerStateRuning ||
				vm.State == domain.PowerStateBooting ||
				vm.State == domain.PowerStatePreparing {
				retVmStatus[vm.Name] = domain.VMInstancePowerState(vm.StateValue).ToString()
			}
		}
	}

	return retVmStatus, nil
}

func (s *vmRepoService) RestoreVmsConfig(ctx context.Context, repoId string, vguestId string, vguestCfg *string, vdiskCfg *string, vnicCfg *string) error {
	var vm domain.VMInstance
	var vdiskList domain.VDiskList
	var vnicList domain.VNicList

	if err := json.Unmarshal([]byte(*vguestCfg), &vm); err != nil {
		log.Info("unmarshal to VMInstance struct failed")
		return err
	}

	if err := json.Unmarshal([]byte(*vdiskCfg), &vdiskList); err != nil {
		log.Info("unmarshal to VDisk struct failed")
		return err
	}

	if err := json.Unmarshal([]byte(*vnicCfg), &vnicList); err != nil {
		log.Info("unmarshal to VNIC struct failed")
		return err
	}

	// vdisk 恢复操作
	newVdiskCfg, err := s.vdiskService.Recover(ctx, &vdiskList, repoId, metav1.UpdateOptions{})
	if err != nil {
		log.Info("restore vdisk etcd fail")
		return err
	}
	*vdiskCfg = newVdiskCfg

	// vnic 恢复操作
	if err := s.vnicService.Recover(ctx, &vnicList, repoId, metav1.UpdateOptions{}); err != nil {
		log.Info("restore vdisk etcd fail")
		return err
	}
	// 恢复vm的etcd
	vm.RepositoryID = repoId
	vm.State = domain.VMInstancePowerState.ToString(domain.VMInstancePowerStateShutdown)
	vm.StateValue = domain.VMInstancePowerStateShutdown
	vm.USBVersion = domain.TemplateUSBModelPIIX3
	vm.USBs = []string{"unmounted", "unmounted", "unmounted", "unmounted"}
	// vdisk
	//vm.VDisks = vdiskList.ListVDiskIDs()
	// vnic
	vm.VNics = vnicList.ListVNicIDs()

	if err := s.store.VMs().Create(ctx, vguestId, &vm, metav1.CreateOptions{}); err != nil {
		log.Info("restore vm etcd fail")
		return err
	}

	return nil
}

func (s *vmRepoService) DeleteVmList(ctx context.Context, repoId string, vguestIds []string, needSaveData bool) error {

	for _, id := range vguestIds {
		var edit dto.VMInstanceRequestEdit

		edit.NeedSaveData = needSaveData
		edit.RepositoryID = repoId
		edit.GuestID = id
		//根据虚拟机ID获取虚拟机的信息
		vGuestsinfo, err := s.store.VMs().Get(ctx, id, metav1.GetOptions{})
		if err != nil {

		}

		//将虚拟机下所有的虚拟盘加入delete数组
		for _, vdiskId := range vGuestsinfo.VDisks {
			vdiskdelete := dto.VDiskDelete{
				VDiskExtended: dto.VDiskExtended{VdiskID: vdiskId},
			}
			edit.Disks.Delete = append(edit.Disks.Delete, vdiskdelete)
		}

		//将虚拟机下所有的虚拟网卡加入delete数组
		for _, vnicId := range vGuestsinfo.VNics {
			vnicdelete := dto.VNicDelete{
				VNicID: vnicId,
			}
			edit.VNics.Delete = append(edit.VNics.Delete, vnicdelete)
		}

		// 删除vguest中数据
		s.store.VMs().Delete(ctx, id, metav1.DeleteOptions{})
		// 删除vdisk数据
		s.vnicService.Delete(ctx, &edit, metav1.DeleteOptions{})
		// 删除vnic数据
		s.vdiskService.Delete(ctx, &edit, metav1.DeleteOptions{})
	}

	return nil
}

package v1

import (
	"context"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/alert"
)

const (
	VMWarningNetworkException = "vm_warning_network_exception" // 当虚拟机所有网卡状态为disconnected触发
	VMErrorOfflineForHost     = "vm_error_offline_for_host"    // 虚拟机所在主机离线
	VMErrorStorageSpaceFull   = "vm_error_storage_space_full"  // 虚拟机所在存储空间已满
	VMErrorStorageDamage      = "vm_error_storage_damage"      // 虚拟机所在存储损坏
	VMErrorStorageLose        = "vm_error_storage_lose"        // 虚拟机所在存储丢失
)

func (s *vmInstanceService) isVMNetException() (alert.StatusEntry, error) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Panic occurred: %v", r)
		}
	}()
	ctx := context.Background()
	var withAlarmIds []string
	var allDeviceUID []string
	allPass := true

	vms, err := s.store.VMs().List(ctx, metav1.ListOptions{})

	for vmid, vm := range vms.Items {
		allDeviceUID = append(allDeviceUID, vmid)
		for _, vnid := range vm.VNics {
			vnic, err := s.store.VNics().Get(ctx, vnid, metav1.GetOptions{})
			if err != nil || vnic == nil {
				continue
			}
			log.Printf("Processing VM %s, vnic %s, network %s", vmid, vnid, vnic.NetworkID)
			net, err := s.store.Networks().Get(ctx, vnic.NetworkID, metav1.GetOptions{})
			if err != nil || net == nil {
				continue
			}
			for _, interStatus := range net.Interfaces {
				if interStatus == nil || interStatus.Items == nil {
					continue
				}
				for _, inter := range interStatus.Items {
					if inter != nil && inter.Status != "disconnected" {
						allPass = false
						break
					}
				}
				if allPass {
					withAlarmIds = append(withAlarmIds, vmid)
				}
			}
		}
	}

	entry := alert.StatusEntry{}
	if len(withAlarmIds) != 0 {
		entry = alert.StatusEntry{
			Component:    s.ComponentType(),
			Severity:     alert.SeverityLevelWarning,
			Message:      VMWarningNetworkException,
			WithAlarmIds: withAlarmIds,
			NorAlarmIds:  s.GetNorAlarmIds(allDeviceUID, withAlarmIds),
		}
	}

	return entry, err
}

func (s *vmInstanceService) isVMOfflineForHost() (alert.StatusEntry, error) {
	ctx := context.Background()
	var allDeviceUID, withAlarmIds []string
	hostID := make(map[string]string)

	vms, err := s.store.VMs().List(ctx, metav1.ListOptions{})
	if err != nil {
		return alert.StatusEntry{}, err
	}

	for vmid, vm := range vms.Items {
		allDeviceUID = append(allDeviceUID, vmid)
		hostID[vmid] = vm.HostID
	}

	netInfo, err := s.redis.CollNetwork().List(ctx, metav1.ListOptions{})
	statusInfo := netInfo.Interfaces

	if err != nil {
		return alert.StatusEntry{}, err
	}

	allPass := true
	for vmid, hostid := range hostID {
		hostIface, err := s.store.Host().ListIfaceOfHost(ctx, hostid, metav1.ListOptions{})
		if err != nil {
			return alert.StatusEntry{}, err
		}

		for _, iface := range hostIface.Items {
			if statusInfo[iface.Name].Status != "disconnected" {
				allPass = false
				break
			}
		}
		if allPass {
			withAlarmIds = append(withAlarmIds, vmid)
		}
	}

	entry := alert.StatusEntry{}
	if len(withAlarmIds) != 0 {
		entry = alert.StatusEntry{
			Component:    s.ComponentType(),
			Severity:     alert.SeverityLevelWarning,
			Message:      VMWarningNetworkException,
			WithAlarmIds: withAlarmIds,
			NorAlarmIds:  s.GetNorAlarmIds(allDeviceUID, withAlarmIds),
		}
	}

	return entry, err
}

func (s *vmInstanceService) isVMStorageSpaceFull() (alert.StatusEntry, error) {
	entry := alert.StatusEntry{}
	return entry, nil
}

func (s *vmInstanceService) isVMStorageDamage() (alert.StatusEntry, error) {
	entry := alert.StatusEntry{}
	return entry, nil
}

func (s *vmInstanceService) isVMStorageLose() (alert.StatusEntry, error) {
	entry := alert.StatusEntry{}
	return entry, nil
}

func (s *vmInstanceService) ComponentType() string {
	return "vm"
}

func (s *vmInstanceService) GetNorAlarmIds(allDeviceUID, withAlarmIds []string) []string {
	withAlarmMap := make(map[string]bool)
	for _, id := range withAlarmIds {
		withAlarmMap[id] = true
	}

	norAlarmIds := make([]string, 0)
	for _, id := range allDeviceUID {
		if !withAlarmMap[id] {
			norAlarmIds = append(norAlarmIds, id)
		}
	}

	return norAlarmIds
}

func (s *vmInstanceService) RegisterAlertHandler() {
	alert.AlertRegister.AddAlertHandler(s.ComponentType(), s.isVMNetException).
		AddAlertHandler(s.ComponentType(), s.isVMOfflineForHost).
		AddAlertHandler(s.ComponentType(), s.isVMStorageSpaceFull).
		AddAlertHandler(s.ComponentType(), s.isVMStorageDamage).
		AddAlertHandler(s.ComponentType(), s.isVMStorageLose)
}

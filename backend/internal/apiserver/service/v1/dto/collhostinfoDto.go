package dto

import metav1 "github.com/marmotedu/component-base/pkg/meta/v1"

// HostList 物理主机列表
type HostListDto struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据
	Hosts           []*HostDto       `json:"hosts"` // 物理主机信息列表
}

// Host 物理主机详细信息
type HostDto struct {
	metav1.ObjectMeta `json:"metadata,omitempty"` // 标准对象元数据

	CPUUsage float64   `json:"cpu_usage"` // 系统 CPU 使用率（百分比）
	Desc     string    `json:"desc"`      // 主机描述信息
	ID       string    `json:"id"`        // 主机 UUID（对应 etcd 中的 host key）
	Name     string    `json:"name"`      // 主机名称（对应 etcd 中的 host/object）
	NICs     []HostNIC `json:"nics"`      // 网络接口列表（设备信息来自 etcd host/nics）
	RAMUsage float64   `json:"ram_usage"` // 系统内存使用率（百分比）
	Status   string    `json:"status"`    // 主机状态（对应 etcd 数据）
	HostType string    `json:"type"`      // 集群健康状态（对应 etcd 数据，使用 tag 重命名避免关键字冲突）
}

// HostNIC 物理主机网络接口信息
type HostNIC struct {
	Device string `json:"device"` // 网络设备名称（对应 etcd 中 network 键）
	Rx     int    `json:"rx"`     // 接收数据量（单位：字节）
	Status string `json:"status"` // 网络连接状态
	Tx     int    `json:"tx"`     // 发送数据量（单位：字节）
	Type   string `json:"type"`   // 接口类型（如 ovseth）
}

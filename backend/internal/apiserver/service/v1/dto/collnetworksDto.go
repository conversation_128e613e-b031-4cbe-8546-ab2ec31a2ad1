package dto

import metav1 "github.com/marmotedu/component-base/pkg/meta/v1"

type NetworkInterfaceDto struct {
	HasSriov      bool   `json:"has_sriov"`      // SRIOV支持状态
	HostName      string `json:"host_name"`      // 主机名称
	InterfaceName string `json:"interface_name"` // 接口名称
	Rx            uint64 `json:"rx"`             // 接收字节数(KB/s)
	SpeedMbps     int    `json:"speed_mbs"`      // 接口速率（Mbps），-1表示未知
	Status        string `json:"status"`         // 连接状态（connected/disconnected）
	Tx            uint64 `json:"tx"`             // 发送字节数(KB/s)
}

type NetworkStateDto struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Interfaces []*NetworkInterfaceDto `json:"interfaces"` // 网络接口列表
}

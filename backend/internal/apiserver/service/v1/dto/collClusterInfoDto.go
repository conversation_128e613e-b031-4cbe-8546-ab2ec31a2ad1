package dto

type ClusterStatusResponse struct {
	ClusterStatus string      `json:"cluster_status"`
	GuestSumm     GuestSumm   `json:"guest_summ"`
	HostSumm      HostSumm    `json:"host_summ"`
	LicenseSumm   LicenseSumm `json:"license_summ"`
	Reasons       []Reason    `json:"reasons"`
	RepoSumm      RepoSumm    `json:"repo_summ"`
}

type GuestSumm struct {
	Error   int `json:"error"`
	Healthy int `json:"healthy"`
	Running int `json:"running"` // 虚拟机特有的运行状态计数
	Warning int `json:"warning"`
}

type HostSumm struct {
	Error   int `json:"error"`
	Healthy int `json:"healthy"`
	Warning int `json:"warning"`
}

type LicenseSumm struct {
	Error   int `json:"error"`
	Healthy int `json:"healthy"`
	Warning int `json:"warning"`
}

type RepoSumm struct {
	Error   int `json:"error"`
	Healthy int `json:"healthy"`
	Warning int `json:"warning"`
}

type Reason struct {
	Num    int    `json:"num"`    // 问题数量
	Status string `json:"status"` // 问题类型标识
}

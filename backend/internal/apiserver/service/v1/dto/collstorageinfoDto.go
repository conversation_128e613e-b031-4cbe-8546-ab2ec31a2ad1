package dto

import metav1 "github.com/marmotedu/component-base/pkg/meta/v1"

type StorageListDto struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据
	Storage         []*StorageDto    `json:"storage"` // 存储信息列表
}

type StorageDto struct {
	Desc        string  `json:"desc"`         // 描述
	HostName    string  `json:"host_name"`    // 物理机名称
	ID          string  `json:"id"`           // 物理机存储池UUID
	Name        string  `json:"name"`         // 存储池名称
	Size        float64 `json:"size"`         // 存储池总大小
	Status      string  `json:"status"`       // 存储池状态
	Type        string  `json:"type"`         // 存储池类型
	Used        float64 `json:"used"`         // 已使用空间
	PercentUsed float64 `json:"percent_used"` // 已使用百分比
	PercentFree float64 `json:"percent_free"` // 剩余百分比
}

package dto

import (
	"encoding/json"

	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

type VDiskInstance struct {
	VDiskMode      int    `json:"vdisk_mode"`      // 磁盘控制模式
	Name           string `json:"name"`            // 虚拟机名称
	Unmap          bool   `json:"unmap"`           // 是否支持 UNMAP/TRIM 操作（用于回收未使用空间）
	IOPSEnable     bool   `json:"iops_enable"`     // 启动io控制
	DevLimit       int    `json:"dev_limit"`       // 最大IOPS
	DevReservation int    `json:"dev_reservation"` // 最小IOPS
	DevWeight      int    `json:"dev_weight"`      // 设备权重
	VDiskSize      int    `json:"vdisk_size"`      // 虚拟盘大小, GB
	Idx            int    `json:"idx"`             // 设备序号
}

type VNicInstance struct {
	PreferSriov bool   `json:"prefer_sriov"` //
	VNicType    int    `json:"vnic_type"`    //磁盘状态类型
	Mac         string `json:"mac"`          // Mac地址
	NetworkId   string `json:"network_id"`   // Mac地址
}

type VMInstance struct {
	ISOImages             []string        `json:"iso_images"`            // 挂载的 ISO 镜像
	Autorun               int             `json:"autorun"`               // 是否自动运行
	BootFrom              string          `json:"boot_from"`             // 启动方式，例如 "disk"
	Bios                  string          `json:"bios"`                  // 引导方式
	KBLayout              string          `json:"kb_layout"`             // 键盘布局
	SerialConsole         bool            `json:"serial_console"`        // 是否启用串行控制台
	USBVersion            int             `json:"usb_version"`           // USB 版本
	USBDeviceName         []string        `json:"usb_device_name"`       // USB 设备名称
	USBs                  []string        `json:"usbs"`                  // 连接的 USB 设备  格式： hostID:Bus:Device:vendorId:productId
	IsWindowsVM           bool            `json:"is_windows_vm"`         // 连接的 USB 设备
	UseOVMF               bool            `json:"use_ovmf"`              // 是否使用 OVMF（UEFI）
	VNics                 []VNicInstance  `json:"vnics"`                 // 网络接口卡 ID
	IsGeneralVM           bool            `json:"is_general_vm"`         // 是否为通用虚拟机
	VDisks                []VDiskInstance `json:"vdisks"`                // 连接的虚拟磁盘
	AutoSwitch            int             `json:"auto_switch"`           // 是否自动切换
	Name                  string          `json:"name"`                  // 虚拟机名称
	VCPUNum               int             `json:"vcpu_num"`              // 虚拟 CPU 数量
	VCPUUsage             int             `json:"vcpu_usage"`            // 虚拟 CPU 数量
	VRAMSize              int64           `json:"vram_size"`             // 虚拟机内存大小（MB）
	VideoCard             string          `json:"video_card"`            // 视频卡类型，例如 "vmvga"
	MachineType           string          `json:"machine_type"`          // 机器类型，例如 "pc"
	CPUWeight             int             `json:"cpu_weight"`            // CPU 权重
	Desc                  string          `json:"desc"`                  // 描述信息
	CPUPassthru           bool            `json:"cpu_passthru"`          // CPU 直通
	HypervEnlighten       bool            `json:"hyperv_enlighten"`      // 是否启用 Hyper-V 特性
	CPUPinNum             int             `json:"cpu_pin_num"`           // CPU 绑定的核数
	RepoId                string          `json:"repo_id"`               //存储池ID
	RepoName              string          `json:"repo_name"`             // 存储池名称
	PoweronAfterCreate    bool            `json:"poweron_after_create"`  // 创建后开机
	GuestId               string          `json:"guest_id"`              // 虚拟机ID
	IncreaseAllocatedSize int             `json:"increaseAllocatedSize"` //
	HostId                string          `json:"host_id"`               // 主机ID
	HostName              string          `json:"host_name"`             // 主机名称
	SnapNum               int             `json:"snap_num"`              // 快照数量

	Status      string `json:"status"`      // 虚拟机状态
	StatusDesc  string `json:"status_desc"` // 虚拟机状态描述
	HostRamSize int64  `json:"host_ram_size"`

	TotalThroughput float64 `json:"total_throughput"` // 虚拟机硬盘总吞吐量
	TotalIOPS       float64 `json:"total_iops"`       // 虚拟机硬盘总IOPS
	TotalIOLatency  float64 `json:"total_io_latency"` // 虚拟机硬盘总延迟
	TotalRxBytes    float64 `json:"total_rx_bytes"`   // 虚拟机网络接受速率
	TotalTxBytes    float64 `json:"total_tx_bytes"`   // 虚拟机网络发送速率

	MemUsage float64 `json:"memory_usage"` // 内存使用率（百分比）
	CpuUsage float64 `json:"cpu_usage"`    // 虚拟 CPU 使用率（百分比）
	// StatusType  string `json:"status_type"`   // 虚拟机状态类型
	// IsActing           bool            `json:"is_acting"`            //
	// AllocatedSize         int64           `json:"allocated_size"`          //
	// Size                  int64           `json:"size"`                    //
}

type VMInstanceRequestEdit struct {
	// 虚拟机标识信息
	HostID       string `json:"host_id"`
	GuestID      string `json:"guest_id"`
	RepositoryID string `json:"repository_id"`
	Name         string `json:"name"`
	Vmstate      string `json:"state"`
	// 存储操作相关
	IncreaseAllocatedSize int  `json:"increaseAllocatedSize"` // 存储扩容大小(GB)
	OrderChanged          bool `json:"order_changed"`         // 磁盘顺序是否变更
	VdiskNum              int  `json:"vdisk_num"`             // 虚拟磁盘数量

	// 保留后端数据
	NeedSaveData bool `json:"need_save_data"`
	// 虚拟磁盘操作集合
	Disks VdiskOpts `json:"disk_ops"`
	// 虚拟网卡操作集合
	VNics VNickOpts `json:"vnic_ops"`
}

// 自定义 JSON 序列化
func (g VMInstanceRequestEdit) MarshalJSON() ([]byte, error) {
	// 创建影子类型避免递归调用
	type Alias VMInstanceRequestEdit
	aux := &struct {
		*Alias
		DiskOrder []VDiskOrder `json:"vdisks_order"`
	}{
		Alias: (*Alias)(&g),
	}

	// 根据条件添加 order 字段
	if g.OrderChanged {
		aux.DiskOrder = g.Disks.Order
	}

	return json.Marshal(aux)
}

func (g *VMInstanceRequestEdit) UnmarshalJSON(data []byte) error {
	type Alias VMInstanceRequestEdit
	aux := &struct {
		*Alias
		DiskOrder []VDiskOrder `json:"vdisks_order"`
	}{
		Alias: (*Alias)(g),
	}

	if err := json.Unmarshal(data, aux); err != nil {
		return err
	}

	// 根据是否存在 order 字段设置标志位
	g.OrderChanged = len(aux.DiskOrder) > 0
	g.Disks.Order = aux.DiskOrder
	return nil
}

type UsbResourceState int

const (
	UsbResourceStateUseing = iota
	UsbResourceListFail
	VMInstanceStateInvalid
)

const (
	UsbStateUseing = "usb_has_been_used"
	UsbListFail    = "usb_has_been_used"
)

func (value UsbResourceState) ToString() string {
	switch value {
	case UsbResourceStateUseing:
		return UsbStateUseing
	case UsbResourceListFail:
		return UsbListFail
	default:
		return "invalid"
	}
}

// resource
type UsbResource struct {
	Disable       bool   `json:"disable"`
	DisableReason string `json:"disable_reason"`
	ProductName   string `json:"product_name"`
	UsbId         string `json:"usb_id"`
	Version       string `json:"version"`
}

type NetResource struct {
	Name         string `json:"name"`
	NetworkId    string `json:"network_id"`
	SupportSriov bool   `json:"support_sriov"`
	// Permitted    bool   `json:"permitted"`
}

type RepoResource struct {
	Name           string `json:"name"`
	FreeSize       int64  `json:"free_size"`
	RepoId         string `json:"repo_id"`
	VolumeSize     int64  `json:"volume_size"`
	RepoFileSystem string `json:"repo_filesystem"`
	Status         string `json:"status"`
	// Permitted  bool   `json:"permitted"`
}

type ImageResource struct {
	Id       string            `json:"id"`
	Name     string            `json:"name"`
	Status   domain.ImageState `json:"status"`
	ISOType  domain.ImageType  `json:"type"`
	UsedSize int64             `json:"used_size"`
	// StatusType string            `json:"status_type"`
}

type VMResource struct {
	MaxCpuNum  int             `json:"max_cpu_num"`
	MaxRamSize int64           `json:"max_ram_size"`
	Usbs       []UsbResource   `json:"usbs"`
	NetWorks   []NetResource   `json:"networks"`
	Repos      []RepoResource  `json:"repos"`
	Images     []ImageResource `json:"images"`
	VideoCards []string        `json:"video_cards"`
	Firmware   []string        `json:"firmware"`
	KBLayout   []string        `json:"keyboard_layout"`
}

type VNCLinkInfo struct {
	Url   string `json:"url"`
	Port  int    `json:"port"`
	Https bool   `json:"https"`
}

type InstanceResult struct {
	Error     error
	TaskId    domain.TaskID
	VDevIDs   []string
	Instances []VMInstance
	Resource  VMResource
	VncLink   VNCLinkInfo
}

package dto

import metav1 "github.com/marmotedu/component-base/pkg/meta/v1"

type VNicDelete struct {
	VNicID         string `json:"virtual_interface_id"` // 虚拟网络接口的唯一标识符
	PreferSRIOV    bool   `json:"location_volume"`      //是否优先支持SR-IOV技术
	VNicType       string `json:"vnic_type"`            //VNic类型
	Type           string `json:"type"`                 //操作类型
	Mac            string `json:"mac,omitempty"`        // Mac地址
	NetworkID      string `json:"network_id"`           // 虚拟网络接口所属的网络的唯一标识符
	OriMac         string `json:"ori_mac"`              //
	OriPreferSriov string `json:"ori_prefer_sriov"`     //
	OriVNicType    int    `json:"ori_vnic_type"`        //
}

type VNicEdit struct {
	VNicID    string `json:"virtual_interface_id"` // 虚拟网络接口的唯一标识符
	NetworkID string `json:"network_id"`           // 虚拟网络接口所属的网络的唯一标识符
}

type VNicAdd struct {
	PreferSRIOV bool   `json:"location_volume"` //是否优先支持SR-IOV技术
	VNicType    int    `json:"vnic_type"`       //VNic类型
	Type        string `json:"type"`            //操作类型
	Mac         string `json:"mac,omitempty"`   // Mac地址
	NetworkID   string `json:"network_id"`      // 虚拟网络接口所属的网络的唯一标识符
}

type VNickOpts struct {
	Add    []VNicAdd    `json:"vdisks_add"`  // 新增vnic
	Delete []VNicDelete `json:"vdisks_del"`  // 删除vnic
	Edit   []VNicEdit   `json:"vdisks_edit"` // 编辑vnic
}

type VNicOnNetoworkDto struct {
	GuestName   string `json:"guest_name"`           // 虚拟机的名称
	GuestID     string `json:"guest_id"`             // 虚拟机的唯一标识符，表明该接口属于哪个虚拟机
	MacAddress  string `json:"mac_address"`          // 虚拟机虚拟网卡mac地址，用于在网络中唯一标识该接口
	VNicID      string `json:"virtual_interface_id"` // 虚拟网络接口的唯一标识符
	SeqNumber   int    `json:"seq_number"`           //虚拟机中网卡的序列号(取VMinstance中的VNIC索引作为seqNumber)
	NetworkID   string `json:"network_id"`           // 虚拟网络接口所属的网络的唯一标识符
	NetworkName string `json:"network_name"`         // 虚拟网络接口所属的网络的名称
	Rx          uint64 `json:"rx"`                   // 接收字节数(KB/s)
	Tx          uint64 `json:"tx"`                   // 发送字节数(KB/s)
	Status      string `json:"status"`
	PreferSRIOV bool   `json:"sr_iov"` //是否优先支持SR-IOV技术             // 连接状态（connected/disconnected）
}

type VNicsOnNetoworkDto struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items []*VNicOnNetoworkDto `json:"interfaces"` // 网络接口列表
}

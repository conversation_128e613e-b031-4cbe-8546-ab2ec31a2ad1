package dto

import (
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
)

const (
	VolumeConfig       = "/etc/volume/volume.conf"
	StorageConfig      = "/etc/volume/storage.conf"
	CacheDir           = "/var/cache/diskinfo"
	BlockDir           = "/sys/block"
	HotSpareConf       = "/etc/hotspare/hotspare.conf"
	HotSpareRepairConf = "/etc/hotspare/hotspare_repair.conf"
	On                 = 1
	Off                = 2
)

type RepositoryPoolDto struct {
	Name               string  `json:"name"`                 //存储空间名字
	Host               string  `json:"host"`                 //主机信息：包含http和https的端口和Ip列表
	HostID             string  `json:"host_id"`              //主机唯一标识
	HostName           string  `json:"host_name"`            //主机名字
	RaidType           string  `json:"raid_type"`            //RAID类型
	RepoId             string  `json:"repo_id"`              //存储空间UUid
	Status             string  `json:"status"`               //存储池状态
	Used               float64 `json:"used"`                 //已用空间大小
	PercentUsed        float64 `json:"percent_used"`         // 已用空间百分比
	VdiskSize          float64 `json:"vdisk_size"`           //已分配虚拟盘空间
	LocationVolume     string  `json:"location_volume"`      //存储空间所在volume
	TotalSize          float64 `json:"total_size"`           //存储空间总容量
	HardLimit          float64 `json:"hard_limit"`           //空间不足阈值
	SoftLimit          float64 `json:"soft_limit"`           //空间不足百分比阈值
	EnableLowerNotify  bool    `json:"enable_lower_notify"`  //可用空间低于"空间不足"阈值时通知
	LastNotifyTreshold string  `json:"last_notify_treshold"` //最后通知阈值
	VolumePath         string  `json:"volume_type"`          //所在卷路径
}

type RepositoryPoolList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items map[string]*RepositoryPoolDto `json:"items"` // 存储空间列表
}

type RepoVMsDto struct {
	Name               string     `json:"name"`                 //存储空间名字
	HostID             string     `json:"host_id"`              //主机唯一标识
	HostName           string     `json:"host_name"`            //主机名字
	RaidType           string     `json:"raid_type"`            //RAID类型
	RepoId             string     `json:"repo_id"`              //存储空间UUid
	Status             string     `json:"status"`               //存储池状态
	Used               string     `json:"used"`                 //已用空间大小
	PercentUsed        float64    `json:"percent_used"`         // 已用空间百分比
	LocationVolume     string     `json:"location_volume"`      //存储空间所在volume
	TotalSize          float64    `json:"total_size"`           //存储空间总容量
	HardLimit          float64    `json:"hard_limit"`           //空间不足阈值
	SoftLimit          float64    `json:"soft_limit"`           //空间不足百分比阈值
	EnableLowerNotify  bool       `json:"enable_lower_notify"`  //可用空间低于"空间不足"阈值时通知
	LastNotifyTreshold string     `json:"last_notify_treshold"` //最后通知阈值
	VolumePath         string     `json:"volume_type"`          //所在卷路径
	VdisksTotalSize    string     `json:"vdisks_total_size"`    //存储池下所有虚拟盘大小之和单位(Byte)
	GuestsInfo         GuestsInfo `json:"guests_info"`          //存储空间下的虚拟机
}

type GuestsInfo struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items map[string]*GuestInfo `json:"items"` // 虚拟机下的存储信息
}

type GuestInfo struct {
	Name            string  `json:"name"`             //虚拟机名称
	Total           float64 `json:"total"`            // 文件系统总容量 unit:b
	Used            float64 `json:"used"`             // 已使用容量 unit:b
	TotalThroughput float64 `json:"total_throughput"` // 总吞吐量（单位：KB/s）
	TotalIOPS       int     `json:"total_iops"`       // 总 IOPS
	TotalIOLatency  int     `json:"total_io_latency"` // 总 I/O 延迟（单位：毫秒）
}

// type Volume struct {
// 	FreeSize   int64  `json:"free_size"`   //可用空间大小
// 	FsType     string `json:"fs_type"`     //文件系统类型
// 	HostId     string `json:"host_id"`     //主机名字
// 	HostName   string `json:"host_name"`   //主机名字
// 	Size       string `json:"size"`        //容量
// 	VolumePath string `json:"volume_path"` //卷路径
// }

type VolumeList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items []*Volume `json:"items"` // 存储空间列表
}

type VolumeMember struct {
	UUID        string `json:"uuid"`                          // 卷文件系统UUID
	ShowName    string `json:"show_name" ini:"name"`          // 前端显示卷名
	Sort        int    `json:"sort" ini:"sort"`               // 前端显示卷序号
	Description string `json:"description" ini:"description"` // 卷描述
	Compression int    `json:"compression" ini:"compression"` // 卷压缩
}
type Capacity struct {
	Value float64 `json:"value"`
	Unit  string  `json:"unit"`
}

type Volume struct {
	VolumeMember
	Name       string   `json:"name"`        // 卷名：example: lv0,md0,...
	Device     string   `json:"device"`      // 块设备  example: /dev/mapper/vg0-lv0, /dev/md0
	FileSystem string   `json:"filesystem"`  // 文件系统  example: ext4, btrfs,...
	Total      Capacity `json:"total"`       // 文件系统总容量 unit:kb
	Used       Capacity `json:"used"`        // 已使用容量 unit:kb
	Available  Capacity `json:"available"`   // 可用容量 unit:kb
	Usage      float64  `json:"usage"`       // 使用率 (35.2% -> 35.2)
	MntPath    string   `json:"mntpath"`     // 挂载点  /Volume1, /Volume2, ...
	Status     string   `json:"status"`      // 卷状态
	VolumeType string   `json:"volume_type"` // FlashCache
	DeFragment string   `json:"defragment"`  // btrfs碎片整理状态
}

type BlockDevice struct {
	UUID       string   `json:"uuid"`           // 卷相关的UUID
	Name       string   `json:"name"`           // 卷的实际名，example:vg0-lv0,md0,...
	LvName     string   `json:"lvname"`         // 卷名，example:lv0,lv1,...
	ShowName   string   `json:"show_name"`      // 卷别名：example:Volume #1,Volume #2
	VolumeSort int      `json:"volumesort"`     // 卷索引：1,2,..
	MountPoint string   `json:"mountpoint"`     // 卷挂载点
	Storage    string   `json:"storage"`        // 存储池，example:vg0,md0,vg1,...
	Raid       []string `json:"raid,omitempty"` // 阵列名
	Size       string   `json:"size"`           // 容量
	Type       string   `json:"type"`           // 文件系统或阵列类型

	Children []BlockDevice `json:"children,omitempty"`
}

type RaidInfo struct {
	Name       string   `json:"name"`              // 阵列名 example:md0,md1,...
	Sort       int      `json:"sort"`              // 阵列顺序
	Level      string   `json:"level"`             // 阵列级别 example: raid1,raid0,raid5,raid10,...
	Size       Capacity `json:"size"`              // 阵列容量, unit: kb
	DevSize    Capacity `json:"devsize"`           // 参与阵列的最小容量
	TotalDisks int      `json:"total_disks"`       // 参与阵列硬盘总数
	UsedDisks  int      `json:"used_disks"`        // 当前正在使用的硬盘数
	WorkDisks  int      `json:"work_disks"`        // 当前正在工作中的硬盘数
	Health     int      `json:"health"`            // 阵列健康状态
	Bitmap     int      `json:"bitmap"`            // bitmap位图
	Process    string   `json:"process,omitempty"` // 阵列同步状态
	Percent    float64  `json:"percent,omitempty"` // 阵列同步进度
	FsType     string   `json:"fs_type"`           // example: LVM2_member,ext4,btrfs,...
	UUID       string   `json:"uuid"`              // 阵列的array_uuid
	Flag       string   `json:"flag"`              // 阵列标记 UTOSUSER-X86-S64
}

type RepoHostList struct {
	HasData bool     `json:"has_data"` //是否存在数据
	Hosts   []string `json:"hosts"`    //集群hostID列表
}

package dto

import (
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
)

// VmListDto 虚拟机列表
type VmListDto struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据
	VMs             []*VmDto         `json:"vms"` // 虚拟机信息列表
}

// VmDto 虚拟机详细信息
type VmDto struct {
	metav1.ObjectMeta `json:"metadata"` // 标准对象元数据

	CPUUsage float64  `json:"cpu_usage"` // 虚拟机 CPU 使用率（百分比）
	Desc     string   `json:"desc"`      // 虚拟机描述信息
	Disks    []VmDisk `json:"disks"`     // 虚拟机磁盘列表
	ID       string   `json:"id"`        // 虚拟机 UUID
	Name     string   `json:"name"`      // 虚拟机名称
	NICs     []VmNIC  `json:"nics"`      // 虚拟机网络接口列表
	RAMUsage float64  `json:"ram_usage"` // 虚拟机内存使用率（百分比）
	Status   string   `json:"status"`    // 虚拟机状态
	VmType   string   `json:"type"`      // 集群健康状态（使用 tag 重命名避免关键字冲突）
}

// VmDisk 虚拟机磁盘信息
type VmDisk struct {
	Idx         int     `json:"idx"`          // 磁盘索引
	RThroughput float64 `json:"r_throughput"` // 读取吞吐量
	VdiskID     string  `json:"vdisk_id"`     // 虚拟磁盘 ID
	WThroughput float64 `json:"w_throughput"` // 写入吞吐量
}

// VmNIC 虚拟机网络接口信息
type VmNIC struct {
	Device    string  `json:"device"`      // 网络设备名称
	HostNetIf string  `json:"host_net_if"` // 主机网络接口
	Rx        float64 `json:"rx"`          // 接收数据量（单位：字节）
	Status    string  `json:"status"`      // 网络连接状态
	Tx        float64 `json:"tx"`          // 发送数据量（单位：字节）
	VnicID    string  `json:"vnic_id"`     // 虚拟网络接口 ID
}

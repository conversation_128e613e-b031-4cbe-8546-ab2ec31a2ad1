package dto

import (
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

type ImageLocate struct {
	// 集群
	// HostID     string            `json:"host_id"`
	// HostName   string            `json:"host_name"`
	RepoName string `json:"repo_name"`
	RepoId   string `json:"repo_id"`
}

type ImageDto struct {
	Name       string            `json:"image_name"`  // 映像名称
	RealName   string            `json:"real_name"`   // 映像文件的文件名
	ImageId    string            `json:"image_id"`    // 映像ID
	Type       domain.ImageType  `json:"type"`        // 映像的类型
	FileSize   uint64            `json:"file_size"`   // 主机的名称（专用网络所在的主机）
	Status     domain.ImageState `json:"status"`      // 映像状态
	CreateTime int64             `json:"create_time"` // 映像创建时间
	ImageRepos []ImageLocate     `json:"repos"`       // 映像存储位置
	Progress   int               `json:"progress"`    // 创建进度
	TaskId     domain.TaskID     `json:"task_id"`     // 上传任务ID
}

type ImageListDto struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据

	Items []*ImageDto `json:"items"` // 网络列表
}

type ImageCreate struct {
	Name         string           `json:"name"`      // 映像名称
	DiskFileName string           `json:"file_name"` // 映像名称
	Type         domain.ImageType `json:"type"`      // 映像的类型
	FileSize     uint64           `json:"file_size"` // 文件的大小
	ImageRepos   []ImageLocate    `json:"repos"`     // 映像存储位置
}

type IamgefileChunk struct {
	FileName    string `json:"file_name"`
	FileSize    int64  `json:"file_size"`
	ChunkData   string `json:"chunk_data"`
	ChunkOffset int64  `json:"chunk_offset"`
	BlockNumber int    `json:"block_number"`
	TotalBlocks int    `json:"total_blocks"`
	BlockHash   string `json:"block_hash"`
}

type ImageEdit struct {
	Name   string   `json:"image_name"`
	Id     string   `json:"image_id"`
	RepoId []string `json:"repos"`
}

package dto

// 虚拟磁盘基础属性
type VDiskBase struct {
	DevLimit       int    `json:"dev_limit"`
	DevReservation int    `json:"dev_reservation"`
	DevWeight      int    `json:"dev_weight"`
	IopsEnable     bool   `json:"iops_enable"`
	Unmap          bool   `json:"unmap"`
	VdiskMode      int    `json:"vdisk_mode"`
	Name           string `json:"name"`
}

// 扩展属性
type VDiskExtended struct {
	IsDummy    bool   `json:"is_dummy"`
	IsMetaDisk bool   `json:"is_meta_disk"`
	LunType    string `json:"lun_type"`
	Size       string `json:"size"`
	VdiskID    string `json:"vdisk_id"`
}

// 原始配置
type VDiskOriginal struct {
	OriVdiskMode      int  `json:"ori_vdisk_mode"`
	OriUnmap          bool `json:"ori_unmap"`
	OriIdx            int  `json:"ori_idx"`
	OriDevLimit       int  `json:"ori_dev_limit"`
	OriDevReservation int  `json:"ori_dev_reservation"`
	OriDevWeight      int  `json:"ori_dev_weight"`
	OriIopsEnable     bool `json:"ori_iops_enable"`
}

// 添加虚拟磁盘
type VDiskAdd struct {
	VDiskBase       `json:",inline"`
	Type            string `json:"type"` // "add"
	SetByUser       bool   `json:"set_by_user"`
	VdiskSize       int    `json:"vdisk_size"`
	Idx             int    `json:"idx"`
	IsVdiskSizeEdit bool   `json:"is_vdisk_size_edit"`
	IsUnmapEdit     bool   `json:"is_unmap_edit"`
}

// 删除虚拟磁盘
type VDiskDelete struct {
	VDiskBase     `json:",inline"`
	VDiskExtended `json:",inline"`
	VDiskOriginal `json:",inline"`
	Type          string `json:"type"` // "delete"
}

// 编辑虚拟磁盘
type VDiskEdit struct {
	VDiskBase       `json:",inline"`
	VDiskExtended   `json:",inline"`
	VDiskOriginal   `json:",inline"`
	Type            string `json:"type"` // "old"
	SetByUser       bool   `json:"set_by_user"`
	VdiskSize       int    `json:"vdisk_size"`
	Idx             int    `json:"idx"`
	IsVdiskSizeEdit bool   `json:"is_vdisk_size_edit"`
	IsUnmapEdit     bool   `json:"is_unmap_edit"`
	IsQosEdit       bool   `json:"is_qos_edit"`
}

// 磁盘顺序调整
type VDiskOrder struct {
	VDiskBase       `json:",inline"`
	VDiskExtended   `json:",inline"`
	VDiskOriginal   `json:",inline"`
	Type            string `json:"type"` // "old"
	VdiskSize       int    `json:"vdisk_size"`
	Idx             int    `json:"idx"`
	IsVdiskSizeEdit bool   `json:"is_vdisk_size_edit"`
	IsUnmapEdit     bool   `json:"is_unmap_edit"`
}

type VdiskOpts struct {
	Add    []VDiskAdd    `json:"vdisks_add"`  // 新增磁盘
	Delete []VDiskDelete `json:"vdisks_del"`  // 删除磁盘
	Edit   []VDiskEdit   `json:"vdisks_edit"` // 编辑磁盘
	Order  []VDiskOrder  `json:"-"`           // 磁盘排序
}

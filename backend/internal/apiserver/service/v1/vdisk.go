package v1

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/errors/gerror"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	uuid "github.com/satori/go.uuid"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/common/utils"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/aclog"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/store"
	"golang.org/x/sys/unix"
)

const (
	ByteUnit  = 1
	KByteUnit = 1 * 1024
	MByteUnit = 1 * 1024 * 1024
	GByteUnit = 1 * 1024 * 1024 * 1024
)

const gcodeOK = 0

func (s vDiskService) Create(ctx context.Context, disk dto.VMInstanceRequestEdit, opts metav1.CreateOptions) ([]string, string, error) {
	if disk.Disks.Add == nil {
		log.L(ctx).Warnf("[VDisk_Service] create failed")
		aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_CREATE_FAILURE, "name", "vdisk_create")
		return []string{}, "", gerror.New("vdisk create - vdisk operate type not add")
	}

	var vdiskAddList = make(map[string]dto.VDiskAdd)
	vdiskList := domain.VDiskList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(0),
		},
		Items: make(map[string]*domain.VDisk),
	}

	vdiskID := make([]string, 0, len(disk.Disks.Add))
	for _, vdiskDto := range disk.Disks.Add {
		vdisk := domain.VDisk{
			DevLimit:       int64(vdiskDto.DevLimit),
			DevReservation: int64(vdiskDto.DevReservation),
			DevWeight:      vdiskDto.DevWeight,
			Format:         2,
			GuestID:        disk.GuestId,
			IOPSEnable:     vdiskDto.IopsEnable,
			IsDummy:        false,
			IsMetaDisk:     false,
			MinorID:        vdiskDto.Idx + 1,
			RepositoryID:   disk.RepoId,
			Size:           vdiskDto.VdiskSize * GByteUnit,
			Type:           2,
			Unmap:          vdiskDto.Unmap,
			LUNUUID:        createLUNUUID(),
			VDiskMode:      domain.TemplateDiskDriver(vdiskDto.VdiskMode),
		}

		vdiskUUID := createVDiskUUID()
		vdiskAddList[vdiskDto.Name] = vdiskDto

		if err := s.createVDiskFile(ctx, vdiskUUID, vdisk); err != nil {
			log.L(ctx).Warnf("[VDisk_Service] get vdiskPath failed - %v", err)
			aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_CREATE_FAILURE, "name", "vdisk_create", "msg", err.Error())
			s.gobackVdiskFile(ctx, vdisk)
			return []string{}, "", gerror.Wrapf(err, "vdisk create - create vdisk sparsefile failed")
		}

		if err := s.store.VDisks().Create(ctx, vdiskUUID, &vdisk, opts); err != nil {
			log.L(ctx).Warnf("[VDisk_Service] create vdisk failed - vdisk by etcd %v", err)
			aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_CREATE_FAILURE, "name", "vdisk_create", "msg", err.Error())
			s.gobackVdiskFile(ctx, vdisk)
			return []string{}, "", gerror.Wrapf(err, "vdisk create - create vdisk storebackend failed")
		}

		vdiskList.Items[vdiskUUID] = &vdisk
		vdiskList.TotalCount++
		vdiskID = append(vdiskID, vdiskUUID)
	}
	vdiskListBytes, err := vdiskList.MarshalBinary()
	if err != nil {
		log.L(ctx).Warnf("[VDisk_Service] vdiskList marshal failed: %v", err)
		aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_CREATE_FAILURE, "name", "vdisk_create", "msg", err.Error())
		return []string{}, "", gerror.Wrapf(err, "vdisk create - vdisk marshalbinary failed")
	}

	aclog.New("vdisk", ctx).Info(aclog.VIRTUAL_DISK_CREATE_SUCCESS, "name", "vdisk_create")
	return vdiskID, string(vdiskListBytes), nil
}

// 运行虚拟机状态时从etcd中获取数据创建targetcli
func (s *vDiskService) Running(ctx context.Context, vdiskIDList []string) error {
	var RepositoryID string

	for _, vdisk := range vdiskIDList {
		log.Debugf("[VMs_Service] running before vdisk %s", vdisk)
	}

	for _, vdiskUUID := range vdiskIDList {
		vdisk, err := s.store.VDisks().Get(ctx, vdiskUUID, metav1.GetOptions{})
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] vdisk get failed - %v", err)
			return gerror.Wrapf(err, "vdisk running - backend store get failed")
		}

		getRepo := func(ctx context.Context, repoID string) (string, error) {
			return s.locateVDiskDir(ctx, repoID)
		}

		vdiskPath, err := vdisk.GetPath(ctx, getRepo)
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] getVDiskPath failed - %v", err)
			return gerror.Wrapf(err, "vdisk running - get file path failed")
		}

		log.Debugf("[VMs_Service] createVDiskbyTargetcli before vdiskid %s, vdisk %v", vdiskUUID, vdisk)
		err = s.createVDiskbyTargetcli(ctx, vdiskPath, *vdisk)
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] create vdisk failed - vdisk by targetcli %v", err)
			s.gobackVdiskTargetcli(ctx)
			return gerror.Wrapf(err, "vdisk running - targetcli create failed")
		}

		vdisk.VDiskDevice, err = s.getVDiskDevice(ctx, *vdisk)
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] vdisk device get failed - %v, vdiskUUID: %s", err, vdiskUUID)
			s.gobackVdiskTargetcli(ctx)
			return gerror.Wrapf(err, "vdisk running - targetcli device failed")
		}

		err = s.store.VDisks().Update(ctx, vdiskUUID, vdisk, metav1.UpdateOptions{})
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] vdisk update failed - for etcd %v", err)
			s.gobackVdiskTargetcli(ctx)
			return gerror.Wrapf(err, "vdisk running - backend store update failed")
		}
		RepositoryID = vdisk.RepositoryID
	}

	s.saveTargetcliInfo(ctx, RepositoryID)
	return nil
}

func (s vDiskService) Get(ctx context.Context, vdiskId string, opts metav1.GetOptions) (dto.VDiskInstance, error) {
	vdisk, err := s.store.VDisks().Get(ctx, vdiskId, metav1.GetOptions{})
	if err != nil {
		log.L(ctx).Warnf("[VDisk_Service] vdisk get failed - %v", err)
		return &dto.VDiskInstance{}, err
	}

	vdiskDto := &dto.VDiskInstance{
		VDiskMode:      int(vdisk.VDiskMode),
		Unmap:          vdisk.Unmap,
		IOPSEnable:     vdisk.IOPSEnable,
		DevLimit:       int(vdisk.DevLimit),
		DevReservation: int(vdisk.DevReservation),
		DevWeight:      vdisk.DevWeight,
		VDiskSize:      vdisk.Size / GByteUnit,
		VdiskId:        vdiskId,
		Idx:            vdisk.MinorID - 1,
	}

	return vdiskDto, err
}

// 虚拟机热编辑接口
func (s vDiskService) RunningEdit(ctx context.Context, disk dto.VMInstanceRequestEdit, opts metav1.UpdateOptions) ([]string, error) {
	var editedDiskId []string

	for _, vdiskDto := range disk.Disks.Edit {
		vdiskUUID := vdiskDto.VdiskID
		oriForEtcd, err := s.store.VDisks().Get(ctx, vdiskUUID, metav1.GetOptions{})
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] runningedit check failed - %v", err)
			aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_EDIT_FAILURE, "name", "vdisk_running_edit", "msg", err.Error())
			return []string{}, gerror.Wrapf(err, "vdisk running edit - backend store get failed")
		}

		vdisk := domain.VDisk{
			DevLimit:       int64(vdiskDto.DevLimit),
			DevReservation: int64(vdiskDto.DevReservation),
			DevWeight:      vdiskDto.DevWeight,
			Format:         2,
			GuestID:        disk.GuestId,
			IOPSEnable:     vdiskDto.IopsEnable,
			IsDummy:        false,
			IsMetaDisk:     false,
			MinorID:        vdiskDto.Idx + 1,
			RepositoryID:   disk.RepoId,
			Size:           vdiskDto.VdiskSize * GByteUnit,
			Type:           2,
			Unmap:          vdiskDto.Unmap,
			LUNUUID:        oriForEtcd.LUNUUID,
			VDiskMode:      domain.TemplateDiskDriver(vdiskDto.VdiskMode),
			VDiskDevice:    oriForEtcd.VDiskDevice,
		}

		// 调整稀疏文件fileio实际存储大小
		if vdiskDto.IsVdiskSizeEdit {
			_, err = s.sparseFile(ctx, vdiskUUID, vdisk)
			if err != nil {
				log.L(ctx).Warnf("[VDisk_Service] sparsefile runningedit failed - %v", err)
				aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_EDIT_FAILURE, "name", "vdisk_running_edit", "msg", err.Error())
				return []string{}, gerror.Wrapf(err, "vdisk running edit - sparsefile failed")
			}
		}

		if err = s.store.VDisks().Update(ctx, vdiskUUID, &vdisk, opts); err != nil {
			log.L(ctx).Warnf("[VDisk_Service] runningedit update failed - %v", err)
			aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_EDIT_FAILURE, "name", "vdisk_running_edit", "msg", err.Error())
			return []string{}, gerror.Wrapf(err, "vdisk running edit - backend store update failed")
		}

		if err = s.deleteTargetcli(ctx, *oriForEtcd); err != nil {
			log.L(ctx).Warnf("[VDisk_Service] runningedit targetcli delete failed - %v", err)
			return []string{}, gerror.Wrapf(err, "vdisk running edit - targetcli delete failed")
		}

		// 新虚拟盘创建targetcli协议
		getRepo := func(ctx context.Context, repoID string) (string, error) {
			return s.locateVDiskDir(ctx, repoID)
		}
		vdiskPath, err := vdisk.GetPath(ctx, getRepo)
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] getVDiskPath failed - %v", err)
			return []string{}, gerror.Wrapf(err, "vdisk running edit - vdisk get file path failed")
		}
		err = s.createVDiskbyTargetcli(ctx, vdiskPath, vdisk)
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] runningedit create vdisk failed - vdisk by targetcli %v", err)
			s.gobackVdiskTargetcli(ctx)
			return []string{}, gerror.Wrapf(err, "vdisk running edit - targetcli create failed")
		}
		editedDiskId = append(editedDiskId, vdiskUUID)
	}

	aclog.New("vdisk", ctx).Info(aclog.VIRTUAL_DISK_EDIT_SUCCESS, "name", "vdisk_update")
	return editedDiskId, nil
}

func sparseFileName(vdiskUUID string, vdisk domain.VDisk) string {
	return "vdisk" + "." +
		vdisk.GuestID + "." +
		vdiskUUID + "." +
		strconv.Itoa(vdisk.Type) + "." +
		strconv.Itoa(vdisk.MinorID-1)
}

func (s *vDiskService) sparseFile(ctx context.Context, vdiskUUID string, vdisk domain.VDisk) (string, error) {
	dir, err := s.locateLunPath(ctx, vdisk.RepositoryID, vdisk.LUNUUID)
	if err != nil {
		log.L(ctx).Warnf("[VDisk_Service] path get failed - repository %v", err)
		return "", err
	}

	sparseFile := filepath.Join(dir, sparseFileName(vdiskUUID, vdisk))

	if _, err := os.Stat(dir); os.IsNotExist(err) {
		if err := os.MkdirAll(dir, 0755); err != nil {
			log.Debugf("[VDisk_Service] directory %s create failed - %v", dir, err)
			return "", err
		}
	}

	file, err := os.OpenFile(sparseFile, os.O_RDWR|os.O_CREATE, 0644)

	if err != nil {
		log.Debugf("[VDisk_Service] file open/create failed - file %s - %v", sparseFile, err)
		return "", err
	}
	defer file.Close()

	fi, err := file.Stat()
	if err != nil {
		log.Debugf("[VDisk_Service] file %s info get failed - %v", sparseFile, err)
		return "", err
	}
	if fi.Size() == vdisk.Size {
		log.Debugf("[VDisk_Service] file %s already exists and is correct - %d bytes", sparseFile, vdisk.Size)
		return sparseFile, nil
	}

	if err := utils.ExpandSparseSizeForFile(file.Name(), vdisk.Size); err != nil {
		log.Errorf("[VDisk_Service] file %s expand failed - %v", sparseFile, err)
		return "", err
	}
	log.Infof("[VDisk_Service] adjust file size - %s (original size %d -> new size %d)", sparseFile, fi.Size(), vdisk.Size)

	return sparseFile, nil
}

// 虚拟机运行新增虚拟盘接口
func (s vDiskService) RunningAdd(ctx context.Context, disk dto.VMInstanceRequestEdit, opts metav1.CreateOptions) ([]string, error) {
	var addedDiskId []string

	for _, vdiskDto := range disk.Disks.Add {
		vdisk := domain.VDisk{
			DevLimit:       int64(vdiskDto.DevLimit),
			DevReservation: int64(vdiskDto.DevReservation),
			DevWeight:      vdiskDto.DevWeight,
			Format:         2,
			GuestID:        disk.GuestId,
			IOPSEnable:     vdiskDto.IopsEnable,
			IsDummy:        false,
			IsMetaDisk:     false,
			MinorID:        vdiskDto.Idx + 1,
			RepositoryID:   disk.RepoId,
			Size:           vdiskDto.VdiskSize * GByteUnit,
			Type:           2,
			Unmap:          vdiskDto.Unmap,
			LUNUUID:        createLUNUUID(),
			VDiskMode:      domain.TemplateDiskDriver(vdiskDto.VdiskMode),
		}

		if vdiskDto.Type == "add" {
			vdiskUUID := createVDiskUUID()

			if err := s.createVDiskFile(ctx, vdiskUUID, vdisk); err != nil {
				log.L(ctx).Warnf("[VDisk_Service] runningadd get vdiskPath failed - %v", err)
				aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_CREATE_FAILURE, "name", "vdisk_running_create", "msg", err.Error())
				s.gobackVdiskFile(ctx, vdisk)
				return []string{}, gerror.Wrapf(err, "vdisk running add - create file path failed")
			}

			if err := s.store.VDisks().Create(ctx, vdiskUUID, &vdisk, opts); err != nil {
				log.L(ctx).Warnf("[VDisk_Service] runningadd vdisk failed - vdisk by etcd %v", err)
				aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_CREATE_FAILURE, "name", "vdisk_running_create", "msg", err.Error())
				s.gobackVdiskFile(ctx, vdisk)
				s.store.VDisks().Delete(ctx, vdiskUUID, metav1.DeleteOptions{})
				return []string{}, gerror.Wrapf(err, "vdisk running add - backend store create failed")
			}

			// 新增targetcli相关配置
			getRepo := func(ctx context.Context, repoID string) (string, error) {
				return s.locateVDiskDir(ctx, repoID)
			}
			vdiskPath, err := vdisk.GetPath(ctx, getRepo)
			if err != nil {
				log.L(ctx).Warnf("[VDisk_Service] runningadd getVDiskPath failed - %v", err)
				return []string{}, gerror.Wrapf(err, "vdisk running add - get file path failed")
			}

			if err = s.createVDiskbyTargetcli(ctx, vdiskPath, vdisk); err != nil {
				log.L(ctx).Warnf("[VDisk_Service] runningadd vdisk failed - vdisk by targetcli %v", err)
				s.gobackVdiskTargetcli(ctx)
				return []string{}, gerror.Wrapf(err, "vdisk running add - targetcli create failed")
			}

			addedDiskId = append(addedDiskId, vdiskUUID)
		} else {
			log.L(ctx).Warnf("[VDisk_Service] runningadd add failed")
			aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_CREATE_FAILURE, "name", "vdisk_running_create")
			return []string{}, gerror.New("vdisk running add - operate type not add")
		}
	}

	log.L(ctx).Infof("[VDisk_Service] running add vdisk succeed")
	return addedDiskId, nil
}

func (s *vDiskService) locatTempDir(vdiskID string) (string, error) {
	tempVdisk, err := s.store.VDisks().Get(context.Background(), vdiskID, metav1.GetOptions{})
	if err != nil {
		return "", err
	}

	tempRepo, err := s.store.RepositoryPool().Get(context.Background(), tempVdisk.RepositoryID, metav1.GetOptions{})
	if err != nil {
		return "", err
	}

	tempDir := filepath.Join(tempRepo.LocationVolume, domain.VolumeTempDir)

	if _, err := os.Stat(tempDir); os.IsNotExist(err) {
		if err := os.MkdirAll(tempDir, 0755); err != nil {
			return "", err
		}
	}

	return tempDir, nil
}

// 关闭虚拟机状态时清除targetcli
func (s *vDiskService) Stop(ctx context.Context, vdiskIDList []string) error {
	for _, vdiskUUID := range vdiskIDList {
		vdisk, err := s.store.VDisks().Get(ctx, vdiskUUID, metav1.GetOptions{})
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] vdisk get failed - %v", err)
			return gerror.Wrapf(err, "vdisk stop - backend store get failed")
		}

		err = s.deleteTargetcli(ctx, *vdisk)
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] targetcli delete failed - %v", err)
			return gerror.Wrapf(err, "vdisk stop - targetcli delete failed")
		}
	}
	log.L(ctx).Infof("[VDisk_Service] vdisk targetcli stop succeed")
	return nil
}

func (s vDiskService) Update(ctx context.Context, disk dto.VMInstanceRequestEdit, opts metav1.UpdateOptions) (string, error) {
	if disk.Disks.Edit == nil {
		log.L(ctx).Warnf("[VDisk_Service] update vdisk failed")
		aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_EDIT_FAILURE, "name", "vdisk_update")
		return "", gerror.New("vdisk update - operate type not edit")
	}

	vdiskList := domain.VDiskList{
		Items: make(map[string]*domain.VDisk),
	}
	for _, vdiskDto := range disk.Disks.Edit {
		vdiskUUID := vdiskDto.VdiskID

		// 从etcd获取虚拟盘修改前信息
		oriForEtcd, err := s.store.VDisks().Get(ctx, vdiskUUID, metav1.GetOptions{})
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] vdiskoriginal check failed - %v", err)
			aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_EDIT_FAILURE, "name", "vdisk_update", "msg", err.Error())
			return "", gerror.Wrapf(err, "vdisk update - backend store get failed")
		}

		vdisk := domain.VDisk{
			DevLimit:       int64(vdiskDto.DevLimit),
			DevReservation: int64(vdiskDto.DevReservation),
			DevWeight:      vdiskDto.DevWeight,
			Format:         2,
			GuestID:        disk.GuestId,
			IOPSEnable:     vdiskDto.IopsEnable,
			IsDummy:        false,
			IsMetaDisk:     false,
			MinorID:        vdiskDto.Idx + 1,
			RepositoryID:   disk.RepoId,
			Size:           vdiskDto.VdiskSize * GByteUnit,
			Type:           2,
			Unmap:          vdiskDto.Unmap,
			LUNUUID:        oriForEtcd.LUNUUID,
			VDiskMode:      domain.TemplateDiskDriver(vdiskDto.VdiskMode),
		}

		// 调整稀疏文件fileio实际存储大小
		if vdiskDto.IsVdiskSizeEdit {
			_, err := s.sparseFile(ctx, vdiskUUID, vdisk)
			if err != nil {
				log.L(ctx).Warnf("[VDisk_Service] sparsefile update failed - %v", err)
				aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_EDIT_FAILURE, "name", "vdisk_update", "msg", err.Error())
				return "", gerror.Wrapf(err, "vdisk update - sparsefile update failed")
			}
		}

		if err = s.store.VDisks().Update(ctx, vdiskUUID, &vdisk, opts); err != nil {
			log.L(ctx).Warnf("[VDisk_Service] vdisk update failed - %v", err)
			aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_EDIT_FAILURE, "name", "vdisk_update", "msg", err.Error())
			return "", gerror.Wrapf(err, "vdisk update - backend store update failed")
		}
	}

	vdiskListptr, err := s.store.VDisks().List(ctx, metav1.ListOptions{})
	if err != nil {
		log.L(ctx).Warnf("[VDisk_Service] vdisklist get failed - %v", err)
		aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_EDIT_FAILURE, "name", "vdisk_update", "msg", err.Error())
		return "", gerror.Wrapf(err, "vdisk update - backend store list failed")
	}
	vdiskList = *vdiskListptr
	vdiskListBytes, err := vdiskList.MarshalBinary()
	if err != nil {
		log.L(ctx).Warnf("[VDisk_Service] vdisklist marshal failed - %v", err)
		aclog.New("vdisk", ctx).Warn(aclog.VIRTUAL_DISK_EDIT_FAILURE, "name", "vdisk_update", "msg", err.Error())
		return "", gerror.Wrapf(err, "vdisk update - vdisklist marshal failed")
	}
	aclog.New("vdisk", ctx).Info(aclog.VIRTUAL_DISK_EDIT_SUCCESS, "name", "vdisk_update")
	return string(vdiskListBytes), nil
}

func (s vDiskService) Sort(ctx context.Context, disk dto.VMInstanceRequestEdit, opts metav1.UpdateOptions) error {
	if disk.Disks.Order == nil {
		log.L(ctx).Warnf("[VDisk_Service] sort failed")
		return fmt.Errorf("sort vdisk failed")
	}

	for _, vdiskDto := range disk.Disks.Order {
		vdiskUUID := vdiskDto.VdiskID

		vdisk := domain.VDisk{
			DevLimit:       int64(vdiskDto.DevLimit),
			DevReservation: int64(vdiskDto.DevReservation),
			DevWeight:      vdiskDto.DevWeight,
			Format:         2,
			GuestID:        disk.GuestId,
			IOPSEnable:     vdiskDto.IopsEnable,
			IsDummy:        false,
			IsMetaDisk:     false,
			MinorID:        vdiskDto.Idx + 1,
			RepositoryID:   disk.RepoId,
			Size:           vdiskDto.VdiskSize * GByteUnit,
			Type:           2,
			Unmap:          vdiskDto.Unmap,
			VDiskMode:      domain.TemplateDiskDriver(vdiskDto.VdiskMode),
		}
		err := s.store.VDisks().Update(ctx, vdiskUUID, &vdisk, opts)
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] vdisk update failed - %v", err)
			return err
		}
	}
	return nil
}

func (s *vDiskService) DeleteByID(id string) error {
	vdisk, err := s.store.VDisks().Get(context.Background(), id, metav1.GetOptions{})
	if err != nil {
		log.Errorf("[VDisk_Service] vdisk get failed - %v", err)
		return gerror.Wrapf(err, "vdisk delete - backend store get failed")
	}

	if err := s.store.VDisks().Delete(context.Background(), id, metav1.DeleteOptions{}); err != nil {
		log.Errorf("[VDisk_Service] vdisk delete failed - %v", err)
		return gerror.Wrapf(err, "vdisk delete - backend store delete failed")
	}

	s.gobackVdiskFile(context.Background(), *vdisk)
	log.Infof("[VDisk_Service] vdisk delete sparsefile succeed - not save data")
	return nil
}

func (s vDiskService) Delete(ctx context.Context, disk dto.VMInstanceRequestEdit, opts metav1.DeleteOptions) error {
	if disk.Disks.Delete == nil {
		log.L(ctx).Errorf("[VDisk_Service] vdisk delete failed")
		aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_DELETE_FAILURE, "name", "vdisk_delete")
		return gerror.New("vdisk delete - disk operate type not delete")
	}

	for _, vdiskDto := range disk.Disks.Delete {
		vdiskUUID := vdiskDto.VdiskID
		vdisk, err := s.store.VDisks().Get(ctx, vdiskUUID, metav1.GetOptions{})
		if err != nil {
			log.L(ctx).Errorf("[VDisk_Service] vdisk get failed - %v", err)
			aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_DELETE_FAILURE, "name", "vdisk_delete", "msg", err.Error())
			return gerror.Wrapf(err, "vdisk delete - backend store get failed")
		}

		if err := s.store.VDisks().Delete(ctx, vdiskUUID, opts); err != nil {
			log.L(ctx).Errorf("[VDisk_Service] vdisk delete failed - %v", err)
			aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_DELETE_FAILURE, "name", "vdisk_delete", "msg", err.Error())
			return gerror.Wrapf(err, "vdisk delete - backend store delete failed")
		}

		if !disk.NeedSaveData {
			s.gobackVdiskFile(ctx, *vdisk)
			log.L(ctx).Infof("[VDisk_Service] vdisk delete sparsefile succeed - not save data")
		}
		aclog.New("vdisk", ctx).Info(aclog.VIRTUAL_DISK_DELETE_SUCCESS, "name", "vdisk_delete")
	}

	return nil
}

func (s vDiskService) Recover(ctx context.Context, vdiskList domain.VDiskList, repositoryUUID string, opts metav1.UpdateOptions) (string, error) {
	for vdiskUUID, vdisk := range vdiskList.Items {
		vdisk.RepositoryID = repositoryUUID
		err := s.store.VDisks().Update(ctx, vdiskUUID, vdisk, opts)
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] vdisk recover failed - %v", err)
			return "", err
		}
	}
	vdiskListBytes, err := vdiskList.MarshalBinary()
	if err != nil {
		log.L(ctx).Warnf("[VDisk_Service] vdisklist marshal failed - %v", err)
		return "", err
	}
	return string(vdiskListBytes), nil
}

func (s vDiskService) List(ctx context.Context, opts metav1.ListOptions) (domain.VDiskList, error) {
	vdiskList, err := s.store.VDisks().List(ctx, opts)
	if err != nil {
		log.L(ctx).Warnf("[VDisk_Service] vdisk list failed - %v", err)
		return &domain.VDiskList{}, err
	}
	return vdiskList, nil
}

func (s *vDiskService) ListByGuestId(ctx context.Context, guestId string, opts metav1.ListOptions) ([]string, string, error) {
	vdiskList, err := s.store.VDisks().List(ctx, opts)
	if err != nil {
		log.L(ctx).Warnf("[VDisk_Service] vdisk list failed - %v", err)
		return []string{}, "", err
	}

	var keysToDelete []string

	for vdiskId, vdisk := range vdiskList.Items {
		if vdisk.GuestID != guestId {
			keysToDelete = append(keysToDelete, vdiskId)
		}
	}

	for _, vdiskId := range keysToDelete {
		delete(vdiskList.Items, vdiskId)
		vdiskList.TotalCount--
	}

	vdiskIds := vdiskList.ListVDiskIDs()

	vdiskCfg, err := json.Marshal(vdiskList)
	if err != nil {
		log.L(ctx).Warnf("[VDisk_Service] vdisk list marshal failed - %v", err)
		return []string{}, "", err
	}

	return vdiskIds, string(vdiskCfg), nil
}

func (s *vDiskService) CheckStatus(ctx context.Context, guestId string) error {
	vm, _ := s.store.VMs().Get(ctx, guestId, metav1.GetOptions{})
	for _, vdId := range vm.VDisks {
		vdisk, err := s.store.VDisks().Get(ctx, vdId, metav1.GetOptions{})
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] vdisk check status failed - backend store not exist - guestId: %s - %v", guestId, err)
			return gerror.Wrapf(err, "vdisk check - backend store not exist")
		}

		getRepo := func(ctx context.Context, repoID string) (string, error) {
			return s.locateVDiskDir(ctx, repoID)
		}

		if _, err := vdisk.GetPath(ctx, getRepo); err != nil {
			log.L(ctx).Warnf("[VDisk_Service] vdisk check status failed - sparse file get failed - guestId: %s - %v", guestId, err)
			return gerror.Wrapf(err, "vdisk check - sparse file get failed")
		}
	}

	return nil
}

func stdOutComand(stdOut io.Writer, command string, args ...string) error {
	cmd := utils.ShellWithCmd(context.Background(), command, args...)
	cmd.Stdout = stdOut
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("running command failed - %s", err.Error())
	}
	return nil
}

func regexpMustCompileFirst(regex string, s string) (string, bool) {
	re := regexp.MustCompile(regex)
	matches := re.FindStringSubmatch(s)
	if len(matches) < 1 {
		return "", false
	}
	return matches[1], true
}

func mountPointAvailableSpace(dir string) (int64, error) {
	stat := unix.Statfs_t{}
	err := unix.Statfs(dir, &stat)
	if err != nil {
		return -1, err
	}
	return int64(stat.Bavail) * stat.Bsize, nil
}

func (s *vDiskService) Clone(ctx context.Context, newGuestID string, vdiskIds []string, channel chan ProgressSync) error {
	if channel == nil {
		log.L(ctx).Errorf("[VDisk_Service] cloning vdisk failed - channel is nil")
		return gerror.New("vdisk clone - channel is nil")
	}

	var waitCloneFiles, cloningTmpFiles, cloningVDiskIds []string
	var sparseFiles, oldNewUUIDMap map[string]string
	var tmpDir string

	sparseFiles, oldNewUUIDMap = make(map[string]string), make(map[string]string)
	isCallBack := true
	defer func() {
		close(channel)

		if !isCallBack {
			return
		}

		for _, parse := range sparseFiles {
			os.RemoveAll(path.Dir(parse))
		}

		for _, id := range cloningVDiskIds {
			s.store.VDisks().Delete(ctx, id, metav1.DeleteOptions{})
		}

		for _, file := range cloningTmpFiles {
			os.Remove(file)
		}
	}()

	for index, id := range vdiskIds {
		vdisk, err := s.store.VDisks().Get(ctx, id, metav1.GetOptions{})
		if err != nil {
			log.L(ctx).Errorf("[VDisk_Service] cloning vdisk failed - '(id %s)'", id)
			return gerror.Wrapf(err, "vdisk clone - backend store get failed")
		}
		file, err := s.sparseFile(ctx, id, *vdisk)
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] cloning vdisk failed - %v", err)
			return gerror.Wrapf(err, "vdisk clone - sparesfile create failed")
		}
		waitCloneFiles = append(waitCloneFiles, file)

		vdisk.GuestID = newGuestID
		vdisk.LUNUUID = createLUNUUID()
		cloningVDiskIds = append(cloningVDiskIds, createVDiskUUID())
		oldNewUUIDMap[id] = cloningVDiskIds[index]

		sparseFilePath, err := s.sparseFile(ctx, cloningVDiskIds[index], *vdisk)
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] cloning vdisk failed - %v", err)
			return gerror.Wrapf(err, "vdisk clone - sparesefile create failed")
		}
		sparseFiles[cloningVDiskIds[index]] = sparseFilePath

		err = s.store.VDisks().Create(ctx, cloningVDiskIds[index], vdisk, metav1.CreateOptions{})
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] cloning vdisk failed - %v", err)
			return gerror.Wrapf(err, "vdisk clone - backend store create failed")
		}

		tmpDir, err = s.locatTempDir(vdiskIds[index])
		if err != nil {
			log.L(ctx).Errorf("[VDisk_Service] clone vdisk failed - locat tempdir %v", err)
			return gerror.Wrapf(err, "vdisk clone - temp dir create failed")
		}
		tmpCloneFile, err := os.CreateTemp(tmpDir, "vms_vdiks_clone_*")
		if err != nil {
			log.L(ctx).Errorf("[VDisk_Service] clone vdisk failed - locat tempdir %v", err)
			return gerror.Wrapf(err, "vdisk clone - tmp clone file create failed")
		}
		utils.ExpandSparseSizeForFile(tmpCloneFile.Name(), vdisk.Size)
		cloningTmpFiles = append(cloningTmpFiles, tmpCloneFile.Name())
		tmpCloneFile.Close()
	}

	utils.CopyFileWithProgress(waitCloneFiles, cloningTmpFiles, func(progress int, isFinish bool, err error) {
		if err != nil {
			utils.NonBlockWriteChan(channel, ProgressSync{VmCloneError.Code(), progress, err, nil, nil})
			return
		}

		if !isFinish {
			utils.NonBlockWriteChan(channel, ProgressSync{gcodeOK, progress, nil, nil, nil})
			return
		}

		for idx, file := range cloningTmpFiles {
			if err := os.Rename(file, sparseFiles[cloningVDiskIds[idx]]); err != nil {
				utils.NonBlockWriteChan(channel, ProgressSync{VmCloneError.Code(), -1, err, nil, nil})
				return
			}
		}

		isCallBack = false
		utils.NonBlockWriteChan(channel, ProgressSync{gcodeOK, progress, nil, oldNewUUIDMap, nil})
	})

	return nil
}

func (s *vDiskService) Export(ctx context.Context, vdiskIds []string, channel chan ProgressSync) error {
	if channel == nil {
		log.L(ctx).Errorf("[VDisk_Service] exporting vdisk failed - channel is nil")
		return gerror.New("vdisk export - channel is nil")
	}

	isCallBack := true
	vmdks := make(map[string]string, 0)
	defer func() {
		close(channel)

		if isCallBack {
			for _, vmdk := range vmdks {
				os.Remove(vmdk)
			}
		}
	}()

	tmpDir := ""
	parseFiles := make([]string, 0)
	tempVmdks := make([]string, 0)
	for _, vdiskId := range vdiskIds {
		vdisk, err := s.store.VDisks().Get(ctx, vdiskId, metav1.GetOptions{})
		if err != nil {
			log.L(ctx).Errorf("[VDisk_Service] exporting vdisk failed - '(id %s)'", vdiskId)
			return gerror.Wrapf(err, "vdisk export - backend store get failed")
		}

		parseFile, err := s.sparseFile(ctx, vdiskId, *vdisk)
		if err != nil {
			log.L(ctx).Errorf("[VDisk_Service] exporting vdisk failed - '(id %s)'", vdiskId)
			return gerror.Wrapf(err, "vdisk export - sparsefile create failed")
		}
		parseFiles = append(parseFiles, parseFile)

		tmpDir, err = s.locatTempDir(vdiskId)
		if err != nil {
			log.L(ctx).Errorf("[VDisk_Service] exporting vdisk failed - '(id %s)'", vdiskId)
			return gerror.Wrapf(err, "vdisk export - temp dir create failed")
		}

		vmdk, err := os.CreateTemp(tmpDir, path.Base(parseFile)+".*")
		if err != nil {
			log.L(ctx).Errorf("[VDisk_Service] exporting vdisk failed - %v", err)
			return gerror.Wrapf(err, "vdisk export - temp file create failed")
		}
		tempVmdks = append(tempVmdks, vmdk.Name())
		vmdk.Close()
	}

	needSpace, parseUseSpace, _ := utils.FilesActualUseTotalSpace(parseFiles)
	availableSpace, _ := mountPointAvailableSpace(tmpDir)
	if availableSpace < needSpace {
		return gerror.New("vdisk export - available space not enough")
	}

	totalProgress := 0
	for iter, file := range tempVmdks {
		pr, pw := io.Pipe()
		convertionErr := error(nil)
		go func() {
			convertionErr = stdOutComand(pw, "/usr/local/VirtualMachines/bin/qemu-img",
				"convert",
				"-p",
				"-O",
				"vmdk",
				"-o",
				"subformat=streamOptimized,compat6",
				parseFiles[iter],
				file)
			pw.Close()
		}()

		vdiskId := vdiskIds[iter]
		vmdks[vdiskId] = file
		percentage := int(float64(parseUseSpace[parseFiles[iter]]) / float64(needSpace) * 100)
		for {
			readprogress := make([]byte, 20)
			_, readErr := pr.Read(readprogress)

			if readErr == io.EOF || convertionErr != nil {
				break
			}

			first, _ := regexpMustCompileFirst(`\(\s*(\d+)(?:\.\d+)?\s*/`, string(readprogress))
			exporting, err := strconv.Atoi(first)
			if err == nil {
				utils.NonBlockWriteChan(channel, ProgressSync{gcodeOK, (totalProgress + percentage*exporting) / 100, nil, nil, nil})
			}
		}

		if convertionErr != nil {
			utils.NonBlockWriteChan(channel, ProgressSync{
				gcodeOK,
				-1,
				gerror.Newf("vdisk export - qemu-img exec faild '(%s)'", convertionErr.Error()),
				nil, nil})
			return nil
		}
		totalProgress += percentage * 100
	}

	for _, vmdk := range vmdks {
		_, err := os.Open(vmdk)
		if os.IsNotExist(err) {
			utils.NonBlockWriteChan(channel, ProgressSync{
				gcodeOK,
				-1,
				gerror.Newf("vdisk export - os open file faild '(%s)'", err.Error()),
				nil, nil})
			return nil
		}
	}

	isCallBack = false
	utils.NonBlockWriteChan(channel, ProgressSync{gcodeOK, 100, nil, vmdks, nil})
	return nil
}

func (s *vDiskService) Import(ctx context.Context, importFiles []string, vdisks []domain.VDisk, channel chan ProgressSync) error {
	if len(importFiles) != len(vdisks) ||
		channel == nil {
		log.L(ctx).Warnf("[VDisk_Service] importing vdisk failed - has error's args")
		return gerror.New("vdisk import - channel is nil")
	}

	isCallBack := true
	sparseFiles := make([]string, 0)
	vdiskIDs := make(map[string]string, len(vdisks))
	defer func() {
		close(channel)
		//删除vmdk文件
		for _, vmdk := range importFiles {
			os.Remove(vmdk)
		}
		if !isCallBack {
			return
		}

		for _, parse := range sparseFiles {
			os.Remove(parse)
		}

		for _, id := range vdiskIDs {
			s.store.VDisks().Delete(ctx, id, metav1.DeleteOptions{})
		}
	}()

	for i := 0; i < len(vdisks); i++ {
		vdisks[i].LUNUUID = createLUNUUID()
		vdiskIDs[importFiles[i]] = createVDiskUUID()
	}

	needSpace, vmdkUseSpace, err := utils.FilesActualUseTotalSpace(importFiles)
	if err != nil {
		log.L(ctx).Warnf("[VDisk_Service] importing vdisk failed - %v", err)
		return gerror.Wrapf(err, "vdisk import - file space failed")
	}

	for index, vdisk := range vdisks {
		sparseFilePath, err := s.sparseFile(ctx, vdiskIDs[importFiles[index]], vdisk)
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] importing vdisk failed - %v", err)
			return gerror.Wrapf(err, "vdisk import - sparsefile create failed")
		}
		sparseFiles = append(sparseFiles, sparseFilePath)

		err = s.store.VDisks().Create(context.Background(), vdiskIDs[importFiles[index]], &vdisk, metav1.CreateOptions{})
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] importing vdisk failed - %v", err)
			return gerror.Wrapf(err, "vdisk import - backend store create failed")
		}
	}

	totalProgress := 0
	for index, vmdk := range importFiles {
		pr, pw := io.Pipe()
		convertionErr := error(nil)
		go func() {
			convertionErr = stdOutComand(pw, "/usr/local/VirtualMachines/bin/qemu-img",
				"convert",
				"--target-is-zero",
				"-p",
				"-n",
				"-O",
				"raw",
				vmdk,
				sparseFiles[index])
			pw.Close()
		}()

		percentage := int(float64(vmdkUseSpace[importFiles[index]]) / float64(needSpace) * 100)
		for {
			readprogress := make([]byte, 20)
			_, readErr := pr.Read(readprogress)

			if readErr == io.EOF || convertionErr != nil {
				break
			}
			first, _ := regexpMustCompileFirst(`\(\s*(\d+)(?:\.\d+)?\s*/`, string(readprogress))
			exporting, err := strconv.Atoi(first)
			if err != nil {
				continue
			}

			utils.NonBlockWriteChan(channel, ProgressSync{gcodeOK, (totalProgress + percentage*exporting) / 100, nil, nil, nil})
		}

		if convertionErr != nil {
			utils.NonBlockWriteChan(channel, ProgressSync{
				gcodeOK,
				-1,
				gerror.Newf("vdisk import - convertion err '(%s)'", convertionErr.Error()),
				nil, nil})
			return nil
		}
		totalProgress += percentage * 100
	}

	isCallBack = false
	utils.NonBlockWriteChan(channel, ProgressSync{gcodeOK, 100, nil, vdiskIDs, nil})
	return nil
}

func (s *vDiskService) getVDiskDevice(ctx context.Context, vdisk domain.VDisk) (string, error) {
	if vdisk.VDiskMode == domain.TemplateDiskDriverVirtioSCSI {
		return "", nil
	}

	// 1. 获取SCSI地址
	naaPath := fmt.Sprintf("/sys/kernel/config/target/loopback/%s/tpgt_1/address", vdisk.LUNUUID)
	addressBytes, err := os.ReadFile(naaPath)
	if err != nil {
		log.L(ctx).Warnf("[VDisk_Service] scsiaddress read failed - %v", err)
		return "", fmt.Errorf("can't get scsi address")
	}
	scsiAddress := strings.TrimSpace(string(addressBytes))
	fmt.Println("scsiAddress: ", scsiAddress)

	// 2. 解析地址格式 (host:channel:target)
	parts := strings.Split(scsiAddress, ":")
	if len(parts) != 3 {
		log.L(ctx).Warnf("[VDisk_Service] scsiaddress format invalid - %s", scsiAddress)
		return "", fmt.Errorf("invalid scsi address format")
	}
	hostNumber := parts[0]

	// 3. 遍历适配器查找匹配路径
	adapters, _ := filepath.Glob("/sys/devices/tcm_loop_*/tcm_loop_adapter_*")
	for _, adapterPath := range adapters {
		// 获取适配器对应的host编号
		hostPath := filepath.Join(adapterPath, fmt.Sprintf("host%s", hostNumber))
		if _, err := os.Stat(hostPath); os.IsNotExist(err) {
			continue
		}

		// 4. 构建目标路径模式
		targetPattern := filepath.Join(
			hostPath,
			fmt.Sprintf("target%s:%s:%s", hostNumber, parts[1], parts[2]),
			fmt.Sprintf("%s:%s:%s:0", hostNumber, parts[1], parts[2]),
			"block/*",
		)

		// 5. 使用通配符匹配设备路径
		matches, _ := filepath.Glob(targetPattern)
		for _, blockPath := range matches {
			// 获取设备名称（目录的最后一部分）
			if stat, err := os.Lstat(blockPath); err == nil && stat.Mode()&os.ModeSymlink == 0 {
				vdisk.VDiskDevice = "/dev/" + filepath.Base(blockPath)
				log.L(ctx).Debugf("[VDisk_Service] find vdisk device - %s", vdisk.VDiskDevice)
				return vdisk.VDiskDevice, nil
			}
		}
	}

	log.L(ctx).Warnf("[VDisk_Service] vdiskDevice cannot find - scsi address %s", scsiAddress)
	return "", fmt.Errorf("can't find vdisk device")
}

// 创建虚拟盘稀疏文件
func (s *vDiskService) createVDiskFile(ctx context.Context, vdiskUUID string, vdisk domain.VDisk) error {
	if vdiskUUID == "" || vdisk == (domain.VDisk{}) {
		log.L(ctx).Warnf("[VDisk_Service] vdiskuuid or vdisk is nil")
		return fmt.Errorf("vdiskuuid or vdisk is nil")
	}

	_, err := s.sparseFile(ctx, vdiskUUID, vdisk)
	if err != nil {
		log.L(ctx).Warnf("[VDisk_Service] sparsefile create failed - %v", err)
		s.gobackVdiskFile(ctx, vdisk)
		return err
	}

	return nil
}

// 通过targetcli创建虚拟盘
func (s *vDiskService) createVDiskbyTargetcli(ctx context.Context, vdiskPath string, vdisk domain.VDisk) error {
	exec.Command("killall", "-q", "targetcli").Run()
	var (
		retryErr = &RetryError{}
	)
	// 带退避的重试机制
	for attempt := 1; attempt <= maxRetries; attempt++ {
		// fileio创建存储后端

		log.Debugf("[VMs_Service] attempt cmd before vdisk %v", vdisk)
		cmd := exec.Command("targetcli", "/backstores/fileio", "create",
			"name="+vdisk.LUNUUID,
			"file_or_dev="+vdiskPath,
			"wwn="+vdisk.LUNUUID)

		var stdout, stderr bytes.Buffer
		cmd.Stdout = &stdout
		cmd.Stderr = &stderr

		if err := cmd.Run(); err != nil {
			// 记录错误详情
			retryErr.Errors = append(retryErr.Errors, fmt.Errorf("attempt%d:%w", attempt, err))
			retryErr.Attempts = attempt

			log.L(ctx).Warnf("[VDisk_Service] create failed (attempt%d/%d)", attempt, maxRetries)
			log.L(ctx).Warnf("stdout:%q\nstderr:%q", stdout.String(), stderr.String())

			// 最后一次尝试后退出循环
			if attempt == maxRetries {
				break
			}

			// 清理残留并等待重试
			s.preventTargetcliNotDeleted(vdisk)
			time.Sleep(time.Duration(attempt) * retryDelay)
			continue
		}

		// 成功执行后续操作
		s.createLUNProtocolByVDiskMode(ctx, vdisk)
		return nil
	}

	// 全部重试失败后处理
	log.L(ctx).Errorf("[VDisk_Service] failed after %d attempts", maxRetries)
	log.L(ctx).Errorf("Python env:%s", os.Getenv("PYTHONPATH"))
	s.gobackVdiskTargetcli(ctx)
	return retryErr
}

// 防止targetcli未删除操作
func (s *vDiskService) preventTargetcliNotDeleted(vdisk domain.VDisk) {
	exec.Command("targetcli", "/loopback", "delete", "wwn="+vdisk.LUNUUID).Run()
	exec.Command("targetcli", "/vhost", "delete", "wwn="+vdisk.LUNUUID).Run()
	exec.Command("targetcli", "/backstores/fileio", "delete", vdisk.LUNUUID).Run()
}

// 保存targetcli信息
func (s *vDiskService) saveTargetcliInfo(ctx context.Context, repositoryUUID string) error {
	targetcliPath, err := s.locateVDiskDir(ctx, repositoryUUID)
	if err != nil {
		log.L(ctx).Warnf("[VDisk_Service] repository path get failed - %v", err)
		return err
	}
	cmd := exec.Command("targetcli", "saveconfig", targetcliPath+"/"+repositoryUUID+".targetcli.json")
	if err := cmd.Run(); err != nil {
		log.L(ctx).Warnf("[VDisk_Service] targetcli config save failed - %v", err)
		return err
	}
	return nil
}

// 删除LUN相关协议
func deleteLUNProtocolByVDiskMode(ctx context.Context, vdisk domain.VDisk, oriVdiskMode int) error {
	var prefix string
	if oriVdiskMode == domain.TemplateDiskDriverVirtioSCSI {
		prefix = "/vhost"
	} else if oriVdiskMode == domain.TemplateDiskDriverIDE || oriVdiskMode == domain.TemplateDiskDriverSATA || oriVdiskMode == domain.TemplateDiskDriverVirtioBlock {
		prefix = "/loopback"
	} else {
		log.L(ctx).Warnf("[VDisk_Service] vdisk mode not support")
		return fmt.Errorf("vdisk mode is not support")
	}

	cmd := exec.Command("targetcli", prefix, "delete",
		"wwn="+vdisk.LUNUUID)
	log.L(ctx).Infof("[VDisk_Service] targetcli delete command:%s", cmd.String())
	if err := cmd.Run(); err != nil {
		log.L(ctx).Warnf("[VDisk_Service] vdisk type %s delete failed - by targetcli %v", prefix, err)
		return err
	}

	return nil
}

// 建立LUN相关协议挂载虚拟盘
func (s *vDiskService) createLUNProtocolByVDiskMode(ctx context.Context, vdisk domain.VDisk) error {
	var prefix, istpg string
	if vdisk.VDiskMode == domain.TemplateDiskDriverVirtioSCSI {
		prefix = "/vhost"
		istpg = "tpg1/"
	} else if vdisk.VDiskMode == domain.TemplateDiskDriverIDE || vdisk.VDiskMode == domain.TemplateDiskDriverSATA || vdisk.VDiskMode == domain.TemplateDiskDriverVirtioBlock {
		prefix = "/loopback"
		istpg = ""
	} else {
		log.L(ctx).Warnf("[VDisk_Service] vdisk mode not support")
		return fmt.Errorf("vdisk mode is not support")
	}

	// 建立wwn
	cmd := exec.Command("targetcli", prefix, "create",
		"wwn="+vdisk.LUNUUID)
	if err := cmd.Run(); err != nil {
		log.L(ctx).Warnf("[VDisk_Service] vdisk vhost create failed - by targetcli %v", err)
		return err
	}

	// 与存储后端fileio链接
	cmd = exec.Command("targetcli",
		prefix+"/"+vdisk.LUNUUID+"/"+istpg+"luns",
		"create",
		"/backstores/fileio/"+vdisk.LUNUUID,
	)
	if err := cmd.Run(); err != nil {
		log.L(ctx).Warnf("[VDisk_Service] vdisk vhost create failed - by targetcli %v", err)
		return err
	}

	return nil
}

// 回滚，删除虚拟盘
func (s *vDiskService) gobackVdiskFile(ctx context.Context, vdisk domain.VDisk) {
	repoPath, _ := s.locateVDiskDir(ctx, vdisk.RepositoryID)
	lunPath := filepath.Join(repoPath, vdisk.LUNUUID)

	if err := os.RemoveAll(lunPath); err != nil {
		log.L(ctx).Warnf("[VDisk_Service] rollback vdisk_file failed - %v", err)
	}
	log.L(ctx).Infof("[VDisk_Service] rollback vdisk_file success - clear all targetcli")
}

// 回滚，清除所有targetcli配置
func (s *vDiskService) gobackVdiskTargetcli(ctx context.Context) {
	cmd := exec.Command("targetcli", "clearconfig", "true")
	if err := cmd.Run(); err != nil {
		log.L(ctx).Warnf("[VDisk_Service] rollback vdisk_targetcli failed - %v", err)
	}
	log.L(ctx).Infof("[VDisk_Service] rollback vdisk_targetcli success - clear all targetcli")
}

// 获取存储池路径/Volume1
func (s *vDiskService) locateLunPath(ctx context.Context, repositoryID string, LUNUUID string) (string, error) {
	targetPath, err := s.locateVDiskDir(ctx, repositoryID)
	if err != nil {
		log.L(ctx).Errorf("[VDisk_Service] getting repository %s failed - %v", repositoryID, err)
		return "", err
	}

	if _, err := os.Stat(targetPath); os.IsNotExist(err) {
		err = os.MkdirAll(targetPath, 0755)
		if err != nil {
			log.L(ctx).Warnf("[VDisk_Service] path %s create failed - %v", targetPath, err)
			return "", err
		}
	}

	vdiskPath := filepath.Join(targetPath, LUNUUID)
	if err := os.Mkdir(vdiskPath, 0755); err != nil && !os.IsExist(err) {
		log.L(ctx).Warnf("[VDisk_Service] path %s create failed - %v", vdiskPath, err)
		return "", err
	}

	return vdiskPath, nil
}

func (s *vDiskService) locateVDiskDir(ctx context.Context, repositoryID string) (string, error) {
	repository, err := s.store.RepositoryPool().Get(ctx, repositoryID, metav1.GetOptions{})
	if err != nil {
		return "", err
	}
	targetPath := filepath.Join(repository.LocationVolume, domain.VDiskVolumePath)
	return targetPath, nil
}

// 创建虚拟盘的UUID
func createVDiskUUID() string {
	UUID := uuid.Must(uuid.NewV4()).String()
	return UUID
}

// 生成 NAA 格式的 UUID (e.g. naa.50014055f714d119)
func createNAAUUID() (string, error) {
	// 生成 7 字节随机数 + 1 字节固定前缀
	b := make([]byte, 8)
	if _, err := rand.Read(b); err != nil {
		return "", err
	}

	// 固定第一个字节的高四位为 0101 (0x5)
	b[0] = (b[0] & 0x0F) | 0x50 // 0x50 = 0101 0000

	// 格式化为 NAA 标准格式
	return fmt.Sprintf("naa.%02x%02x%02x%02x%02x%02x%02x%02x",
		b[0], b[1], b[2], b[3], b[4], b[5], b[6], b[7]), nil
}

// 创建LUN的UUID
func createLUNUUID() string {
	UUID, err := createNAAUUID()
	if err != nil {
		log.Warnf("[VDisk_Service] naauuid create failed - %v", err)
		return ""
	}
	return UUID
}

// 删除Targetcli配置信息
func (s *vDiskService) deleteTargetcli(ctx context.Context, vdisk domain.VDisk) error {
	exec.Command("killall", "-q", "targetcli").Run()

	// 消除LUN协议
	deleteLUNProtocolByVDiskMode(ctx, vdisk, int(vdisk.VDiskMode))
	// 消除fileio
	if err := exec.Command("targetcli", "/backstores/fileio", "delete", vdisk.LUNUUID).Run(); err != nil {
		log.L(ctx).Warnf("[VDisk_Service] targetcli %s delete failed - %v", vdisk.LUNUUID, err)
		return err
	}
	return nil
}

// 定义重试常量
const (
	maxRetries = 3
	retryDelay = 500 * time.Millisecond
)

// 创建带错误聚合的自定义错误类型
type RetryError struct {
	Attempts int
	Errors   []error
}

func (e *RetryError) Error() string {
	return fmt.Sprintf("after %d attempts, last error: %v", e.Attempts, e.Errors[len(e.Errors)-1])
}

type vDiskService struct {
	store      store.Factory
	redisStore store.RedisFactory
}

func newVDisk(s service) vDiskService {
	return &vDiskService{
		store:      s.store,
		redisStore: s.redis,
	}
}

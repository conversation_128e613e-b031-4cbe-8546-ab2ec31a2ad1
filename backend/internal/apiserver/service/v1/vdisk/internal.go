package vdisk_service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/gogf/gf/errors/gerror"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/vdisk/dto"
)

// VDiskInternalSrv provides internal vdisk related services
type VDiskInternalSrv struct {
	store           store.Factory
	repoInternalSrv RepositoryInternalSrv
	vmInternalSrv   VMInternalSrv
	storageInternalSrv StorageInternalSrv
}

// NewVDiskInternalSrv creates a new VDiskInternalSrv instance
func NewVDiskInternalSrv(
	store store.Factory,
	repoInternalSrv RepositoryInternalSrv,
	vmInternalSrv VMInternalSrv,
	storageInternalSrv StorageInternalSrv,
) *VDiskInternalSrv {
	return &VDiskInternalSrv{
		store:              store,
		repoInternalSrv:    repoInternalSrv,
		vmInternalSrv:      vmInternalSrv,
		storageInternalSrv: storageInternalSrv,
	}
}

// GetVDiskFilePaths returns the file paths for given vdisk IDs
func (srv *VDiskInternalSrv) GetVDiskFilePaths(vdiskIDs []string) ([]string, error) {
	vdiskList, err := srv.store.VDisks().List(context.Background(), metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	filePaths := make([]string, 0, len(vdiskIDs))
	for _, vdiskID := range vdiskIDs {
		// Handle special case for "not used" device
		if strings.EqualFold(vdiskID, domain.DeviceNotUse) {
			filePaths = append(filePaths, "")
			continue
		}

		// Check if path exists directly
		if _, err := os.Stat(vdiskID); err == nil {
			filePaths = append(filePaths, vdiskID)
			continue
		}

		// Lookup in stored vdisks
		vdisk, ok := vdiskList.Items[vdiskID]
		if !ok {
			return nil, gerror.WrapCode(VDiskNotFound, fmt.Errorf("vdisk [%s] not found", vdiskID))
		}

		// Try to locate vdisk in repository
		repoPath, err := srv.repoInternalSrv.LocatePoolPath(vdisk.RepositoryID)
		if err != nil {
			return nil, gerror.WrapCode(VDiskStorageError, fmt.Errorf("failed to locate repository path: %v", err))
		}

		vdiskFilePath := srv.buildVDiskFilePath(repoPath, vdisk.LUNUUID)
		if _, err := os.Stat(vdiskFilePath); err == nil {
			filePaths = append(filePaths, vdiskFilePath)
		} else {
			return nil, gerror.WrapCode(VDiskNotFound, fmt.Errorf("vdisk file not found: %s", vdiskFilePath))
		}
	}

	return filePaths, nil
}

// DeleteVDiskList deletes vdisks from repository and optionally from storage
func (srv *VDiskInternalSrv) DeleteVDiskList(repoID string, locationVolume string, needSaveData bool) error {
	vdiskList, err := srv.store.VDisks().List(context.Background(), metav1.ListOptions{})
	if err != nil {
		log.Errorf("[VDisk_Internal] delete vdisk list failed - Backend storage failed to list: %v", err)
		return err
	}

	delVDisks := make([]string, 0)
	// First pass: find vdisks to delete and update repositories
	for vdiskID, vdisk := range vdiskList.Items {
		if vdisk.RepositoryID == repoID {
			delVDisks = append(delVDisks, vdiskID)
		}
	}

	// Second pass: delete vdisks
	for _, vdiskID := range delVDisks {
		if err := srv.store.VDisks().Delete(context.Background(), vdiskID, metav1.DeleteOptions{}); err != nil {
			log.Warnf("[VDisk_Internal] delete vdisk failed - Backend storage failed to delete (vdisk id %s): %v", vdiskID, err)
		}
	}

	// If we need to save data, return early
	if needSaveData {
		return nil
	}

	// Delete vdisk directories
	for _, vdiskID := range delVDisks {
		vdisk := vdiskList.Items[vdiskID]
		vdiskDir := srv.buildVDiskDirPath(locationVolume, vdisk.LUNUUID)
		if err := os.RemoveAll(vdiskDir); err != nil {
			log.Warnf("[VDisk_Internal] file removing failed: %v", err)
		}
	}

	return nil
}

// RestoreVDiskList restores vdisks from storage to repository
func (srv *VDiskInternalSrv) RestoreVDiskList(repoID string, needImportData bool) error {
	location, err := srv.repoInternalSrv.LocatePoolPath(repoID)
	if err != nil {
		log.Errorf("[VDisk_Internal] repository pool getting failed - Backend storage failed to get (repository pool id %s): %v", repoID, err)
		return err
	}

	vdiskDir := filepath.Join(location, domain.VDiskVolumePath)

	err = filepath.WalkDir(vdiskDir, func(filePath string, d os.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// Clean up directories if not importing data
		if d.IsDir() && !needImportData && filePath != vdiskDir {
			return os.RemoveAll(filePath)
		}

		// Process vdisk directories
		if d.IsDir() && filePath != vdiskDir {
			lunUUID := filepath.Base(filePath)
			
			// Check if vdisk already exists
			existingVDisks, err := srv.store.VDisks().List(context.Background(), metav1.ListOptions{})
			if err != nil {
				log.Warnf("[VDisk_Internal] vdisk restoring failed - failed to list existing vdisks: %v", err)
				return nil
			}

			// Find vdisk with matching LUNUUID
			var existingVDiskID string
			for vdiskID, vdisk := range existingVDisks.Items {
				if vdisk.LUNUUID == lunUUID {
					existingVDiskID = vdiskID
					break
				}
			}

			if existingVDiskID != "" {
				// VDisk exists, update repository
				existingVDisk := existingVDisks.Items[existingVDiskID]
				existingVDisk.RepositoryID = repoID
				if err := srv.store.VDisks().Update(context.Background(), existingVDiskID, existingVDisk, metav1.UpdateOptions{}); err != nil {
					log.Warnf("[VDisk_Internal] vdisk restoring failed - (vdisk id %s): %v", existingVDiskID, err)
				}
			} else {
				// New vdisk, create from directory structure
				vdisk := &domain.VDisk{
					LUNUUID:      lunUUID,
					RepositoryID: repoID,
					// Other fields would need to be determined from file system or defaults
					Format:     2,
					Type:       2,
					VDiskMode:  domain.TemplateDiskDriver(2), // Default to SATA
				}

				// Try to determine size from actual file
				if srv.storageInternalSrv != nil {
					vdiskFilePath := srv.buildVDiskFilePath(location, lunUUID)
					if fileInfo, err := srv.storageInternalSrv.GetFileInfo(vdiskFilePath); err == nil {
						if size, ok := fileInfo["size"].(int64); ok {
							vdisk.Size = size
						}
					}
				}

				newVDiskID := srv.generateVDiskUUID()
				if err := srv.store.VDisks().Create(context.Background(), newVDiskID, vdisk, metav1.CreateOptions{}); err != nil {
					log.Warnf("[VDisk_Internal] vdisk restoring failed - (vdisk id %s): %v", newVDiskID, err)
				}
			}
		}

		return nil
	})

	return err
}

// GetVDisksByVM returns all vdisks attached to a specific VM
func (srv *VDiskInternalSrv) GetVDisksByVM(vmID string) (map[string]*domain.VDisk, error) {
	vdiskList, err := srv.store.VDisks().List(context.Background(), metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	vmVDisks := make(map[string]*domain.VDisk)
	for vdiskID, vdisk := range vdiskList.Items {
		if vdisk.GuestID == vmID {
			vmVDisks[vdiskID] = vdisk
		}
	}

	return vmVDisks, nil
}

// GetVDiskUsage returns usage information for vdisks
func (srv *VDiskInternalSrv) GetVDiskUsage(vdiskIDs []string) (map[string]*dto.VDiskUsage, error) {
	usage := make(map[string]*dto.VDiskUsage)
	
	for _, vdiskID := range vdiskIDs {
		vdisk, err := srv.store.VDisks().Get(context.Background(), vdiskID, metav1.GetOptions{})
		if err != nil {
			continue
		}

		// Get repository path
		repoPath, err := srv.repoInternalSrv.LocatePoolPath(vdisk.RepositoryID)
		if err != nil {
			continue
		}

		// Get file info
		vdiskFilePath := srv.buildVDiskFilePath(repoPath, vdisk.LUNUUID)
		if srv.storageInternalSrv != nil {
			if fileInfo, err := srv.storageInternalSrv.GetFileInfo(vdiskFilePath); err == nil {
				usage[vdiskID] = &dto.VDiskUsage{
					Size: float64(vdisk.Size),
					Used: 0, // Would need to be calculated from actual usage
				}
				if usedSize, ok := fileInfo["used_size"].(float64); ok {
					usage[vdiskID].Used = usedSize
				}
			}
		}
	}

	return usage, nil
}

// Helper methods

// buildVDiskFilePath builds the full path for a vdisk file
func (srv *VDiskInternalSrv) buildVDiskFilePath(repoPath, lunUUID string) string {
	return filepath.Join(repoPath, domain.VDiskVolumePath, lunUUID, "vdisk.img")
}

// buildVDiskDirPath builds the directory path for a vdisk
func (srv *VDiskInternalSrv) buildVDiskDirPath(repoPath, lunUUID string) string {
	return filepath.Join(repoPath, domain.VDiskVolumePath, lunUUID)
}

// generateVDiskUUID generates a new UUID for vdisk
func (srv *VDiskInternalSrv) generateVDiskUUID() string {
	// This should use the same UUID generation as the main service
	// For now, using a simple implementation
	return fmt.Sprintf("vdisk-%d", len(srv.store.VDisks().List(context.Background(), metav1.ListOptions{})))
}

// VDiskUsage represents vdisk usage information
type VDiskUsage struct {
	Size float64 `json:"size"` // Total size in bytes
	Used float64 `json:"used"` // Used size in bytes
}

package vdisk_service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/vdisk/dto"
)

// Mock implementations

type mockVDiskStore struct {
	mock.Mock
	mu    sync.Mutex
	store map[string]*domain.VDisk
}

func newMockVDiskStore() *mockVDiskStore {
	return &mockVDiskStore{
		store: make(map[string]*domain.VDisk),
	}
}

func (m *mockVDiskStore) Create(ctx context.Context, vdiskID string, vdisk *domain.VDisk, opts metav1.CreateOptions) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if len(m.Mock.ExpectedCalls) > 0 {
		args := m.Called(ctx, vdiskID, vdisk, opts)
		return args.Error(0)
	}
	
	m.store[vdiskID] = vdisk
	return nil
}

func (m *mockVDiskStore) Update(ctx context.Context, vdiskID string, vdisk *domain.VDisk, opts metav1.UpdateOptions) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if len(m.Mock.ExpectedCalls) > 0 {
		args := m.Called(ctx, vdiskID, vdisk, opts)
		return args.Error(0)
	}
	
	m.store[vdiskID] = vdisk
	return nil
}

func (m *mockVDiskStore) Get(ctx context.Context, vdiskID string, opts metav1.GetOptions) (*domain.VDisk, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if vdisk, ok := m.store[vdiskID]; ok {
		return vdisk, nil
	}
	return nil, fmt.Errorf("vdisk not found")
}

func (m *mockVDiskStore) Delete(ctx context.Context, vdiskID string, opts metav1.DeleteOptions) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	delete(m.store, vdiskID)
	return nil
}

func (m *mockVDiskStore) List(ctx context.Context, opts metav1.ListOptions) (*domain.VDiskList, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	list := &domain.VDiskList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(m.store)),
		},
		Items: make(map[string]*domain.VDisk),
	}
	
	for id, vdisk := range m.store {
		list.Items[id] = vdisk
	}
	
	return list, nil
}

func (m *mockVDiskStore) ListUUID(ctx context.Context, opts metav1.ListOptions) ([]string, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	uuids := make([]string, 0, len(m.store))
	for id := range m.store {
		uuids = append(uuids, id)
	}
	return uuids, nil
}

type mockStoreFactory struct {
	vdiskStore *mockVDiskStore
}

func (m *mockStoreFactory) VDisks() store.VDisk {
	return m.vdiskStore
}

func (m *mockStoreFactory) Networks() store.NetworkStore { return nil }
func (m *mockStoreFactory) Images() store.ImageStore { return nil }
func (m *mockStoreFactory) RepositoryPool() store.RepositoryPoolStore { return nil }
func (m *mockStoreFactory) Host() store.HostStore { return nil }
func (m *mockStoreFactory) VMs() store.VMStore { return nil }
func (m *mockStoreFactory) VNics() store.VNicStore { return nil }
func (m *mockStoreFactory) Logs() store.Log { return nil }
func (m *mockStoreFactory) Close() error { return nil }

type mockRepositoryService struct {
	mock.Mock
}

func (m *mockRepositoryService) LocatePoolPath(repoId string) (string, error) {
	args := m.Called(repoId)
	return args.String(0), args.Error(1)
}

func (m *mockRepositoryService) ListPoolNames(repoIds []string) ([]string, error) {
	args := m.Called(repoIds)
	return args.Get(0).([]string), args.Error(1)
}

func (m *mockRepositoryService) GetPoolInfo(repoId string) (map[string]interface{}, error) {
	args := m.Called(repoId)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

type mockVMService struct {
	mock.Mock
}

func (m *mockVMService) ListVMsByVDisk(vdiskId string) (map[string]string, error) {
	args := m.Called(vdiskId)
	return args.Get(0).(map[string]string), args.Error(1)
}

func (m *mockVMService) IsVMRunning(vmId string) bool {
	args := m.Called(vmId)
	return args.Bool(0)
}

func (m *mockVMService) DetachVDiskFromVM(vmId, vdiskId string) error {
	args := m.Called(vmId, vdiskId)
	return args.Error(0)
}

type mockStorageService struct {
	mock.Mock
	files map[string]int64
	mu    sync.Mutex
}

func newMockStorageService() *mockStorageService {
	return &mockStorageService{
		files: make(map[string]int64),
	}
}

func (m *mockStorageService) CreateSparseFile(path string, size int64) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if len(m.Mock.ExpectedCalls) > 0 {
		args := m.Called(path, size)
		if args.Error(0) == nil {
			m.files[path] = size
		}
		return args.Error(0)
	}
	
	m.files[path] = size
	return nil
}

func (m *mockStorageService) DeleteFile(path string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if len(m.Mock.ExpectedCalls) > 0 {
		args := m.Called(path)
		if args.Error(0) == nil {
			delete(m.files, path)
		}
		return args.Error(0)
	}
	
	delete(m.files, path)
	return nil
}

func (m *mockStorageService) ResizeFile(path string, newSize int64) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	args := m.Called(path, newSize)
	if args.Error(0) == nil {
		m.files[path] = newSize
	}
	return args.Error(0)
}

func (m *mockStorageService) GetFileInfo(path string) (map[string]interface{}, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if size, ok := m.files[path]; ok {
		return map[string]interface{}{
			"size": size,
			"used_size": float64(size / 2), // Mock 50% usage
		}, nil
	}
	return nil, fmt.Errorf("file not found")
}

type mockSystemService struct {
	mock.Mock
}

func (m *mockSystemService) ExecuteCommand(cmd string, args ...string) ([]byte, error) {
	mockArgs := m.Called(cmd, args)
	return mockArgs.Get(0).([]byte), mockArgs.Error(1)
}

func (m *mockSystemService) CheckDiskSpace(path string) (int64, error) {
	args := m.Called(path)
	return args.Get(0).(int64), args.Error(1)
}

func (m *mockSystemService) GetSystemInfo() (map[string]interface{}, error) {
	args := m.Called()
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

// Test setup helper
func setupTestService() (*VDiskService, *mockStoreFactory, *mockRepositoryService, *mockVMService, *mockStorageService, *mockSystemService) {
	vdiskStore := newMockVDiskStore()
	storeFactory := &mockStoreFactory{vdiskStore: vdiskStore}
	
	repoSrv := &mockRepositoryService{}
	vmSrv := &mockVMService{}
	storageSrv := newMockStorageService()
	systemSrv := &mockSystemService{}
	
	service := NewVDiskService(
		storeFactory,
		nil, // redis store not needed for these tests
		repoSrv,
		vmSrv,
		storageSrv,
		systemSrv,
	)
	
	return service, storeFactory, repoSrv, vmSrv, storageSrv, systemSrv
}

// Tests

func TestVDiskService_Create(t *testing.T) {
	service, _, repoSrv, _, storageSrv, systemSrv := setupTestService()
	defer service.Close()
	
	// Setup mocks
	repoSrv.On("LocatePoolPath", "repo1").Return("/tmp/test/repo1", nil)
	systemSrv.On("CheckDiskSpace", "/tmp/test/repo1").Return(int64(100*GByteUnit), nil)
	
	request := dto.BatchCreateRequest{
		GuestID: "vm1",
		VDisks: []dto.CreateRequest{
			{
				BaseVDisk: dto.BaseVDisk{
					Name:      "test-vdisk",
					VDiskMode: dto.VDiskModeSATA,
					Type:      dto.VDiskTypeData,
				},
				VDiskQoS: dto.VDiskQoS{
					DevLimit:       1000,
					DevReservation: 100,
					DevWeight:      100,
					IOPSEnable:     true,
				},
				RepositoryID: "repo1",
				SizeGB:       10,
			},
		},
	}
	
	response, err := service.Create(context.Background(), request)
	
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Len(t, response.VDiskIDs, 1)
	assert.NotEmpty(t, response.Config)
	
	// Verify vdisk was created in store
	vdisk, err := service.Get(context.Background(), response.VDiskIDs[0])
	assert.NoError(t, err)
	assert.Equal(t, "vm1", vdisk.GuestID)
	assert.Equal(t, int64(10*GByteUnit), vdisk.Size)
}

func TestVDiskService_Delete(t *testing.T) {
	service, storeFactory, _, vmSrv, storageSrv, _ := setupTestService()
	defer service.Close()
	
	// Create a test vdisk first
	vdiskID := "test-vdisk-1"
	vdisk := &domain.VDisk{
		GuestID:      "vm1",
		RepositoryID: "repo1",
		Size:         10 * GByteUnit,
		LUNUUID:      "lun-uuid-1",
	}
	storeFactory.vdiskStore.store[vdiskID] = vdisk
	
	// Setup mocks
	vmSrv.On("ListVMsByVDisk", vdiskID).Return(map[string]string{}, nil)
	
	request := dto.BatchDeleteRequest{
		GuestID:  "vm1",
		VDiskIDs: []string{vdiskID},
		Force:    false,
	}
	
	err := service.Delete(context.Background(), request)
	
	assert.NoError(t, err)
	
	// Verify vdisk was deleted from store
	_, err = service.Get(context.Background(), vdiskID)
	assert.Error(t, err)
}

package vdisk_service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"time"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/common/utils"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/aclog"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/vdisk/dto"
)

// Step interface for pipeline pattern
type Step interface {
	Execute(task *CreateVDiskTask) error
	RollBack(task *CreateVDiskTask)
}

// CreateVDiskTask represents a vdisk creation task
type CreateVDiskTask struct {
	ctx context.Context

	// Task information
	TaskID     uint64
	VDiskID    string
	GuestID    string
	CreateTime int64
	Status     dto.VDiskState

	// Request data
	Request dto.CreateRequest

	// Generated data
	VDisk    *domain.VDisk
	FilePath string

	// Services
	vdiskSrv *VDiskService
}

// VDiskCreator manages the vdisk creation pipeline
type VDiskCreator struct {
	service *VDiskService
}

// NewVDiskCreator creates a new VDiskCreator
func NewVDiskCreator(service *VDiskService) *VDiskCreator {
	return &VDiskCreator{
		service: service,
	}
}

// CreateVDisk creates a vdisk using the pipeline pattern
func (c *VDiskCreator) CreateVDisk(ctx context.Context, request dto.CreateRequest, guestID string) (*dto.Response, error) {
	// Create task
	task := &CreateVDiskTask{
		ctx:        ctx,
		TaskID:     c.service.taskGenerator(),
		VDiskID:    c.service.generateVDiskUUID(),
		GuestID:    guestID,
		CreateTime: time.Now().Unix(),
		Status:     dto.VDiskStateCreating,
		Request:    request,
		vdiskSrv:   c.service,
	}

	// Build vdisk domain object
	task.VDisk = c.service.buildVDiskFromRequest(request, guestID)

	// Define pipeline steps
	steps := []Step{
		&ValidateStep{},
		&PrepareStorageStep{},
		&CreateFileStep{},
		&StoreMetadataStep{},
		&FinalizeStep{},
	}

	// Execute pipeline
	if err := c.runPipeline(task, steps); err != nil {
		log.Errorf("[VDisk_Creator] create vdisk failed - %v", err)
		aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_CREATE_FAILURE, "vdisk_id", task.VDiskID, "msg", err.Error())
		return nil, err
	}

	aclog.New("vdisk", ctx).Info(aclog.VIRTUAL_DISK_CREATE_SUCCESS, "vdisk_id", task.VDiskID)

	// Return response
	return c.service.Get(ctx, task.VDiskID)
}

// runPipeline executes the pipeline steps with rollback on failure
func (c *VDiskCreator) runPipeline(task *CreateVDiskTask, steps []Step) error {
	for i, step := range steps {
		if err := step.Execute(task); err != nil {
			// Rollback all executed steps in reverse order
			for j := i; j >= 0; j-- {
				steps[j].RollBack(task)
			}
			return err
		}
	}
	return nil
}

// Pipeline Steps

// ValidateStep validates the creation request
type ValidateStep struct{}

func (s *ValidateStep) Execute(task *CreateVDiskTask) error {
	// Validate request
	if err := task.Request.Validate(); err != nil {
		return fmt.Errorf("validation failed: %v", err)
	}

	// Check repository exists
	if task.vdiskSrv.repoSrv != nil {
		if _, err := task.vdiskSrv.repoSrv.LocatePoolPath(task.Request.RepositoryID); err != nil {
			return fmt.Errorf("repository not found: %v", err)
		}
	}

	// Check disk space if available
	if task.vdiskSrv.systemSrv != nil {
		repoPath, _ := task.vdiskSrv.repoSrv.LocatePoolPath(task.Request.RepositoryID)
		if availableSpace, err := task.vdiskSrv.systemSrv.CheckDiskSpace(repoPath); err == nil {
			requiredSpace := int64(task.Request.SizeGB) * GByteUnit
			if availableSpace < requiredSpace {
				return fmt.Errorf("insufficient disk space: required %d, available %d", requiredSpace, availableSpace)
			}
		}
	}

	log.Infof("[VDisk_Creator] validation passed for vdisk %s", task.VDiskID)
	return nil
}

func (s *ValidateStep) RollBack(task *CreateVDiskTask) {
	// Nothing to rollback for validation
}

// PrepareStorageStep prepares the storage location
type PrepareStorageStep struct{}

func (s *PrepareStorageStep) Execute(task *CreateVDiskTask) error {
	// Get repository path
	repoPath, err := task.vdiskSrv.repoSrv.LocatePoolPath(task.VDisk.RepositoryID)
	if err != nil {
		return fmt.Errorf("failed to locate repository path: %v", err)
	}

	// Build vdisk directory path
	vdiskDir := filepath.Join(repoPath, domain.VDiskVolumePath, task.VDisk.LUNUUID)

	// Create directory
	if err := os.MkdirAll(vdiskDir, 0755); err != nil {
		return fmt.Errorf("failed to create vdisk directory: %v", err)
	}

	// Set file path
	task.FilePath = filepath.Join(vdiskDir, "vdisk.img")

	log.Infof("[VDisk_Creator] storage prepared for vdisk %s at %s", task.VDiskID, task.FilePath)
	return nil
}

func (s *PrepareStorageStep) RollBack(task *CreateVDiskTask) {
	if task.FilePath != "" {
		vdiskDir := filepath.Dir(task.FilePath)
		os.RemoveAll(vdiskDir)
		log.Infof("[VDisk_Creator] rolled back storage preparation for vdisk %s", task.VDiskID)
	}
}

// CreateFileStep creates the actual vdisk file
type CreateFileStep struct{}

func (s *CreateFileStep) Execute(task *CreateVDiskTask) error {
	if task.vdiskSrv.storageSrv == nil {
		return fmt.Errorf("storage service not available")
	}

	// Create sparse file
	if err := task.vdiskSrv.storageSrv.CreateSparseFile(task.FilePath, task.VDisk.Size); err != nil {
		return fmt.Errorf("failed to create sparse file: %v", err)
	}

	log.Infof("[VDisk_Creator] file created for vdisk %s", task.VDiskID)
	return nil
}

func (s *CreateFileStep) RollBack(task *CreateVDiskTask) {
	if task.FilePath != "" && task.vdiskSrv.storageSrv != nil {
		task.vdiskSrv.storageSrv.DeleteFile(task.FilePath)
		log.Infof("[VDisk_Creator] rolled back file creation for vdisk %s", task.VDiskID)
	}
}

// StoreMetadataStep stores the vdisk metadata in the backend store
type StoreMetadataStep struct{}

func (s *StoreMetadataStep) Execute(task *CreateVDiskTask) error {
	// Store in backend
	if err := task.vdiskSrv.store.VDisks().Create(task.ctx, task.VDiskID, task.VDisk, metav1.CreateOptions{}); err != nil {
		return fmt.Errorf("failed to store vdisk metadata: %v", err)
	}

	log.Infof("[VDisk_Creator] metadata stored for vdisk %s", task.VDiskID)
	return nil
}

func (s *StoreMetadataStep) RollBack(task *CreateVDiskTask) {
	task.vdiskSrv.store.VDisks().Delete(task.ctx, task.VDiskID, metav1.DeleteOptions{})
	log.Infof("[VDisk_Creator] rolled back metadata storage for vdisk %s", task.VDiskID)
}

// FinalizeStep finalizes the vdisk creation
type FinalizeStep struct{}

func (s *FinalizeStep) Execute(task *CreateVDiskTask) error {
	// Update status
	task.Status = dto.VDiskStateAvailable

	// Send completion notification if needed
	if task.vdiskSrv.awaiting != nil {
		task.vdiskSrv.taskRunningMut.Lock()
		if resultChan, ok := task.vdiskSrv.awaiting[task.TaskID]; ok {
			result := dto.AsyncResult{
				ID:         task.VDiskID,
				Name:       task.Request.Name,
				Status:     dto.VDiskStateAvailable,
				Progress:   100,
				TaskID:     task.TaskID,
				CreateTime: task.CreateTime,
				Size:       task.VDisk.Size,
				Location:   dto.Location{RepoID: task.VDisk.RepositoryID},
			}
			utils.NonBlockWriteChan(resultChan, result)
		}
		task.vdiskSrv.taskRunningMut.Unlock()
	}

	log.Infof("[VDisk_Creator] vdisk creation finalized %s", task.VDiskID)
	return nil
}

func (s *FinalizeStep) RollBack(task *CreateVDiskTask) {
	// Update status to error
	task.Status = dto.VDiskStateError

	// Send error notification if needed
	if task.vdiskSrv.awaiting != nil {
		task.vdiskSrv.taskRunningMut.Lock()
		if resultChan, ok := task.vdiskSrv.awaiting[task.TaskID]; ok {
			result := dto.AsyncResult{
				ID:           task.VDiskID,
				Name:         task.Request.Name,
				Status:       dto.VDiskStateError,
				StatusCode:   VDiskCreateError.Code(),
				StatusReason: "Creation failed",
				Progress:     -1,
				TaskID:       task.TaskID,
				CreateTime:   task.CreateTime,
				Size:         task.VDisk.Size,
				Location:     dto.Location{RepoID: task.VDisk.RepositoryID},
			}
			utils.NonBlockWriteChan(resultChan, result)
		}
		task.vdiskSrv.taskRunningMut.Unlock()
	}

	log.Infof("[VDisk_Creator] rolled back finalization for vdisk %s", task.VDiskID)
}

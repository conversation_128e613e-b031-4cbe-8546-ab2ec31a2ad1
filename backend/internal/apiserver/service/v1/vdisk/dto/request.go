package dto

// CreateRequest represents a request to create a new vdisk
type CreateRequest struct {
	BaseVDisk `json:",inline"`
	VDiskQoS  `json:",inline"`
	
	// Repository information
	RepositoryID string `json:"repository_id" binding:"required"` // Repository pool ID
	
	// VM information
	GuestID string `json:"guest_id" binding:"required"` // VM ID
	
	// Disk configuration
	MinorID int `json:"minor_id"` // Device minor ID (index)
	
	// Size in GB (will be converted to bytes internally)
	SizeGB int `json:"size_gb" binding:"required,min=1"`
}

// UpdateRequest represents a request to update an existing vdisk
type UpdateRequest struct {
	BaseVDisk `json:",inline"`
	VDiskQoS  `json:",inline"`
	
	// Size in GB (for resize operations)
	SizeGB int `json:"size_gb,omitempty"`
	
	// Flags to indicate what fields are being updated
	IsQoSEdit  bool `json:"is_qos_edit"`
	IsSizeEdit bool `json:"is_size_edit"`
	IsUnmapEdit bool `json:"is_unmap_edit"`
}

// DeleteRequest represents a request to delete a vdisk
type DeleteRequest struct {
	ID      string `json:"id" binding:"required"`      // VDisk UUID
	GuestID string `json:"guest_id" binding:"required"` // VM ID
	Force   bool   `json:"force"`                       // Force delete even if attached
}

// AttachRequest represents a request to attach a vdisk to a VM
type AttachRequest struct {
	VDiskID string `json:"vdisk_id" binding:"required"` // VDisk UUID
	GuestID string `json:"guest_id" binding:"required"` // VM ID
	MinorID int    `json:"minor_id"`                    // Device minor ID
}

// DetachRequest represents a request to detach a vdisk from a VM
type DetachRequest struct {
	VDiskID string `json:"vdisk_id" binding:"required"` // VDisk UUID
	GuestID string `json:"guest_id" binding:"required"` // VM ID
}

// ResizeRequest represents a request to resize a vdisk
type ResizeRequest struct {
	ID     string `json:"id" binding:"required"`              // VDisk UUID
	SizeGB int    `json:"size_gb" binding:"required,min=1"`   // New size in GB
}

// BatchCreateRequest represents a request to create multiple vdisks
type BatchCreateRequest struct {
	GuestID string          `json:"guest_id" binding:"required"` // VM ID
	VDisks  []CreateRequest `json:"vdisks" binding:"required"`   // List of vdisks to create
}

// BatchUpdateRequest represents a request to update multiple vdisks
type BatchUpdateRequest struct {
	GuestID string          `json:"guest_id" binding:"required"` // VM ID
	VDisks  []UpdateRequest `json:"vdisks" binding:"required"`   // List of vdisks to update
}

// BatchDeleteRequest represents a request to delete multiple vdisks
type BatchDeleteRequest struct {
	GuestID  string   `json:"guest_id" binding:"required"` // VM ID
	VDiskIDs []string `json:"vdisk_ids" binding:"required"` // List of vdisk UUIDs
	Force    bool     `json:"force"`                        // Force delete even if attached
}

// SortRequest represents a request to reorder vdisks
type SortRequest struct {
	GuestID string             `json:"guest_id" binding:"required"` // VM ID
	Order   []VDiskOrderItem   `json:"order" binding:"required"`    // New order
}

// VDiskOrderItem represents an item in the sort order
type VDiskOrderItem struct {
	VDiskID string `json:"vdisk_id" binding:"required"` // VDisk UUID
	MinorID int    `json:"minor_id"`                    // New minor ID (index)
}

// Validate validates the CreateRequest
func (r *CreateRequest) Validate() error {
	if r.SizeGB <= 0 {
		return ErrInvalidSize
	}
	if r.RepositoryID == "" {
		return ErrMissingRepository
	}
	if r.GuestID == "" {
		return ErrMissingGuestID
	}
	return nil
}

// Validate validates the UpdateRequest
func (r *UpdateRequest) Validate() error {
	if r.IsSizeEdit && r.SizeGB <= 0 {
		return ErrInvalidSize
	}
	return nil
}

// Validate validates the DeleteRequest
func (r *DeleteRequest) Validate() error {
	if r.ID == "" {
		return ErrMissingVDiskID
	}
	if r.GuestID == "" {
		return ErrMissingGuestID
	}
	return nil
}

// Common validation errors
var (
	ErrInvalidSize       = NewValidationError("invalid size specified")
	ErrMissingRepository = NewValidationError("repository ID is required")
	ErrMissingGuestID    = NewValidationError("guest ID is required")
	ErrMissingVDiskID    = NewValidationError("vdisk ID is required")
)

// ValidationError represents a validation error
type ValidationError struct {
	Message string
}

func (e ValidationError) Error() string {
	return e.Message
}

// NewValidationError creates a new validation error
func NewValidationError(message string) ValidationError {
	return ValidationError{Message: message}
}

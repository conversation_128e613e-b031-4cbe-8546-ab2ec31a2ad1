package dto

import (
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/domain/v1"
)

// Response represents a vdisk response
type Response struct {
	BaseVDisk   `json:",inline"`
	VDiskQoS    `json:",inline"`
	VDiskConfig `json:",inline"`

	Status       string   `json:"status"`        // VDisk status
	StatusCode   int      `json:"status_code"`   // Error code
	StatusReason string   `json:"status_reason"` // Error reason
	CreateTime   int64    `json:"create_time"`   // Creation timestamp
	UpdateTime   int64    `json:"update_time"`   // Last update timestamp
	Location     Location `json:"location"`      // Repository location
	Progress     int      `json:"progress"`      // Operation progress (0-100)
	TaskID       uint64   `json:"task_id"`       // Async task ID

	// Additional information
	SizeGB      int    `json:"size_gb"`      // Size in GB
	UsedSizeGB  int    `json:"used_size_gb"` // Used size in GB
	VDiskDevice string `json:"vdisk_device"` // Device path
	AttachedVM  string `json:"attached_vm"`  // Attached VM ID
	IsAttached  bool   `json:"is_attached"`  // Whether attached to a VM
}

// ResponseList represents a list of vdisk responses
type ResponseList struct {
	metav1.ListMeta `json:",inline"` // Standard list metadata
	Items           []*Response      `json:"items"` // VDisk list
}

// CreateResponse represents the response for vdisk creation
type CreateResponse struct {
	VDiskIDs []string `json:"vdisk_ids"` // Created vdisk UUIDs
	TaskID   uint64   `json:"task_id"`   // Async task ID
	Config   string   `json:"config"`    // VDisk configuration
}

// TaskResponse represents the response for async task status
type TaskResponse struct {
	TaskID       uint64     `json:"task_id"`
	Status       VDiskState `json:"status"`
	Progress     int        `json:"progress"`
	StatusCode   int        `json:"status_code"`
	StatusReason string     `json:"status_reason"`
	VDiskID      string     `json:"vdisk_id"`
	CreateTime   int64      `json:"create_time"`
}

// BuildResponse creates a Response from domain VDisk
func BuildResponse(vdiskID string, vdisk *domain.VDisk, repoName string) *Response {
	response := &Response{
		BaseVDisk: BaseVDisk{
			ID:         vdiskID,
			Size:       vdisk.Size,
			VDiskMode:  VDiskMode(vdisk.VDiskMode),
			Type:       VDiskType(vdisk.Type),
			Unmap:      vdisk.Unmap,
			IsDummy:    vdisk.IsDummy,
			IsMetaDisk: vdisk.IsMetaDisk,
		},
		VDiskQoS: VDiskQoS{
			DevLimit:       int(vdisk.DevLimit),
			DevReservation: int(vdisk.DevReservation),
			DevWeight:      vdisk.DevWeight,
			IOPSEnable:     vdisk.IOPSEnable,
		},
		VDiskConfig: VDiskConfig{
			LUNUUID:      vdisk.LUNUUID,
			MinorID:      vdisk.MinorID,
			RepositoryID: vdisk.RepositoryID,
			GuestID:      vdisk.GuestID,
			VDiskDevice:  vdisk.VDiskDevice,
			Format:       vdisk.Format,
		},
		Status:     VDiskStateAvailable.String(),
		StatusCode: 0,
		Location: Location{
			RepoID:   vdisk.RepositoryID,
			RepoName: repoName,
		},
		SizeGB:     int(vdisk.Size / (1024 * 1024 * 1024)), // Convert bytes to GB
		Progress:   100,
		IsAttached: vdisk.GuestID != "",
		AttachedVM: vdisk.GuestID,
	}

	return response
}

// ToResponseList converts domain VDiskList to ResponseList
func ToResponseList(vdiskList *domain.VDiskList, getRepoName func(string) string) *ResponseList {
	responseList := &ResponseList{
		ListMeta: vdiskList.ListMeta,
		Items:    make([]*Response, 0, len(vdiskList.Items)),
	}

	for vdiskID, vdisk := range vdiskList.Items {
		repoName := ""
		if getRepoName != nil {
			repoName = getRepoName(vdisk.RepositoryID)
		}

		response := BuildResponse(vdiskID, vdisk, repoName)
		responseList.Items = append(responseList.Items, response)
	}

	return responseList
}

// SetStatus sets the status of the response
func (r *Response) SetStatus(status VDiskState) {
	r.Status = status.String()
}

// SetProgress sets the progress of the response
func (r *Response) SetProgress(progress int) {
	r.Progress = progress
}

// SetStatusCode sets the status code of the response
func (r *Response) SetStatusCode(code int) {
	r.StatusCode = code
}

// SetStatusReason sets the status reason of the response
func (r *Response) SetStatusReason(reason string) {
	r.StatusReason = reason
}

// SetTaskID sets the task ID of the response
func (r *Response) SetTaskID(taskID uint64) {
	r.TaskID = taskID
}

// IsError returns true if the response indicates an error
func (r *Response) IsError() bool {
	return r.Status == VDiskStateError.String() || r.StatusCode != 0
}

// IsCompleted returns true if the operation is completed
func (r *Response) IsCompleted() bool {
	return r.Status == VDiskStateAvailable.String() && r.Progress == 100
}

// IsInProgress returns true if the operation is in progress
func (r *Response) IsInProgress() bool {
	return r.Status == VDiskStateCreating.String() ||
		r.Status == VDiskStateAttaching.String() ||
		r.Status == VDiskStateDetaching.String() ||
		r.Status == VDiskStateDeleting.String() ||
		r.Status == VDiskStateResizing.String()
}

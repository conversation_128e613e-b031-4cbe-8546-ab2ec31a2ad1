package dto

import (
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/domain/v1"
)

// VDiskMode represents the disk controller mode
type VDiskMode int

const (
	VDiskModeIDE    VDiskMode = 1 // IDE mode
	VDiskModeSATA   VDiskMode = 2 // SATA mode
	VDiskModeVirtio VDiskMode = 3 // Virtio mode
)

// VDiskType represents the disk type
type VDiskType int

const (
	VDiskTypeSystem VDiskType = 1 // System disk
	VDiskTypeData   VDiskType = 2 // Data disk
)

// VDiskState represents the state of a vdisk
type VDiskState string

const (
	VDiskStateCreating  VDiskState = "creating"
	VDiskStateAvailable VDiskState = "available"
	VDiskStateAttaching VDiskState = "attaching"
	VDiskStateAttached  VDiskState = "attached"
	VDiskStateDetaching VDiskState = "detaching"
	VDiskStateDeleting  VDiskState = "deleting"
	VDiskStateError     VDiskState = "error"
	VDiskStateResizing  VDiskState = "resizing"
)

// String returns the string representation of VDiskState
func (s VDiskState) String() string {
	return string(s)
}

// Location represents a repository location
type Location struct {
	RepoName string `json:"repo_name"`
	RepoID   string `json:"repo_id"`
}

// BaseVDisk contains the basic properties of a virtual disk
type BaseVDisk struct {
	ID         string    `json:"id"`           // VDisk UUID
	Name       string    `json:"name"`         // VDisk name
	Size       int64     `json:"size"`         // Size in bytes
	VDiskMode  VDiskMode `json:"vdisk_mode"`   // Disk controller mode
	Type       VDiskType `json:"type"`         // Disk type
	Unmap      bool      `json:"unmap"`        // Support UNMAP/TRIM
	IsDummy    bool      `json:"is_dummy"`     // Is dummy disk
	IsMetaDisk bool      `json:"is_meta_disk"` // Is metadata disk
}

// VDiskQoS contains Quality of Service settings
type VDiskQoS struct {
	DevLimit       int  `json:"dev_limit"`       // Maximum IOPS
	DevReservation int  `json:"dev_reservation"` // Minimum IOPS
	DevWeight      int  `json:"dev_weight"`      // Device weight
	IOPSEnable     bool `json:"iops_enable"`     // Enable IOPS control
}

// VDiskConfig contains configuration for vdisk creation
type VDiskConfig struct {
	LUNUUID      string `json:"lun_uuid"`      // LUN UUID
	MinorID      int    `json:"minor_id"`      // Device minor ID
	RepositoryID string `json:"repository_id"` // Repository pool ID
	GuestID      string `json:"guest_id"`      // VM ID
	VDiskDevice  string `json:"vdisk_device"`  // Device path
	Format       int    `json:"format"`        // Disk format
}

// AsyncResult represents the result of an asynchronous operation
type AsyncResult struct {
	ID           string     `json:"id"`
	Name         string     `json:"name"`
	Status       VDiskState `json:"status"`
	StatusCode   int        `json:"status_code"`
	StatusReason string     `json:"status_reason"`
	Progress     int        `json:"progress"`
	TaskID       uint64     `json:"task_id"`
	CreateTime   int64      `json:"create_time"`
	Size         int64      `json:"size"`
	Location     Location   `json:"location"`
}

// ToResponse converts AsyncResult to Response
func (r AsyncResult) ToResponse() *Response {
	return &Response{
		BaseVDisk: BaseVDisk{
			ID:   r.ID,
			Name: r.Name,
			Size: r.Size,
		},
		Status:       r.Status.String(),
		StatusCode:   r.StatusCode,
		StatusReason: r.StatusReason,
		Progress:     r.Progress,
		TaskID:       r.TaskID,
		CreateTime:   r.CreateTime,
		Location:     r.Location,
	}
}

// FromVDisk creates AsyncResult from domain VDisk
func (r AsyncResult) FromVDisk(id string, vdisk *domain.VDisk) AsyncResult {
	return AsyncResult{
		ID:         id,
		Name:       "", // Name should be provided separately
		Status:     VDiskStateAvailable,
		StatusCode: 0,
		Progress:   100,
		TaskID:     0,
		CreateTime: 0, // Should be set from vdisk creation time
		Size:       vdisk.Size,
		Location:   Location{RepoID: vdisk.RepositoryID},
	}
}

// LocateToRepos converts Location slice to repository ID slice
func LocateToRepos(locations []Location) []string {
	repos := make([]string, 0, len(locations))
	for _, loc := range locations {
		repos = append(repos, loc.RepoID)
	}
	return repos
}

// VDiskUsage represents vdisk usage information
type VDiskUsage struct {
	Size float64 `json:"size"` // Total size in bytes
	Used float64 `json:"used"` // Used size in bytes
}

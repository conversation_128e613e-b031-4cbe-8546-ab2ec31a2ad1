package vdisk_service

// RepositoryInternalSrv defines the interface for repository operations
type RepositoryInternalSrv interface {
	// LocatePoolPath returns the physical path for a repository pool
	LocatePoolPath(repoId string) (string, error)
	// ListPoolNames returns the names of repository pools by their IDs
	ListPoolNames(repoIds []string) ([]string, error)
	// GetPoolInfo returns detailed information about a repository pool
	GetPoolInfo(repoId string) (map[string]interface{}, error)
}

// VMInternalSrv defines the interface for VM operations
type VMInternalSrv interface {
	// ListVMsByVDisk returns VMs that are using the specified vdisk
	ListVMsByVDisk(vdiskId string) (map[string]string, error)
	// IsVMRunning checks if a VM is currently running
	IsVMRunning(vmId string) bool
	// DetachVDiskFromVM detaches a vdisk from a VM
	DetachVDiskFromVM(vmId, vdiskId string) error
}

// StorageInternalSrv defines the interface for storage operations
type StorageInternalSrv interface {
	// CreateSparseFile creates a sparse file for vdisk
	CreateSparseFile(path string, size int64) error
	// DeleteFile removes a file from storage
	DeleteFile(path string) error
	// ResizeFile resizes an existing file
	ResizeFile(path string, newSize int64) error
	// GetFileInfo returns information about a file
	GetFileInfo(path string) (map[string]interface{}, error)
}

// SystemInternalSrv defines the interface for system operations
type SystemInternalSrv interface {
	// ExecuteCommand executes a system command
	ExecuteCommand(cmd string, args ...string) ([]byte, error)
	// CheckDiskSpace checks available disk space
	CheckDiskSpace(path string) (int64, error)
	// GetSystemInfo returns system information
	GetSystemInfo() (map[string]interface{}, error)
}

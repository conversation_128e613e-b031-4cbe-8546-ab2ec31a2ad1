package vdisk_service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/gogf/gf/errors/gcode"
	"github.com/gogf/gf/errors/gerror"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/log"
	uuid "github.com/satori/go.uuid"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/aclog"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/vdisk/dto"
)

// Error codes for VDisk operations
var (
	VDiskCreateError     = gcode.New(6001, "vdisk create error", nil)
	VDiskDeleteError     = gcode.New(6002, "vdisk delete error", nil)
	VDiskUpdateError     = gcode.New(6003, "vdisk update error", nil)
	VDiskNotFound        = gcode.New(6004, "vdisk not found", nil)
	VDiskInUse           = gcode.New(6005, "vdisk in use", nil)
	VDiskAttachError     = gcode.New(6006, "vdisk attach error", nil)
	VDiskDetachError     = gcode.New(6007, "vdisk detach error", nil)
	VDiskResizeError     = gcode.New(6008, "vdisk resize error", nil)
	VDiskStorageError    = gcode.New(6009, "vdisk storage error", nil)
	VDiskValidationError = gcode.New(6010, "vdisk validation error", nil)
)

// Constants
const (
	ByteUnit  = 1
	KByteUnit = 1 * 1024
	MByteUnit = 1 * 1024 * 1024
	GByteUnit = 1 * 1024 * 1024 * 1024
)

// VDiskService provides virtual disk management operations
type VDiskService struct {
	store      store.Factory
	redisStore store.RedisFactory
	repoSrv    RepositoryInternalSrv
	vmSrv      VMInternalSrv
	storageSrv StorageInternalSrv
	systemSrv  SystemInternalSrv

	// Async task management
	awaiting       map[uint64]chan dto.AsyncResult
	createTasks    map[uint64]*createVDiskTask
	taskLastResult map[uint64]dto.AsyncResult
	taskRunningMut sync.Mutex
	nextID         chan uint64
	closed         chan struct{}
}

// NewVDiskService creates a new VDiskService instance
func NewVDiskService(
	store store.Factory,
	redisStore store.RedisFactory,
	repoSrv RepositoryInternalSrv,
	vmSrv VMInternalSrv,
	storageSrv StorageInternalSrv,
	systemSrv SystemInternalSrv,
) *VDiskService {
	service := &VDiskService{
		store:          store,
		redisStore:     redisStore,
		repoSrv:        repoSrv,
		vmSrv:          vmSrv,
		storageSrv:     storageSrv,
		systemSrv:      systemSrv,
		awaiting:       make(map[uint64]chan dto.AsyncResult),
		createTasks:    make(map[uint64]*createVDiskTask),
		taskLastResult: make(map[uint64]dto.AsyncResult),
		nextID:         make(chan uint64),
		closed:         make(chan struct{}),
	}

	go service.idGenerator()
	return service
}

// Create creates new virtual disks
func (s *VDiskService) Create(ctx context.Context, request dto.BatchCreateRequest) (*dto.CreateResponse, error) {
	// Validate request
	if err := s.validateBatchCreateRequest(request); err != nil {
		log.Errorf("[VDisk_Service] create validation failed - %v", err)
		aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_CREATE_FAILURE, "guest_id", request.GuestID, "msg", err.Error())
		return nil, gerror.WrapCode(VDiskValidationError, err)
	}

	vdiskIDs := make([]string, 0, len(request.VDisks))
	vdiskList := &domain.VDiskList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(request.VDisks)),
		},
		Items: make(map[string]*domain.VDisk),
	}

	// Create each vdisk
	for _, vdiskReq := range request.VDisks {
		vdiskID := s.generateVDiskUUID()
		vdisk := s.buildVDiskFromRequest(vdiskReq, request.GuestID)

		// Create vdisk file
		if err := s.createVDiskFile(ctx, vdiskID, vdisk); err != nil {
			log.Errorf("[VDisk_Service] create vdisk file failed - %v", err)
			aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_CREATE_FAILURE, "vdisk_id", vdiskID, "msg", err.Error())
			s.rollbackVDiskFile(ctx, vdisk)
			return nil, gerror.WrapCode(VDiskCreateError, err)
		}

		// Store vdisk in backend
		if err := s.store.VDisks().Create(ctx, vdiskID, vdisk, metav1.CreateOptions{}); err != nil {
			log.Errorf("[VDisk_Service] create vdisk in store failed - %v", err)
			aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_CREATE_FAILURE, "vdisk_id", vdiskID, "msg", err.Error())
			s.rollbackVDiskFile(ctx, vdisk)
			return nil, gerror.WrapCode(VDiskCreateError, err)
		}

		vdiskList.Items[vdiskID] = vdisk
		vdiskIDs = append(vdiskIDs, vdiskID)
	}

	// Generate configuration
	vdiskConfig, err := vdiskList.MarshalBinary()
	if err != nil {
		log.Errorf("[VDisk_Service] marshal vdisk config failed - %v", err)
		aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_CREATE_FAILURE, "guest_id", request.GuestID, "msg", err.Error())
		return nil, gerror.WrapCode(VDiskCreateError, err)
	}

	aclog.New("vdisk", ctx).Info(aclog.VIRTUAL_DISK_CREATE_SUCCESS, "guest_id", request.GuestID, "count", len(vdiskIDs))

	return &dto.CreateResponse{
		VDiskIDs: vdiskIDs,
		Config:   string(vdiskConfig),
	}, nil
}

// Delete deletes virtual disks
func (s *VDiskService) Delete(ctx context.Context, request dto.BatchDeleteRequest) error {
	// Validate request
	if err := s.validateBatchDeleteRequest(request); err != nil {
		log.Errorf("[VDisk_Service] delete validation failed - %v", err)
		aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_DELETE_FAILURE, "guest_id", request.GuestID, "msg", err.Error())
		return gerror.WrapCode(VDiskValidationError, err)
	}

	// Check if vdisks are in use (unless force delete)
	if !request.Force {
		for _, vdiskID := range request.VDiskIDs {
			if inUse, err := s.isVDiskInUse(ctx, vdiskID); err != nil {
				return gerror.WrapCode(VDiskDeleteError, err)
			} else if inUse {
				return gerror.WrapCode(VDiskInUse, fmt.Errorf("vdisk %s is in use", vdiskID))
			}
		}
	}

	// Delete each vdisk
	for _, vdiskID := range request.VDiskIDs {
		vdisk, err := s.store.VDisks().Get(ctx, vdiskID, metav1.GetOptions{})
		if err != nil {
			log.Errorf("[VDisk_Service] get vdisk failed - %v", err)
			continue // Continue with other vdisks
		}

		// Delete vdisk file
		if err := s.deleteVDiskFile(ctx, vdiskID, vdisk); err != nil {
			log.Errorf("[VDisk_Service] delete vdisk file failed - %v", err)
			// Continue with store deletion even if file deletion fails
		}

		// Delete from store
		if err := s.store.VDisks().Delete(ctx, vdiskID, metav1.DeleteOptions{}); err != nil {
			log.Errorf("[VDisk_Service] delete vdisk from store failed - %v", err)
			aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_DELETE_FAILURE, "vdisk_id", vdiskID, "msg", err.Error())
			return gerror.WrapCode(VDiskDeleteError, err)
		}
	}

	aclog.New("vdisk", ctx).Info(aclog.VIRTUAL_DISK_DELETE_SUCCESS, "guest_id", request.GuestID, "count", len(request.VDiskIDs))
	return nil
}

// Get retrieves a virtual disk by ID
func (s *VDiskService) Get(ctx context.Context, vdiskID string) (*dto.Response, error) {
	vdisk, err := s.store.VDisks().Get(ctx, vdiskID, metav1.GetOptions{})
	if err != nil {
		log.Errorf("[VDisk_Service] get vdisk failed - %v", err)
		return nil, gerror.WrapCode(VDiskNotFound, err)
	}

	// Get repository name
	repoName := ""
	if s.repoSrv != nil {
		if names, err := s.repoSrv.ListPoolNames([]string{vdisk.RepositoryID}); err == nil && len(names) > 0 {
			repoName = names[0]
		}
	}

	response := dto.BuildResponse(vdiskID, vdisk, repoName)
	return response, nil
}

// List retrieves all virtual disks
func (s *VDiskService) List(ctx context.Context, opts metav1.ListOptions) (*dto.ResponseList, error) {
	vdiskList, err := s.store.VDisks().List(ctx, opts)
	if err != nil {
		log.Errorf("[VDisk_Service] list vdisks failed - %v", err)
		return nil, err
	}

	// Get repository names
	getRepoName := func(repoID string) string {
		if s.repoSrv != nil {
			if names, err := s.repoSrv.ListPoolNames([]string{repoID}); err == nil && len(names) > 0 {
				return names[0]
			}
		}
		return ""
	}

	responseList := dto.ToResponseList(vdiskList, getRepoName)
	return responseList, nil
}

// Update updates a virtual disk
func (s *VDiskService) Update(ctx context.Context, vdiskID string, request dto.UpdateRequest) (*dto.Response, error) {
	// Validate request
	if err := request.Validate(); err != nil {
		log.Errorf("[VDisk_Service] update validation failed - %v", err)
		aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_UPDATE_FAILURE, "vdisk_id", vdiskID, "msg", err.Error())
		return nil, gerror.WrapCode(VDiskValidationError, err)
	}

	// Get existing vdisk
	vdisk, err := s.store.VDisks().Get(ctx, vdiskID, metav1.GetOptions{})
	if err != nil {
		log.Errorf("[VDisk_Service] get vdisk for update failed - %v", err)
		return nil, gerror.WrapCode(VDiskNotFound, err)
	}

	// Update vdisk properties
	s.updateVDiskFromRequest(vdisk, request)

	// Handle resize if needed
	if request.IsSizeEdit {
		newSize := int64(request.SizeGB) * GByteUnit
		if err := s.resizeVDisk(ctx, vdiskID, vdisk, newSize); err != nil {
			log.Errorf("[VDisk_Service] resize vdisk failed - %v", err)
			aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_RESIZE_FAILURE, "vdisk_id", vdiskID, "msg", err.Error())
			return nil, gerror.WrapCode(VDiskResizeError, err)
		}
		vdisk.Size = newSize
	}

	// Update in store
	if err := s.store.VDisks().Update(ctx, vdiskID, vdisk, metav1.UpdateOptions{}); err != nil {
		log.Errorf("[VDisk_Service] update vdisk in store failed - %v", err)
		aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_UPDATE_FAILURE, "vdisk_id", vdiskID, "msg", err.Error())
		return nil, gerror.WrapCode(VDiskUpdateError, err)
	}

	aclog.New("vdisk", ctx).Info(aclog.VIRTUAL_DISK_UPDATE_SUCCESS, "vdisk_id", vdiskID)

	// Return updated response
	return s.Get(ctx, vdiskID)
}

// Helper methods

// validateBatchCreateRequest validates the batch create request
func (s *VDiskService) validateBatchCreateRequest(request dto.BatchCreateRequest) error {
	if request.GuestID == "" {
		return fmt.Errorf("guest ID is required")
	}

	if len(request.VDisks) == 0 {
		return fmt.Errorf("at least one vdisk is required")
	}

	for i, vdisk := range request.VDisks {
		if err := vdisk.Validate(); err != nil {
			return fmt.Errorf("vdisk %d validation failed: %v", i, err)
		}
	}

	return nil
}

// validateBatchDeleteRequest validates the batch delete request
func (s *VDiskService) validateBatchDeleteRequest(request dto.BatchDeleteRequest) error {
	if request.GuestID == "" {
		return fmt.Errorf("guest ID is required")
	}

	if len(request.VDiskIDs) == 0 {
		return fmt.Errorf("at least one vdisk ID is required")
	}

	return nil
}

// buildVDiskFromRequest builds a domain VDisk from create request
func (s *VDiskService) buildVDiskFromRequest(request dto.CreateRequest, guestID string) *domain.VDisk {
	return &domain.VDisk{
		DevLimit:       int64(request.DevLimit),
		DevReservation: int64(request.DevReservation),
		DevWeight:      request.DevWeight,
		Format:         2, // Default format
		GuestID:        guestID,
		IOPSEnable:     request.IOPSEnable,
		IsDummy:        request.IsDummy,
		IsMetaDisk:     request.IsMetaDisk,
		MinorID:        request.MinorID,
		RepositoryID:   request.RepositoryID,
		Size:           int64(request.SizeGB) * GByteUnit,
		Type:           int(request.Type),
		Unmap:          request.Unmap,
		LUNUUID:        s.generateLUNUUID(),
		VDiskMode:      domain.TemplateDiskDriver(request.VDiskMode),
	}
}

// updateVDiskFromRequest updates a domain VDisk from update request
func (s *VDiskService) updateVDiskFromRequest(vdisk *domain.VDisk, request dto.UpdateRequest) {
	if request.IsQoSEdit {
		vdisk.DevLimit = int64(request.DevLimit)
		vdisk.DevReservation = int64(request.DevReservation)
		vdisk.DevWeight = request.DevWeight
		vdisk.IOPSEnable = request.IOPSEnable
	}

	if request.IsUnmapEdit {
		vdisk.Unmap = request.Unmap
	}

	// Update other fields as needed
	if request.Name != "" {
		// Note: VDisk domain doesn't have Name field, might need to handle differently
	}
}

// generateVDiskUUID generates a new UUID for vdisk
func (s *VDiskService) generateVDiskUUID() string {
	return uuid.Must(uuid.NewV4()).String()
}

// generateLUNUUID generates a new UUID for LUN
func (s *VDiskService) generateLUNUUID() string {
	return uuid.Must(uuid.NewV4()).String()
}

// isVDiskInUse checks if a vdisk is currently in use
func (s *VDiskService) isVDiskInUse(ctx context.Context, vdiskID string) (bool, error) {
	if s.vmSrv == nil {
		return false, nil
	}

	vms, err := s.vmSrv.ListVMsByVDisk(vdiskID)
	if err != nil {
		return false, err
	}

	// Check if any VM is running
	for vmID, _ := range vms {
		if s.vmSrv.IsVMRunning(vmID) {
			return true, nil
		}
	}

	return false, nil
}

// createVDiskFile creates the physical vdisk file
func (s *VDiskService) createVDiskFile(ctx context.Context, vdiskID string, vdisk *domain.VDisk) error {
	if s.storageSrv == nil {
		return fmt.Errorf("storage service not available")
	}

	// Get repository path
	repoPath, err := s.repoSrv.LocatePoolPath(vdisk.RepositoryID)
	if err != nil {
		return fmt.Errorf("failed to locate repository path: %v", err)
	}

	// Build vdisk file path
	vdiskPath := s.buildVDiskPath(repoPath, vdisk.LUNUUID)

	// Create sparse file
	if err := s.storageSrv.CreateSparseFile(vdiskPath, vdisk.Size); err != nil {
		return fmt.Errorf("failed to create sparse file: %v", err)
	}

	return nil
}

// deleteVDiskFile deletes the physical vdisk file
func (s *VDiskService) deleteVDiskFile(ctx context.Context, vdiskID string, vdisk *domain.VDisk) error {
	if s.storageSrv == nil {
		return fmt.Errorf("storage service not available")
	}

	// Get repository path
	repoPath, err := s.repoSrv.LocatePoolPath(vdisk.RepositoryID)
	if err != nil {
		return fmt.Errorf("failed to locate repository path: %v", err)
	}

	// Build vdisk file path
	vdiskPath := s.buildVDiskPath(repoPath, vdisk.LUNUUID)

	// Delete file
	if err := s.storageSrv.DeleteFile(vdiskPath); err != nil {
		return fmt.Errorf("failed to delete file: %v", err)
	}

	return nil
}

// rollbackVDiskFile rolls back vdisk file creation
func (s *VDiskService) rollbackVDiskFile(ctx context.Context, vdisk *domain.VDisk) {
	if err := s.deleteVDiskFile(ctx, "", vdisk); err != nil {
		log.Errorf("[VDisk_Service] rollback vdisk file failed - %v", err)
	}
}

// resizeVDisk resizes a vdisk
func (s *VDiskService) resizeVDisk(ctx context.Context, vdiskID string, vdisk *domain.VDisk, newSize int64) error {
	if s.storageSrv == nil {
		return fmt.Errorf("storage service not available")
	}

	// Get repository path
	repoPath, err := s.repoSrv.LocatePoolPath(vdisk.RepositoryID)
	if err != nil {
		return fmt.Errorf("failed to locate repository path: %v", err)
	}

	// Build vdisk file path
	vdiskPath := s.buildVDiskPath(repoPath, vdisk.LUNUUID)

	// Resize file
	if err := s.storageSrv.ResizeFile(vdiskPath, newSize); err != nil {
		return fmt.Errorf("failed to resize file: %v", err)
	}

	return nil
}

// buildVDiskPath builds the full path for a vdisk file
func (s *VDiskService) buildVDiskPath(repoPath, lunUUID string) string {
	return fmt.Sprintf("%s/%s/%s", repoPath, domain.VDiskVolumePath, lunUUID)
}

// Async task management

// createVDiskTask represents a vdisk creation task
type createVDiskTask struct {
	ctx context.Context

	TaskID     uint64
	VDiskID    string
	GuestID    string
	CreateTime int64
	Request    dto.CreateRequest
	Status     dto.VDiskState
}

// idGenerator generates unique task IDs
func (s *VDiskService) idGenerator() {
	nextID := uint64(0x9A011E4C77)
	for {
		nextID++
		select {
		case s.nextID <- nextID:
		case <-s.closed:
			return
		}
	}
}

// taskGenerator generates a new task ID and creates a result channel
func (s *VDiskService) taskGenerator() uint64 {
	var id uint64
	select {
	case id = <-s.nextID:
	case <-s.closed:
		return 0
	}

	s.taskRunningMut.Lock()
	if ch := s.awaiting[id]; ch != nil {
		log.Panicf("[VDisk_Service] task ID already in use - %d", id)
	}
	s.awaiting[id] = make(chan dto.AsyncResult, 1)
	s.taskRunningMut.Unlock()

	return id
}

// destroyTask cleans up task resources
func (s *VDiskService) destroyTask(taskID uint64) {
	s.taskRunningMut.Lock()
	delete(s.awaiting, taskID)
	delete(s.createTasks, taskID)
	delete(s.taskLastResult, taskID)
	s.taskRunningMut.Unlock()

	log.Infof("[VDisk_Service] task resources cleaned up - %d", taskID)
}

// listTaskProgress returns the progress of all running tasks
func (s *VDiskService) listTaskProgress() ([]dto.AsyncResult, error) {
	listProgress := make([]dto.AsyncResult, 0)
	destroyTasks := make([]uint64, 0)
	timeout := time.Tick(10 * time.Millisecond)

	s.taskRunningMut.Lock()
	for taskID, resultChan := range s.awaiting {
		select {
		case result := <-resultChan:
			listProgress = append(listProgress, result)
			s.taskLastResult[taskID] = result
		case <-timeout:
			if lastResult, ok := s.taskLastResult[taskID]; ok {
				listProgress = append(listProgress, lastResult)
			}
		}

		if lastResult, ok := s.taskLastResult[taskID]; ok &&
			(lastResult.Status == dto.VDiskStateError || lastResult.Status == dto.VDiskStateAvailable) {
			destroyTasks = append(destroyTasks, taskID)
		}
	}
	s.taskRunningMut.Unlock()

	// Clean up completed tasks
	for _, taskID := range destroyTasks {
		s.destroyTask(taskID)
	}

	return listProgress, nil
}

// Close closes the service and cleans up resources
func (s *VDiskService) Close() error {
	close(s.closed)

	s.taskRunningMut.Lock()
	for taskID := range s.awaiting {
		delete(s.awaiting, taskID)
		delete(s.createTasks, taskID)
		delete(s.taskLastResult, taskID)
	}
	s.taskRunningMut.Unlock()

	return nil
}

package vdisk_service

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/errors/gerror"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/aclog"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/dto"
	vdisk_dto "gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/vdisk/dto"
)

// VDiskServiceAdapter adapts the new VDiskService to the old interface
type VDiskServiceAdapter struct {
	service *VDiskService
}

// NewVDiskServiceAdapter creates a new adapter
func NewVDiskServiceAdapter(service *VDiskService) *VDiskServiceAdapter {
	return &VDiskServiceAdapter{
		service: service,
	}
}

// Create implements the old VDiskService interface
func (a *VDiskServiceAdapter) Create(ctx context.Context, vmRequest *dto.VMInstanceRequestEdit, opts metav1.CreateOptions) ([]string, string, error) {
	if vmRequest.Disks.Add == nil {
		log.Errorf("[VDisk_Adapter] create failed - no disks to add")
		aclog.New("vdisk", ctx).Error(aclog.VIRTUAL_DISK_CREATE_FAILURE, "guest_id", vmRequest.GuestId)
		return []string{}, "", gerror.New("vdisk create - no disks to add")
	}

	// Convert old DTO to new DTO
	createRequests := make([]vdisk_dto.CreateRequest, 0, len(vmRequest.Disks.Add))
	for _, oldVDisk := range vmRequest.Disks.Add {
		newRequest := vdisk_dto.CreateRequest{
			BaseVDisk: vdisk_dto.BaseVDisk{
				Name:       oldVDisk.Name,
				VDiskMode:  vdisk_dto.VDiskMode(oldVDisk.VdiskMode),
				Type:       vdisk_dto.VDiskTypeData, // Default to data disk
				Unmap:      oldVDisk.Unmap,
				IsDummy:    false,
				IsMetaDisk: false,
			},
			VDiskQoS: vdisk_dto.VDiskQoS{
				DevLimit:       oldVDisk.DevLimit,
				DevReservation: oldVDisk.DevReservation,
				DevWeight:      oldVDisk.DevWeight,
				IOPSEnable:     oldVDisk.IopsEnable,
			},
			RepositoryID: vmRequest.RepoId,
			GuestID:      vmRequest.GuestId,
			MinorID:      oldVDisk.Idx,
			SizeGB:       oldVDisk.VdiskSize,
		}
		createRequests = append(createRequests, newRequest)
	}

	batchRequest := vdisk_dto.BatchCreateRequest{
		GuestID: vmRequest.GuestId,
		VDisks:  createRequests,
	}

	response, err := a.service.Create(ctx, batchRequest)
	if err != nil {
		return []string{}, "", err
	}

	return response.VDiskIDs, response.Config, nil
}

// Update implements the old VDiskService interface
func (a *VDiskServiceAdapter) Update(ctx context.Context, vmRequest *dto.VMInstanceRequestEdit, opts metav1.UpdateOptions) (string, error) {
	if vmRequest.Disks.Edit == nil {
		return "", gerror.New("vdisk update - no disks to edit")
	}

	// Process each edit request
	for _, oldVDisk := range vmRequest.Disks.Edit {
		updateRequest := vdisk_dto.UpdateRequest{
			BaseVDisk: vdisk_dto.BaseVDisk{
				ID:         oldVDisk.VdiskID,
				Name:       oldVDisk.Name,
				VDiskMode:  vdisk_dto.VDiskMode(oldVDisk.VdiskMode),
				Unmap:      oldVDisk.Unmap,
			},
			VDiskQoS: vdisk_dto.VDiskQoS{
				DevLimit:       oldVDisk.DevLimit,
				DevReservation: oldVDisk.DevReservation,
				DevWeight:      oldVDisk.DevWeight,
				IOPSEnable:     oldVDisk.IopsEnable,
			},
			SizeGB:      oldVDisk.VdiskSize,
			IsQoSEdit:   oldVDisk.IsQosEdit,
			IsSizeEdit:  oldVDisk.IsVdiskSizeEdit,
			IsUnmapEdit: oldVDisk.IsUnmapEdit,
		}

		_, err := a.service.Update(ctx, oldVDisk.VdiskID, updateRequest)
		if err != nil {
			return "", err
		}
	}

	// Return empty config for compatibility
	return "", nil
}

// Sort implements the old VDiskService interface
func (a *VDiskServiceAdapter) Sort(ctx context.Context, vmRequest *dto.VMInstanceRequestEdit, opts metav1.UpdateOptions) error {
	// This would need to be implemented based on the new service capabilities
	// For now, return success as sorting might be handled differently
	log.Infof("[VDisk_Adapter] sort operation requested for guest %s", vmRequest.GuestId)
	return nil
}

// Delete implements the old VDiskService interface
func (a *VDiskServiceAdapter) Delete(ctx context.Context, vmRequest *dto.VMInstanceRequestEdit, opts metav1.DeleteOptions) error {
	if vmRequest.Disks.Delete == nil {
		return gerror.New("vdisk delete - no disks to delete")
	}

	// Collect vdisk IDs to delete
	vdiskIDs := make([]string, 0, len(vmRequest.Disks.Delete))
	for _, oldVDisk := range vmRequest.Disks.Delete {
		vdiskIDs = append(vdiskIDs, oldVDisk.VdiskID)
	}

	deleteRequest := vdisk_dto.BatchDeleteRequest{
		GuestID:  vmRequest.GuestId,
		VDiskIDs: vdiskIDs,
		Force:    false, // Default to safe delete
	}

	return a.service.Delete(ctx, deleteRequest)
}

// Recover implements the old VDiskService interface
func (a *VDiskServiceAdapter) Recover(ctx context.Context, vdiskList *domain.VDiskList, recoverUUID string, opts metav1.UpdateOptions) (string, error) {
	// This would need to be implemented based on recovery requirements
	// For now, return empty config
	log.Infof("[VDisk_Adapter] recover operation requested for UUID %s", recoverUUID)
	
	// Convert vdiskList to JSON for compatibility
	if vdiskList != nil {
		configBytes, err := json.Marshal(vdiskList)
		if err != nil {
			return "", err
		}
		return string(configBytes), nil
	}
	
	return "", nil
}

// Running implements the old VDiskService interface
func (a *VDiskServiceAdapter) Running(ctx context.Context, vdiskIDList []string) error {
	// This operation might not be needed in the new architecture
	// VDisks don't have a "running" state in the same way VMs do
	log.Infof("[VDisk_Adapter] running operation requested for vdisks: %v", vdiskIDList)
	return nil
}

// Stop implements the old VDiskService interface
func (a *VDiskServiceAdapter) Stop(ctx context.Context, vdiskIDList []string) error {
	// This operation might not be needed in the new architecture
	// VDisks don't have a "stop" state in the same way VMs do
	log.Infof("[VDisk_Adapter] stop operation requested for vdisks: %v", vdiskIDList)
	return nil
}

// List implements the old VDiskService interface
func (a *VDiskServiceAdapter) List(ctx context.Context, opts metav1.ListOptions) (*domain.VDiskList, error) {
	responseList, err := a.service.List(ctx, opts)
	if err != nil {
		return nil, err
	}

	// Convert new response list to old domain list
	domainList := &domain.VDiskList{
		ListMeta: responseList.ListMeta,
		Items:    make(map[string]*domain.VDisk),
	}

	for _, response := range responseList.Items {
		vdisk := &domain.VDisk{
			DevLimit:       int64(response.DevLimit),
			DevReservation: int64(response.DevReservation),
			DevWeight:      response.DevWeight,
			Format:         response.Format,
			GuestID:        response.GuestID,
			IOPSEnable:     response.IOPSEnable,
			IsDummy:        response.IsDummy,
			IsMetaDisk:     response.IsMetaDisk,
			MinorID:        response.MinorID,
			RepositoryID:   response.RepositoryID,
			Size:           response.Size,
			Type:           int(response.Type),
			Unmap:          response.Unmap,
			LUNUUID:        response.LUNUUID,
			VDiskMode:      domain.TemplateDiskDriver(response.VDiskMode),
			VDiskDevice:    response.VDiskDevice,
		}
		domainList.Items[response.ID] = vdisk
	}

	return domainList, nil
}

// Helper methods for backward compatibility

// ConvertOldVDiskToNew converts old VDisk DTO to new CreateRequest
func ConvertOldVDiskToNew(oldVDisk dto.VDiskAdd, guestID, repoID string) vdisk_dto.CreateRequest {
	return vdisk_dto.CreateRequest{
		BaseVDisk: vdisk_dto.BaseVDisk{
			Name:       oldVDisk.Name,
			VDiskMode:  vdisk_dto.VDiskMode(oldVDisk.VdiskMode),
			Type:       vdisk_dto.VDiskTypeData,
			Unmap:      oldVDisk.Unmap,
			IsDummy:    false,
			IsMetaDisk: false,
		},
		VDiskQoS: vdisk_dto.VDiskQoS{
			DevLimit:       oldVDisk.DevLimit,
			DevReservation: oldVDisk.DevReservation,
			DevWeight:      oldVDisk.DevWeight,
			IOPSEnable:     oldVDisk.IopsEnable,
		},
		RepositoryID: repoID,
		GuestID:      guestID,
		MinorID:      oldVDisk.Idx,
		SizeGB:       oldVDisk.VdiskSize,
	}
}

// ConvertNewVDiskToOld converts new Response to old domain VDisk
func ConvertNewVDiskToOld(response *vdisk_dto.Response) *domain.VDisk {
	return &domain.VDisk{
		DevLimit:       int64(response.DevLimit),
		DevReservation: int64(response.DevReservation),
		DevWeight:      response.DevWeight,
		Format:         response.Format,
		GuestID:        response.GuestID,
		IOPSEnable:     response.IOPSEnable,
		IsDummy:        response.IsDummy,
		IsMetaDisk:     response.IsMetaDisk,
		MinorID:        response.MinorID,
		RepositoryID:   response.RepositoryID,
		Size:           response.Size,
		Type:           int(response.Type),
		Unmap:          response.Unmap,
		LUNUUID:        response.LUNUUID,
		VDiskMode:      domain.TemplateDiskDriver(response.VDiskMode),
		VDiskDevice:    response.VDiskDevice,
	}
}

# VDisk Service Refactoring

This document describes the refactoring of the VDisk service module following the architecture pattern of the Image service.

## Architecture Overview

The refactored VDisk service follows a clean, modular architecture with the following components:

### Directory Structure
```
backend/internal/apiserver/service/v1/vdisk/
├── service.go          # Main service class with core business logic
├── dependencies.go     # Dependency interface definitions
├── internal.go         # Internal service implementation
├── creator.go          # VDisk creation pipeline using Step pattern
├── adapter.go          # Adapter for backward compatibility
├── vdisk_test.go       # Unit tests
├── dto/               # Data Transfer Objects
│   ├── dto.go         # Base DTO definitions
│   ├── request.go     # Request DTOs
│   └── response.go    # Response DTOs
└── README.md          # This documentation
```

## Key Design Patterns

### 1. Service Layer Architecture
- **Main Service**: `VDiskService` - Core business logic and orchestration
- **Internal Service**: `VDiskInternalSrv` - Internal operations and utilities
- **Dependencies**: Abstracted through interfaces for better testability

### 2. Dependency Injection
All external dependencies are injected through interfaces:
- `RepositoryInternalSrv` - Repository operations
- `VMInternalSrv` - VM-related operations  
- `StorageInternalSrv` - Storage operations
- `SystemInternalSrv` - System-level operations

### 3. Pipeline Pattern (Creator)
VDisk creation uses a pipeline pattern with rollback capabilities:
- `ValidateStep` - Request validation
- `PrepareStorageStep` - Storage preparation
- `CreateFileStep` - File creation
- `StoreMetadataStep` - Metadata storage
- `FinalizeStep` - Finalization

### 4. DTO Layer Separation
Clean separation of data structures:
- **Base DTOs**: Common data structures
- **Request DTOs**: Input validation and structure
- **Response DTOs**: Output formatting and conversion

### 5. Adapter Pattern
Maintains backward compatibility with existing code through `VDiskServiceAdapter`.

## Error Handling

Unified error code management:
```go
var (
    VDiskCreateError     = gcode.New(6001, "vdisk create error", nil)
    VDiskDeleteError     = gcode.New(6002, "vdisk delete error", nil)
    VDiskUpdateError     = gcode.New(6003, "vdisk update error", nil)
    VDiskNotFound        = gcode.New(6004, "vdisk not found", nil)
    VDiskInUse           = gcode.New(6005, "vdisk in use", nil)
    // ... more error codes
)
```

## Async Task Management

Support for asynchronous operations with progress tracking:
- Task ID generation
- Progress monitoring
- Result channels
- Automatic cleanup

## Key Features

### 1. Batch Operations
- `BatchCreateRequest` - Create multiple VDisks
- `BatchDeleteRequest` - Delete multiple VDisks
- `BatchUpdateRequest` - Update multiple VDisks

### 2. Validation
- Request validation with detailed error messages
- Resource availability checks
- Disk space validation

### 3. Rollback Support
- Automatic rollback on pipeline failures
- Resource cleanup on errors
- Consistent state maintenance

### 4. Monitoring & Logging
- Structured logging with context
- Audit logging integration
- Progress tracking for long operations

## Usage Examples

### Creating VDisks
```go
request := dto.BatchCreateRequest{
    GuestID: "vm-123",
    VDisks: []dto.CreateRequest{
        {
            BaseVDisk: dto.BaseVDisk{
                Name:      "data-disk-1",
                VDiskMode: dto.VDiskModeSATA,
                Type:      dto.VDiskTypeData,
            },
            VDiskQoS: dto.VDiskQoS{
                DevLimit:   1000,
                IOPSEnable: true,
            },
            RepositoryID: "repo-1",
            SizeGB:       100,
        },
    },
}

response, err := vdiskService.Create(ctx, request)
```

### Updating VDisks
```go
updateRequest := dto.UpdateRequest{
    BaseVDisk: dto.BaseVDisk{
        ID: "vdisk-123",
    },
    VDiskQoS: dto.VDiskQoS{
        DevLimit: 2000,
    },
    IsQoSEdit: true,
}

response, err := vdiskService.Update(ctx, "vdisk-123", updateRequest)
```

## Migration Strategy

### Phase 1: Parallel Implementation
- New service runs alongside old service
- Adapter provides backward compatibility
- Gradual feature migration

### Phase 2: Feature Toggle
- Configuration-based service selection
- A/B testing capabilities
- Performance comparison

### Phase 3: Full Migration
- Remove old service implementation
- Update all calling code
- Remove adapter layer

## Testing

Comprehensive test coverage includes:
- Unit tests with mocks
- Integration tests
- Pipeline step testing
- Error scenario testing
- Performance testing

### Mock Implementations
- `mockVDiskStore` - Storage operations
- `mockRepositoryService` - Repository operations
- `mockVMService` - VM operations
- `mockStorageService` - File operations
- `mockSystemService` - System operations

## Configuration

The service supports configuration for:
- Error retry policies
- Async task timeouts
- Storage paths
- Resource limits
- Feature flags

## Performance Considerations

- Async operations for long-running tasks
- Batch operations to reduce overhead
- Resource pooling for file operations
- Efficient error handling and rollback

## Security

- Input validation and sanitization
- Resource access control
- Audit logging for all operations
- Secure file operations

## Future Enhancements

1. **Advanced Features**
   - Snapshot support
   - Cloning capabilities
   - Migration between repositories
   - Compression and deduplication

2. **Performance Optimizations**
   - Parallel processing
   - Caching strategies
   - Resource pooling
   - Background cleanup

3. **Monitoring & Observability**
   - Metrics collection
   - Health checks
   - Performance monitoring
   - Alerting integration

4. **High Availability**
   - Failover support
   - Data replication
   - Disaster recovery
   - Load balancing

## Dependencies

- `github.com/gogf/gf` - Error handling
- `github.com/marmotedu/component-base` - Base components
- `github.com/marmotedu/log` - Logging
- `github.com/satori/go.uuid` - UUID generation
- `github.com/stretchr/testify` - Testing framework

## Contributing

When contributing to this module:
1. Follow the established patterns
2. Add comprehensive tests
3. Update documentation
4. Ensure backward compatibility
5. Add proper error handling
6. Include audit logging

## Troubleshooting

Common issues and solutions:
1. **Import path errors** - Ensure correct module paths
2. **Interface compatibility** - Check adapter implementation
3. **Test failures** - Verify mock implementations
4. **Performance issues** - Check async task management
5. **Resource leaks** - Verify proper cleanup in rollback

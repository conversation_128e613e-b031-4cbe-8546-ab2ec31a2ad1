package v1

import (
	"context"
	"os"
	"strings"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domainv1 "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/golibrary/errors"
)

type CollStorageInfoSrv interface {
	List(ctx context.Context, opts metav1.ListOptions) (*dto.StorageListDto, error)
}

func (cs collStorageInfoService) List(ctx context.Context, opts metav1.ListOptions) (*dto.StorageListDto, error) {
	collStorage, err := cs.redisStore.CollVolume().List(ctx, opts)
	if err != nil {
		log.L(ctx).Errorf("[Storage_Service] volume get failed - %s", err.Error())

		return nil, errors.WithCode(code.ErrRedisCmd, err.Error())
	}

	var storageDtos dto.StorageListDto
	storageDtos.TotalCount = collStorage.GetTotalCount()
	storageDtos.Storage = make([]*dto.StorageDto, 0)

	storeageList, err := cs.etcdStore.RepositoryPool().List(ctx, opts)
	if err != nil {
		log.L(ctx).Errorf("[Storage_Service] volume get failed - %s", err.Error())
		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	for _, volume := range collStorage.Storage {
		id := cs.getStorageID(volume, storeageList)
		name := cs.getStorageName(volume, storeageList)
		status, types := cs.getStorageStatus(volume, storeageList)
		if id != "unknown" || name != "unknown" {
			storageDtos.Storage = append(storageDtos.Storage, &dto.StorageDto{
				Desc: "info",
				HostName: func() string {
					hostname, err := os.Hostname()
					if err != nil {
						return "unknown"
					}
					return hostname
				}(),
				ID:          id,
				Name:        name,
				Size:        volume.SizeUsedByte + volume.SizeFreeByte,
				Used:        volume.SizeUsedByte,
				PercentUsed: 100 - volume.PercentFree,
				PercentFree: volume.PercentFree,
				Status:      status,
				Type:        types,
			})
		}
	}

	return &storageDtos, nil
}

func (cs collStorageInfoService) getStorageStatus(volume *domain.StorageInfo, storeageList *domainv1.RepositoryPoolList) (string, string) {
	for _, storage := range storeageList.Items {
		if strings.TrimPrefix(volume.VolumePath, "/") == strings.TrimPrefix(storage.LocationVolume, "/") {
			softLimit := int(storage.SoftLimit)
			if int(volume.PercentFree) < softLimit {
				return "warning", "warning"
			}
		}
	}
	return "none", "healthy"
}

func (cs collStorageInfoService) getStorageID(volume *domain.StorageInfo, storeageList *domainv1.RepositoryPoolList) string {
	for _, storage := range storeageList.Items {
		if strings.TrimPrefix(volume.VolumePath, "/") == strings.TrimPrefix(storage.LocationVolume, "/") {
			return storage.RepoId
		}
	}
	return "unknown"
}

func (cs collStorageInfoService) getStorageName(volume *domain.StorageInfo, storeageList *domainv1.RepositoryPoolList) string {
	for _, storage := range storeageList.Items {
		if strings.TrimPrefix(volume.VolumePath, "/") == strings.TrimPrefix(storage.LocationVolume, "/") {
			return storage.Name
		}
	}
	return "unknown"
}

type collStorageInfoService struct {
	redisStore store.RedisFactory
	etcdStore  store.Factory
}

var _ CollStorageInfoSrv = (*collStorageInfoService)(nil)

func newCollStorageInfo(srv *collService) *collStorageInfoService {
	return &collStorageInfoService{
		redisStore: srv.redisStore,
		etcdStore:  srv.etcdStore,
	}
}

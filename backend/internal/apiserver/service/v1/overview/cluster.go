package overview_service

import (
	"context"
	"fmt"
	"strings"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/log"
	domainv1 "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/overview/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
)

// ClusterInfoService provides cluster information collection and analysis
type ClusterInfoService struct {
	store      store.Factory
	redisStore store.RedisFactory
	
	// Service dependencies
	vmService      VMInternalSrv
	hostService    HostInternalSrv
	storageService StorageInternalSrv
	networkService NetworkInternalSrv
}

// NewClusterInfoService creates a new ClusterInfoService
func NewClusterInfoService(
	store store.Factory,
	redisStore store.RedisFactory,
	vmService VMInternalSrv,
	hostService HostInternalSrv,
	storageService StorageInternalSrv,
	networkService NetworkInternalSrv,
) *ClusterInfoService {
	return &ClusterInfoService{
		store:          store,
		redisStore:     redisStore,
		vmService:      vmService,
		hostService:    hostService,
		storageService: storageService,
		networkService: networkService,
	}
}

// GetClusterStatus returns cluster status information
func (s *ClusterInfoService) GetClusterStatus(ctx context.Context, opts metav1.ListOptions) (*dto.ClusterOverviewResponse, error) {
	response := &dto.ClusterOverviewResponse{
		BaseResponse: *dto.NewBaseResponse("success", "Cluster status retrieved successfully"),
		ClusterID:    "default-cluster",
		ClusterName:  "Default Cluster",
	}
	
	// Get VM status
	vmStatus, err := s.vmService.ListVMStatus(ctx, opts)
	if err != nil {
		log.Errorf("[Cluster_Service] failed to get VM status - %v", err)
		return nil, err
	}
	
	// Get Host status
	hostStatus, err := s.hostService.ListHostStatus(ctx, opts)
	if err != nil {
		log.Errorf("[Cluster_Service] failed to get host status - %v", err)
		return nil, err
	}
	
	// Get Storage status
	storageStatus, err := s.storageService.ListRepositoryStatus(ctx, opts)
	if err != nil {
		log.Errorf("[Cluster_Service] failed to get storage status - %v", err)
		return nil, err
	}
	
	// Build summary
	response.Summary = &dto.ClusterSummary{
		VMs:     s.buildResourceSummary(dto.ResourceTypeVM, vmStatus),
		Hosts:   s.buildResourceSummary(dto.ResourceTypeHost, hostStatus),
		Storage: s.buildResourceSummary(dto.ResourceTypeStorage, storageStatus),
	}
	
	// Determine cluster status and reasons
	response.ClusterStatus, response.Reasons = s.determineClusterStatus(vmStatus, hostStatus, storageStatus)
	
	return response, nil
}

// GetClusterStatusLegacy returns cluster status in legacy format (compatible with old collClusterInfo)
func (s *ClusterInfoService) GetClusterStatusLegacy(ctx context.Context, opts metav1.ListOptions) (*LegacyClusterStatusResponse, error) {
	// Get VM usage data
	vmUsage, err := s.redisStore.CollVMUsage().List(ctx, opts)
	if err != nil {
		log.Warnf("[Cluster_Service] failed to get VM usage - %v", err)
		vmUsage = &domain.VMUsageList{} // Use empty list
	}
	
	// Get repository pools
	repoPool, err := s.store.RepositoryPool().List(ctx, opts)
	if err != nil {
		log.Errorf("[Cluster_Service] failed to get repository pools - %v", err)
		return nil, err
	}
	
	// Get volume data
	volumeData, err := s.redisStore.CollVolume().List(ctx, opts)
	if err != nil {
		log.Errorf("[Cluster_Service] failed to get volume data - %v", err)
		return nil, err
	}
	
	// Get VM list
	vmList, err := s.store.VMs().List(ctx, opts)
	if err != nil {
		log.Errorf("[Cluster_Service] failed to get VM list - %v", err)
		return nil, err
	}
	
	// Build legacy response
	response := &LegacyClusterStatusResponse{
		GuestInfo: &GuestSummary{
			Error:   0, // TODO: Calculate error count
			Running: s.getGuestRunning(vmList),
			Warning: s.getGuestWarning(ctx, vmUsage),
			Healthy: int(vmUsage.GetTotalCount()) - s.getGuestWarning(ctx, vmUsage),
		},
		HostInfo: &HostSummary{
			Error:   0,
			Warning: s.getHostWarning(ctx),
			Healthy: 1 - s.getHostWarning(ctx),
		},
		StorageInfo: &StorageSummary{
			Error:   0, // TODO: Calculate error count
			Warning: s.getRepoWarning(repoPool, volumeData),
			Healthy: len(repoPool.Items) - s.getRepoWarning(repoPool, volumeData),
		},
	}
	
	return response, nil
}

// Helper methods

// buildResourceSummary builds resource summary from status entries
func (s *ClusterInfoService) buildResourceSummary(resourceType dto.ResourceType, statusEntries []domainv1.StatusEntry) *dto.ResourceSummary {
	summary := dto.NewResourceSummary(resourceType)
	for _, entry := range statusEntries {
		summary.AddStatus(dto.ConvertDomainSeverity(entry.Severity))
	}
	return summary
}

// determineClusterStatus determines overall cluster status and reasons
func (s *ClusterInfoService) determineClusterStatus(vmStatus, hostStatus, storageStatus []domainv1.StatusEntry) (dto.HealthStatus, []dto.StatusReason) {
	reasons := make([]dto.StatusReason, 0)
	maxSeverity := domainv1.Healthy
	
	// Count status by type
	statusCount := make(map[string]int)
	
	// Process VM status
	for _, status := range vmStatus {
		key := fmt.Sprintf("vm_%s", status.Severity)
		statusCount[key]++
		if status.Severity > maxSeverity {
			maxSeverity = status.Severity
		}
	}
	
	// Process Host status
	for _, status := range hostStatus {
		key := fmt.Sprintf("host_%s", status.Severity)
		statusCount[key]++
		if status.Severity > maxSeverity {
			maxSeverity = status.Severity
		}
	}
	
	// Process Storage status
	for _, status := range storageStatus {
		key := fmt.Sprintf("storage_%s", status.Severity)
		statusCount[key]++
		if status.Severity > maxSeverity {
			maxSeverity = status.Severity
		}
	}
	
	// Build reasons
	for key, count := range statusCount {
		if count > 0 {
			parts := strings.Split(key, "_")
			if len(parts) == 2 {
				resourceType := parts[0]
				severity := parts[1]
				
				if severity != "healthy" {
					reasons = append(reasons, dto.StatusReason{
						Type:        dto.ResourceType(resourceType),
						Severity:    dto.ConvertToHealthStatus(severity),
						Count:       count,
						Description: fmt.Sprintf("%d %s resources in %s state", count, resourceType, severity),
					})
				}
			}
		}
	}
	
	// Convert severity to health status
	var clusterStatus dto.HealthStatus
	switch maxSeverity {
	case domainv1.Error:
		clusterStatus = dto.HealthStatusError
	case domainv1.Warning:
		clusterStatus = dto.HealthStatusWarning
	default:
		clusterStatus = dto.HealthStatusHealthy
	}
	
	return clusterStatus, reasons
}

// Legacy methods for backward compatibility

// getGuestRunning returns the number of running VMs
func (s *ClusterInfoService) getGuestRunning(vmList *domainv1.VMInstanceList) int {
	running := 0
	for _, vm := range vmList.Items {
		if vm.State == domainv1.PowerStateRuning {
			running++
		}
	}
	return running
}

// getGuestWarning returns the number of VMs with warnings
func (s *ClusterInfoService) getGuestWarning(ctx context.Context, vmUsage *domain.VMUsageList) int {
	warning := 0
	for _, vm := range vmUsage.Items {
		allDisconnected := true
		for _, net := range vm.Network {
			if s.networkService != nil {
				if netInterface, err := s.networkService.GetNetworkInterface(ctx, net.Device, metav1.GetOptions{}); err == nil {
					if netInterface.Status == "connected" {
						allDisconnected = false
						break
					}
				}
			}
		}
		if allDisconnected {
			warning++
		}
	}
	return warning
}

// getHostWarning returns the number of hosts with warnings
func (s *ClusterInfoService) getHostWarning(ctx context.Context) int {
	if s.networkService != nil {
		if networkState, err := s.networkService.ListNetworkInterfaces(ctx, metav1.ListOptions{}); err == nil {
			for _, iface := range networkState.Interfaces {
				if iface.Status == "connected" {
					return 0
				}
			}
		}
	}
	return 1
}

// getRepoWarning returns the number of repositories with warnings
func (s *ClusterInfoService) getRepoWarning(repoPool *domainv1.RepositoryPoolList, volumeData *domain.StorageState) int {
	warning := 0
	for _, vol := range volumeData.Storage {
		for _, repo := range repoPool.Items {
			if strings.TrimPrefix(vol.VolumePath, "/") == strings.TrimPrefix(repo.LocationVolume, "/") {
				softLimit := repo.SoftLimit
				if vol.PercentFree < softLimit {
					warning++
				}
			}
		}
	}
	return warning
}

// Legacy response types for backward compatibility

// LegacyClusterStatusResponse represents the legacy cluster status response
type LegacyClusterStatusResponse struct {
	GuestInfo   *GuestSummary   `json:"guest_info"`
	HostInfo    *HostSummary    `json:"host_info"`
	StorageInfo *StorageSummary `json:"storage_info"`
}

// GuestSummary represents guest summary information
type GuestSummary struct {
	Error   int `json:"error"`
	Running int `json:"running"`
	Warning int `json:"warning"`
	Healthy int `json:"healthy"`
}

// HostSummary represents host summary information
type HostSummary struct {
	Error   int `json:"error"`
	Warning int `json:"warning"`
	Healthy int `json:"healthy"`
}

// StorageSummary represents storage summary information
type StorageSummary struct {
	Error   int `json:"error"`
	Warning int `json:"warning"`
	Healthy int `json:"healthy"`
}

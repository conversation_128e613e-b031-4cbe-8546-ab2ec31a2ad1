# Overview Service Refactoring

This document describes the refactoring of the Overview service module (coll* functions) following the architecture pattern of the Image service.

## Architecture Overview

The refactored Overview service follows a clean, modular architecture with the following components:

### Directory Structure
```
backend/internal/apiserver/service/v1/overview/
├── service.go          # Main overview service with core business logic
├── dependencies.go     # Dependency interface definitions
├── cluster.go          # Cluster information service
├── host.go             # Host information service
├── vm.go               # VM information service
├── storage.go          # Storage information service
├── collector.go        # Data collection management
├── strategies.go       # Collection strategies (Strategy pattern)
├── overview_test.go    # Unit tests
├── dto/               # Data Transfer Objects
│   ├── dto.go         # Base DTO definitions
│   ├── request.go     # Request DTOs
│   └── response.go    # Response DTOs
└── README.md          # This documentation
```

## Key Design Patterns

### 1. Service Layer Architecture
- **Main Service**: `OverviewService` - Core orchestration and caching
- **Sub Services**: `ClusterInfoService`, `HostInfoService`, `VMInfoService`, `StorageInfoService`
- **Data Collector**: `DataCollector` - Centralized data collection with caching
- **Dependencies**: Abstracted through interfaces for better testability

### 2. Strategy Pattern (Data Collection)
Different collection strategies for different resource types:
- `VMCollectionStrategy` - VM data collection
- `HostCollectionStrategy` - Host data collection  
- `StorageCollectionStrategy` - Storage data collection
- `NetworkCollectionStrategy` - Network data collection
- `ClusterCollectionStrategy` - Cluster-wide data collection

### 3. Dependency Injection
All external dependencies are injected through interfaces:
- `DataCollectorSrv` - Data collection operations
- `VMInternalSrv` - VM-related operations
- `HostInternalSrv` - Host-related operations
- `StorageInternalSrv` - Storage-related operations
- `NetworkInternalSrv` - Network-related operations
- `SystemInternalSrv` - System-level operations
- `CacheInternalSrv` - Caching operations
- `MetricsInternalSrv` - Metrics operations
- `AlertInternalSrv` - Alerting operations

### 4. DTO Layer Separation
Clean separation of data structures:
- **Base DTOs**: Common data structures (`BaseResource`, `ResourceSummary`, etc.)
- **Request DTOs**: Input validation and structure
- **Response DTOs**: Output formatting and conversion
- **Legacy DTOs**: Backward compatibility structures

### 5. Caching Strategy
Multi-level caching approach:
- **Service Level**: Response caching with TTL
- **Collector Level**: Data source caching
- **Background Collection**: Proactive data collection

## Error Handling

Unified error code management:
```go
var (
    OverviewDataError      = gcode.New(7001, "overview data error", nil)
    OverviewCacheError     = gcode.New(7002, "overview cache error", nil)
    OverviewValidationError = gcode.New(7003, "overview validation error", nil)
    OverviewNotFound       = gcode.New(7004, "overview data not found", nil)
    OverviewTimeout        = gcode.New(7005, "overview operation timeout", nil)
    // ... more error codes
)
```

## Backward Compatibility

### Legacy Function Mapping
The refactored service maintains backward compatibility with existing coll* functions:

- `collClusterInfo` → `CollClusterInfo` (adapter function)
- `collhostinfo` → `CollHostInfo` (adapter function)
- `collguestinfo` → `CollGuestInfo` (adapter function)
- `collstorageinfo` → `CollStorageInfo` (adapter function)

### Legacy Response Types
Maintains existing response structures:
- `LegacyClusterStatusResponse`
- `LegacyHostListResponse`
- `LegacyVMListResponse`
- `LegacyStorageListResponse`

## Key Features

### 1. Multi-Resource Overview
- **Cluster Overview**: Aggregated cluster health and status
- **Host Overview**: Detailed host information and metrics
- **VM Overview**: Virtual machine status and usage
- **Storage Overview**: Storage pool status and capacity

### 2. Real-time Data Collection
- Background data collection workers
- Configurable collection intervals
- Automatic cache refresh
- Error resilience and retry logic

### 3. Health Monitoring
- Resource health scoring (0-100)
- Status aggregation and reasoning
- Alert integration
- Threshold-based warnings

### 4. Performance Optimization
- Concurrent data collection
- Intelligent caching strategies
- Background workers for heavy operations
- Configurable cache TTL

### 5. Extensibility
- Plugin-based collection strategies
- Configurable thresholds and intervals
- Event-driven architecture
- Metrics and monitoring integration

## Usage Examples

### Getting Cluster Overview
```go
request := dto.NewClusterOverviewRequest()
request.IncludeSummary = true
request.IncludeHealth = true
request.IncludePerformance = true

response, err := overviewService.GetClusterOverview(ctx, request)
```

### Getting Host Information
```go
request := &dto.HostOverviewRequest{
    IncludeNetworks:   true,
    IncludeDisks:      true,
    IncludeSystemInfo: true,
}

response, err := overviewService.GetHostOverview(ctx, request)
```

### Using Legacy Functions
```go
// Backward compatible usage
clusterStatus, err := CollClusterInfo(ctx, store, redisStore, metav1.ListOptions{})
hostList, err := CollHostInfo(ctx, store, redisStore, metav1.ListOptions{})
vmList, err := CollGuestInfo(ctx, store, redisStore, metav1.ListOptions{})
storageList, err := CollStorageInfo(ctx, store, redisStore, metav1.ListOptions{})
```

## Data Flow

### 1. Request Processing
```
Client Request → Service → Validation → Cache Check → Data Collection → Response
```

### 2. Data Collection
```
Collection Strategy → Data Sources (etcd/Redis) → Data Aggregation → Cache Storage
```

### 3. Background Processing
```
Background Workers → Periodic Collection → Cache Update → Metrics Recording
```

## Configuration

The service supports configuration for:
- Collection intervals and timeouts
- Cache TTL settings
- Health check thresholds
- Alert configurations
- Resource limits and quotas

## Performance Considerations

- **Concurrent Collection**: Multiple data sources collected in parallel
- **Smart Caching**: Multi-level caching with intelligent invalidation
- **Background Processing**: Heavy operations moved to background workers
- **Resource Pooling**: Efficient resource utilization
- **Graceful Degradation**: Partial data collection on failures

## Security

- Input validation and sanitization
- Resource access control through interfaces
- Audit logging for all operations
- Secure data handling and storage

## Migration Strategy

### Phase 1: Parallel Implementation
- New service runs alongside old functions
- Adapter provides backward compatibility
- Gradual feature migration

### Phase 2: Feature Toggle
- Configuration-based service selection
- A/B testing capabilities
- Performance comparison

### Phase 3: Full Migration
- Remove old coll* functions
- Update all calling code
- Remove adapter layer

## Testing

Comprehensive test coverage includes:
- Unit tests with mocks for all services
- Integration tests with real data sources
- Performance tests for concurrent operations
- Cache behavior testing
- Error scenario testing

### Mock Implementations
- Complete mock implementations for all dependencies
- Configurable mock responses
- Error injection capabilities
- Performance simulation

## Future Enhancements

1. **Advanced Analytics**
   - Trend analysis and prediction
   - Anomaly detection
   - Capacity planning recommendations

2. **Real-time Streaming**
   - WebSocket-based real-time updates
   - Event-driven notifications
   - Live dashboard support

3. **Multi-Cluster Support**
   - Cross-cluster data aggregation
   - Federated monitoring
   - Global resource views

4. **AI/ML Integration**
   - Intelligent alerting
   - Predictive maintenance
   - Automated optimization

## Dependencies

- `github.com/gogf/gf` - Error handling and utilities
- `github.com/marmotedu/component-base` - Base components
- `github.com/marmotedu/log` - Logging framework
- `github.com/satori/go.uuid` - UUID generation
- `github.com/stretchr/testify` - Testing framework

## Contributing

When contributing to this module:
1. Follow the established patterns and interfaces
2. Add comprehensive tests for new functionality
3. Update documentation for API changes
4. Ensure backward compatibility
5. Add proper error handling and logging
6. Include performance considerations

## Troubleshooting

Common issues and solutions:
1. **Cache misses** - Check TTL settings and collection intervals
2. **Performance issues** - Review concurrent collection limits
3. **Data inconsistency** - Verify data source synchronization
4. **Memory usage** - Monitor cache size and cleanup intervals
5. **Error rates** - Check dependency service health

package overview_service

import (
	"context"
	"os"
	"strings"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/log"
	domainv1 "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/overview/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
)

// StorageInfoService provides storage information collection and analysis
type StorageInfoService struct {
	store      store.Factory
	redisStore store.RedisFactory
	
	// Service dependencies
	dataCollector  DataCollectorSrv
	storageService StorageInternalSrv
	systemService  SystemInternalSrv
}

// NewStorageInfoService creates a new StorageInfoService
func NewStorageInfoService(
	store store.Factory,
	redisStore store.RedisFactory,
	dataCollector DataCollectorSrv,
	storageService StorageInternalSrv,
	systemService SystemInternalSrv,
) *StorageInfoService {
	return &StorageInfoService{
		store:          store,
		redisStore:     redisStore,
		dataCollector:  dataCollector,
		storageService: storageService,
		systemService:  systemService,
	}
}

// GetStorageOverview returns storage overview information
func (s *StorageInfoService) GetStorageOverview(ctx context.Context, request *dto.StorageOverviewRequest) (*dto.StorageOverviewResponse, error) {
	response := &dto.StorageOverviewResponse{
		ListResponse: dto.ListResponse{
			BaseResponse: *dto.NewBaseResponse("success", "Storage overview retrieved successfully"),
		},
		Storage: make([]dto.StorageInfo, 0),
	}
	
	// Collect storage data
	storage, err := s.collectStorageData(ctx, request)
	if err != nil {
		log.Errorf("[Storage_Service] failed to collect storage data - %v", err)
		return nil, err
	}
	
	response.Storage = storage
	response.TotalCount = int64(len(storage))
	
	return response, nil
}

// GetStorageList returns storage list in legacy format (compatible with old collstorageinfo)
func (s *StorageInfoService) GetStorageList(ctx context.Context, opts metav1.ListOptions) (*LegacyStorageListResponse, error) {
	// Get volume data from Redis
	volumeData, err := s.redisStore.CollVolume().List(ctx, opts)
	if err != nil {
		log.Errorf("[Storage_Service] failed to get volume data - %v", err)
		return nil, err
	}
	
	// Get repository pools from etcd
	repoList, err := s.store.RepositoryPool().List(ctx, opts)
	if err != nil {
		log.Errorf("[Storage_Service] failed to get repository pools - %v", err)
		return nil, err
	}
	
	// Build legacy response
	response := &LegacyStorageListResponse{
		TotalCount: volumeData.GetTotalCount(),
		Storage:    make([]*LegacyStorageInfo, 0),
	}
	
	// Get hostname
	hostname := s.getHostname()
	
	// Process each volume
	for _, volume := range volumeData.Storage {
		id := s.getStorageID(volume, repoList)
		name := s.getStorageName(volume, repoList)
		status, storageType := s.getStorageStatus(volume, repoList)
		
		if id != "unknown" || name != "unknown" {
			storage := &LegacyStorageInfo{
				Desc:        "info",
				HostName:    hostname,
				ID:          id,
				Name:        name,
				Size:        volume.SizeUsedByte + volume.SizeFreeByte,
				Used:        volume.SizeUsedByte,
				PercentUsed: 100 - volume.PercentFree,
				PercentFree: volume.PercentFree,
				Status:      status,
				Type:        storageType,
			}
			
			response.Storage = append(response.Storage, storage)
		}
	}
	
	return response, nil
}

// collectStorageData collects detailed storage data
func (s *StorageInfoService) collectStorageData(ctx context.Context, request *dto.StorageOverviewRequest) ([]dto.StorageInfo, error) {
	storage := make([]dto.StorageInfo, 0)
	
	// Get repository pools
	repoList, err := s.store.RepositoryPool().List(ctx, request.ListOptions)
	if err != nil {
		return nil, err
	}
	
	// Get volume data if available
	var volumeData *domain.StorageState
	if s.dataCollector != nil {
		if volumes, err := s.dataCollector.CollectVolumeState(ctx, request.ListOptions); err == nil {
			volumeData = volumes
		}
	}
	
	// Get hostname
	hostname := s.getHostname()
	
	// Process each repository pool
	for repoID, repo := range repoList.Items {
		// Apply filters
		if request.HostID != "" && repo.HostID != request.HostID {
			continue
		}
		
		if request.StorageType != "" && repo.Type != request.StorageType {
			continue
		}
		
		// Find matching volume data
		var matchingVolume *domain.StorageInfo
		if volumeData != nil {
			for _, volume := range volumeData.Storage {
				if s.volumeMatchesRepo(volume, repo) {
					matchingVolume = volume
					break
				}
			}
		}
		
		// Build storage info
		storageInfo := dto.StorageInfo{
			BaseResource: dto.BaseResource{
				ID:          repoID,
				Name:        repo.Name,
				Type:        dto.ResourceTypeStorage,
				Status:      s.determineStorageHealth(repo, matchingVolume),
				Description: repo.Description,
			},
			HostName:    hostname,
			StorageType: repo.Type,
		}
		
		// Add usage information if available
		if matchingVolume != nil {
			storageInfo.Size = matchingVolume.SizeUsedByte + matchingVolume.SizeFreeByte
			storageInfo.Used = matchingVolume.SizeUsedByte
			storageInfo.PercentUsed = 100 - matchingVolume.PercentFree
			storageInfo.PercentFree = matchingVolume.PercentFree
			
			// Add usage statistics if requested
			if request.IncludeUsage {
				storageInfo.Usage = s.buildStorageUsage(matchingVolume)
			}
		}
		
		storage = append(storage, storageInfo)
	}
	
	return storage, nil
}

// Helper methods

// getHostname returns the system hostname
func (s *StorageInfoService) getHostname() string {
	if s.systemService != nil {
		if hostname, err := s.systemService.GetHostname(); err == nil {
			return hostname
		}
	}
	
	// Fallback to os.Hostname
	if hostname, err := os.Hostname(); err == nil {
		return hostname
	}
	
	return "unknown"
}

// getStorageID returns the storage ID for a volume
func (s *StorageInfoService) getStorageID(volume *domain.StorageInfo, repoList *domainv1.RepositoryPoolList) string {
	for repoID, repo := range repoList.Items {
		if s.volumeMatchesRepo(volume, repo) {
			return repoID
		}
	}
	return "unknown"
}

// getStorageName returns the storage name for a volume
func (s *StorageInfoService) getStorageName(volume *domain.StorageInfo, repoList *domainv1.RepositoryPoolList) string {
	for _, repo := range repoList.Items {
		if s.volumeMatchesRepo(volume, repo) {
			return repo.Name
		}
	}
	return "unknown"
}

// getStorageStatus returns the storage status and type for a volume
func (s *StorageInfoService) getStorageStatus(volume *domain.StorageInfo, repoList *domainv1.RepositoryPoolList) (string, string) {
	for _, repo := range repoList.Items {
		if s.volumeMatchesRepo(volume, repo) {
			softLimit := int(repo.SoftLimit)
			if int(volume.PercentFree) < softLimit {
				return "warning", "warning"
			}
			return "none", "healthy"
		}
	}
	return "none", "healthy"
}

// volumeMatchesRepo checks if a volume matches a repository pool
func (s *StorageInfoService) volumeMatchesRepo(volume *domain.StorageInfo, repo *domainv1.RepositoryPool) bool {
	return strings.TrimPrefix(volume.VolumePath, "/") == strings.TrimPrefix(repo.LocationVolume, "/")
}

// determineStorageHealth determines storage health status
func (s *StorageInfoService) determineStorageHealth(repo *domainv1.RepositoryPool, volume *domain.StorageInfo) dto.HealthStatus {
	if volume == nil {
		return dto.HealthStatusUnknown
	}
	
	softLimit := repo.SoftLimit
	if volume.PercentFree < softLimit {
		return dto.HealthStatusWarning
	}
	
	// Check for critical threshold (e.g., 95% usage)
	if volume.PercentUsed > 95.0 {
		return dto.HealthStatusError
	}
	
	return dto.HealthStatusHealthy
}

// buildStorageUsage builds storage usage statistics
func (s *StorageInfoService) buildStorageUsage(volume *domain.StorageInfo) *dto.StorageUsage {
	// Mock implementation - should collect real I/O statistics
	return &dto.StorageUsage{
		ReadBytes:    1024 * 1024 * 100, // 100MB
		WriteBytes:   1024 * 1024 * 50,  // 50MB
		ReadOps:      10000,
		WriteOps:     5000,
		ReadLatency:  5.2,  // ms
		WriteLatency: 8.1,  // ms
	}
}

// Legacy response types for backward compatibility

// LegacyStorageListResponse represents the legacy storage list response
type LegacyStorageListResponse struct {
	TotalCount int64                  `json:"total_count"`
	Storage    []*LegacyStorageInfo   `json:"storage"`
}

// LegacyStorageInfo represents legacy storage information
type LegacyStorageInfo struct {
	Desc        string  `json:"desc"`
	HostName    string  `json:"host_name"`
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Size        float64 `json:"size"`
	Status      string  `json:"status"`
	Type        string  `json:"type"`
	Used        float64 `json:"used"`
	PercentUsed float64 `json:"percent_used"`
	PercentFree float64 `json:"percent_free"`
}

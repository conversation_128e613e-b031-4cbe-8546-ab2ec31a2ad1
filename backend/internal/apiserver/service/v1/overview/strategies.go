package overview_service

import (
	"context"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/overview/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
)

// VMCollectionStrategy implements collection strategy for VM data
type VMCollectionStrategy struct {
	store      store.Factory
	redisStore store.RedisFactory
}

// Collect collects VM data
func (s *VMCollectionStrategy) Collect(ctx context.Context, opts metav1.ListOptions) (interface{}, error) {
	// Get VM list from etcd
	vmList, err := s.store.VMs().List(ctx, opts)
	if err != nil {
		log.Errorf("[VMCollectionStrategy] failed to get VM list - %v", err)
		return nil, err
	}
	
	// Get VM usage data from Redis
	vmUsage, err := s.redisStore.CollVMUsage().List(ctx, opts)
	if err != nil {
		log.Warnf("[VMCollectionStrategy] failed to get VM usage - %v", err)
		// Continue with VM list only
	}
	
	// Combine data
	result := &VMCollectionResult{
		VMList:  vmList,
		VMUsage: vmUsage,
	}
	
	return result, nil
}

// GetType returns the resource type
func (s *VMCollectionStrategy) GetType() dto.ResourceType {
	return dto.ResourceTypeVM
}

// GetName returns the strategy name
func (s *VMCollectionStrategy) GetName() string {
	return "vm_collection"
}

// HostCollectionStrategy implements collection strategy for host data
type HostCollectionStrategy struct {
	store      store.Factory
	redisStore store.RedisFactory
}

// Collect collects host data
func (s *HostCollectionStrategy) Collect(ctx context.Context, opts metav1.ListOptions) (interface{}, error) {
	// Get host list from etcd
	hostList, err := s.store.Host().List(ctx, opts)
	if err != nil {
		log.Errorf("[HostCollectionStrategy] failed to get host list - %v", err)
		return nil, err
	}
	
	// Get system utilization from Redis
	sysUtil, err := s.redisStore.CollMonitoring().List(ctx, opts)
	if err != nil {
		log.Warnf("[HostCollectionStrategy] failed to get system utilization - %v", err)
		// Continue with host list only
	}
	
	// Get network data from Redis
	networkData, err := s.redisStore.CollNetwork().List(ctx, opts)
	if err != nil {
		log.Warnf("[HostCollectionStrategy] failed to get network data - %v", err)
		// Continue without network data
	}
	
	// Combine data
	result := &HostCollectionResult{
		HostList:    hostList,
		SysUtil:     sysUtil,
		NetworkData: networkData,
	}
	
	return result, nil
}

// GetType returns the resource type
func (s *HostCollectionStrategy) GetType() dto.ResourceType {
	return dto.ResourceTypeHost
}

// GetName returns the strategy name
func (s *HostCollectionStrategy) GetName() string {
	return "host_collection"
}

// StorageCollectionStrategy implements collection strategy for storage data
type StorageCollectionStrategy struct {
	store      store.Factory
	redisStore store.RedisFactory
}

// Collect collects storage data
func (s *StorageCollectionStrategy) Collect(ctx context.Context, opts metav1.ListOptions) (interface{}, error) {
	// Get repository pools from etcd
	repoList, err := s.store.RepositoryPool().List(ctx, opts)
	if err != nil {
		log.Errorf("[StorageCollectionStrategy] failed to get repository pools - %v", err)
		return nil, err
	}
	
	// Get volume data from Redis
	volumeData, err := s.redisStore.CollVolume().List(ctx, opts)
	if err != nil {
		log.Warnf("[StorageCollectionStrategy] failed to get volume data - %v", err)
		// Continue with repository list only
	}
	
	// Combine data
	result := &StorageCollectionResult{
		RepoList:   repoList,
		VolumeData: volumeData,
	}
	
	return result, nil
}

// GetType returns the resource type
func (s *StorageCollectionStrategy) GetType() dto.ResourceType {
	return dto.ResourceTypeStorage
}

// GetName returns the strategy name
func (s *StorageCollectionStrategy) GetName() string {
	return "storage_collection"
}

// NetworkCollectionStrategy implements collection strategy for network data
type NetworkCollectionStrategy struct {
	store      store.Factory
	redisStore store.RedisFactory
}

// Collect collects network data
func (s *NetworkCollectionStrategy) Collect(ctx context.Context, opts metav1.ListOptions) (interface{}, error) {
	// Get network list from etcd
	networkList, err := s.store.Networks().List(ctx, opts)
	if err != nil {
		log.Errorf("[NetworkCollectionStrategy] failed to get network list - %v", err)
		return nil, err
	}
	
	// Get network state from Redis
	networkState, err := s.redisStore.CollNetwork().List(ctx, opts)
	if err != nil {
		log.Warnf("[NetworkCollectionStrategy] failed to get network state - %v", err)
		// Continue with network list only
	}
	
	// Combine data
	result := &NetworkCollectionResult{
		NetworkList:  networkList,
		NetworkState: networkState,
	}
	
	return result, nil
}

// GetType returns the resource type
func (s *NetworkCollectionStrategy) GetType() dto.ResourceType {
	return dto.ResourceTypeNetwork
}

// GetName returns the strategy name
func (s *NetworkCollectionStrategy) GetName() string {
	return "network_collection"
}

// ClusterCollectionStrategy implements collection strategy for cluster-wide data
type ClusterCollectionStrategy struct {
	store      store.Factory
	redisStore store.RedisFactory
	
	// Sub-strategies
	vmStrategy      *VMCollectionStrategy
	hostStrategy    *HostCollectionStrategy
	storageStrategy *StorageCollectionStrategy
	networkStrategy *NetworkCollectionStrategy
}

// NewClusterCollectionStrategy creates a new cluster collection strategy
func NewClusterCollectionStrategy(store store.Factory, redisStore store.RedisFactory) *ClusterCollectionStrategy {
	return &ClusterCollectionStrategy{
		store:      store,
		redisStore: redisStore,
		vmStrategy: &VMCollectionStrategy{
			store:      store,
			redisStore: redisStore,
		},
		hostStrategy: &HostCollectionStrategy{
			store:      store,
			redisStore: redisStore,
		},
		storageStrategy: &StorageCollectionStrategy{
			store:      store,
			redisStore: redisStore,
		},
		networkStrategy: &NetworkCollectionStrategy{
			store:      store,
			redisStore: redisStore,
		},
	}
}

// Collect collects cluster-wide data
func (s *ClusterCollectionStrategy) Collect(ctx context.Context, opts metav1.ListOptions) (interface{}, error) {
	result := &ClusterCollectionResult{}
	
	// Collect VM data
	if vmData, err := s.vmStrategy.Collect(ctx, opts); err == nil {
		if vmResult, ok := vmData.(*VMCollectionResult); ok {
			result.VMData = vmResult
		}
	} else {
		log.Warnf("[ClusterCollectionStrategy] failed to collect VM data - %v", err)
	}
	
	// Collect Host data
	if hostData, err := s.hostStrategy.Collect(ctx, opts); err == nil {
		if hostResult, ok := hostData.(*HostCollectionResult); ok {
			result.HostData = hostResult
		}
	} else {
		log.Warnf("[ClusterCollectionStrategy] failed to collect host data - %v", err)
	}
	
	// Collect Storage data
	if storageData, err := s.storageStrategy.Collect(ctx, opts); err == nil {
		if storageResult, ok := storageData.(*StorageCollectionResult); ok {
			result.StorageData = storageResult
		}
	} else {
		log.Warnf("[ClusterCollectionStrategy] failed to collect storage data - %v", err)
	}
	
	// Collect Network data
	if networkData, err := s.networkStrategy.Collect(ctx, opts); err == nil {
		if networkResult, ok := networkData.(*NetworkCollectionResult); ok {
			result.NetworkData = networkResult
		}
	} else {
		log.Warnf("[ClusterCollectionStrategy] failed to collect network data - %v", err)
	}
	
	return result, nil
}

// GetType returns the resource type
func (s *ClusterCollectionStrategy) GetType() dto.ResourceType {
	return dto.ResourceTypeCluster
}

// GetName returns the strategy name
func (s *ClusterCollectionStrategy) GetName() string {
	return "cluster_collection"
}

// Collection result types

// VMCollectionResult contains VM collection results
type VMCollectionResult struct {
	VMList  interface{} // *domainv1.VMInstanceList
	VMUsage interface{} // *domain.VMUsageList
}

// HostCollectionResult contains host collection results
type HostCollectionResult struct {
	HostList    interface{} // *domainv1.HostList
	SysUtil     interface{} // *domain.SystemUtilization
	NetworkData interface{} // *domain.NetworkState
}

// StorageCollectionResult contains storage collection results
type StorageCollectionResult struct {
	RepoList   interface{} // *domainv1.RepositoryPoolList
	VolumeData interface{} // *domain.StorageState
}

// NetworkCollectionResult contains network collection results
type NetworkCollectionResult struct {
	NetworkList  interface{} // *domainv1.NetworkList
	NetworkState interface{} // *domain.NetworkState
}

// ClusterCollectionResult contains cluster-wide collection results
type ClusterCollectionResult struct {
	VMData      *VMCollectionResult
	HostData    *HostCollectionResult
	StorageData *StorageCollectionResult
	NetworkData *NetworkCollectionResult
}

// Strategy factory functions

// CreateCollectionStrategy creates a collection strategy by type
func CreateCollectionStrategy(resourceType dto.ResourceType, store store.Factory, redisStore store.RedisFactory) CollectionStrategy {
	switch resourceType {
	case dto.ResourceTypeVM:
		return &VMCollectionStrategy{
			store:      store,
			redisStore: redisStore,
		}
	case dto.ResourceTypeHost:
		return &HostCollectionStrategy{
			store:      store,
			redisStore: redisStore,
		}
	case dto.ResourceTypeStorage:
		return &StorageCollectionStrategy{
			store:      store,
			redisStore: redisStore,
		}
	case dto.ResourceTypeNetwork:
		return &NetworkCollectionStrategy{
			store:      store,
			redisStore: redisStore,
		}
	case dto.ResourceTypeCluster:
		return NewClusterCollectionStrategy(store, redisStore)
	default:
		return nil
	}
}

// GetAvailableStrategies returns all available collection strategies
func GetAvailableStrategies(store store.Factory, redisStore store.RedisFactory) []CollectionStrategy {
	return []CollectionStrategy{
		&VMCollectionStrategy{store: store, redisStore: redisStore},
		&HostCollectionStrategy{store: store, redisStore: redisStore},
		&StorageCollectionStrategy{store: store, redisStore: redisStore},
		&NetworkCollectionStrategy{store: store, redisStore: redisStore},
		NewClusterCollectionStrategy(store, redisStore),
	}
}

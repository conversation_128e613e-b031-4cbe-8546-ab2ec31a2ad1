package overview_service

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domainv1 "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/overview/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
)

// Mock implementations

type mockDataCollectorSrv struct {
	mock.Mock
}

func (m *mockDataCollectorSrv) CollectSystemUtilization(ctx context.Context, opts metav1.ListOptions) (*domain.SystemUtilization, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(*domain.SystemUtilization), args.Error(1)
}

func (m *mockDataCollectorSrv) CollectNetworkState(ctx context.Context, opts metav1.ListOptions) (*domain.NetworkState, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(*domain.NetworkState), args.Error(1)
}

func (m *mockDataCollectorSrv) CollectVMUsage(ctx context.Context, opts metav1.ListOptions) (*domain.VMUsageList, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(*domain.VMUsageList), args.Error(1)
}

func (m *mockDataCollectorSrv) CollectVolumeState(ctx context.Context, opts metav1.ListOptions) (*domain.StorageState, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(*domain.StorageState), args.Error(1)
}

type mockVMInternalSrv struct {
	mock.Mock
}

func (m *mockVMInternalSrv) ListVMs(ctx context.Context, opts metav1.ListOptions) (*domainv1.VMInstanceList, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(*domainv1.VMInstanceList), args.Error(1)
}

func (m *mockVMInternalSrv) ListVMStatus(ctx context.Context, opts metav1.ListOptions) ([]domainv1.StatusEntry, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).([]domainv1.StatusEntry), args.Error(1)
}

func (m *mockVMInternalSrv) GetVMByID(ctx context.Context, vmID string, opts metav1.GetOptions) (*domainv1.VMInstance, error) {
	args := m.Called(ctx, vmID, opts)
	return args.Get(0).(*domainv1.VMInstance), args.Error(1)
}

type mockHostInternalSrv struct {
	mock.Mock
}

func (m *mockHostInternalSrv) ListHosts(ctx context.Context, opts metav1.ListOptions) (*domainv1.HostList, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(*domainv1.HostList), args.Error(1)
}

func (m *mockHostInternalSrv) ListHostStatus(ctx context.Context, opts metav1.ListOptions) ([]domainv1.StatusEntry, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).([]domainv1.StatusEntry), args.Error(1)
}

func (m *mockHostInternalSrv) GetHostByID(ctx context.Context, hostID string, opts metav1.GetOptions) (*domainv1.Host, error) {
	args := m.Called(ctx, hostID, opts)
	return args.Get(0).(*domainv1.Host), args.Error(1)
}

type mockStorageInternalSrv struct {
	mock.Mock
}

func (m *mockStorageInternalSrv) ListRepositoryPools(ctx context.Context, opts metav1.ListOptions) (*domainv1.RepositoryPoolList, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(*domainv1.RepositoryPoolList), args.Error(1)
}

func (m *mockStorageInternalSrv) ListRepositoryStatus(ctx context.Context, opts metav1.ListOptions) ([]domainv1.StatusEntry, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).([]domainv1.StatusEntry), args.Error(1)
}

func (m *mockStorageInternalSrv) GetRepositoryByID(ctx context.Context, repoID string, opts metav1.GetOptions) (*domainv1.RepositoryPool, error) {
	args := m.Called(ctx, repoID, opts)
	return args.Get(0).(*domainv1.RepositoryPool), args.Error(1)
}

type mockNetworkInternalSrv struct {
	mock.Mock
}

func (m *mockNetworkInternalSrv) GetNetworkInterface(ctx context.Context, device string, opts metav1.GetOptions) (*domain.NetworkInterface, error) {
	args := m.Called(ctx, device, opts)
	return args.Get(0).(*domain.NetworkInterface), args.Error(1)
}

func (m *mockNetworkInternalSrv) ListNetworkInterfaces(ctx context.Context, opts metav1.ListOptions) (*domain.NetworkState, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(*domain.NetworkState), args.Error(1)
}

type mockSystemInternalSrv struct {
	mock.Mock
}

func (m *mockSystemInternalSrv) GetHostname() (string, error) {
	args := m.Called()
	return args.String(0), args.Error(1)
}

func (m *mockSystemInternalSrv) GetSystemInfo(ctx context.Context) (map[string]interface{}, error) {
	args := m.Called(ctx)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *mockSystemInternalSrv) CheckSystemHealth(ctx context.Context) (string, error) {
	args := m.Called(ctx)
	return args.String(0), args.Error(1)
}

type mockCacheInternalSrv struct {
	mock.Mock
}

func (m *mockCacheInternalSrv) GetCachedData(ctx context.Context, key string) (interface{}, error) {
	args := m.Called(ctx, key)
	return args.Get(0), args.Error(1)
}

func (m *mockCacheInternalSrv) SetCachedData(ctx context.Context, key string, data interface{}, ttl int) error {
	args := m.Called(ctx, key, data, ttl)
	return args.Error(0)
}

func (m *mockCacheInternalSrv) InvalidateCache(ctx context.Context, pattern string) error {
	args := m.Called(ctx, pattern)
	return args.Error(0)
}

type mockMetricsInternalSrv struct {
	mock.Mock
}

func (m *mockMetricsInternalSrv) RecordMetric(ctx context.Context, name string, value float64, labels map[string]string) error {
	args := m.Called(ctx, name, value, labels)
	return args.Error(0)
}

func (m *mockMetricsInternalSrv) GetMetrics(ctx context.Context, query string) (interface{}, error) {
	args := m.Called(ctx, query)
	return args.Get(0), args.Error(1)
}

func (m *mockMetricsInternalSrv) GetHealthMetrics(ctx context.Context) (map[string]float64, error) {
	args := m.Called(ctx)
	return args.Get(0).(map[string]float64), args.Error(1)
}

type mockAlertInternalSrv struct {
	mock.Mock
}

func (m *mockAlertInternalSrv) SendAlert(ctx context.Context, alert Alert) error {
	args := m.Called(ctx, alert)
	return args.Error(0)
}

func (m *mockAlertInternalSrv) GetActiveAlerts(ctx context.Context) ([]Alert, error) {
	args := m.Called(ctx)
	return args.Get(0).([]Alert), args.Error(1)
}

func (m *mockAlertInternalSrv) ResolveAlert(ctx context.Context, alertID string) error {
	args := m.Called(ctx, alertID)
	return args.Error(0)
}

type mockConfigProvider struct {
	mock.Mock
}

func (m *mockConfigProvider) GetConfig(key string) (interface{}, error) {
	args := m.Called(key)
	return args.Get(0), args.Error(1)
}

func (m *mockConfigProvider) GetThresholds() (map[string]float64, error) {
	args := m.Called()
	return args.Get(0).(map[string]float64), args.Error(1)
}

func (m *mockConfigProvider) GetRefreshInterval() (int, error) {
	args := m.Called()
	return args.Int(0), args.Error(1)
}

// Test setup helper
func setupTestOverviewService() (*OverviewService, *mockDataCollectorSrv, *mockVMInternalSrv, *mockHostInternalSrv, *mockStorageInternalSrv) {
	dataCollector := &mockDataCollectorSrv{}
	vmService := &mockVMInternalSrv{}
	hostService := &mockHostInternalSrv{}
	storageService := &mockStorageInternalSrv{}
	networkService := &mockNetworkInternalSrv{}
	systemService := &mockSystemInternalSrv{}
	cacheService := &mockCacheInternalSrv{}
	metricsService := &mockMetricsInternalSrv{}
	alertService := &mockAlertInternalSrv{}
	config := &mockConfigProvider{}
	
	service := NewOverviewService(
		nil, // store not needed for these tests
		nil, // redis store not needed for these tests
		dataCollector,
		vmService,
		hostService,
		storageService,
		networkService,
		systemService,
		cacheService,
		metricsService,
		alertService,
		config,
	)
	
	return service, dataCollector, vmService, hostService, storageService
}

// Tests

func TestOverviewService_GetClusterOverview(t *testing.T) {
	service, _, vmService, hostService, storageService := setupTestOverviewService()
	defer service.Close()
	
	// Setup mocks
	vmService.On("ListVMStatus", mock.Anything, mock.Anything).Return([]domainv1.StatusEntry{
		{Severity: domainv1.Healthy},
		{Severity: domainv1.Warning},
	}, nil)
	
	hostService.On("ListHostStatus", mock.Anything, mock.Anything).Return([]domainv1.StatusEntry{
		{Severity: domainv1.Healthy},
	}, nil)
	
	storageService.On("ListRepositoryStatus", mock.Anything, mock.Anything).Return([]domainv1.StatusEntry{
		{Severity: domainv1.Healthy},
		{Severity: domainv1.Healthy},
	}, nil)
	
	request := dto.NewClusterOverviewRequest()
	request.IncludeSummary = true
	request.IncludeHealth = true
	
	response, err := service.GetClusterOverview(context.Background(), request)
	
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, "success", response.Status)
	assert.NotNil(t, response.Summary)
	assert.NotNil(t, response.Health)
	
	// Check VM summary
	assert.Equal(t, 2, response.Summary.VMs.Total)
	assert.Equal(t, 1, response.Summary.VMs.Healthy)
	assert.Equal(t, 1, response.Summary.VMs.Warning)
	
	// Check Host summary
	assert.Equal(t, 1, response.Summary.Hosts.Total)
	assert.Equal(t, 1, response.Summary.Hosts.Healthy)
	
	// Check Storage summary
	assert.Equal(t, 2, response.Summary.Storage.Total)
	assert.Equal(t, 2, response.Summary.Storage.Healthy)
	
	// Verify mocks were called
	vmService.AssertExpectations(t)
	hostService.AssertExpectations(t)
	storageService.AssertExpectations(t)
}

func TestDataCollector_CollectSystemUtilization(t *testing.T) {
	// Mock store factory
	mockStore := &store.MockFactory{}
	mockRedisStore := &store.MockRedisFactory{}
	
	// Setup mock data
	expectedSysUtil := &domain.SystemUtilization{
		CPU: &domain.CPUUtilization{
			TotalUsage: "75.5%",
		},
		Memory: &domain.MemoryUtilization{
			Utilization: "60.2%",
		},
	}
	
	mockRedisStore.On("CollMonitoring").Return(&store.MockCollMonitoringStore{})
	mockRedisStore.CollMonitoring().(*store.MockCollMonitoringStore).On("List", mock.Anything, mock.Anything).Return(expectedSysUtil, nil)
	
	collector := NewDataCollector(mockStore, mockRedisStore)
	defer collector.Close()
	
	result, err := collector.CollectSystemUtilization(context.Background(), metav1.ListOptions{})
	
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "75.5%", result.CPU.TotalUsage)
	assert.Equal(t, "60.2%", result.Memory.Utilization)
}

func TestVMCollectionStrategy(t *testing.T) {
	mockStore := &store.MockFactory{}
	mockRedisStore := &store.MockRedisFactory{}
	
	strategy := &VMCollectionStrategy{
		store:      mockStore,
		redisStore: mockRedisStore,
	}
	
	// Setup mock data
	expectedVMList := &domainv1.VMInstanceList{
		ListMeta: metav1.ListMeta{TotalCount: 1},
		Items: map[string]*domainv1.VMInstance{
			"vm-1": {
				Name:  "test-vm",
				State: domainv1.PowerStateRuning,
			},
		},
	}
	
	expectedVMUsage := &domain.VMUsageList{
		ListMeta: metav1.ListMeta{TotalCount: 1},
		Items: map[string]*domain.VMUsage{
			"vm-1": {
				VCPUUsage:   45.2,
				MemoryUsage: 70.8,
			},
		},
	}
	
	mockStore.On("VMs").Return(&store.MockVMStore{})
	mockStore.VMs().(*store.MockVMStore).On("List", mock.Anything, mock.Anything).Return(expectedVMList, nil)
	
	mockRedisStore.On("CollVMUsage").Return(&store.MockCollVMUsageStore{})
	mockRedisStore.CollVMUsage().(*store.MockCollVMUsageStore).On("List", mock.Anything, mock.Anything).Return(expectedVMUsage, nil)
	
	result, err := strategy.Collect(context.Background(), metav1.ListOptions{})
	
	assert.NoError(t, err)
	assert.NotNil(t, result)
	
	vmResult, ok := result.(*VMCollectionResult)
	assert.True(t, ok)
	assert.NotNil(t, vmResult.VMList)
	assert.NotNil(t, vmResult.VMUsage)
	
	assert.Equal(t, dto.ResourceTypeVM, strategy.GetType())
	assert.Equal(t, "vm_collection", strategy.GetName())
}

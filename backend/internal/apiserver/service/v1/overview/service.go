package overview_service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/gogf/gf/errors/gcode"
	"github.com/gogf/gf/errors/gerror"
	"github.com/marmotedu/log"
	uuid "github.com/satori/go.uuid"
	domainv1 "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/overview/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/aclog"
)

// Error codes for Overview operations
var (
	OverviewDataError       = gcode.New(7001, "overview data error", nil)
	OverviewCacheError      = gcode.New(7002, "overview cache error", nil)
	OverviewValidationError = gcode.New(7003, "overview validation error", nil)
	OverviewNotFound        = gcode.New(7004, "overview data not found", nil)
	OverviewTimeout         = gcode.New(7005, "overview operation timeout", nil)
	OverviewServiceError    = gcode.New(7006, "overview service error", nil)
	OverviewHealthError     = gcode.New(7007, "overview health check error", nil)
	OverviewMetricsError    = gcode.New(7008, "overview metrics error", nil)
)

// OverviewService provides system overview and monitoring operations
type OverviewService struct {
	// Data stores
	store      store.Factory
	redisStore store.RedisFactory

	// Service dependencies
	dataCollector  DataCollectorSrv
	vmService      VMInternalSrv
	hostService    HostInternalSrv
	storageService StorageInternalSrv
	networkService NetworkInternalSrv
	systemService  SystemInternalSrv
	cacheService   CacheInternalSrv
	metricsService MetricsInternalSrv
	alertService   AlertInternalSrv

	// Configuration
	config ConfigProvider

	// Internal state
	mu           sync.RWMutex
	lastUpdate   map[string]int64
	cacheEnabled bool
	cacheTTL     int

	// Background workers
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// NewOverviewService creates a new OverviewService instance
func NewOverviewService(
	store store.Factory,
	redisStore store.RedisFactory,
	dataCollector DataCollectorSrv,
	vmService VMInternalSrv,
	hostService HostInternalSrv,
	storageService StorageInternalSrv,
	networkService NetworkInternalSrv,
	systemService SystemInternalSrv,
	cacheService CacheInternalSrv,
	metricsService MetricsInternalSrv,
	alertService AlertInternalSrv,
	config ConfigProvider,
) *OverviewService {
	ctx, cancel := context.WithCancel(context.Background())

	service := &OverviewService{
		store:          store,
		redisStore:     redisStore,
		dataCollector:  dataCollector,
		vmService:      vmService,
		hostService:    hostService,
		storageService: storageService,
		networkService: networkService,
		systemService:  systemService,
		cacheService:   cacheService,
		metricsService: metricsService,
		alertService:   alertService,
		config:         config,
		lastUpdate:     make(map[string]int64),
		cacheEnabled:   true,
		cacheTTL:       300, // 5 minutes default
		ctx:            ctx,
		cancel:         cancel,
	}

	// Start background workers
	service.startBackgroundWorkers()

	return service
}

// GetClusterOverview returns cluster overview information
func (s *OverviewService) GetClusterOverview(ctx context.Context, request *dto.ClusterOverviewRequest) (*dto.ClusterOverviewResponse, error) {
	// Validate request
	if err := request.Validate(); err != nil {
		log.Errorf("[Overview_Service] cluster overview validation failed - %v", err)
		aclog.New("overview", ctx).Error(aclog.SYSTEM_OVERVIEW_FAILURE, "msg", err.Error())
		return nil, gerror.WrapCode(OverviewValidationError, err)
	}

	// Check cache if enabled
	cacheKey := s.buildCacheKey("cluster_overview", request)
	if request.UseCache && s.cacheEnabled {
		if cached, err := s.getCachedResponse(ctx, cacheKey); err == nil {
			if response, ok := cached.(*dto.ClusterOverviewResponse); ok {
				return response, nil
			}
		}
	}

	// Build response
	response := &dto.ClusterOverviewResponse{
		BaseResponse: *dto.NewBaseResponse("success", "Cluster overview retrieved successfully"),
		ClusterID:    s.getClusterID(),
		ClusterName:  s.getClusterName(),
	}

	// Set timestamps
	now := time.Now().Unix()
	response.Timestamp = now
	response.RequestID = s.generateRequestID()

	// Collect data concurrently
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make([]error, 0)

	// Get VM status
	if request.IncludeSummary || request.IncludeHealth {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if vmStatus, err := s.vmService.ListVMStatus(ctx, request.ListOptions); err == nil {
				mu.Lock()
				if response.Summary == nil {
					response.Summary = &dto.ClusterSummary{}
				}
				response.Summary.VMs = s.buildVMSummary(vmStatus)
				mu.Unlock()
			} else {
				mu.Lock()
				errors = append(errors, fmt.Errorf("failed to get VM status: %v", err))
				mu.Unlock()
			}
		}()
	}

	// Get Host status
	if request.IncludeSummary || request.IncludeHealth {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if hostStatus, err := s.hostService.ListHostStatus(ctx, request.ListOptions); err == nil {
				mu.Lock()
				if response.Summary == nil {
					response.Summary = &dto.ClusterSummary{}
				}
				response.Summary.Hosts = s.buildHostSummary(hostStatus)
				mu.Unlock()
			} else {
				mu.Lock()
				errors = append(errors, fmt.Errorf("failed to get host status: %v", err))
				mu.Unlock()
			}
		}()
	}

	// Get Storage status
	if request.IncludeSummary || request.IncludeHealth {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if storageStatus, err := s.storageService.ListRepositoryStatus(ctx, request.ListOptions); err == nil {
				mu.Lock()
				if response.Summary == nil {
					response.Summary = &dto.ClusterSummary{}
				}
				response.Summary.Storage = s.buildStorageSummary(storageStatus)
				mu.Unlock()
			} else {
				mu.Lock()
				errors = append(errors, fmt.Errorf("failed to get storage status: %v", err))
				mu.Unlock()
			}
		}()
	}

	// Get Performance data
	if request.IncludePerformance {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if performance, err := s.collectPerformanceData(ctx, request.MetricsTimeRange); err == nil {
				mu.Lock()
				response.Performance = performance
				mu.Unlock()
			} else {
				mu.Lock()
				errors = append(errors, fmt.Errorf("failed to get performance data: %v", err))
				mu.Unlock()
			}
		}()
	}

	// Get Capacity data
	if request.IncludeCapacity {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if capacity, err := s.collectCapacityData(ctx); err == nil {
				mu.Lock()
				response.Capacity = capacity
				mu.Unlock()
			} else {
				mu.Lock()
				errors = append(errors, fmt.Errorf("failed to get capacity data: %v", err))
				mu.Unlock()
			}
		}()
	}

	// Get Alerts
	if request.IncludeAlerts {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if alerts, err := s.getRecentAlerts(ctx, 10); err == nil {
				mu.Lock()
				response.Alerts = alerts
				mu.Unlock()
			} else {
				mu.Lock()
				errors = append(errors, fmt.Errorf("failed to get alerts: %v", err))
				mu.Unlock()
			}
		}()
	}

	// Wait for all goroutines to complete
	wg.Wait()

	// Check for errors
	if len(errors) > 0 {
		log.Warnf("[Overview_Service] some data collection failed: %v", errors)
		// Continue with partial data
	}

	// Determine cluster status and reasons
	if response.Summary != nil {
		response.ClusterStatus, response.Reasons = s.determineClusterStatus(response.Summary)
	}

	// Build health information
	if request.IncludeHealth {
		response.Health = s.buildClusterHealth(response.Summary, response.ClusterStatus)
	}

	// Cache response if enabled
	if request.UseCache && s.cacheEnabled {
		s.setCachedResponse(ctx, cacheKey, response, request.CacheTTL)
	}

	// Record metrics
	if s.metricsService != nil {
		s.metricsService.RecordMetric(ctx, "overview_cluster_requests_total", 1, map[string]string{
			"status": "success",
		})
	}

	aclog.New("overview", ctx).Info(aclog.SYSTEM_OVERVIEW_SUCCESS, "type", "cluster")
	return response, nil
}

// GetHostOverview returns host overview information
func (s *OverviewService) GetHostOverview(ctx context.Context, request *dto.HostOverviewRequest) (*dto.HostOverviewResponse, error) {
	// Validate request
	if err := request.Validate(); err != nil {
		log.Errorf("[Overview_Service] host overview validation failed - %v", err)
		return nil, gerror.WrapCode(OverviewValidationError, err)
	}

	// Check cache
	cacheKey := s.buildCacheKey("host_overview", request)
	if request.UseCache && s.cacheEnabled {
		if cached, err := s.getCachedResponse(ctx, cacheKey); err == nil {
			if response, ok := cached.(*dto.HostOverviewResponse); ok {
				return response, nil
			}
		}
	}

	// Build response
	response := &dto.HostOverviewResponse{
		ListResponse: dto.ListResponse{
			BaseResponse: *dto.NewBaseResponse("success", "Host overview retrieved successfully"),
		},
		Hosts: make([]dto.HostInfo, 0),
	}

	// Set timestamps
	now := time.Now().Unix()
	response.Timestamp = now
	response.RequestID = s.generateRequestID()

	// Collect host data
	hosts, err := s.collectHostData(ctx, request)
	if err != nil {
		log.Errorf("[Overview_Service] failed to collect host data - %v", err)
		return nil, gerror.WrapCode(OverviewDataError, err)
	}

	response.Hosts = hosts
	response.TotalCount = int64(len(hosts))

	// Cache response
	if request.UseCache && s.cacheEnabled {
		s.setCachedResponse(ctx, cacheKey, response, request.CacheTTL)
	}

	// Record metrics
	if s.metricsService != nil {
		s.metricsService.RecordMetric(ctx, "overview_host_requests_total", 1, map[string]string{
			"status": "success",
		})
	}

	aclog.New("overview", ctx).Info(aclog.SYSTEM_OVERVIEW_SUCCESS, "type", "host", "count", len(hosts))
	return response, nil
}

// GetVMOverview returns VM overview information
func (s *OverviewService) GetVMOverview(ctx context.Context, request *dto.VMOverviewRequest) (*dto.VMOverviewResponse, error) {
	// Validate request
	if err := request.Validate(); err != nil {
		log.Errorf("[Overview_Service] VM overview validation failed - %v", err)
		return nil, gerror.WrapCode(OverviewValidationError, err)
	}

	// Check cache
	cacheKey := s.buildCacheKey("vm_overview", request)
	if request.UseCache && s.cacheEnabled {
		if cached, err := s.getCachedResponse(ctx, cacheKey); err == nil {
			if response, ok := cached.(*dto.VMOverviewResponse); ok {
				return response, nil
			}
		}
	}

	// Build response
	response := &dto.VMOverviewResponse{
		ListResponse: dto.ListResponse{
			BaseResponse: *dto.NewBaseResponse("success", "VM overview retrieved successfully"),
		},
		VMs: make([]dto.VMInfo, 0),
	}

	// Set timestamps
	now := time.Now().Unix()
	response.Timestamp = now
	response.RequestID = s.generateRequestID()

	// Collect VM data
	vms, err := s.collectVMData(ctx, request)
	if err != nil {
		log.Errorf("[Overview_Service] failed to collect VM data - %v", err)
		return nil, gerror.WrapCode(OverviewDataError, err)
	}

	response.VMs = vms
	response.TotalCount = int64(len(vms))

	// Cache response
	if request.UseCache && s.cacheEnabled {
		s.setCachedResponse(ctx, cacheKey, response, request.CacheTTL)
	}

	// Record metrics
	if s.metricsService != nil {
		s.metricsService.RecordMetric(ctx, "overview_vm_requests_total", 1, map[string]string{
			"status": "success",
		})
	}

	aclog.New("overview", ctx).Info(aclog.SYSTEM_OVERVIEW_SUCCESS, "type", "vm", "count", len(vms))
	return response, nil
}

// GetStorageOverview returns storage overview information
func (s *OverviewService) GetStorageOverview(ctx context.Context, request *dto.StorageOverviewRequest) (*dto.StorageOverviewResponse, error) {
	// Validate request
	if err := request.Validate(); err != nil {
		log.Errorf("[Overview_Service] storage overview validation failed - %v", err)
		return nil, gerror.WrapCode(OverviewValidationError, err)
	}

	// Check cache
	cacheKey := s.buildCacheKey("storage_overview", request)
	if request.UseCache && s.cacheEnabled {
		if cached, err := s.getCachedResponse(ctx, cacheKey); err == nil {
			if response, ok := cached.(*dto.StorageOverviewResponse); ok {
				return response, nil
			}
		}
	}

	// Build response
	response := &dto.StorageOverviewResponse{
		ListResponse: dto.ListResponse{
			BaseResponse: *dto.NewBaseResponse("success", "Storage overview retrieved successfully"),
		},
		Storage: make([]dto.StorageInfo, 0),
	}

	// Set timestamps
	now := time.Now().Unix()
	response.Timestamp = now
	response.RequestID = s.generateRequestID()

	// Collect storage data
	storage, err := s.collectStorageData(ctx, request)
	if err != nil {
		log.Errorf("[Overview_Service] failed to collect storage data - %v", err)
		return nil, gerror.WrapCode(OverviewDataError, err)
	}

	response.Storage = storage
	response.TotalCount = int64(len(storage))

	// Cache response
	if request.UseCache && s.cacheEnabled {
		s.setCachedResponse(ctx, cacheKey, response, request.CacheTTL)
	}

	// Record metrics
	if s.metricsService != nil {
		s.metricsService.RecordMetric(ctx, "overview_storage_requests_total", 1, map[string]string{
			"status": "success",
		})
	}

	aclog.New("overview", ctx).Info(aclog.SYSTEM_OVERVIEW_SUCCESS, "type", "storage", "count", len(storage))
	return response, nil
}

// Helper methods

// buildCacheKey builds a cache key for the request
func (s *OverviewService) buildCacheKey(prefix string, request interface{}) string {
	// Simple implementation - in production, should use proper serialization
	return fmt.Sprintf("overview:%s:%d", prefix, time.Now().Unix()/300) // 5-minute buckets
}

// getCachedResponse retrieves cached response
func (s *OverviewService) getCachedResponse(ctx context.Context, key string) (interface{}, error) {
	if s.cacheService == nil {
		return nil, fmt.Errorf("cache service not available")
	}
	return s.cacheService.GetCachedData(ctx, key)
}

// setCachedResponse stores response in cache
func (s *OverviewService) setCachedResponse(ctx context.Context, key string, data interface{}, ttl int) {
	if s.cacheService == nil {
		return
	}
	if ttl <= 0 {
		ttl = s.cacheTTL
	}
	s.cacheService.SetCachedData(ctx, key, data, ttl)
}

// generateRequestID generates a unique request ID
func (s *OverviewService) generateRequestID() string {
	return uuid.Must(uuid.NewV4()).String()
}

// getClusterID returns the cluster ID
func (s *OverviewService) getClusterID() string {
	if s.config != nil {
		if id, err := s.config.GetConfig("cluster.id"); err == nil {
			if idStr, ok := id.(string); ok {
				return idStr
			}
		}
	}
	return "default-cluster"
}

// getClusterName returns the cluster name
func (s *OverviewService) getClusterName() string {
	if s.config != nil {
		if name, err := s.config.GetConfig("cluster.name"); err == nil {
			if nameStr, ok := name.(string); ok {
				return nameStr
			}
		}
	}
	return "Default Cluster"
}

// buildVMSummary builds VM resource summary from status entries
func (s *OverviewService) buildVMSummary(statusEntries []domainv1.StatusEntry) *dto.ResourceSummary {
	summary := dto.NewResourceSummary(dto.ResourceTypeVM)
	for _, entry := range statusEntries {
		summary.AddStatus(dto.ConvertDomainSeverity(entry.Severity))
	}
	summary.LastUpdated = time.Now().Unix()
	return summary
}

// buildHostSummary builds host resource summary from status entries
func (s *OverviewService) buildHostSummary(statusEntries []domainv1.StatusEntry) *dto.ResourceSummary {
	summary := dto.NewResourceSummary(dto.ResourceTypeHost)
	for _, entry := range statusEntries {
		summary.AddStatus(dto.ConvertDomainSeverity(entry.Severity))
	}
	summary.LastUpdated = time.Now().Unix()
	return summary
}

// buildStorageSummary builds storage resource summary from status entries
func (s *OverviewService) buildStorageSummary(statusEntries []domainv1.StatusEntry) *dto.ResourceSummary {
	summary := dto.NewResourceSummary(dto.ResourceTypeStorage)
	for _, entry := range statusEntries {
		summary.AddStatus(dto.ConvertDomainSeverity(entry.Severity))
	}
	summary.LastUpdated = time.Now().Unix()
	return summary
}

// determineClusterStatus determines overall cluster status and reasons
func (s *OverviewService) determineClusterStatus(summary *dto.ClusterSummary) (dto.HealthStatus, []dto.StatusReason) {
	reasons := make([]dto.StatusReason, 0)
	maxSeverity := dto.HealthStatusHealthy

	// Check VM status
	if summary.VMs != nil {
		if summary.VMs.Error > 0 {
			maxSeverity = dto.HealthStatusError
			reasons = append(reasons, dto.StatusReason{
				Type:        dto.ResourceTypeVM,
				Severity:    dto.HealthStatusError,
				Count:       summary.VMs.Error,
				Description: fmt.Sprintf("%d VMs in error state", summary.VMs.Error),
			})
		} else if summary.VMs.Warning > 0 && maxSeverity != dto.HealthStatusError {
			maxSeverity = dto.HealthStatusWarning
			reasons = append(reasons, dto.StatusReason{
				Type:        dto.ResourceTypeVM,
				Severity:    dto.HealthStatusWarning,
				Count:       summary.VMs.Warning,
				Description: fmt.Sprintf("%d VMs in warning state", summary.VMs.Warning),
			})
		}
	}

	// Check Host status
	if summary.Hosts != nil {
		if summary.Hosts.Error > 0 {
			maxSeverity = dto.HealthStatusError
			reasons = append(reasons, dto.StatusReason{
				Type:        dto.ResourceTypeHost,
				Severity:    dto.HealthStatusError,
				Count:       summary.Hosts.Error,
				Description: fmt.Sprintf("%d hosts in error state", summary.Hosts.Error),
			})
		} else if summary.Hosts.Warning > 0 && maxSeverity != dto.HealthStatusError {
			maxSeverity = dto.HealthStatusWarning
			reasons = append(reasons, dto.StatusReason{
				Type:        dto.ResourceTypeHost,
				Severity:    dto.HealthStatusWarning,
				Count:       summary.Hosts.Warning,
				Description: fmt.Sprintf("%d hosts in warning state", summary.Hosts.Warning),
			})
		}
	}

	// Check Storage status
	if summary.Storage != nil {
		if summary.Storage.Error > 0 {
			maxSeverity = dto.HealthStatusError
			reasons = append(reasons, dto.StatusReason{
				Type:        dto.ResourceTypeStorage,
				Severity:    dto.HealthStatusError,
				Count:       summary.Storage.Error,
				Description: fmt.Sprintf("%d storage pools in error state", summary.Storage.Error),
			})
		} else if summary.Storage.Warning > 0 && maxSeverity != dto.HealthStatusError {
			maxSeverity = dto.HealthStatusWarning
			reasons = append(reasons, dto.StatusReason{
				Type:        dto.ResourceTypeStorage,
				Severity:    dto.HealthStatusWarning,
				Count:       summary.Storage.Warning,
				Description: fmt.Sprintf("%d storage pools in warning state", summary.Storage.Warning),
			})
		}
	}

	return maxSeverity, reasons
}

// buildClusterHealth builds cluster health information
func (s *OverviewService) buildClusterHealth(summary *dto.ClusterSummary, status dto.HealthStatus) *dto.ClusterHealth {
	health := &dto.ClusterHealth{
		OverallStatus: status,
		Score:         s.calculateHealthScore(summary),
		Components:    make(map[string]dto.HealthStatus),
		LastCheck:     time.Now().Unix(),
		NextCheck:     time.Now().Add(5 * time.Minute).Unix(),
	}

	if summary != nil {
		if summary.VMs != nil {
			health.Components["vms"] = s.getResourceHealth(summary.VMs)
		}
		if summary.Hosts != nil {
			health.Components["hosts"] = s.getResourceHealth(summary.Hosts)
		}
		if summary.Storage != nil {
			health.Components["storage"] = s.getResourceHealth(summary.Storage)
		}
	}

	return health
}

// calculateHealthScore calculates overall health score
func (s *OverviewService) calculateHealthScore(summary *dto.ClusterSummary) int {
	if summary == nil {
		return 0
	}

	totalResources := 0
	healthyResources := 0

	if summary.VMs != nil {
		totalResources += summary.VMs.Total
		healthyResources += summary.VMs.Healthy
	}
	if summary.Hosts != nil {
		totalResources += summary.Hosts.Total
		healthyResources += summary.Hosts.Healthy
	}
	if summary.Storage != nil {
		totalResources += summary.Storage.Total
		healthyResources += summary.Storage.Healthy
	}

	if totalResources == 0 {
		return 100
	}

	return int(float64(healthyResources) / float64(totalResources) * 100)
}

// getResourceHealth determines health status for a resource summary
func (s *OverviewService) getResourceHealth(summary *dto.ResourceSummary) dto.HealthStatus {
	if summary.Error > 0 {
		return dto.HealthStatusError
	}
	if summary.Warning > 0 {
		return dto.HealthStatusWarning
	}
	return dto.HealthStatusHealthy
}

// collectPerformanceData collects performance metrics
func (s *OverviewService) collectPerformanceData(ctx context.Context, timeRange *dto.TimeRange) (*dto.ClusterPerformance, error) {
	// Mock implementation - should collect real performance data
	return &dto.ClusterPerformance{
		CPU: &dto.PerformanceMetrics{
			Timestamp:   time.Now().Unix(),
			CPUUsage:    75.5,
			MemoryUsage: 60.2,
			DiskIO:      1024.0,
			NetworkIO:   2048.0,
			LoadAverage: 1.5,
		},
		Memory: &dto.PerformanceMetrics{
			Timestamp:   time.Now().Unix(),
			CPUUsage:    0,
			MemoryUsage: 60.2,
			DiskIO:      0,
			NetworkIO:   0,
			LoadAverage: 0,
		},
		Storage: &dto.PerformanceMetrics{
			Timestamp:   time.Now().Unix(),
			CPUUsage:    0,
			MemoryUsage: 0,
			DiskIO:      1024.0,
			NetworkIO:   0,
			LoadAverage: 0,
		},
		Network: &dto.PerformanceMetrics{
			Timestamp:   time.Now().Unix(),
			CPUUsage:    0,
			MemoryUsage: 0,
			DiskIO:      0,
			NetworkIO:   2048.0,
			LoadAverage: 0,
		},
	}, nil
}

// collectCapacityData collects capacity information
func (s *OverviewService) collectCapacityData(ctx context.Context) (*dto.ClusterCapacity, error) {
	// Mock implementation - should collect real capacity data
	return &dto.ClusterCapacity{
		CPU: &dto.CapacityInfo{
			Total:     100.0,
			Used:      75.5,
			Available: 24.5,
			Reserved:  0,
			Unit:      "cores",
			Percent:   75.5,
		},
		Memory: &dto.CapacityInfo{
			Total:     1024 * 1024 * 1024 * 64, // 64GB
			Used:      1024 * 1024 * 1024 * 38, // 38GB
			Available: 1024 * 1024 * 1024 * 26, // 26GB
			Reserved:  0,
			Unit:      "bytes",
			Percent:   59.4,
		},
		Storage: &dto.CapacityInfo{
			Total:     1024 * 1024 * 1024 * 1024 * 2, // 2TB
			Used:      1024 * 1024 * 1024 * 1024 * 1, // 1TB
			Available: 1024 * 1024 * 1024 * 1024 * 1, // 1TB
			Reserved:  0,
			Unit:      "bytes",
			Percent:   50.0,
		},
		Network: &dto.CapacityInfo{
			Total:     10000, // 10Gbps
			Used:      2048,  // 2Gbps
			Available: 7952,  // 7.952Gbps
			Reserved:  0,
			Unit:      "Mbps",
			Percent:   20.48,
		},
	}, nil
}

// getRecentAlerts gets recent alerts
func (s *OverviewService) getRecentAlerts(ctx context.Context, limit int) ([]dto.AlertInfo, error) {
	if s.alertService == nil {
		return []dto.AlertInfo{}, nil
	}

	alerts, err := s.alertService.GetActiveAlerts(ctx)
	if err != nil {
		return nil, err
	}

	// Convert to DTO format
	result := make([]dto.AlertInfo, 0, len(alerts))
	for i, alert := range alerts {
		if i >= limit {
			break
		}
		result = append(result, dto.AlertInfo{
			ID:          alert.ID,
			Type:        alert.Type,
			Severity:    dto.ConvertToHealthStatus(alert.Severity),
			Title:       alert.Title,
			Description: alert.Description,
			Source:      alert.Source,
			Timestamp:   alert.Timestamp,
			Resolved:    false,
			Labels:      alert.Labels,
			Annotations: alert.Annotations,
		})
	}

	return result, nil
}

// collectHostData collects host data
func (s *OverviewService) collectHostData(ctx context.Context, request *dto.HostOverviewRequest) ([]dto.HostInfo, error) {
	// Mock implementation - should collect real host data
	hostname := "localhost"
	if s.systemService != nil {
		if h, err := s.systemService.GetHostname(); err == nil {
			hostname = h
		}
	}

	hosts := []dto.HostInfo{
		{
			BaseResource: dto.BaseResource{
				ID:          "host-1",
				Name:        hostname,
				Type:        dto.ResourceTypeHost,
				Status:      dto.HealthStatusHealthy,
				Description: "Primary host",
				CreateTime:  time.Now().Unix() - 86400, // 1 day ago
				UpdateTime:  time.Now().Unix(),
			},
			Hostname:    hostname,
			HostType:    "physical",
			CPUUsage:    75.5,
			MemoryUsage: 60.2,
			Networks: []dto.NetworkInterface{
				{
					Name:       "eth0",
					Status:     "connected",
					IPAddress:  "*************",
					MACAddress: "00:11:22:33:44:55",
					Speed:      1000,
					BytesIn:    1024 * 1024,
					BytesOut:   512 * 1024,
				},
			},
			Disks: []dto.DiskInfo{
				{
					Name:        "/dev/sda1",
					Size:        1024 * 1024 * 1024 * 100, // 100GB
					Used:        1024 * 1024 * 1024 * 50,  // 50GB
					Free:        1024 * 1024 * 1024 * 50,  // 50GB
					PercentUsed: 50.0,
					PercentFree: 50.0,
					MountPoint:  "/",
					FileSystem:  "ext4",
				},
			},
		},
	}

	return hosts, nil
}

// collectVMData collects VM data
func (s *OverviewService) collectVMData(ctx context.Context, request *dto.VMOverviewRequest) ([]dto.VMInfo, error) {
	// Mock implementation - should collect real VM data
	vms := []dto.VMInfo{
		{
			BaseResource: dto.BaseResource{
				ID:          "vm-1",
				Name:        "test-vm-1",
				Type:        dto.ResourceTypeVM,
				Status:      dto.HealthStatusHealthy,
				Description: "Test virtual machine",
				CreateTime:  time.Now().Unix() - 3600, // 1 hour ago
				UpdateTime:  time.Now().Unix(),
			},
			GuestID:     "vm-1",
			HostID:      "host-1",
			State:       "running",
			CPUUsage:    45.2,
			MemoryUsage: 70.8,
			Config: &dto.VMConfig{
				CPUCount:     2,
				MemorySize:   4 * 1024 * 1024 * 1024, // 4GB
				OSType:       "linux",
				Architecture: "x86_64",
			},
			Disks: []dto.VMDisk{
				{
					Name:       "vda",
					Path:       "/var/lib/libvirt/images/vm-1.qcow2",
					Size:       20 * 1024 * 1024 * 1024, // 20GB
					Used:       10 * 1024 * 1024 * 1024, // 10GB
					Type:       "qcow2",
					ReadBytes:  1024 * 1024,
					WriteBytes: 512 * 1024,
					ReadOps:    1000,
					WriteOps:   500,
				},
			},
			Networks: []dto.VMNetwork{
				{
					Name:       "vnet0",
					MACAddress: "52:54:00:12:34:56",
					IPAddress:  "***************",
					BytesIn:    1024 * 512,
					BytesOut:   1024 * 256,
					PacketsIn:  1000,
					PacketsOut: 800,
				},
			},
		},
	}

	return vms, nil
}

// collectStorageData collects storage data
func (s *OverviewService) collectStorageData(ctx context.Context, request *dto.StorageOverviewRequest) ([]dto.StorageInfo, error) {
	// Mock implementation - should collect real storage data
	hostname := "localhost"
	if s.systemService != nil {
		if h, err := s.systemService.GetHostname(); err == nil {
			hostname = h
		}
	}

	storage := []dto.StorageInfo{
		{
			BaseResource: dto.BaseResource{
				ID:          "storage-1",
				Name:        "default-pool",
				Type:        dto.ResourceTypeStorage,
				Status:      dto.HealthStatusHealthy,
				Description: "Default storage pool",
				CreateTime:  time.Now().Unix() - 86400, // 1 day ago
				UpdateTime:  time.Now().Unix(),
			},
			HostName:    hostname,
			Size:        1024 * 1024 * 1024 * 1024, // 1TB
			Used:        1024 * 1024 * 1024 * 500,  // 500GB
			PercentUsed: 50.0,
			PercentFree: 50.0,
			StorageType: "local",
			Usage: &dto.StorageUsage{
				ReadBytes:    1024 * 1024 * 100,
				WriteBytes:   1024 * 1024 * 50,
				ReadOps:      10000,
				WriteOps:     5000,
				ReadLatency:  5.2,
				WriteLatency: 8.1,
			},
		},
	}

	return storage, nil
}

// startBackgroundWorkers starts background worker goroutines
func (s *OverviewService) startBackgroundWorkers() {
	// Start cache cleanup worker
	s.wg.Add(1)
	go s.cacheCleanupWorker()

	// Start metrics collection worker
	s.wg.Add(1)
	go s.metricsCollectionWorker()

	// Start health check worker
	s.wg.Add(1)
	go s.healthCheckWorker()
}

// cacheCleanupWorker periodically cleans up expired cache entries
func (s *OverviewService) cacheCleanupWorker() {
	defer s.wg.Done()

	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			if s.cacheService != nil {
				s.cacheService.InvalidateCache(s.ctx, "overview:*:expired")
			}
		}
	}
}

// metricsCollectionWorker periodically collects metrics
func (s *OverviewService) metricsCollectionWorker() {
	defer s.wg.Done()

	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.collectAndStoreMetrics()
		}
	}
}

// healthCheckWorker periodically performs health checks
func (s *OverviewService) healthCheckWorker() {
	defer s.wg.Done()

	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.performHealthChecks()
		}
	}
}

// collectAndStoreMetrics collects and stores metrics
func (s *OverviewService) collectAndStoreMetrics() {
	if s.metricsService == nil {
		return
	}

	// Collect basic metrics
	s.metricsService.RecordMetric(s.ctx, "overview_service_active", 1, map[string]string{
		"service": "overview",
	})

	// Update last update time
	s.mu.Lock()
	s.lastUpdate["metrics"] = time.Now().Unix()
	s.mu.Unlock()
}

// performHealthChecks performs periodic health checks
func (s *OverviewService) performHealthChecks() {
	// Perform basic health checks
	log.Debugf("[Overview_Service] performing health checks")

	// Update last update time
	s.mu.Lock()
	s.lastUpdate["health"] = time.Now().Unix()
	s.mu.Unlock()
}

// Close closes the service and cleans up resources
func (s *OverviewService) Close() error {
	s.cancel()
	s.wg.Wait()
	return nil
}

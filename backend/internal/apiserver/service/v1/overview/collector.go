package overview_service

import (
	"context"
	"fmt"
	"sync"
	"time"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/overview/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
)

// CollectionStrategy defines the interface for data collection strategies
type CollectionStrategy interface {
	// Collect collects data using the specific strategy
	Collect(ctx context.Context, opts metav1.ListOptions) (interface{}, error)
	// GetType returns the type of data this strategy collects
	GetType() dto.ResourceType
	// GetName returns the name of the strategy
	GetName() string
}

// DataCollector manages data collection using different strategies
type DataCollector struct {
	store      store.Factory
	redisStore store.RedisFactory
	
	// Collection strategies
	strategies map[dto.ResourceType]CollectionStrategy
	
	// Cache management
	cache      map[string]*CacheEntry
	cacheMutex sync.RWMutex
	cacheTTL   time.Duration
	
	// Background collection
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
	
	// Configuration
	collectInterval time.Duration
	enabled         bool
}

// CacheEntry represents a cached data entry
type CacheEntry struct {
	Data      interface{}
	Timestamp time.Time
	TTL       time.Duration
}

// NewDataCollector creates a new DataCollector
func NewDataCollector(store store.Factory, redisStore store.RedisFactory) *DataCollector {
	ctx, cancel := context.WithCancel(context.Background())
	
	collector := &DataCollector{
		store:           store,
		redisStore:      redisStore,
		strategies:      make(map[dto.ResourceType]CollectionStrategy),
		cache:           make(map[string]*CacheEntry),
		cacheTTL:        5 * time.Minute,
		ctx:             ctx,
		cancel:          cancel,
		collectInterval: 1 * time.Minute,
		enabled:         true,
	}
	
	// Register default strategies
	collector.registerDefaultStrategies()
	
	// Start background collection
	if collector.enabled {
		collector.startBackgroundCollection()
	}
	
	return collector
}

// RegisterStrategy registers a collection strategy
func (c *DataCollector) RegisterStrategy(strategy CollectionStrategy) {
	c.strategies[strategy.GetType()] = strategy
}

// CollectSystemUtilization collects system utilization data
func (c *DataCollector) CollectSystemUtilization(ctx context.Context, opts metav1.ListOptions) (*domain.SystemUtilization, error) {
	cacheKey := "system_utilization"
	
	// Check cache first
	if cached := c.getCachedData(cacheKey); cached != nil {
		if sysUtil, ok := cached.(*domain.SystemUtilization); ok {
			return sysUtil, nil
		}
	}
	
	// Collect from Redis
	sysUtil, err := c.redisStore.CollMonitoring().List(ctx, opts)
	if err != nil {
		log.Errorf("[DataCollector] failed to collect system utilization - %v", err)
		return nil, err
	}
	
	// Cache the result
	c.setCachedData(cacheKey, sysUtil, c.cacheTTL)
	
	return sysUtil, nil
}

// CollectNetworkState collects network state data
func (c *DataCollector) CollectNetworkState(ctx context.Context, opts metav1.ListOptions) (*domain.NetworkState, error) {
	cacheKey := "network_state"
	
	// Check cache first
	if cached := c.getCachedData(cacheKey); cached != nil {
		if netState, ok := cached.(*domain.NetworkState); ok {
			return netState, nil
		}
	}
	
	// Collect from Redis
	netState, err := c.redisStore.CollNetwork().List(ctx, opts)
	if err != nil {
		log.Errorf("[DataCollector] failed to collect network state - %v", err)
		return nil, err
	}
	
	// Cache the result
	c.setCachedData(cacheKey, netState, c.cacheTTL)
	
	return netState, nil
}

// CollectVMUsage collects VM usage data
func (c *DataCollector) CollectVMUsage(ctx context.Context, opts metav1.ListOptions) (*domain.VMUsageList, error) {
	cacheKey := "vm_usage"
	
	// Check cache first
	if cached := c.getCachedData(cacheKey); cached != nil {
		if vmUsage, ok := cached.(*domain.VMUsageList); ok {
			return vmUsage, nil
		}
	}
	
	// Collect from Redis
	vmUsage, err := c.redisStore.CollVMUsage().List(ctx, opts)
	if err != nil {
		log.Errorf("[DataCollector] failed to collect VM usage - %v", err)
		return nil, err
	}
	
	// Cache the result
	c.setCachedData(cacheKey, vmUsage, c.cacheTTL)
	
	return vmUsage, nil
}

// CollectVolumeState collects volume state data
func (c *DataCollector) CollectVolumeState(ctx context.Context, opts metav1.ListOptions) (*domain.StorageState, error) {
	cacheKey := "volume_state"
	
	// Check cache first
	if cached := c.getCachedData(cacheKey); cached != nil {
		if volState, ok := cached.(*domain.StorageState); ok {
			return volState, nil
		}
	}
	
	// Collect from Redis
	volState, err := c.redisStore.CollVolume().List(ctx, opts)
	if err != nil {
		log.Errorf("[DataCollector] failed to collect volume state - %v", err)
		return nil, err
	}
	
	// Cache the result
	c.setCachedData(cacheKey, volState, c.cacheTTL)
	
	return volState, nil
}

// CollectByStrategy collects data using a specific strategy
func (c *DataCollector) CollectByStrategy(ctx context.Context, resourceType dto.ResourceType, opts metav1.ListOptions) (interface{}, error) {
	strategy, exists := c.strategies[resourceType]
	if !exists {
		return nil, fmt.Errorf("no strategy found for resource type: %s", resourceType)
	}
	
	cacheKey := fmt.Sprintf("strategy_%s", strategy.GetName())
	
	// Check cache first
	if cached := c.getCachedData(cacheKey); cached != nil {
		return cached, nil
	}
	
	// Collect using strategy
	data, err := strategy.Collect(ctx, opts)
	if err != nil {
		log.Errorf("[DataCollector] strategy %s failed - %v", strategy.GetName(), err)
		return nil, err
	}
	
	// Cache the result
	c.setCachedData(cacheKey, data, c.cacheTTL)
	
	return data, nil
}

// Cache management methods

// getCachedData retrieves data from cache
func (c *DataCollector) getCachedData(key string) interface{} {
	c.cacheMutex.RLock()
	defer c.cacheMutex.RUnlock()
	
	entry, exists := c.cache[key]
	if !exists {
		return nil
	}
	
	// Check if cache entry is expired
	if time.Since(entry.Timestamp) > entry.TTL {
		// Remove expired entry
		delete(c.cache, key)
		return nil
	}
	
	return entry.Data
}

// setCachedData stores data in cache
func (c *DataCollector) setCachedData(key string, data interface{}, ttl time.Duration) {
	c.cacheMutex.Lock()
	defer c.cacheMutex.Unlock()
	
	c.cache[key] = &CacheEntry{
		Data:      data,
		Timestamp: time.Now(),
		TTL:       ttl,
	}
}

// clearExpiredCache removes expired cache entries
func (c *DataCollector) clearExpiredCache() {
	c.cacheMutex.Lock()
	defer c.cacheMutex.Unlock()
	
	now := time.Now()
	for key, entry := range c.cache {
		if now.Sub(entry.Timestamp) > entry.TTL {
			delete(c.cache, key)
		}
	}
}

// Background collection methods

// startBackgroundCollection starts background data collection
func (c *DataCollector) startBackgroundCollection() {
	c.wg.Add(1)
	go c.backgroundCollectionWorker()
	
	c.wg.Add(1)
	go c.cacheCleanupWorker()
}

// backgroundCollectionWorker runs background data collection
func (c *DataCollector) backgroundCollectionWorker() {
	defer c.wg.Done()
	
	ticker := time.NewTicker(c.collectInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.performBackgroundCollection()
		}
	}
}

// cacheCleanupWorker runs cache cleanup
func (c *DataCollector) cacheCleanupWorker() {
	defer c.wg.Done()
	
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.clearExpiredCache()
		}
	}
}

// performBackgroundCollection performs background data collection
func (c *DataCollector) performBackgroundCollection() {
	ctx, cancel := context.WithTimeout(c.ctx, 30*time.Second)
	defer cancel()
	
	// Collect system utilization
	if _, err := c.CollectSystemUtilization(ctx, metav1.ListOptions{}); err != nil {
		log.Warnf("[DataCollector] background collection of system utilization failed - %v", err)
	}
	
	// Collect network state
	if _, err := c.CollectNetworkState(ctx, metav1.ListOptions{}); err != nil {
		log.Warnf("[DataCollector] background collection of network state failed - %v", err)
	}
	
	// Collect VM usage
	if _, err := c.CollectVMUsage(ctx, metav1.ListOptions{}); err != nil {
		log.Warnf("[DataCollector] background collection of VM usage failed - %v", err)
	}
	
	// Collect volume state
	if _, err := c.CollectVolumeState(ctx, metav1.ListOptions{}); err != nil {
		log.Warnf("[DataCollector] background collection of volume state failed - %v", err)
	}
}

// registerDefaultStrategies registers default collection strategies
func (c *DataCollector) registerDefaultStrategies() {
	// Register VM collection strategy
	c.RegisterStrategy(&VMCollectionStrategy{
		store:      c.store,
		redisStore: c.redisStore,
	})
	
	// Register Host collection strategy
	c.RegisterStrategy(&HostCollectionStrategy{
		store:      c.store,
		redisStore: c.redisStore,
	})
	
	// Register Storage collection strategy
	c.RegisterStrategy(&StorageCollectionStrategy{
		store:      c.store,
		redisStore: c.redisStore,
	})
}

// Close closes the data collector and cleans up resources
func (c *DataCollector) Close() error {
	c.cancel()
	c.wg.Wait()
	
	// Clear cache
	c.cacheMutex.Lock()
	c.cache = make(map[string]*CacheEntry)
	c.cacheMutex.Unlock()
	
	return nil
}

// GetCacheStats returns cache statistics
func (c *DataCollector) GetCacheStats() map[string]interface{} {
	c.cacheMutex.RLock()
	defer c.cacheMutex.RUnlock()
	
	stats := map[string]interface{}{
		"total_entries": len(c.cache),
		"cache_ttl":     c.cacheTTL.String(),
		"entries":       make([]map[string]interface{}, 0),
	}
	
	for key, entry := range c.cache {
		entryStats := map[string]interface{}{
			"key":       key,
			"timestamp": entry.Timestamp,
			"ttl":       entry.TTL.String(),
			"age":       time.Since(entry.Timestamp).String(),
		}
		stats["entries"] = append(stats["entries"].([]map[string]interface{}), entryStats)
	}
	
	return stats
}

package dto

import (
	"fmt"
	"time"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
)

// ListRequest represents a generic list request
type ListRequest struct {
	metav1.ListOptions `json:",inline"`
	
	// Filtering options
	Filter *FilterOptions `json:"filter,omitempty"` // Filter criteria
	
	// Sorting options
	Sort *SortOptions `json:"sort,omitempty"` // Sort criteria
	
	// Pagination options
	Pagination *Pagination `json:"pagination,omitempty"` // Pagination settings
	
	// Additional options
	IncludeMetrics bool `json:"include_metrics,omitempty"` // Include performance metrics
	IncludeAlerts  bool `json:"include_alerts,omitempty"`  // Include alert information
	IncludeEvents  bool `json:"include_events,omitempty"`  // Include recent events
	
	// Cache options
	UseCache   bool `json:"use_cache,omitempty"`   // Use cached data if available
	CacheTTL   int  `json:"cache_ttl,omitempty"`   // Cache TTL in seconds
	ForceRefresh bool `json:"force_refresh,omitempty"` // Force refresh from source
}

// ClusterOverviewRequest represents a request for cluster overview
type ClusterOverviewRequest struct {
	ListRequest `json:",inline"`
	
	// Specific options for cluster overview
	IncludeSummary     bool `json:"include_summary,omitempty"`     // Include resource summaries
	IncludeHealth      bool `json:"include_health,omitempty"`      // Include health information
	IncludePerformance bool `json:"include_performance,omitempty"` // Include performance data
	IncludeCapacity    bool `json:"include_capacity,omitempty"`    // Include capacity information
	
	// Time range for metrics
	MetricsTimeRange *TimeRange `json:"metrics_time_range,omitempty"` // Time range for metrics
}

// HostOverviewRequest represents a request for host overview
type HostOverviewRequest struct {
	ListRequest `json:",inline"`
	
	// Host-specific options
	IncludeSystemInfo bool `json:"include_system_info,omitempty"` // Include system information
	IncludeNetworks   bool `json:"include_networks,omitempty"`    // Include network interfaces
	IncludeDisks      bool `json:"include_disks,omitempty"`       // Include disk information
	IncludeProcesses  bool `json:"include_processes,omitempty"`   // Include process information
	
	// Specific host ID (optional)
	HostID string `json:"host_id,omitempty"` // Specific host to query
}

// VMOverviewRequest represents a request for VM overview
type VMOverviewRequest struct {
	ListRequest `json:",inline"`
	
	// VM-specific options
	IncludeConfig    bool `json:"include_config,omitempty"`    // Include VM configuration
	IncludeSnapshots bool `json:"include_snapshots,omitempty"` // Include snapshot information
	IncludeDisks     bool `json:"include_disks,omitempty"`     // Include disk information
	IncludeNetworks  bool `json:"include_networks,omitempty"`  // Include network information
	
	// Filter by host
	HostID string `json:"host_id,omitempty"` // Filter VMs by host
	
	// Filter by status
	Status []string `json:"status,omitempty"` // Filter by VM status
}

// StorageOverviewRequest represents a request for storage overview
type StorageOverviewRequest struct {
	ListRequest `json:",inline"`
	
	// Storage-specific options
	IncludeUsage     bool `json:"include_usage,omitempty"`     // Include usage statistics
	IncludeQuotas    bool `json:"include_quotas,omitempty"`    // Include quota information
	IncludeSnapshots bool `json:"include_snapshots,omitempty"` // Include snapshot information
	IncludeBackups   bool `json:"include_backups,omitempty"`   // Include backup information
	
	// Filter by storage type
	StorageType string `json:"storage_type,omitempty"` // Filter by storage type
	
	// Filter by host
	HostID string `json:"host_id,omitempty"` // Filter storage by host
}

// HealthCheckRequest represents a request for health check
type HealthCheckRequest struct {
	// Resource selection
	ResourceType ResourceType `json:"resource_type,omitempty"` // Type of resource to check
	ResourceID   string       `json:"resource_id,omitempty"`   // Specific resource ID
	
	// Check options
	DeepCheck    bool `json:"deep_check,omitempty"`    // Perform deep health check
	IncludeTests bool `json:"include_tests,omitempty"` // Include individual test results
	
	// Timeout settings
	Timeout int `json:"timeout,omitempty"` // Timeout in seconds
}

// MetricsRequest represents a request for metrics data
type MetricsRequest struct {
	// Resource selection
	ResourceType ResourceType `json:"resource_type,omitempty"` // Type of resource
	ResourceID   string       `json:"resource_id,omitempty"`   // Specific resource ID
	
	// Metrics selection
	MetricNames []string `json:"metric_names,omitempty"` // Specific metrics to retrieve
	
	// Time range
	TimeRange *TimeRange `json:"time_range,omitempty"` // Time range for metrics
	
	// Aggregation options
	Interval    string `json:"interval,omitempty"`    // Aggregation interval (e.g., "5m", "1h")
	Aggregation string `json:"aggregation,omitempty"` // Aggregation function (avg, max, min, sum)
	
	// Output options
	Format string `json:"format,omitempty"` // Output format (json, csv, etc.)
}

// AlertRequest represents a request for alert information
type AlertRequest struct {
	ListRequest `json:",inline"`
	
	// Alert filtering
	Severity     []HealthStatus `json:"severity,omitempty"`     // Filter by severity
	ResourceType ResourceType   `json:"resource_type,omitempty"` // Filter by resource type
	ResourceID   string         `json:"resource_id,omitempty"`   // Filter by resource ID
	Resolved     *bool          `json:"resolved,omitempty"`      // Filter by resolved status
	
	// Time range
	TimeRange *TimeRange `json:"time_range,omitempty"` // Time range for alerts
}

// EventRequest represents a request for event information
type EventRequest struct {
	ListRequest `json:",inline"`
	
	// Event filtering
	EventType    []string     `json:"event_type,omitempty"`    // Filter by event type
	ResourceType ResourceType `json:"resource_type,omitempty"` // Filter by resource type
	ResourceID   string       `json:"resource_id,omitempty"`   // Filter by resource ID
	Action       []string     `json:"action,omitempty"`        // Filter by action
	
	// Time range
	TimeRange *TimeRange `json:"time_range,omitempty"` // Time range for events
}

// ConfigRequest represents a request for configuration
type ConfigRequest struct {
	// Configuration selection
	Keys []string `json:"keys,omitempty"` // Specific configuration keys
	
	// Options
	IncludeDefaults bool `json:"include_defaults,omitempty"` // Include default values
	IncludeMetadata bool `json:"include_metadata,omitempty"` // Include metadata
}

// Validation methods

// Validate validates the ListRequest
func (r *ListRequest) Validate() error {
	if r.Pagination != nil {
		if r.Pagination.Page < 1 {
			return fmt.Errorf("page must be >= 1")
		}
		if r.Pagination.PageSize < 1 || r.Pagination.PageSize > 1000 {
			return fmt.Errorf("page_size must be between 1 and 1000")
		}
	}
	
	if r.Filter != nil && r.Filter.TimeRange != nil {
		if r.Filter.TimeRange.Start > r.Filter.TimeRange.End {
			return fmt.Errorf("time range start must be before end")
		}
	}
	
	return nil
}

// Validate validates the HealthCheckRequest
func (r *HealthCheckRequest) Validate() error {
	if r.Timeout < 0 || r.Timeout > 300 {
		return fmt.Errorf("timeout must be between 0 and 300 seconds")
	}
	
	return nil
}

// Validate validates the MetricsRequest
func (r *MetricsRequest) Validate() error {
	if r.TimeRange != nil {
		if r.TimeRange.Start > r.TimeRange.End {
			return fmt.Errorf("time range start must be before end")
		}
		
		// Check if time range is not too large
		duration := time.Unix(r.TimeRange.End, 0).Sub(time.Unix(r.TimeRange.Start, 0))
		if duration > 30*24*time.Hour { // 30 days
			return fmt.Errorf("time range cannot exceed 30 days")
		}
	}
	
	if r.Interval != "" {
		// Validate interval format
		validIntervals := []string{"1m", "5m", "15m", "30m", "1h", "6h", "12h", "1d"}
		valid := false
		for _, interval := range validIntervals {
			if r.Interval == interval {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("invalid interval: %s", r.Interval)
		}
	}
	
	if r.Aggregation != "" {
		validAggregations := []string{"avg", "max", "min", "sum", "count"}
		valid := false
		for _, agg := range validAggregations {
			if r.Aggregation == agg {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("invalid aggregation: %s", r.Aggregation)
		}
	}
	
	return nil
}

// Helper functions

// NewListRequest creates a new ListRequest with default values
func NewListRequest() *ListRequest {
	return &ListRequest{
		ListOptions: metav1.ListOptions{},
		UseCache:    true,
		CacheTTL:    300, // 5 minutes default
	}
}

// NewClusterOverviewRequest creates a new ClusterOverviewRequest with default values
func NewClusterOverviewRequest() *ClusterOverviewRequest {
	return &ClusterOverviewRequest{
		ListRequest:        *NewListRequest(),
		IncludeSummary:     true,
		IncludeHealth:      true,
		IncludePerformance: false,
		IncludeCapacity:    true,
	}
}

// SetTimeRange sets the time range for the request
func (r *ListRequest) SetTimeRange(start, end int64) {
	if r.Filter == nil {
		r.Filter = &FilterOptions{}
	}
	r.Filter.TimeRange = &TimeRange{
		Start: start,
		End:   end,
	}
}

// SetPagination sets the pagination for the request
func (r *ListRequest) SetPagination(page, pageSize int) {
	r.Pagination = &Pagination{
		Page:     page,
		PageSize: pageSize,
	}
}

package dto

import (
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domainv1 "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

// ResourceType represents the type of resource
type ResourceType string

const (
	ResourceTypeVM      ResourceType = "vm"
	ResourceTypeHost    ResourceType = "host"
	ResourceTypeStorage ResourceType = "storage"
	ResourceTypeCluster ResourceType = "cluster"
	ResourceTypeNetwork ResourceType = "network"
)

// HealthStatus represents health status levels
type HealthStatus string

const (
	HealthStatusHealthy HealthStatus = "healthy"
	HealthStatusWarning HealthStatus = "warning"
	HealthStatusError   HealthStatus = "error"
	HealthStatusUnknown HealthStatus = "unknown"
)

// String returns the string representation of HealthStatus
func (h HealthStatus) String() string {
	return string(h)
}

// BaseResource contains common resource information
type BaseResource struct {
	ID          string       `json:"id"`           // Resource UUID
	Name        string       `json:"name"`         // Resource name
	Type        ResourceType `json:"type"`         // Resource type
	Status      HealthStatus `json:"status"`       // Health status
	Description string       `json:"description"`  // Resource description
	CreateTime  int64        `json:"create_time"`  // Creation timestamp
	UpdateTime  int64        `json:"update_time"`  // Last update timestamp
}

// ResourceSummary contains summary information for a resource type
type ResourceSummary struct {
	Type        ResourceType `json:"type"`         // Resource type
	Total       int          `json:"total"`        // Total count
	Healthy     int          `json:"healthy"`      // Healthy count
	Warning     int          `json:"warning"`      // Warning count
	Error       int          `json:"error"`        // Error count
	Unknown     int          `json:"unknown"`      // Unknown count
	LastUpdated int64        `json:"last_updated"` // Last update timestamp
}

// UsageMetrics contains usage metrics
type UsageMetrics struct {
	CPUUsage    float64 `json:"cpu_usage"`    // CPU usage percentage (0-100)
	MemoryUsage float64 `json:"memory_usage"` // Memory usage percentage (0-100)
	DiskUsage   float64 `json:"disk_usage"`   // Disk usage percentage (0-100)
	NetworkIn   float64 `json:"network_in"`   // Network input bytes/sec
	NetworkOut  float64 `json:"network_out"`  // Network output bytes/sec
}

// NetworkInterface represents a network interface
type NetworkInterface struct {
	Name        string  `json:"name"`         // Interface name
	Status      string  `json:"status"`       // Interface status (connected/disconnected)
	IPAddress   string  `json:"ip_address"`   // IP address
	MACAddress  string  `json:"mac_address"`  // MAC address
	Speed       int64   `json:"speed"`        // Interface speed (Mbps)
	BytesIn     float64 `json:"bytes_in"`     // Bytes received
	BytesOut    float64 `json:"bytes_out"`    // Bytes transmitted
	PacketsIn   float64 `json:"packets_in"`   // Packets received
	PacketsOut  float64 `json:"packets_out"`  // Packets transmitted
	ErrorsIn    float64 `json:"errors_in"`    // Input errors
	ErrorsOut   float64 `json:"errors_out"`   // Output errors
}

// DiskInfo represents disk information
type DiskInfo struct {
	Name        string  `json:"name"`         // Disk name/path
	Size        float64 `json:"size"`         // Total size in bytes
	Used        float64 `json:"used"`         // Used space in bytes
	Free        float64 `json:"free"`         // Free space in bytes
	PercentUsed float64 `json:"percent_used"` // Usage percentage
	PercentFree float64 `json:"percent_free"` // Free percentage
	MountPoint  string  `json:"mount_point"`  // Mount point
	FileSystem  string  `json:"file_system"`  // File system type
}

// AlertInfo represents alert information
type AlertInfo struct {
	ID          string                 `json:"id"`           // Alert ID
	Type        string                 `json:"type"`         // Alert type
	Severity    HealthStatus           `json:"severity"`     // Alert severity
	Title       string                 `json:"title"`        // Alert title
	Description string                 `json:"description"`  // Alert description
	Source      string                 `json:"source"`       // Alert source
	ResourceID  string                 `json:"resource_id"`  // Related resource ID
	Timestamp   int64                  `json:"timestamp"`    // Alert timestamp
	Resolved    bool                   `json:"resolved"`     // Whether resolved
	Labels      map[string]string      `json:"labels"`       // Alert labels
	Annotations map[string]interface{} `json:"annotations"`  // Alert annotations
}

// StatusReason represents a status reason
type StatusReason struct {
	Type        ResourceType `json:"type"`        // Resource type
	Severity    HealthStatus `json:"severity"`    // Severity level
	Count       int          `json:"count"`       // Count of resources with this status
	Description string       `json:"description"` // Status description
	Details     []string     `json:"details"`     // Detailed information
}

// PerformanceMetrics contains performance metrics
type PerformanceMetrics struct {
	Timestamp   int64   `json:"timestamp"`    // Metric timestamp
	CPUUsage    float64 `json:"cpu_usage"`    // CPU usage percentage
	MemoryUsage float64 `json:"memory_usage"` // Memory usage percentage
	DiskIO      float64 `json:"disk_io"`      // Disk I/O operations per second
	NetworkIO   float64 `json:"network_io"`   // Network I/O bytes per second
	LoadAverage float64 `json:"load_average"` // System load average
}

// HealthCheckResult represents a health check result
type HealthCheckResult struct {
	ResourceID   string                 `json:"resource_id"`   // Resource ID
	ResourceType ResourceType           `json:"resource_type"` // Resource type
	Status       HealthStatus           `json:"status"`        // Health status
	Score        int                    `json:"score"`         // Health score (0-100)
	Message      string                 `json:"message"`       // Status message
	Details      map[string]interface{} `json:"details"`       // Additional details
	Timestamp    int64                  `json:"timestamp"`     // Check timestamp
	Duration     int64                  `json:"duration"`      // Check duration (ms)
}

// ConfigurationInfo represents configuration information
type ConfigurationInfo struct {
	Key         string      `json:"key"`         // Configuration key
	Value       interface{} `json:"value"`       // Configuration value
	Type        string      `json:"type"`        // Value type
	Description string      `json:"description"` // Configuration description
	Default     interface{} `json:"default"`     // Default value
	Required    bool        `json:"required"`    // Whether required
}

// EventInfo represents event information
type EventInfo struct {
	ID          string                 `json:"id"`           // Event ID
	Type        string                 `json:"type"`         // Event type
	Source      string                 `json:"source"`       // Event source
	Subject     string                 `json:"subject"`      // Event subject
	Action      string                 `json:"action"`       // Event action
	ResourceID  string                 `json:"resource_id"`  // Related resource ID
	Timestamp   int64                  `json:"timestamp"`    // Event timestamp
	Data        map[string]interface{} `json:"data"`         // Event data
	Metadata    map[string]string      `json:"metadata"`     // Event metadata
}

// Pagination represents pagination information
type Pagination struct {
	Page     int `json:"page"`      // Current page (1-based)
	PageSize int `json:"page_size"` // Page size
	Total    int `json:"total"`     // Total count
	Pages    int `json:"pages"`     // Total pages
}

// FilterOptions represents filter options
type FilterOptions struct {
	ResourceType ResourceType           `json:"resource_type,omitempty"` // Filter by resource type
	Status       HealthStatus           `json:"status,omitempty"`        // Filter by status
	Name         string                 `json:"name,omitempty"`          // Filter by name (partial match)
	Labels       map[string]string      `json:"labels,omitempty"`        // Filter by labels
	TimeRange    *TimeRange             `json:"time_range,omitempty"`    // Filter by time range
	Custom       map[string]interface{} `json:"custom,omitempty"`        // Custom filters
}

// TimeRange represents a time range
type TimeRange struct {
	Start int64 `json:"start"` // Start timestamp
	End   int64 `json:"end"`   // End timestamp
}

// SortOptions represents sort options
type SortOptions struct {
	Field string `json:"field"` // Sort field
	Order string `json:"order"` // Sort order (asc/desc)
}

// Helper functions

// NewResourceSummary creates a new ResourceSummary
func NewResourceSummary(resourceType ResourceType) *ResourceSummary {
	return &ResourceSummary{
		Type:        resourceType,
		Total:       0,
		Healthy:     0,
		Warning:     0,
		Error:       0,
		Unknown:     0,
		LastUpdated: 0,
	}
}

// AddStatus adds a status to the summary
func (rs *ResourceSummary) AddStatus(status HealthStatus) {
	rs.Total++
	switch status {
	case HealthStatusHealthy:
		rs.Healthy++
	case HealthStatusWarning:
		rs.Warning++
	case HealthStatusError:
		rs.Error++
	default:
		rs.Unknown++
	}
}

// GetHealthPercentage returns the health percentage
func (rs *ResourceSummary) GetHealthPercentage() float64 {
	if rs.Total == 0 {
		return 0
	}
	return float64(rs.Healthy) / float64(rs.Total) * 100
}

// ConvertDomainSeverity converts domain severity to HealthStatus
func ConvertDomainSeverity(severity domainv1.SystemSeverityLevel) HealthStatus {
	switch severity {
	case domainv1.Healthy:
		return HealthStatusHealthy
	case domainv1.Warning:
		return HealthStatusWarning
	case domainv1.Error:
		return HealthStatusError
	default:
		return HealthStatusUnknown
	}
}

// ConvertToHealthStatus converts string to HealthStatus
func ConvertToHealthStatus(status string) HealthStatus {
	switch status {
	case "healthy":
		return HealthStatusHealthy
	case "warning":
		return HealthStatusWarning
	case "error":
		return HealthStatusError
	default:
		return HealthStatusUnknown
	}
}

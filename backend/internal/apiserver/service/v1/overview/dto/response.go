package dto

import (
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
)

// BaseResponse contains common response fields
type BaseResponse struct {
	Status    string `json:"status"`     // Response status (success, error)
	Message   string `json:"message"`    // Response message
	Timestamp int64  `json:"timestamp"`  // Response timestamp
	RequestID string `json:"request_id"` // Request ID for tracing
}

// ListResponse represents a generic list response
type ListResponse struct {
	BaseResponse `json:",inline"`
	
	metav1.ListMeta `json:",inline"` // Standard list metadata
	
	// Pagination information
	Pagination *Pagination `json:"pagination,omitempty"` // Pagination details
	
	// Additional metadata
	CacheHit   bool  `json:"cache_hit,omitempty"`   // Whether data came from cache
	DataAge    int64 `json:"data_age,omitempty"`    // Age of data in seconds
	NextUpdate int64 `json:"next_update,omitempty"` // Next scheduled update timestamp
}

// ClusterOverviewResponse represents cluster overview response
type ClusterOverviewResponse struct {
	BaseResponse `json:",inline"`
	
	// Cluster information
	ClusterID     string       `json:"cluster_id"`     // Cluster identifier
	ClusterName   string       `json:"cluster_name"`   // Cluster name
	ClusterStatus HealthStatus `json:"cluster_status"` // Overall cluster status
	
	// Resource summaries
	Summary *ClusterSummary `json:"summary,omitempty"` // Resource summaries
	
	// Health information
	Health *ClusterHealth `json:"health,omitempty"` // Health details
	
	// Performance metrics
	Performance *ClusterPerformance `json:"performance,omitempty"` // Performance data
	
	// Capacity information
	Capacity *ClusterCapacity `json:"capacity,omitempty"` // Capacity details
	
	// Status reasons
	Reasons []StatusReason `json:"reasons,omitempty"` // Status reasons
	
	// Recent alerts
	Alerts []AlertInfo `json:"alerts,omitempty"` // Recent alerts
	
	// Recent events
	Events []EventInfo `json:"events,omitempty"` // Recent events
}

// ClusterSummary contains cluster resource summaries
type ClusterSummary struct {
	VMs     *ResourceSummary `json:"vms"`     // VM summary
	Hosts   *ResourceSummary `json:"hosts"`   // Host summary
	Storage *ResourceSummary `json:"storage"` // Storage summary
	Network *ResourceSummary `json:"network"` // Network summary
}

// ClusterHealth contains cluster health information
type ClusterHealth struct {
	OverallStatus HealthStatus           `json:"overall_status"` // Overall health status
	Score         int                    `json:"score"`          // Health score (0-100)
	Components    map[string]HealthStatus `json:"components"`     // Component health status
	LastCheck     int64                  `json:"last_check"`     // Last health check timestamp
	NextCheck     int64                  `json:"next_check"`     // Next health check timestamp
}

// ClusterPerformance contains cluster performance metrics
type ClusterPerformance struct {
	CPU     *PerformanceMetrics `json:"cpu"`     // CPU metrics
	Memory  *PerformanceMetrics `json:"memory"`  // Memory metrics
	Storage *PerformanceMetrics `json:"storage"` // Storage metrics
	Network *PerformanceMetrics `json:"network"` // Network metrics
}

// ClusterCapacity contains cluster capacity information
type ClusterCapacity struct {
	CPU     *CapacityInfo `json:"cpu"`     // CPU capacity
	Memory  *CapacityInfo `json:"memory"`  // Memory capacity
	Storage *CapacityInfo `json:"storage"` // Storage capacity
	Network *CapacityInfo `json:"network"` // Network capacity
}

// CapacityInfo contains capacity information
type CapacityInfo struct {
	Total     float64 `json:"total"`     // Total capacity
	Used      float64 `json:"used"`      // Used capacity
	Available float64 `json:"available"` // Available capacity
	Reserved  float64 `json:"reserved"`  // Reserved capacity
	Unit      string  `json:"unit"`      // Capacity unit
	Percent   float64 `json:"percent"`   // Usage percentage
}

// HostOverviewResponse represents host overview response
type HostOverviewResponse struct {
	ListResponse `json:",inline"`
	
	Hosts []HostInfo `json:"hosts"` // Host information list
}

// HostInfo contains detailed host information
type HostInfo struct {
	BaseResource `json:",inline"`
	
	// Host details
	Hostname    string       `json:"hostname"`     // System hostname
	HostType    string       `json:"host_type"`    // Host type
	CPUUsage    float64      `json:"cpu_usage"`    // CPU usage percentage
	MemoryUsage float64      `json:"memory_usage"` // Memory usage percentage
	
	// Network interfaces
	Networks []NetworkInterface `json:"networks,omitempty"` // Network interfaces
	
	// Disk information
	Disks []DiskInfo `json:"disks,omitempty"` // Disk information
	
	// System information
	SystemInfo map[string]interface{} `json:"system_info,omitempty"` // System details
	
	// Performance metrics
	Metrics *PerformanceMetrics `json:"metrics,omitempty"` // Performance data
	
	// Health check results
	HealthCheck *HealthCheckResult `json:"health_check,omitempty"` // Health check result
}

// VMOverviewResponse represents VM overview response
type VMOverviewResponse struct {
	ListResponse `json:",inline"`
	
	VMs []VMInfo `json:"vms"` // VM information list
}

// VMInfo contains detailed VM information
type VMInfo struct {
	BaseResource `json:",inline"`
	
	// VM details
	GuestID     string       `json:"guest_id"`     // Guest ID
	HostID      string       `json:"host_id"`      // Host ID where VM runs
	State       string       `json:"state"`        // VM state
	CPUUsage    float64      `json:"cpu_usage"`    // CPU usage percentage
	MemoryUsage float64      `json:"memory_usage"` // Memory usage percentage
	
	// VM configuration
	Config *VMConfig `json:"config,omitempty"` // VM configuration
	
	// Disk information
	Disks []VMDisk `json:"disks,omitempty"` // VM disks
	
	// Network information
	Networks []VMNetwork `json:"networks,omitempty"` // VM networks
	
	// Performance metrics
	Metrics *PerformanceMetrics `json:"metrics,omitempty"` // Performance data
	
	// Health check results
	HealthCheck *HealthCheckResult `json:"health_check,omitempty"` // Health check result
}

// VMConfig contains VM configuration information
type VMConfig struct {
	CPUCount    int    `json:"cpu_count"`    // Number of CPUs
	MemorySize  int64  `json:"memory_size"`  // Memory size in bytes
	OSType      string `json:"os_type"`      // Operating system type
	Architecture string `json:"architecture"` // CPU architecture
}

// VMDisk contains VM disk information
type VMDisk struct {
	Name        string  `json:"name"`         // Disk name
	Path        string  `json:"path"`         // Disk path
	Size        int64   `json:"size"`         // Disk size in bytes
	Used        int64   `json:"used"`         // Used space in bytes
	Type        string  `json:"type"`         // Disk type
	ReadBytes   float64 `json:"read_bytes"`   // Bytes read
	WriteBytes  float64 `json:"write_bytes"`  // Bytes written
	ReadOps     float64 `json:"read_ops"`     // Read operations
	WriteOps    float64 `json:"write_ops"`    // Write operations
}

// VMNetwork contains VM network information
type VMNetwork struct {
	Name       string  `json:"name"`        // Network interface name
	MACAddress string  `json:"mac_address"` // MAC address
	IPAddress  string  `json:"ip_address"`  // IP address
	BytesIn    float64 `json:"bytes_in"`    // Bytes received
	BytesOut   float64 `json:"bytes_out"`   // Bytes transmitted
	PacketsIn  float64 `json:"packets_in"`  // Packets received
	PacketsOut float64 `json:"packets_out"` // Packets transmitted
}

// StorageOverviewResponse represents storage overview response
type StorageOverviewResponse struct {
	ListResponse `json:",inline"`
	
	Storage []StorageInfo `json:"storage"` // Storage information list
}

// StorageInfo contains detailed storage information
type StorageInfo struct {
	BaseResource `json:",inline"`
	
	// Storage details
	HostName    string  `json:"host_name"`     // Host name
	Size        float64 `json:"size"`          // Total size
	Used        float64 `json:"used"`          // Used space
	PercentUsed float64 `json:"percent_used"`  // Usage percentage
	PercentFree float64 `json:"percent_free"`  // Free percentage
	StorageType string  `json:"storage_type"`  // Storage type
	
	// Usage statistics
	Usage *StorageUsage `json:"usage,omitempty"` // Usage statistics
	
	// Health check results
	HealthCheck *HealthCheckResult `json:"health_check,omitempty"` // Health check result
}

// StorageUsage contains storage usage statistics
type StorageUsage struct {
	ReadBytes    float64 `json:"read_bytes"`    // Bytes read
	WriteBytes   float64 `json:"write_bytes"`   // Bytes written
	ReadOps      float64 `json:"read_ops"`      // Read operations
	WriteOps     float64 `json:"write_ops"`     // Write operations
	ReadLatency  float64 `json:"read_latency"`  // Read latency (ms)
	WriteLatency float64 `json:"write_latency"` // Write latency (ms)
}

// HealthCheckResponse represents health check response
type HealthCheckResponse struct {
	BaseResponse `json:",inline"`
	
	Results []HealthCheckResult `json:"results"` // Health check results
}

// MetricsResponse represents metrics response
type MetricsResponse struct {
	BaseResponse `json:",inline"`
	
	Metrics []MetricData `json:"metrics"` // Metrics data
}

// MetricData contains metric data
type MetricData struct {
	Name        string                 `json:"name"`        // Metric name
	Type        string                 `json:"type"`        // Metric type
	Unit        string                 `json:"unit"`        // Metric unit
	Description string                 `json:"description"` // Metric description
	Values      []MetricValue          `json:"values"`      // Metric values
	Labels      map[string]string      `json:"labels"`      // Metric labels
	Metadata    map[string]interface{} `json:"metadata"`    // Additional metadata
}

// MetricValue contains a metric value with timestamp
type MetricValue struct {
	Timestamp int64   `json:"timestamp"` // Value timestamp
	Value     float64 `json:"value"`     // Metric value
}

// AlertResponse represents alert response
type AlertResponse struct {
	ListResponse `json:",inline"`
	
	Alerts []AlertInfo `json:"alerts"` // Alert information list
}

// EventResponse represents event response
type EventResponse struct {
	ListResponse `json:",inline"`
	
	Events []EventInfo `json:"events"` // Event information list
}

// ConfigResponse represents configuration response
type ConfigResponse struct {
	BaseResponse `json:",inline"`
	
	Configurations []ConfigurationInfo `json:"configurations"` // Configuration list
}

// Helper methods

// NewBaseResponse creates a new BaseResponse
func NewBaseResponse(status, message string) *BaseResponse {
	return &BaseResponse{
		Status:    status,
		Message:   message,
		Timestamp: 0, // Should be set by the service
		RequestID: "", // Should be set by the service
	}
}

// SetSuccess sets the response as successful
func (r *BaseResponse) SetSuccess(message string) {
	r.Status = "success"
	r.Message = message
}

// SetError sets the response as error
func (r *BaseResponse) SetError(message string) {
	r.Status = "error"
	r.Message = message
}

// IsSuccess returns true if the response is successful
func (r *BaseResponse) IsSuccess() bool {
	return r.Status == "success"
}

// IsError returns true if the response is error
func (r *BaseResponse) IsError() bool {
	return r.Status == "error"
}

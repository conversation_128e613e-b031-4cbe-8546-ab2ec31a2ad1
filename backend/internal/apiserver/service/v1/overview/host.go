package overview_service

import (
	"context"
	"os"
	"strconv"
	"strings"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/overview/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
)

// HostInfoService provides host information collection and analysis
type HostInfoService struct {
	store      store.Factory
	redisStore store.RedisFactory
	
	// Service dependencies
	dataCollector  DataCollectorSrv
	systemService  SystemInternalSrv
	networkService NetworkInternalSrv
}

// NewHostInfoService creates a new HostInfoService
func NewHostInfoService(
	store store.Factory,
	redisStore store.RedisFactory,
	dataCollector DataCollectorSrv,
	systemService SystemInternalSrv,
	networkService NetworkInternalSrv,
) *HostInfoService {
	return &HostInfoService{
		store:          store,
		redisStore:     redisStore,
		dataCollector:  dataCollector,
		systemService:  systemService,
		networkService: networkService,
	}
}

// GetHostOverview returns host overview information
func (s *HostInfoService) GetHostOverview(ctx context.Context, request *dto.HostOverviewRequest) (*dto.HostOverviewResponse, error) {
	response := &dto.HostOverviewResponse{
		ListResponse: dto.ListResponse{
			BaseResponse: *dto.NewBaseResponse("success", "Host overview retrieved successfully"),
		},
		Hosts: make([]dto.HostInfo, 0),
	}
	
	// Collect host data
	hosts, err := s.collectHostData(ctx, request)
	if err != nil {
		log.Errorf("[Host_Service] failed to collect host data - %v", err)
		return nil, err
	}
	
	response.Hosts = hosts
	response.TotalCount = int64(len(hosts))
	
	return response, nil
}

// GetHostList returns host list in legacy format (compatible with old collHostInfo)
func (s *HostInfoService) GetHostList(ctx context.Context, opts metav1.ListOptions) (*LegacyHostListResponse, error) {
	// Get system utilization data
	sysUtil, err := s.redisStore.CollMonitoring().List(ctx, opts)
	if err != nil {
		log.Errorf("[Host_Service] failed to get system utilization - %v", err)
		return nil, err
	}
	
	// Get network data
	networkData, err := s.redisStore.CollNetwork().List(ctx, opts)
	if err != nil {
		log.Errorf("[Host_Service] failed to get network data - %v", err)
		return nil, err
	}
	
	// Build legacy response
	response := &LegacyHostListResponse{
		TotalCount: 1, // Single host for now
		Hosts:      make([]*LegacyHostInfo, 0),
	}
	
	// Get hostname
	hostname := s.getHostname()
	
	// Build host info
	host := &LegacyHostInfo{
		Desc:     "Info",
		Name:     hostname,
		CPUUsage: s.parsePercentage(sysUtil.CPU.TotalUsage),
		RAMUsage: s.parsePercentage(sysUtil.Memory.Utilization),
		NICs:     s.buildNICs(sysUtil.Network.Interfaces, networkData.Interfaces),
		Status:   "running",
		HostType: "healthy",
	}
	
	response.Hosts = append(response.Hosts, host)
	
	return response, nil
}

// collectHostData collects detailed host data
func (s *HostInfoService) collectHostData(ctx context.Context, request *dto.HostOverviewRequest) ([]dto.HostInfo, error) {
	hosts := make([]dto.HostInfo, 0)
	
	// Get hostname
	hostname := s.getHostname()
	
	// Collect system utilization if available
	var cpuUsage, memoryUsage float64
	if s.dataCollector != nil {
		if sysUtil, err := s.dataCollector.CollectSystemUtilization(ctx, metav1.ListOptions{}); err == nil {
			cpuUsage = s.parsePercentage(sysUtil.CPU.TotalUsage)
			memoryUsage = s.parsePercentage(sysUtil.Memory.Utilization)
		}
	}
	
	// Build host info
	host := dto.HostInfo{
		BaseResource: dto.BaseResource{
			ID:          "host-1",
			Name:        hostname,
			Type:        dto.ResourceTypeHost,
			Status:      dto.HealthStatusHealthy,
			Description: "Primary host",
		},
		Hostname:    hostname,
		HostType:    "physical",
		CPUUsage:    cpuUsage,
		MemoryUsage: memoryUsage,
	}
	
	// Add network interfaces if requested
	if request.IncludeNetworks {
		networks, err := s.collectNetworkInterfaces(ctx)
		if err == nil {
			host.Networks = networks
		}
	}
	
	// Add disk information if requested
	if request.IncludeDisks {
		disks, err := s.collectDiskInfo(ctx)
		if err == nil {
			host.Disks = disks
		}
	}
	
	// Add system information if requested
	if request.IncludeSystemInfo {
		systemInfo, err := s.collectSystemInfo(ctx)
		if err == nil {
			host.SystemInfo = systemInfo
		}
	}
	
	hosts = append(hosts, host)
	
	return hosts, nil
}

// collectNetworkInterfaces collects network interface information
func (s *HostInfoService) collectNetworkInterfaces(ctx context.Context) ([]dto.NetworkInterface, error) {
	interfaces := make([]dto.NetworkInterface, 0)
	
	if s.networkService != nil {
		if networkState, err := s.networkService.ListNetworkInterfaces(ctx, metav1.ListOptions{}); err == nil {
			for name, iface := range networkState.Interfaces {
				interfaces = append(interfaces, dto.NetworkInterface{
					Name:       name,
					Status:     iface.Status,
					IPAddress:  iface.IPAddress,
					MACAddress: iface.MACAddress,
					Speed:      iface.Speed,
					BytesIn:    iface.BytesIn,
					BytesOut:   iface.BytesOut,
					PacketsIn:  iface.PacketsIn,
					PacketsOut: iface.PacketsOut,
					ErrorsIn:   iface.ErrorsIn,
					ErrorsOut:  iface.ErrorsOut,
				})
			}
		}
	}
	
	return interfaces, nil
}

// collectDiskInfo collects disk information
func (s *HostInfoService) collectDiskInfo(ctx context.Context) ([]dto.DiskInfo, error) {
	disks := make([]dto.DiskInfo, 0)
	
	// Mock implementation - should collect real disk data
	disks = append(disks, dto.DiskInfo{
		Name:        "/dev/sda1",
		Size:        100 * 1024 * 1024 * 1024, // 100GB
		Used:        50 * 1024 * 1024 * 1024,  // 50GB
		Free:        50 * 1024 * 1024 * 1024,  // 50GB
		PercentUsed: 50.0,
		PercentFree: 50.0,
		MountPoint:  "/",
		FileSystem:  "ext4",
	})
	
	return disks, nil
}

// collectSystemInfo collects system information
func (s *HostInfoService) collectSystemInfo(ctx context.Context) (map[string]interface{}, error) {
	systemInfo := make(map[string]interface{})
	
	if s.systemService != nil {
		if info, err := s.systemService.GetSystemInfo(ctx); err == nil {
			systemInfo = info
		}
	}
	
	// Add basic system information
	systemInfo["hostname"] = s.getHostname()
	systemInfo["os"] = "linux"
	systemInfo["architecture"] = "x86_64"
	
	return systemInfo, nil
}

// Helper methods

// getHostname returns the system hostname
func (s *HostInfoService) getHostname() string {
	if s.systemService != nil {
		if hostname, err := s.systemService.GetHostname(); err == nil {
			return hostname
		}
	}
	
	// Fallback to os.Hostname
	if hostname, err := os.Hostname(); err == nil {
		return hostname
	}
	
	return "unknown"
}

// parsePercentage parses percentage string to float64
func (s *HostInfoService) parsePercentage(percentStr string) float64 {
	// Remove % sign if present
	cleanStr := strings.TrimSuffix(percentStr, "%")
	
	// Parse as float
	if value, err := strconv.ParseFloat(cleanStr, 64); err == nil {
		return value
	}
	
	return 0.0
}

// buildNICs builds network interface information from system and network data
func (s *HostInfoService) buildNICs(sysInterfaces map[string]*domain.NetworkInterface, netInterfaces map[string]*domain.NetworkInterface) []LegacyNIC {
	nics := make([]LegacyNIC, 0)
	
	// Merge data from both sources
	interfaceMap := make(map[string]*LegacyNIC)
	
	// Add system interfaces
	for name, iface := range sysInterfaces {
		interfaceMap[name] = &LegacyNIC{
			Name:       name,
			Status:     iface.Status,
			IPAddress:  iface.IPAddress,
			MACAddress: iface.MACAddress,
			Speed:      iface.Speed,
			BytesIn:    iface.BytesIn,
			BytesOut:   iface.BytesOut,
			PacketsIn:  iface.PacketsIn,
			PacketsOut: iface.PacketsOut,
		}
	}
	
	// Update with network data
	for name, iface := range netInterfaces {
		if nic, exists := interfaceMap[name]; exists {
			// Update existing interface
			if iface.Status != "" {
				nic.Status = iface.Status
			}
			if iface.IPAddress != "" {
				nic.IPAddress = iface.IPAddress
			}
			if iface.MACAddress != "" {
				nic.MACAddress = iface.MACAddress
			}
		} else {
			// Add new interface
			interfaceMap[name] = &LegacyNIC{
				Name:       name,
				Status:     iface.Status,
				IPAddress:  iface.IPAddress,
				MACAddress: iface.MACAddress,
				Speed:      iface.Speed,
				BytesIn:    iface.BytesIn,
				BytesOut:   iface.BytesOut,
				PacketsIn:  iface.PacketsIn,
				PacketsOut: iface.PacketsOut,
			}
		}
	}
	
	// Convert map to slice
	for _, nic := range interfaceMap {
		nics = append(nics, *nic)
	}
	
	return nics
}

// Legacy response types for backward compatibility

// LegacyHostListResponse represents the legacy host list response
type LegacyHostListResponse struct {
	TotalCount int64              `json:"total_count"`
	Hosts      []*LegacyHostInfo  `json:"hosts"`
}

// LegacyHostInfo represents legacy host information
type LegacyHostInfo struct {
	Desc     string      `json:"desc"`
	Name     string      `json:"name"`
	CPUUsage float64     `json:"cpu_usage"`
	RAMUsage float64     `json:"ram_usage"`
	NICs     []LegacyNIC `json:"nics"`
	Status   string      `json:"status"`
	HostType string      `json:"type"`
}

// LegacyNIC represents legacy network interface information
type LegacyNIC struct {
	Name       string  `json:"name"`
	Status     string  `json:"status"`
	IPAddress  string  `json:"ip_address"`
	MACAddress string  `json:"mac_address"`
	Speed      int64   `json:"speed"`
	BytesIn    float64 `json:"bytes_in"`
	BytesOut   float64 `json:"bytes_out"`
	PacketsIn  float64 `json:"packets_in"`
	PacketsOut float64 `json:"packets_out"`
}

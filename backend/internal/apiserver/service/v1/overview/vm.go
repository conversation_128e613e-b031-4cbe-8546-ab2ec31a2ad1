package overview_service

import (
	"context"
	"fmt"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/log"
	domainv1 "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/overview/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
)

// VMInfoService provides VM information collection and analysis
type VMInfoService struct {
	store      store.Factory
	redisStore store.RedisFactory
	
	// Service dependencies
	dataCollector  DataCollectorSrv
	vmService      VMInternalSrv
	networkService NetworkInternalSrv
}

// NewVMInfoService creates a new VMInfoService
func NewVMInfoService(
	store store.Factory,
	redisStore store.RedisFactory,
	dataCollector DataCollectorSrv,
	vmService VMInternalSrv,
	networkService NetworkInternalSrv,
) *VMInfoService {
	return &VMInfoService{
		store:          store,
		redisStore:     redisStore,
		dataCollector:  dataCollector,
		vmService:      vmService,
		networkService: networkService,
	}
}

// GetVMOverview returns VM overview information
func (s *VMInfoService) GetVMOverview(ctx context.Context, request *dto.VMOverviewRequest) (*dto.VMOverviewResponse, error) {
	response := &dto.VMOverviewResponse{
		ListResponse: dto.ListResponse{
			BaseResponse: *dto.NewBaseResponse("success", "VM overview retrieved successfully"),
		},
		VMs: make([]dto.VMInfo, 0),
	}
	
	// Collect VM data
	vms, err := s.collectVMData(ctx, request)
	if err != nil {
		log.Errorf("[VM_Service] failed to collect VM data - %v", err)
		return nil, err
	}
	
	response.VMs = vms
	response.TotalCount = int64(len(vms))
	
	return response, nil
}

// GetVMList returns VM list in legacy format (compatible with old collguestinfo)
func (s *VMInfoService) GetVMList(ctx context.Context, opts metav1.ListOptions) (*LegacyVMListResponse, error) {
	// Get VM list from etcd
	vmList, err := s.store.VMs().List(ctx, opts)
	if err != nil {
		log.Errorf("[VM_Service] failed to get VM list - %v", err)
		return nil, err
	}
	
	// Build legacy response
	response := &LegacyVMListResponse{
		TotalCount: vmList.GetTotalCount(),
		VMs:        make([]*LegacyVMInfo, 0),
	}
	
	// Process each VM
	for guestID := range vmList.Items {
		// Get VM usage data from Redis
		vmUsage, err := s.redisStore.CollVMUsage().Get(ctx, guestID, metav1.GetOptions{})
		if err != nil {
			log.Warnf("[VM_Service] failed to get VM usage for %s - %v", guestID, err)
			continue
		}
		
		// Build VM info
		vm := &LegacyVMInfo{
			ID:       guestID,
			Name:     s.getVMName(ctx, guestID),
			CPUUsage: vmUsage.VCPUUsage,
			RAMUsage: vmUsage.MemoryUsage,
			Status:   s.getVMStatus(ctx, guestID),
			Desc:     "info",
			Disks:    s.buildVMDisks(vmUsage.Disk),
			Networks: s.buildVMNetworks(vmUsage.Network),
		}
		
		response.VMs = append(response.VMs, vm)
	}
	
	return response, nil
}

// collectVMData collects detailed VM data
func (s *VMInfoService) collectVMData(ctx context.Context, request *dto.VMOverviewRequest) ([]dto.VMInfo, error) {
	vms := make([]dto.VMInfo, 0)
	
	// Get VM list
	vmList, err := s.store.VMs().List(ctx, request.ListOptions)
	if err != nil {
		return nil, err
	}
	
	// Process each VM
	for guestID, vmInstance := range vmList.Items {
		// Apply filters
		if request.HostID != "" && vmInstance.HostID != request.HostID {
			continue
		}
		
		if len(request.Status) > 0 {
			statusMatch := false
			for _, status := range request.Status {
				if string(vmInstance.State) == status {
					statusMatch = true
					break
				}
			}
			if !statusMatch {
				continue
			}
		}
		
		// Build VM info
		vm := dto.VMInfo{
			BaseResource: dto.BaseResource{
				ID:          guestID,
				Name:        s.getVMName(ctx, guestID),
				Type:        dto.ResourceTypeVM,
				Status:      s.convertVMStateToHealth(vmInstance.State),
				Description: fmt.Sprintf("Virtual machine %s", guestID),
			},
			GuestID: guestID,
			HostID:  vmInstance.HostID,
			State:   string(vmInstance.State),
		}
		
		// Get usage data if available
		if vmUsage, err := s.redisStore.CollVMUsage().Get(ctx, guestID, metav1.GetOptions{}); err == nil {
			vm.CPUUsage = vmUsage.VCPUUsage
			vm.MemoryUsage = vmUsage.MemoryUsage
			
			// Add disk information if requested
			if request.IncludeDisks {
				vm.Disks = s.convertVMDisks(vmUsage.Disk)
			}
			
			// Add network information if requested
			if request.IncludeNetworks {
				vm.Networks = s.convertVMNetworks(vmUsage.Network)
			}
		}
		
		// Add configuration if requested
		if request.IncludeConfig {
			vm.Config = s.buildVMConfig(vmInstance)
		}
		
		vms = append(vms, vm)
	}
	
	return vms, nil
}

// Helper methods

// getVMName returns the VM name
func (s *VMInfoService) getVMName(ctx context.Context, guestID string) string {
	if s.vmService != nil {
		if vm, err := s.vmService.GetVMByID(ctx, guestID, metav1.GetOptions{}); err == nil {
			return vm.Name
		}
	}
	return guestID
}

// getVMStatus returns the VM status
func (s *VMInfoService) getVMStatus(ctx context.Context, guestID string) string {
	if s.vmService != nil {
		if vm, err := s.vmService.GetVMByID(ctx, guestID, metav1.GetOptions{}); err == nil {
			return string(vm.State)
		}
	}
	return "unknown"
}

// convertVMStateToHealth converts VM state to health status
func (s *VMInfoService) convertVMStateToHealth(state domainv1.PowerState) dto.HealthStatus {
	switch state {
	case domainv1.PowerStateRuning:
		return dto.HealthStatusHealthy
	case domainv1.PowerStateStopped:
		return dto.HealthStatusWarning
	case domainv1.PowerStatePaused:
		return dto.HealthStatusWarning
	default:
		return dto.HealthStatusError
	}
}

// buildVMConfig builds VM configuration
func (s *VMInfoService) buildVMConfig(vm *domainv1.VMInstance) *dto.VMConfig {
	return &dto.VMConfig{
		CPUCount:     int(vm.VCPU),
		MemorySize:   vm.Memory,
		OSType:       vm.OSType,
		Architecture: vm.Architecture,
	}
}

// convertVMDisks converts VM disk usage to DTO format
func (s *VMInfoService) convertVMDisks(diskUsage []*domain.DiskUsage) []dto.VMDisk {
	disks := make([]dto.VMDisk, 0, len(diskUsage))
	
	for _, disk := range diskUsage {
		disks = append(disks, dto.VMDisk{
			Name:       disk.Device,
			Path:       disk.Path,
			Size:       disk.Size,
			Used:       disk.Used,
			Type:       disk.Type,
			ReadBytes:  disk.ReadBytes,
			WriteBytes: disk.WriteBytes,
			ReadOps:    disk.ReadOps,
			WriteOps:   disk.WriteOps,
		})
	}
	
	return disks
}

// convertVMNetworks converts VM network usage to DTO format
func (s *VMInfoService) convertVMNetworks(networkUsage []*domain.NetworkUsage) []dto.VMNetwork {
	networks := make([]dto.VMNetwork, 0, len(networkUsage))
	
	for _, net := range networkUsage {
		networks = append(networks, dto.VMNetwork{
			Name:       net.Device,
			MACAddress: net.MACAddress,
			IPAddress:  net.IPAddress,
			BytesIn:    net.BytesIn,
			BytesOut:   net.BytesOut,
			PacketsIn:  net.PacketsIn,
			PacketsOut: net.PacketsOut,
		})
	}
	
	return networks
}

// buildVMDisks builds VM disk information for legacy format
func (s *VMInfoService) buildVMDisks(diskUsage []*domain.DiskUsage) []LegacyVMDisk {
	disks := make([]LegacyVMDisk, 0, len(diskUsage))
	
	for _, disk := range diskUsage {
		disks = append(disks, LegacyVMDisk{
			Device:     disk.Device,
			Path:       disk.Path,
			Size:       disk.Size,
			Used:       disk.Used,
			ReadBytes:  disk.ReadBytes,
			WriteBytes: disk.WriteBytes,
			ReadOps:    disk.ReadOps,
			WriteOps:   disk.WriteOps,
		})
	}
	
	return disks
}

// buildVMNetworks builds VM network information for legacy format
func (s *VMInfoService) buildVMNetworks(networkUsage []*domain.NetworkUsage) []LegacyVMNetwork {
	networks := make([]LegacyVMNetwork, 0, len(networkUsage))
	
	for _, net := range networkUsage {
		networks = append(networks, LegacyVMNetwork{
			Device:     net.Device,
			MACAddress: net.MACAddress,
			IPAddress:  net.IPAddress,
			BytesIn:    net.BytesIn,
			BytesOut:   net.BytesOut,
			PacketsIn:  net.PacketsIn,
			PacketsOut: net.PacketsOut,
		})
	}
	
	return networks
}

// Legacy response types for backward compatibility

// LegacyVMListResponse represents the legacy VM list response
type LegacyVMListResponse struct {
	TotalCount int64            `json:"total_count"`
	VMs        []*LegacyVMInfo  `json:"vms"`
}

// LegacyVMInfo represents legacy VM information
type LegacyVMInfo struct {
	ID       string             `json:"id"`
	Name     string             `json:"name"`
	CPUUsage float64            `json:"cpu_usage"`
	RAMUsage float64            `json:"ram_usage"`
	Status   string             `json:"status"`
	Desc     string             `json:"desc"`
	Disks    []LegacyVMDisk     `json:"disks"`
	Networks []LegacyVMNetwork  `json:"networks"`
}

// LegacyVMDisk represents legacy VM disk information
type LegacyVMDisk struct {
	Device     string  `json:"device"`
	Path       string  `json:"path"`
	Size       int64   `json:"size"`
	Used       int64   `json:"used"`
	ReadBytes  float64 `json:"read_bytes"`
	WriteBytes float64 `json:"write_bytes"`
	ReadOps    float64 `json:"read_ops"`
	WriteOps   float64 `json:"write_ops"`
}

// LegacyVMNetwork represents legacy VM network information
type LegacyVMNetwork struct {
	Device     string  `json:"device"`
	MACAddress string  `json:"mac_address"`
	IPAddress  string  `json:"ip_address"`
	BytesIn    float64 `json:"bytes_in"`
	BytesOut   float64 `json:"bytes_out"`
	PacketsIn  float64 `json:"packets_in"`
	PacketsOut float64 `json:"packets_out"`
}

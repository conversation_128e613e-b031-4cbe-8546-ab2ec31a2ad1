package overview_service

import (
	"context"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domainv1 "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
)

// DataCollectorSrv defines the interface for data collection operations
type DataCollectorSrv interface {
	// CollectSystemUtilization collects system utilization data
	CollectSystemUtilization(ctx context.Context, opts metav1.ListOptions) (*domain.SystemUtilization, error)
	// CollectNetworkState collects network state data
	CollectNetworkState(ctx context.Context, opts metav1.ListOptions) (*domain.NetworkState, error)
	// CollectVMUsage collects VM usage data
	CollectVMUsage(ctx context.Context, opts metav1.ListOptions) (*domain.VMUsageList, error)
	// CollectVolumeState collects volume state data
	CollectVolumeState(ctx context.Context, opts metav1.ListOptions) (*domain.StorageState, error)
}

// VMInternalSrv defines the interface for VM operations
type VMInternalSrv interface {
	// ListVMs returns all VMs
	ListVMs(ctx context.Context, opts metav1.ListOptions) (*domainv1.VMInstanceList, error)
	// ListVMStatus returns VM status information
	ListVMStatus(ctx context.Context, opts metav1.ListOptions) ([]domainv1.StatusEntry, error)
	// GetVMByID returns a specific VM by ID
	GetVMByID(ctx context.Context, vmID string, opts metav1.GetOptions) (*domainv1.VMInstance, error)
}

// HostInternalSrv defines the interface for host operations
type HostInternalSrv interface {
	// ListHosts returns all hosts
	ListHosts(ctx context.Context, opts metav1.ListOptions) (*domainv1.HostList, error)
	// ListHostStatus returns host status information
	ListHostStatus(ctx context.Context, opts metav1.ListOptions) ([]domainv1.StatusEntry, error)
	// GetHostByID returns a specific host by ID
	GetHostByID(ctx context.Context, hostID string, opts metav1.GetOptions) (*domainv1.Host, error)
}

// StorageInternalSrv defines the interface for storage operations
type StorageInternalSrv interface {
	// ListRepositoryPools returns all repository pools
	ListRepositoryPools(ctx context.Context, opts metav1.ListOptions) (*domainv1.RepositoryPoolList, error)
	// ListRepositoryStatus returns repository status information
	ListRepositoryStatus(ctx context.Context, opts metav1.ListOptions) ([]domainv1.StatusEntry, error)
	// GetRepositoryByID returns a specific repository by ID
	GetRepositoryByID(ctx context.Context, repoID string, opts metav1.GetOptions) (*domainv1.RepositoryPool, error)
}

// NetworkInternalSrv defines the interface for network operations
type NetworkInternalSrv interface {
	// GetNetworkInterface returns network interface information
	GetNetworkInterface(ctx context.Context, device string, opts metav1.GetOptions) (*domain.NetworkInterface, error)
	// ListNetworkInterfaces returns all network interfaces
	ListNetworkInterfaces(ctx context.Context, opts metav1.ListOptions) (*domain.NetworkState, error)
}

// SystemInternalSrv defines the interface for system operations
type SystemInternalSrv interface {
	// GetHostname returns the system hostname
	GetHostname() (string, error)
	// GetSystemInfo returns system information
	GetSystemInfo(ctx context.Context) (map[string]interface{}, error)
	// CheckSystemHealth checks system health status
	CheckSystemHealth(ctx context.Context) (string, error)
}

// CacheInternalSrv defines the interface for caching operations
type CacheInternalSrv interface {
	// GetCachedData retrieves cached data by key
	GetCachedData(ctx context.Context, key string) (interface{}, error)
	// SetCachedData stores data in cache
	SetCachedData(ctx context.Context, key string, data interface{}, ttl int) error
	// InvalidateCache invalidates cache entries
	InvalidateCache(ctx context.Context, pattern string) error
}

// MetricsInternalSrv defines the interface for metrics operations
type MetricsInternalSrv interface {
	// RecordMetric records a metric value
	RecordMetric(ctx context.Context, name string, value float64, labels map[string]string) error
	// GetMetrics retrieves metrics data
	GetMetrics(ctx context.Context, query string) (interface{}, error)
	// GetHealthMetrics returns health-related metrics
	GetHealthMetrics(ctx context.Context) (map[string]float64, error)
}

// AlertInternalSrv defines the interface for alerting operations
type AlertInternalSrv interface {
	// SendAlert sends an alert
	SendAlert(ctx context.Context, alert Alert) error
	// GetActiveAlerts returns active alerts
	GetActiveAlerts(ctx context.Context) ([]Alert, error)
	// ResolveAlert resolves an alert
	ResolveAlert(ctx context.Context, alertID string) error
}

// Alert represents an alert
type Alert struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Source      string                 `json:"source"`
	Timestamp   int64                  `json:"timestamp"`
	Labels      map[string]string      `json:"labels"`
	Annotations map[string]interface{} `json:"annotations"`
}

// HealthChecker defines the interface for health checking
type HealthChecker interface {
	// CheckHealth performs a health check
	CheckHealth(ctx context.Context) (HealthStatus, error)
	// GetHealthScore returns a health score (0-100)
	GetHealthScore(ctx context.Context) (int, error)
}

// HealthStatus represents health status
type HealthStatus struct {
	Status    string                 `json:"status"`    // healthy, warning, error
	Score     int                    `json:"score"`     // 0-100
	Details   map[string]interface{} `json:"details"`   // Additional details
	Timestamp int64                  `json:"timestamp"` // Check timestamp
}

// DataAggregator defines the interface for data aggregation
type DataAggregator interface {
	// AggregateVMData aggregates VM data
	AggregateVMData(ctx context.Context, vms []*domainv1.VMInstance, usage *domain.VMUsageList) (interface{}, error)
	// AggregateHostData aggregates host data
	AggregateHostData(ctx context.Context, hosts []*domainv1.Host, sysUtil *domain.SystemUtilization) (interface{}, error)
	// AggregateStorageData aggregates storage data
	AggregateStorageData(ctx context.Context, repos []*domainv1.RepositoryPool, volumes *domain.StorageState) (interface{}, error)
	// AggregateClusterData aggregates cluster-wide data
	AggregateClusterData(ctx context.Context, vmData, hostData, storageData interface{}) (interface{}, error)
}

// ConfigProvider defines the interface for configuration
type ConfigProvider interface {
	// GetConfig returns configuration value
	GetConfig(key string) (interface{}, error)
	// GetThresholds returns warning/error thresholds
	GetThresholds() (map[string]float64, error)
	// GetRefreshInterval returns data refresh interval
	GetRefreshInterval() (int, error)
}

// EventPublisher defines the interface for event publishing
type EventPublisher interface {
	// PublishEvent publishes an event
	PublishEvent(ctx context.Context, event Event) error
	// PublishStatusChange publishes a status change event
	PublishStatusChange(ctx context.Context, resourceType, resourceID, oldStatus, newStatus string) error
}

// Event represents an event
type Event struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Source      string                 `json:"source"`
	Subject     string                 `json:"subject"`
	Action      string                 `json:"action"`
	Timestamp   int64                  `json:"timestamp"`
	Data        map[string]interface{} `json:"data"`
	Metadata    map[string]string      `json:"metadata"`
}

// ResourceMonitor defines the interface for resource monitoring
type ResourceMonitor interface {
	// MonitorResource monitors a specific resource
	MonitorResource(ctx context.Context, resourceType, resourceID string) error
	// StopMonitoring stops monitoring a resource
	StopMonitoring(ctx context.Context, resourceType, resourceID string) error
	// GetMonitoringStatus returns monitoring status
	GetMonitoringStatus(ctx context.Context) (map[string]interface{}, error)
}

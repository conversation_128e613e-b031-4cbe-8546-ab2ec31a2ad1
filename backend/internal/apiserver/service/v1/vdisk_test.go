package v1

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"os"
	"os/exec"
	"strconv"
	"testing"
	"time"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store/redis"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

func getTestRedisStore(t *testing.T) store.RedisFactory {
	opts := options.NewRedisOptions()
	opts.Address = "localhost:6379"
	opts.DB = 0
	opts.Namespace = "collectd"
	redisStore, err := redis.GetRedisFactoryOr(opts)
	assert.NoError(t, err)
	return redisStore
}

func getTestVDiskService(t *testing.T) *vDiskService {
	err := os.Setenv("ETCDCTL_API", "3")
	if err != nil {
		panic(err)
	}
	etcdStore := getTestEtcdStore(t)
	redisStore := getTestRedisStore(t)
	return newVDisk(&service{store: etcdStore, redis: redisStore})
}

var createVDiskTest = dto.VMInstanceRequestEdit{
	GuestID:      "489d071f-6c3c-490e-a6d1-93353c47a41b",
	RepositoryID: "7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9",
	Name:         "test001",

	IncreaseAllocatedSize: 43,
	OrderChanged:          false,
	VdiskNum:              3,
	Disks: struct {
		Add    []dto.VDiskAdd    `json:"vdisks_add"`
		Delete []dto.VDiskDelete `json:"vdisks_del"`
		Edit   []dto.VDiskEdit   `json:"vdisks_edit"`
		Order  []dto.VDiskOrder  `json:"-"`
	}{
		Add: []dto.VDiskAdd{
			{
				VDiskBase: dto.VDiskBase{
					DevLimit:       0,
					DevReservation: 0,
					DevWeight:      3,
					IopsEnable:     true,
					Unmap:          true,
					VdiskMode:      3,
					Name:           "虚拟盘 1",
				},
				Type:            "add",
				SetByUser:       true,
				VdiskSize:       20,
				Idx:             0,
				IsVdiskSizeEdit: false,
				IsUnmapEdit:     false,
			},
			{
				VDiskBase: dto.VDiskBase{
					VdiskMode:      1,
					Name:           "虚拟盘 2",
					Unmap:          true,
					IopsEnable:     true,
					DevLimit:       0,
					DevReservation: 0,
					DevWeight:      3,
				},
				Type:            "add",
				SetByUser:       true,
				VdiskSize:       11,
				Idx:             1,
				IsVdiskSizeEdit: false,
				IsUnmapEdit:     false,
			},
			{
				VDiskBase: dto.VDiskBase{
					VdiskMode:      2,
					Name:           "虚拟盘 3",
					Unmap:          false,
					IopsEnable:     false,
					DevLimit:       0,
					DevReservation: 0,
					DevWeight:      3,
				},
				Type:            "add",
				VdiskSize:       12,
				Idx:             2,
				IsVdiskSizeEdit: false,
				IsUnmapEdit:     false,
			},
		},
	},
}

func TestVDiskService_Create(t *testing.T) {
	tests := []struct {
		name     string
		wantCode int
		data     dto.VMInstanceRequestEdit
		before   func(t *testing.T)
		after    func(t *testing.T)
	}{
		{
			name:     "create vdisk test001",
			wantCode: http.StatusOK,
			data:     createVDiskTest,
			before: func(t *testing.T) {
				_ = exec.Command("sh", "-c", "rm -r /Volume1/@VMs_iSCSI/LUN/VDISK_BLUN/*").Run()

				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/vdisk/", "--prefix").Run()
				_ = exec.Command("killall", "-q", "targetcli").Run()
				_ = exec.Command("targetcli", "clearconfig", "true").Run()

				guestJson := `{"creating_host": "9b5910c9-557c-42a7-b592-7996d606dcc0","repository_id": "7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9"}`
				if err := runEtcdctlCommand(
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/guests/489d071f-6c3c-490e-a6d1-93353c47a41b", guestJson,
				); err != nil {
					log.Errorf("Failed to put guest data into etcd: %v", err)
				}

				repositoryJson := `{"host_id":"9b5910c9-557c-42a7-b592-7996d606dcc0","location_volume":"/Volume1"}`
				if err := runEtcdctlCommand(
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/repository/7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9", repositoryJson,
				); err != nil {
					log.Errorf("Failed to put repository data into etcd: %v", err)
				}
			},
			after: func(t *testing.T) {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/*", "--prefix").Run()
				_ = exec.Command("targetcli", "clearconfig", "true").Run()
				_ = exec.Command("sh", "-c", "rm -r /Volume1/@VMs_iSCSI/LUN/VDISK_BLUN/*").Run()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			vdiskService := getTestVDiskService(t)
			tt.before(t)
			defer tt.after(t)
			_, acultData, err := vdiskService.Create(context.Background(), &tt.data, metav1.CreateOptions{})
			assert.NoError(t, err)
			acultDataList := domain.VDiskList{}
			err = acultDataList.UnmarshalBinary([]byte(acultData))

			assert.NoError(t, err)
			expectedData, err := vdiskService.store.VDisks().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)
			for vdiskUUID, vdisk := range expectedData.Items {
				assert.Equal(t, vdisk.DevLimit, acultDataList.Items[vdiskUUID].DevLimit)
				assert.Equal(t, vdisk.DevReservation, acultDataList.Items[vdiskUUID].DevReservation)
				assert.Equal(t, vdisk.DevWeight, acultDataList.Items[vdiskUUID].DevWeight)
				assert.Equal(t, vdisk.IOPSEnable, acultDataList.Items[vdiskUUID].IOPSEnable)
				assert.Equal(t, vdisk.Unmap, acultDataList.Items[vdiskUUID].Unmap)
				assert.Equal(t, vdisk.VDiskMode, acultDataList.Items[vdiskUUID].VDiskMode)
				assert.Equal(t, vdisk.IsDummy, acultDataList.Items[vdiskUUID].IsDummy)
				assert.Equal(t, vdisk.IsMetaDisk, acultDataList.Items[vdiskUUID].IsMetaDisk)
				assert.Equal(t, vdisk.MinorID, acultDataList.Items[vdiskUUID].MinorID)
				assert.Equal(t, vdisk.Size, acultDataList.Items[vdiskUUID].Size)
				assert.Equal(t, vdisk.RepositoryID, acultDataList.Items[vdiskUUID].RepositoryID)
				assert.Equal(t, vdisk.VDiskDevice, acultDataList.Items[vdiskUUID].VDiskDevice)
				assert.Equal(t, vdisk.LUNUUID, acultDataList.Items[vdiskUUID].LUNUUID)
				assert.Equal(t, vdisk.Type, acultDataList.Items[vdiskUUID].Type)
				assert.Equal(t, vdisk.GuestID, acultDataList.Items[vdiskUUID].GuestID)
				assert.Equal(t, vdisk.VDiskMode, acultDataList.Items[vdiskUUID].VDiskMode)
				assert.Equal(t, vdisk.Format, acultDataList.Items[vdiskUUID].Format)
				assert.Equal(t, vdisk.Size, acultDataList.Items[vdiskUUID].Size)
			}

		})
	}
}

func TestVDiskService_Running(t *testing.T) {
	tests := []struct {
		name     string
		wantCode int
		data     dto.VMInstanceRequestEdit
		before   func(t *testing.T)
		after    func(t *testing.T)
	}{
		{
			name:     "create vdisk test001",
			wantCode: http.StatusOK,
			data:     createVDiskTest,
			before: func(t *testing.T) {
				_ = exec.Command("sh", "-c", "rm -r /Volume1/@VMs_iSCSI/LUN/VDISK_BLUN/*").Run()

				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/vdisk/", "--prefix").Run()
				_ = exec.Command("killall", "-q", "targetcli").Run()
				_ = exec.Command("targetcli", "clearconfig", "true").Run()

				guestJson := `{"creating_host": "9b5910c9-557c-42a7-b592-7996d606dcc0","repository_id": "7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9"}`
				if err := runEtcdctlCommand(
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/guests/489d071f-6c3c-490e-a6d1-93353c47a41b", guestJson,
				); err != nil {
					log.Errorf("Failed to put guest data into etcd: %v", err)
				}

				repositoryJson := `{"host_id":"9b5910c9-557c-42a7-b592-7996d606dcc0","location_volume":"/Volume1"}`
				if err := runEtcdctlCommand(
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/repository/7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9", repositoryJson,
				); err != nil {
					log.Errorf("Failed to put repository data into etcd: %v", err)
				}
			},
			after: func(t *testing.T) {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/*", "--prefix").Run()
				_ = exec.Command("targetcli", "clearconfig", "true").Run()
				_ = exec.Command("sh", "-c", "rm -r /Volume1/@VMs_iSCSI/LUN/VDISK_BLUN/*").Run()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			vdiskService := getTestVDiskService(t)
			tt.before(t)
			defer tt.after(t)
			_, _, err := vdiskService.Create(context.Background(), &tt.data, metav1.CreateOptions{})
			vdiskList, _ := vdiskService.List(context.Background(), metav1.ListOptions{})
			var vdiskUUIDs []string
			for uuid, _ := range vdiskList.Items {
				vdiskUUIDs = append(vdiskUUIDs, uuid)
			}

			vdiskService.Running(context.Background(), vdiskUUIDs)

			assert.NoError(t, err) //需要使用targetcli命令手动查看是否创建完成

		})
	}
}

func TestVDiskService_Stop(t *testing.T) {
	tests := []struct {
		name     string
		wantCode int
		data     dto.VMInstanceRequestEdit
		before   func(t *testing.T)
		after    func(t *testing.T)
	}{
		{
			name:     "create vdisk test001",
			wantCode: http.StatusOK,
			data:     createVDiskTest,
			before: func(t *testing.T) {
				_ = exec.Command("sh", "-c", "rm -r /Volume1/@VMs_iSCSI/LUN/VDISK_BLUN/*").Run()

				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/vdisk/", "--prefix").Run()
				_ = exec.Command("killall", "-q", "targetcli").Run()
				_ = exec.Command("targetcli", "clearconfig", "true").Run()

				guestJson := `{"creating_host": "9b5910c9-557c-42a7-b592-7996d606dcc0","repository_id": "7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9"}`
				if err := runEtcdctlCommand(
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/guests/489d071f-6c3c-490e-a6d1-93353c47a41b", guestJson,
				); err != nil {
					log.Errorf("Failed to put guest data into etcd: %v", err)
				}

				repositoryJson := `{"host_id":"9b5910c9-557c-42a7-b592-7996d606dcc0","location_volume":"/Volume1"}`
				if err := runEtcdctlCommand(
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/repository/7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9", repositoryJson,
				); err != nil {
					log.Errorf("Failed to put repository data into etcd: %v", err)
				}
			},
			after: func(t *testing.T) {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/*", "--prefix").Run()
				_ = exec.Command("targetcli", "clearconfig", "true").Run()
				_ = exec.Command("sh", "-c", "rm -r /Volume1/@VMs_iSCSI/LUN/VDISK_BLUN/*").Run()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			vdiskService := getTestVDiskService(t)
			tt.before(t)
			defer tt.after(t)
			_, _, err := vdiskService.Create(context.Background(), &tt.data, metav1.CreateOptions{})
			vdiskList, _ := vdiskService.List(context.Background(), metav1.ListOptions{})
			var vdiskUUIDs []string
			for uuid, _ := range vdiskList.Items {
				vdiskUUIDs = append(vdiskUUIDs, uuid)
			}

			vdiskService.Running(context.Background(), vdiskUUIDs)

			time.Sleep(5 * time.Second)

			vdiskService.Stop(context.Background(), vdiskUUIDs)

			assert.NoError(t, err)

		})
	}
}

func updateVdiskTestdata(vdiskList *domain.VDiskList) dto.VMInstanceRequestEdit {
	var data dto.VMInstanceRequestEdit
	data.GuestID = "489d071f-6c3c-490e-a6d1-93353c47a41b"
	data.RepositoryID = "7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9"
	data.Name = "虚拟机1"
	data.IncreaseAllocatedSize = 43
	data.OrderChanged = false
	data.VdiskNum = 3
	data.NeedSaveData = true

	i := 0
	sizes := []int{25, 30, 35}
	vdiskmode := []int{1, 2, 2}
	sizesBytes := []string{}
	for _, size := range sizes {
		sizeInBytes := int64(size) * 1024 * 1024 * 1024 // 将 GB 转换为 Bytes
		sizesBytes = append(sizesBytes, strconv.FormatInt(sizeInBytes, 10))
	}
	for vduuid, vdisk := range vdiskList.Items {
		data.Disks.Edit = append(data.Disks.Edit, dto.VDiskEdit{
			VDiskBase: dto.VDiskBase{
				DevLimit:       int(vdisk.DevLimit),
				DevReservation: int(vdisk.DevReservation),
				DevWeight:      int(vdisk.DevWeight),
				IopsEnable:     vdisk.IOPSEnable,
				Unmap:          vdisk.Unmap,
				VdiskMode:      vdiskmode[i],
				Name:           "虚拟盘" + strconv.Itoa(i),
			},
			VDiskExtended: dto.VDiskExtended{
				Size:       sizesBytes[i],
				VdiskID:    vduuid,
				LunType:    "blun",
				IsDummy:    vdisk.IsDummy,
				IsMetaDisk: vdisk.IsMetaDisk,
			},
			VDiskOriginal: dto.VDiskOriginal{
				OriVdiskMode:      int(vdisk.VDiskMode),
				OriUnmap:          vdisk.Unmap,
				OriIdx:            vdisk.MinorID - 1,
				OriDevLimit:       int(vdisk.DevLimit),
				OriDevReservation: int(vdisk.DevReservation),
				OriDevWeight:      int(vdisk.DevWeight),
				OriIopsEnable:     vdisk.IOPSEnable,
			},
			Type:            "old",
			SetByUser:       true,
			VdiskSize:       sizes[i],
			Idx:             vdisk.MinorID - 1,
			IsVdiskSizeEdit: true,
			IsUnmapEdit:     false,
			IsQosEdit:       false,
		})
		fmt.Println(vduuid, "  ", vdisk.VDiskMode)
		i++
	}
	return data
}

func TestVDiskService_Update(t *testing.T) {
	tests := []struct {
		name     string
		wantCode int
		before   func(t *testing.T) (dto.VMInstanceRequestEdit, *domain.VDiskList)
		after    func(t *testing.T)
	}{
		{
			name:     "update vdisk test001",
			wantCode: http.StatusOK,
			before: func(t *testing.T) (dto.VMInstanceRequestEdit, *domain.VDiskList) {
				cmd := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/vdisk/", "--prefix")
				cmd.Env = append(os.Environ(), "ETCDCTL_API=3")
				_ = cmd.Run()
				_ = exec.Command("killall", "-q", "targetcli").Run()
				_ = exec.Command("targetcli", "clearconfig", "true").Run()
				_ = exec.Command("sh", "-c", "rm -r /Volume1/@VMs_iSCSI/LUN/VDISK_BLUN/*").Run()

				guestJson := `{"creating_host": "9b5910c9-557c-42a7-b592-7996d606dcc0","repository_id": "7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9"}`
				if err := runEtcdctlCommand(
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/guests/489d071f-6c3c-490e-a6d1-93353c47a41b", guestJson,
				); err != nil {
					log.Errorf("Failed to put guest data into etcd: %v", err)
				}

				repositoryJson := `{"host_id":"9b5910c9-557c-42a7-b592-7996d606dcc0","location_volume":"/Volume1"}`
				if err := runEtcdctlCommand(
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/repository/7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9", repositoryJson,
				); err != nil {
					log.Errorf("Failed to put repository data into etcd: %v", err)
				}

				vdiskService := getTestVDiskService(t)
				_, _, err := vdiskService.Create(context.Background(), &createVDiskTest, metav1.CreateOptions{})
				assert.NoError(t, err)
				vdiskList, _ := vdiskService.store.VDisks().List(context.Background(), metav1.ListOptions{})
				return updateVdiskTestdata(vdiskList), vdiskList
			},
			after: func(t *testing.T) {
				cmd := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster", "--prefix")
				cmd.Env = append(os.Environ(), "ETCDCTL_API=3")
				_ = cmd.Run()
				_ = exec.Command("targetcli", "clearconfig", "true").Run()
				_ = exec.Command("sh", "-c", "rm -r /Volume1/@VMs_iSCSI/LUN/VDISK_BLUN/*").Run()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			vdiskService := getTestVDiskService(t)
			Data, wantData := tt.before(t)

			defer tt.after(t)
			acultDataBytes, err := vdiskService.Update(context.Background(), &Data, metav1.UpdateOptions{})
			assert.NoError(t, err)
			acultData := domain.VDiskList{}
			err = acultData.UnmarshalBinary([]byte(acultDataBytes))

			assert.NoError(t, err)
			fmt.Println(acultData)
			fmt.Println(wantData)
			for vdiskUUID, vdisk := range wantData.Items {
				assert.Equal(t, vdisk.DevLimit, acultData.Items[vdiskUUID].DevLimit)
				assert.Equal(t, vdisk.DevReservation, acultData.Items[vdiskUUID].DevReservation)
				assert.Equal(t, vdisk.DevWeight, acultData.Items[vdiskUUID].DevWeight)
				assert.Equal(t, vdisk.IOPSEnable, acultData.Items[vdiskUUID].IOPSEnable)
				assert.Equal(t, vdisk.Unmap, acultData.Items[vdiskUUID].Unmap)
				assert.Equal(t, vdisk.IsDummy, acultData.Items[vdiskUUID].IsDummy)
				assert.Equal(t, vdisk.IsMetaDisk, acultData.Items[vdiskUUID].IsMetaDisk)
				assert.Equal(t, vdisk.MinorID, acultData.Items[vdiskUUID].MinorID)
				assert.Equal(t, vdisk.RepositoryID, acultData.Items[vdiskUUID].RepositoryID)
				assert.Equal(t, vdisk.Type, acultData.Items[vdiskUUID].Type)
				assert.Equal(t, vdisk.LUNUUID, acultData.Items[vdiskUUID].LUNUUID)
				assert.Equal(t, vdisk.Format, acultData.Items[vdiskUUID].Format)
				assert.Equal(t, vdisk.GuestID, acultData.Items[vdiskUUID].GuestID)

			}
		})
	}
}

var sortVDiskTest = dto.VMInstanceRequestEdit{
	GuestID:      "489d071f-6c3c-490e-a6d1-93353c47a41b",
	RepositoryID: "7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9",
	Name:         "test001",

	IncreaseAllocatedSize: 43,
	OrderChanged:          false,
	VdiskNum:              3,
	Disks: struct {
		Add    []dto.VDiskAdd    `json:"vdisks_add"`
		Delete []dto.VDiskDelete `json:"vdisks_del"`
		Edit   []dto.VDiskEdit   `json:"vdisks_edit"`
		Order  []dto.VDiskOrder  `json:"-"`
	}{
		Order: []dto.VDiskOrder{
			{
				VDiskBase: dto.VDiskBase{
					DevLimit:       0,
					DevReservation: 0,
					DevWeight:      3,
					IopsEnable:     true,
					Unmap:          true,
					Name:           "虚拟盘 1",
				},
				VDiskExtended: dto.VDiskExtended{
					VdiskID: "1",
				},
				Type:            "old",
				VdiskSize:       20,
				Idx:             1,
				IsVdiskSizeEdit: false,
				IsUnmapEdit:     false,
			},
			{
				VDiskBase: dto.VDiskBase{
					Name:           "虚拟盘 2",
					Unmap:          true,
					IopsEnable:     true,
					DevLimit:       0,
					DevReservation: 0,
					DevWeight:      3,
				},
				VDiskExtended: dto.VDiskExtended{
					VdiskID: "2",
				},
				Type:            "old",
				VdiskSize:       11,
				Idx:             2,
				IsVdiskSizeEdit: false,
				IsUnmapEdit:     false,
			},
		},
	},
}

func TestVDiskService_Sort(t *testing.T) {
	var thescendVDiskID, thefirstVDiskID string
	var thescendMode, thefirstMode int
	tests := []struct {
		name   string
		before func(t *testing.T) *domain.VDiskList
		after  func(t *testing.T)
	}{
		{
			name: "sort vdisk test001",
			before: func(t *testing.T) *domain.VDiskList {
				cmd := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/vdisk/", "--prefix")
				cmd.Env = append(os.Environ(), "ETCDCTL_API=3")
				_ = cmd.Run()
				_ = exec.Command("killall", "-q", "targetcli").Run()
				_ = exec.Command("targetcli", "clearconfig", "true").Run()
				_ = exec.Command("sh", "-c", "rm -r /Volume1/@VMs_iSCSI/LUN/VDISK_BLUN/*").Run()

				guestJson := `{"creating_host": "9b5910c9-557c-42a7-b592-7996d606dcc0","repository_id": "7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9"}`
				if err := runEtcdctlCommand(
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/guests/489d071f-6c3c-490e-a6d1-93353c47a41b", guestJson,
				); err != nil {
					log.Errorf("Failed to put guest data into etcd: %v", err)
				}

				repositoryJson := `{"host_id":"9b5910c9-557c-42a7-b592-7996d606dcc0","location_volume":"/Volume1"}`
				if err := runEtcdctlCommand(
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/repository/7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9", repositoryJson,
				); err != nil {
					log.Errorf("Failed to put repository data into etcd: %v", err)
				}

				vdiskService := getTestVDiskService(t)
				_, vdiskidListBytes, err := vdiskService.Create(context.Background(), &createVDiskTest, metav1.CreateOptions{})
				assert.NoError(t, err)
				vdiskidList := domain.VDiskList{}
				err = vdiskidList.UnmarshalBinary([]byte(vdiskidListBytes))
				assert.NoError(t, err)

				i := 0

				for vdiskUUID, _ := range vdiskidList.Items {
					if i == 1 {
						thescendVDiskID = vdiskUUID
						thescendMode = int(vdiskidList.Items[vdiskUUID].VDiskMode)
					}
					if i == 0 {
						thefirstVDiskID = vdiskUUID
						thefirstMode = int(vdiskidList.Items[vdiskUUID].VDiskMode)
					}
					i++
				}
				vdiskidList.Items[thescendVDiskID].MinorID = 3
				vdiskidList.Items[thefirstVDiskID].MinorID = 2
				return &vdiskidList
			},
			after: func(t *testing.T) {
				cmd := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster", "--prefix")
				cmd.Env = append(os.Environ(), "ETCDCTL_API=3")
				_ = cmd.Run()
				_ = exec.Command("targetcli", "clearconfig", "true").Run()
				_ = exec.Command("sh", "-c", "rm -r /Volume1/@VMs_iSCSI/LUN/VDISK_BLUN/*").Run()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			vDiskService := *getTestVDiskService(t)
			wantData := tt.before(t)
			//defer tt.after(t)

			sortVDiskTest.Disks.Order[0].VDiskExtended.VdiskID = thefirstVDiskID
			sortVDiskTest.Disks.Order[0].VDiskBase.VdiskMode = thefirstMode
			sortVDiskTest.Disks.Order[1].VDiskExtended.VdiskID = thescendVDiskID
			sortVDiskTest.Disks.Order[1].VDiskBase.VdiskMode = thescendMode
			err := vDiskService.Sort(context.Background(), &sortVDiskTest, metav1.UpdateOptions{})
			assert.NoError(t, err)

			acultData, err := vDiskService.List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)

			assert.Equal(t, len(wantData.Items), len(acultData.Items))

			for uuid, item := range wantData.Items {
				fmt.Println(uuid, "  ", item.Size)
				assert.Equal(t, item.MinorID, acultData.Items[uuid].MinorID)
				assert.Equal(t, item.DevLimit, acultData.Items[uuid].DevLimit)
				assert.Equal(t, item.DevReservation, acultData.Items[uuid].DevReservation)
				assert.Equal(t, item.DevWeight, acultData.Items[uuid].DevWeight)
				assert.Equal(t, item.VDiskMode, acultData.Items[uuid].VDiskMode)
				assert.Equal(t, item.GuestID, acultData.Items[uuid].GuestID)
				assert.Equal(t, item.RepositoryID, acultData.Items[uuid].RepositoryID)
				assert.Equal(t, item.Type, acultData.Items[uuid].Type)
				assert.Equal(t, item.Format, acultData.Items[uuid].Format)
			}
		})
	}
}

var deleteVDiskTest = dto.VMInstanceRequestEdit{
	GuestID:      "489d071f-6c3c-490e-a6d1-93353c47a41b",
	RepositoryID: "7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9",
	Name:         "test001",

	IncreaseAllocatedSize: 43,
	OrderChanged:          false,
	VdiskNum:              3,
	NeedSaveData:          true,
	Disks: struct {
		Add    []dto.VDiskAdd    `json:"vdisks_add"`
		Delete []dto.VDiskDelete `json:"vdisks_del"`
		Edit   []dto.VDiskEdit   `json:"vdisks_edit"`
		Order  []dto.VDiskOrder  `json:"-"`
	}{
		Delete: []dto.VDiskDelete{
			{
				Type: "delete",
				VDiskExtended: dto.VDiskExtended{
					VdiskID: "3",
				},
			},
		},
	},
}

func TestVDiskService_Delete(t *testing.T) {
	var deleteUUID string
	var oriVdiskMode int
	tests := []struct {
		name   string
		before func(t *testing.T) domain.VDiskList
		after  func(t *testing.T)
	}{
		{
			name: "delete vdisk test001",
			before: func(t *testing.T) domain.VDiskList {
				cmd := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/vdisk/", "--prefix")
				cmd.Env = append(os.Environ(), "ETCDCTL_API=3")
				_ = cmd.Run()
				_ = exec.Command("killall", "-q", "targetcli").Run()
				_ = exec.Command("targetcli", "clearconfig", "true").Run()
				_ = exec.Command("sh", "-c", "rm -r /Volume1/@VMs_iSCSI/LUN/VDISK_BLUN/*").Run()

				guestJson := `{"creating_host": "9b5910c9-557c-42a7-b592-7996d606dcc0","repository_id": "7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9"}`
				if err := runEtcdctlCommand(
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/guests/489d071f-6c3c-490e-a6d1-93353c47a41b", guestJson,
				); err != nil {
					log.Errorf("Failed to put guest data into etcd: %v", err)
				}

				repositoryJson := `{"host_id":"9b5910c9-557c-42a7-b592-7996d606dcc0","location_volume":"/Volume1"}`
				if err := runEtcdctlCommand(
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/repository/7dcfe2eb-3cdb-4762-a46e-1e0fdb8778a9", repositoryJson,
				); err != nil {
					log.Errorf("Failed to put repository data into etcd: %v", err)
				}

				vdiskService := getTestVDiskService(t)
				_, _, err := vdiskService.Create(context.Background(), &createVDiskTest, metav1.CreateOptions{})
				assert.NoError(t, err)
				time.Sleep(5 * time.Second)

				uuidList, err := vdiskService.store.VDisks().ListUUID(context.Background(), metav1.ListOptions{})
				assert.NoError(t, err)

				vdisk0, _ := vdiskService.store.VDisks().Get(context.Background(), uuidList[0], metav1.GetOptions{})
				vdisk1, _ := vdiskService.store.VDisks().Get(context.Background(), uuidList[1], metav1.GetOptions{})
				vdisk2, _ := vdiskService.store.VDisks().Get(context.Background(), uuidList[2], metav1.GetOptions{})
				deleteUUID = uuidList[2]
				oriVdiskMode = int(vdisk2.VDiskMode)
				return domain.VDiskList{
					Items: map[string]*domain.VDisk{
						uuidList[0]: vdisk0,
						uuidList[1]: vdisk1,
					},
				}
			},
			after: func(t *testing.T) {
				cmd := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster", "--prefix")
				cmd.Env = append(os.Environ(), "ETCDCTL_API=3")
				_ = cmd.Run()
				_ = exec.Command("targetcli", "clearconfig", "true").Run()
				_ = exec.Command("sh", "-c", "rm -r /Volume1/@VMs_iSCSI/LUN/VDISK_BLUN/*").Run()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			vDiskService := *getTestVDiskService(t)
			wantData := tt.before(t)
			//defer tt.after(t)
			deleteVDiskTest.Disks.Delete[0].VDiskExtended.VdiskID = deleteUUID
			deleteVDiskTest.Disks.Delete[0].VDiskOriginal.OriVdiskMode = oriVdiskMode
			deleteVDiskTest.NeedSaveData = true
			err := vDiskService.Delete(context.Background(), &deleteVDiskTest, metav1.DeleteOptions{})
			assert.NoError(t, err)

			acultData, err := vDiskService.List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)

			// 比较长度
			assert.Equal(t, len(wantData.Items), len(acultData.Items))

			for uuid, item := range wantData.Items {
				assert.Equal(t, item.Size, acultData.Items[uuid].Size)
				assert.Equal(t, item.MinorID, acultData.Items[uuid].MinorID)
				assert.Equal(t, item.DevLimit, acultData.Items[uuid].DevLimit)
				assert.Equal(t, item.DevReservation, acultData.Items[uuid].DevReservation)
				assert.Equal(t, item.DevWeight, acultData.Items[uuid].DevWeight)
				assert.Equal(t, item.IOPSEnable, acultData.Items[uuid].IOPSEnable)
				assert.Equal(t, item.Unmap, acultData.Items[uuid].Unmap)
				assert.Equal(t, item.VDiskMode, acultData.Items[uuid].VDiskMode)
				assert.Equal(t, item.GuestID, acultData.Items[uuid].GuestID)
				assert.Equal(t, item.RepositoryID, acultData.Items[uuid].RepositoryID)
				assert.Equal(t, item.Type, acultData.Items[uuid].Type)
				assert.Equal(t, item.Format, acultData.Items[uuid].Format)
			}
		})
		// t.Run(tt.name, func(t *testing.T) {
		// 	vDiskService := *getTestVDiskService(t)
		// 	wantData := tt.before(t)
		// 	//defer tt.after(t)
		// 	deleteVDiskTest.Disks.Delete[0].VDiskExtended.VdiskID = deleteUUID
		// 	deleteVDiskTest.Disks.Delete[0].VDiskOriginal.OriVdiskMode = oriVdiskMode
		// 	deleteVDiskTest.NeedSaveData = false
		// 	err := vDiskService.Delete(context.Background(), &deleteVDiskTest, metav1.DeleteOptions{})
		// 	assert.NoError(t, err)

		// 	acultData, err := vDiskService.List(context.Background(), metav1.ListOptions{})
		// 	assert.NoError(t, err)

		// 	// 比较长度
		// 	assert.Equal(t, len(wantData.Items), len(acultData.Items))

		// 	for uuid, item := range wantData.Items {
		// 		assert.Equal(t, item.Size, acultData.Items[uuid].Size)
		// 		assert.Equal(t, item.MinorID, acultData.Items[uuid].MinorID)
		// 		assert.Equal(t, item.DevLimit, acultData.Items[uuid].DevLimit)
		// 		assert.Equal(t, item.DevReservation, acultData.Items[uuid].DevReservation)
		// 		assert.Equal(t, item.DevWeight, acultData.Items[uuid].DevWeight)
		// 		assert.Equal(t, item.IOPSEnable, acultData.Items[uuid].IOPSEnable)
		// 		assert.Equal(t, item.Unmap, acultData.Items[uuid].Unmap)
		// 		assert.Equal(t, item.VDiskMode, acultData.Items[uuid].VDiskMode)
		// 		assert.Equal(t, item.GuestID, acultData.Items[uuid].GuestID)
		// 		assert.Equal(t, item.RepositoryID, acultData.Items[uuid].RepositoryID)
		// 		assert.Equal(t, item.Type, acultData.Items[uuid].Type)
		// 		assert.Equal(t, item.Format, acultData.Items[uuid].Format)
		// 	}
		// })
	}
}

func runEtcdctlCommand(args ...string) error {
	cmd := exec.Command("etcdctl", args...)
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		log.Errorf("Failed to run etcdctl: %v\nStdout: %s\nStderr: %s", err, out.String(), stderr.String())
		return err
	}

	log.Infof("etcdctl command executed successfully\nStdout: %s", out.String())
	return nil
}

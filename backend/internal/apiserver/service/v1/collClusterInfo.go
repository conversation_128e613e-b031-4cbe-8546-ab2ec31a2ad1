package v1

import (
	"context"
	"encoding/json"
	"strings"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domainv1 "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/golibrary/errors"
)

type CollClusterInfoSrv interface {
	ListOLD(ctx context.Context, opts metav1.ListOptions) (*dto.ClusterStatusResponse, error)
	List(ctx context.Context, opts metav1.ListOptions) (*dto.ClusterStatusResponse, error)
}

func (c *collClusterInfoService) ListOLD(ctx context.Context, opts metav1.ListOptions) (*dto.ClusterStatusResponse, error) {
	colVm, err := c.redisStore.CollVMUsage().List(ctx, opts)
	if err != nil {
		log.L(ctx).Warnf("list vm_usage from redis storage failed: %s", err.Error())

		return nil, errors.WithCode(code.ErrRedisCmd, err.Error())
	}

	repoPool, err := c.etcdStore.RepositoryPool().List(ctx, opts)
	if err != nil {
		log.L(ctx).Errorf("get warning_threshold from etcd storage failed: %s", err.Error())

		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	colVol, err := c.redisStore.CollVolume().List(ctx, opts)
	if err != nil {
		log.L(ctx).Errorf("list volume from redis storage failed: %s", err.Error())

		return nil, errors.WithCode(code.ErrRedisCmd, err.Error())
	}

	vmList, err := c.etcdStore.VMs().List(ctx, opts)
	if err != nil {
		log.L(ctx).Errorf("get vm from etcd storage failed: %s", err.Error())

		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}
	guestInfo := &dto.GuestSumm{
		Error:   0,                         //目前不知如何获取
		Running: c.getGuestRunning(vmList), //etcd中获取
		Warning: c.getGuestWarning(ctx, colVm),
		Healthy: int(colVm.GetTotalCount()) - c.getGuestWarning(ctx, colVm),
	}
	hostInfo := &dto.HostSumm{
		Error:   0,
		Warning: c.getHostWarning(ctx),
		Healthy: 1 - c.getHostWarning(ctx),
	}
	licenseInfo := &dto.LicenseSumm{
		Error:   0,
		Warning: 0,
		Healthy: 0,
	}
	repoInfo := &dto.RepoSumm{
		Error:   0,
		Warning: getRepoWarning(repoPool, colVol),
		Healthy: getRepoHealthy(repoPool, colVol),
	}

	// 构建Reasons列表
	// Host_Warning_NetworkException(主机网络异常警告)当主机所有网卡网络状态为disconnect触发
	// Host_Warning_offline(主机离线警告)
	// 虚拟机:
	// VM_Warning_NetworkException(虚拟机网络异常警告)当虚拟机所有网卡网络状态为disconnect触发
	// VM_Error_OfflineForHost(虚拟机所属主机离线)
	// VM_Error_StorageSpaceFull(虚拟机所属存储池已满)
	// VM_Error_StorageDamage(虚拟机所属存储池损坏)
	// VM_Error_StorageLose(虚拟机所属存储池丢失)
	// 存储池:
	// RepPool_Warning_ThresholdExceeded(存储池超出阈值警告)存储池剩余空间超出设置阈值
	// RepPool_Warning_Usable(存储池堪用警告)
	// RepPool_Error_SpaceFull(存储池已满)
	// RepPool_Error_Damage(存储池损坏)
	// RepPool_Error_Lose(存储池丢失)
	// RepPool_Error_other(存储池其余错误)
	var reasons []dto.Reason
	if guestInfo.Error > 0 {
		reasons = append(reasons, dto.Reason{
			Num:    guestInfo.Error,
			Status: "VM_Error_OfflineForHost",
		})
	} else if guestInfo.Warning > 0 {
		reasons = append(reasons, dto.Reason{
			Num:    guestInfo.Warning,
			Status: "VM_Warning_NetworkException",
		})
	}

	if hostInfo.Error > 0 {
		reasons = append(reasons, dto.Reason{
			Num:    hostInfo.Error,
			Status: "Host_Warning_offline",
		})
	} else if hostInfo.Warning > 0 {
		reasons = append(reasons, dto.Reason{
			Num:    hostInfo.Warning,
			Status: "Host_Warning_NetworkException",
		})
	}

	if repoInfo.Error > 0 {
		reasons = append(reasons, dto.Reason{
			Num:    repoInfo.Error,
			Status: "RepPool_Warning_ThresholdExceeded",
		})
	} else if repoInfo.Warning > 0 {
		reasons = append(reasons, dto.Reason{
			Num:    repoInfo.Warning,
			Status: "RepPool_Error_Damage",
		})
	}

	hasErrorOrWarning := guestInfo.Error > 0 || guestInfo.Warning > 0 ||
		hostInfo.Error > 0 || hostInfo.Warning > 0 ||
		repoInfo.Error > 0 || repoInfo.Warning > 0

	if !hasErrorOrWarning {
		// 如果没有错误或警告，添加正常状态
		reasons = append(reasons, dto.Reason{
			Num:    0,
			Status: "normal",
		})
	}

	// 确定集群状态
	clusterStatus := "healthy"
	hasError := false
	hasWarning := false

	for _, reason := range reasons {
		if endsWith(reason.Status, "error") {
			hasError = true
			break // 发现错误立即终止循环
		} else if endsWith(reason.Status, "warning") {
			hasWarning = true
		}
	}

	switch {
	case hasError:
		clusterStatus = "error"
	case hasWarning:
		clusterStatus = "warning"
	}

	return &dto.ClusterStatusResponse{
		ClusterStatus: clusterStatus,
		GuestSumm:     *guestInfo,
		HostSumm:      *hostInfo,
		LicenseSumm:   *licenseInfo,
		RepoSumm:      *repoInfo,
		Reasons:       reasons,
	}, nil
}

// 辅助函数检查字符串后缀
func endsWith(s, suffix string) bool {
	return len(s) >= len(suffix) && s[len(s)-len(suffix):] == suffix
}

func getRepoHealthy(repoPool *domainv1.RepositoryPoolList, colVol *domain.StorageState) int {
	healthy := 0
	for _, vol := range colVol.Storage {
		for _, repo := range repoPool.Items {
			if strings.TrimPrefix(vol.VolumePath, "/") == strings.TrimPrefix(repo.LocationVolume, "/") {
				if repo.Name != "" && repo.RepoId != "" {
					healthy++
				}
			}
		}
	}
	return healthy
}

func getRepoWarning(repoPool *domainv1.RepositoryPoolList, colVol *domain.StorageState) int {
	warning := 0
	for _, vol := range colVol.Storage {
		for _, repo := range repoPool.Items {
			if strings.TrimPrefix(vol.VolumePath, "/") == strings.TrimPrefix(repo.LocationVolume, "/") {
				softLimit := repo.SoftLimit
				if vol.PercentFree < softLimit {
					warning++
				}
			}
		}
	}
	return warning
}

func (c *collClusterInfoService) getGuestRunning(vmList *domainv1.VMInstanceList) int {
	running := 0
	for _, vm := range vmList.Items {
		if vm.State == domainv1.PowerStateRuning {
			running++
		}
	}
	return running
}

func (c *collClusterInfoService) getGuestWarning(ctx context.Context, colVm *domain.VMUsageList) int {
	warning := 0
	for _, vm := range colVm.Items {
		allDisconnected := true
		for _, net := range vm.Network {
			connectStr, _ := c.redisStore.CollNetwork().Get(ctx, net.Device, metav1.GetOptions{})
			var networkStatus domain.NetworkInterface
			if err := json.Unmarshal([]byte(connectStr), &networkStatus); err == nil {
				if networkStatus.Status == "connected" {
					allDisconnected = false
					break
				}
			}
		}
		if allDisconnected {
			warning++
		}
	}
	return warning
}

func (c *collClusterInfoService) getHostWarning(ctx context.Context) int {
	networkState, _ := c.redisStore.CollNetwork().List(ctx, metav1.ListOptions{})

	for _, iface := range networkState.Interfaces {
		if iface.Status == "connected" {
			return 0
		}
	}
	return 1
}

func (c *collClusterInfoService) List(ctx context.Context, opts metav1.ListOptions) (*dto.ClusterStatusResponse, error) {
	vmStatus, err := c.etcdStore.VMs().ListStatus(ctx, opts)
	if err != nil {
		log.L(ctx).Errorf("[Cluster_Service] vmlist get failed - %s", err.Error())
		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	hostStatus, err := c.etcdStore.Host().ListStatus(ctx, opts)
	if err != nil {
		log.L(ctx).Errorf("[Cluster_Service] hostlist get failed - %s", err.Error())
		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	repoStatus, err := c.etcdStore.RepositoryPool().ListStatus(ctx, opts)
	if err != nil {
		log.L(ctx).Errorf("[Cluster_Service] repolist get failed - %s", err.Error())
		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	guestInfo := &dto.GuestSumm{
		Error:   c.getTypeSum(vmStatus, domainv1.SeverityLevelError),
		Running: c.getTypeSum(vmStatus, domainv1.SeverityLevelRunning),
		Warning: c.getTypeSum(vmStatus, domainv1.SeverityLevelWarning),
		Healthy: c.getTypeSum(vmStatus, domainv1.SeverityLevelHealthy),
	}
	hostInfo := &dto.HostSumm{
		Error:   c.getTypeSum(hostStatus, domainv1.LogLevelError),
		Warning: c.getTypeSum(hostStatus, domainv1.SeverityLevelWarning),
		Healthy: c.getTypeSum(hostStatus, domainv1.SeverityLevelHealthy),
	}
	licenseInfo := &dto.LicenseSumm{
		Error:   0,
		Warning: 0,
		Healthy: 0,
	}
	repoInfo := &dto.RepoSumm{
		Error:   c.getTypeSum(repoStatus, domainv1.LogLevelError),
		Warning: c.getTypeSum(repoStatus, domainv1.SeverityLevelWarning),
		Healthy: c.getTypeSum(repoStatus, domainv1.SeverityLevelHealthy),
	}

	reasons, clusterStatus := c.getReasonsAndClusterStatus(vmStatus, hostStatus, repoStatus)
	return &dto.ClusterStatusResponse{
		ClusterStatus: clusterStatus,
		GuestSumm:     *guestInfo,
		HostSumm:      *hostInfo,
		LicenseSumm:   *licenseInfo,
		RepoSumm:      *repoInfo,
		Reasons:       reasons,
	}, nil
}

func (c *collClusterInfoService) getReasonsAndClusterStatus(vmStatus []domainv1.StatusEntry, hostStatus []domainv1.StatusEntry, repoStatus []domainv1.StatusEntry) ([]dto.Reason, string) {
	statusCount := make(map[string]int)
	maxSeverity := domainv1.Healthy

	// 合并处理所有状态组
	for _, group := range [][]domainv1.StatusEntry{vmStatus, hostStatus, repoStatus} {
		for _, status := range group {
			// 防御性检查
			if status.Message == "" {
				continue
			}

			// 统计消息
			statusCount[status.Message]++

			// 更新最高优先级
			switch status.Severity {
			case domainv1.SeverityLevelError:
				maxSeverity = domainv1.Error
			case domainv1.SeverityLevelWarning:
				if maxSeverity != domainv1.Error {
					maxSeverity = domainv1.Warning
				}
			}

			// 发现错误立即跳出内层循环
			if maxSeverity == domainv1.Error {
				break
			}
		}

		// 发现错误立即终止处理
		if maxSeverity == domainv1.Error {
			break
		}
	}

	// 构建响应
	reasons := make([]dto.Reason, 0, len(statusCount))
	for msg, count := range statusCount {
		reasons = append(reasons, dto.Reason{
			Num:    count,
			Status: msg,
		})
	}

	// 确定集群状态
	var clusterStatus string
	switch maxSeverity {
	case domainv1.Error:
		clusterStatus = "error"
	case domainv1.Warning:
		clusterStatus = "warning"
	default:
		clusterStatus = "healthy"
	}

	return reasons, clusterStatus
}

func (c *collClusterInfoService) getTypeSum(status []domainv1.StatusEntry, severity domainv1.SystemSeverityLevel) int {
	sum := 0
	for _, v := range status {
		if v.Severity == severity {
			sum++
		}
	}
	return sum
}

type collClusterInfoService struct {
	redisStore store.RedisFactory
	etcdStore  store.Factory
}

var _ CollClusterInfoSrv = (*collClusterInfoService)(nil)

func newCollClusterInfo(srv *collService) *collClusterInfoService {
	return &collClusterInfoService{
		redisStore: srv.redisStore,
		etcdStore:  srv.etcdStore,
	}
}

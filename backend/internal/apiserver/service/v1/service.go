// Package v1
/**
* @Project : terravirtualmachine
* @File    : service.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/20 17:03
**/

package v1

import (
	"sync"

	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
)

// Service 定义用于返回资源接口的函数
type Service interface {
	// Networks 用于获取网络相关的接口
	Networks() NetworkSrv
	Images() ImageSrv
	RepositoryPool() RepositoryPoolSrv
	VMs() VMSrv
	Logs() LogSrv
}

var _ Service = (*service)(nil)

type service struct {
	store        store.Factory
	libvirtStore store.LibvirtFactory
	redis        store.RedisFactory
}

func NewService(store store.Factory, libvirtStore store.LibvirtFactory, redis store.RedisFactory) Service {
	return &service{
		store:        store,
		libvirtStore: libvirtStore,
		redis:        redis,
	}
}

func (s *service) RepositoryPool() RepositoryPoolSrv {
	return newRepositoryPool(s)
}

func (s *service) Networks() NetworkSrv {
	return newNetworks(s)
}

// 镜像使用单列模式
var (
	ImageService ImageSrv
	once         sync.Once
)

func (s *service) Images() ImageSrv {
	once.Do(func() {
		ImageService = newImages(s)
	})
	return ImageService
}

func (s *service) VMs() VMSrv { return newVmInstanceService(s) }

func (s *service) Logs() LogSrv { return newLogDto(s) }

// collected
type CollService interface {
	CollHostInfo() CollHostInfoSrv
	CollVMInfo() CollVMInfoSrv
	CollStorageInfo() CollStorageInfoSrv
	CollClusterInfo() CollClusterInfoSrv
}

type collService struct {
	redisStore store.RedisFactory
	etcdStore  store.Factory
}

func NewCollService(redisStore store.RedisFactory, etcdStore store.Factory) CollService {
	return &collService{
		redisStore: redisStore,
		etcdStore:  etcdStore,
	}
}

func (cs *collService) CollHostInfo() CollHostInfoSrv {
	return newCollHostInfo(cs)
}

func (cs *collService) CollVMInfo() CollVMInfoSrv {
	return newCollVMInfo(cs)
}

func (cs *collService) CollStorageInfo() CollStorageInfoSrv {
	return newCollStorageInfo(cs)
}

func (cs *collService) CollClusterInfo() CollClusterInfoSrv {
	return newCollClusterInfo(cs)
}

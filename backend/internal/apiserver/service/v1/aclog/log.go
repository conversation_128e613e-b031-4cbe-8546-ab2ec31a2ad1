const (
	// ================= 主机/集群状态 =================
	HOST_CONNECTION_SUCCESS = "host_connection_successful" // 主机连接成功
	HOST_CONNECTION_FAILURE = "host_connection_failed"     // 主机连接失败
	CLUSTER_STATE_CHANGED   = "cluster_state_changed"      // 集群状态变更

	// ================= 存储池操作 =====================
	STORAGE_POOL_CREATE_SUCCESS = "storage_pool_created_successful" // 存储池创建成功
	STORAGE_POOL_CREATE_FAILURE = "storage_pool_creation_failed"    // 存储池创建失败
	STORAGE_POOL_EDIT_SUCCESS   = "storage_pool_edited_successful"  // 存储池编辑成功
	STORAGE_POOL_EDIT_FAILURE   = "storage_pool_editing_failed"     // 存储池编辑失败
	STORAGE_POOL_DELETE_SUCCESS = "storage_pool_deleted_successful" // 存储池删除成功
	STORAGE_POOL_DELETE_FAILURE = "storage_pool_deletion_failed"    // 存储池删除失败
	STORAGE_POOL_STATE_WARNING  = "storage_pool_state_warning"      // 存储池状态告警

	// ================= 虚拟磁盘操作 ===================
	VIRTUAL_DISK_CREATE_SUCCESS = "virtual_disk_created_successful" // 虚拟磁盘创建成功
	VIRTUAL_DISK_CREATE_FAILURE = "virtual_disk_creation_failed"    // 虚拟磁盘创建失败
	VIRTUAL_DISK_EDIT_SUCCESS   = "virtual_disk_edited_successful"  // 虚拟磁盘编辑成功
	VIRTUAL_DISK_EDIT_FAILURE   = "virtual_disk_editing_failed"     // 虚拟磁盘编辑失败
	VIRTUAL_DISK_DELETE_SUCCESS = "virtual_disk_deleted_successful" // 虚拟磁盘删除成功
	VIRTUAL_DISK_DELETE_FAILURE = "virtual_disk_deletion_failed"    // 虚拟磁盘删除失败

	// ================= 虚拟机生命周期 =================
	VM_POWERON_SUCCESS  = "vm_powered_on_successful"  // 虚拟机开机成功
	VM_POWERON_FAILURE  = "vm_poweron_failed"         // 虚拟机开机失败
	VM_POWEROFF_SUCCESS = "vm_powered_off_successful" // 虚拟机关机成功
	VM_POWEROFF_FAILURE = "vm_poweroff_failed"        // 虚拟机关机失败
	VM_CREATE_SUCCESS   = "vm_created_successful"     // 虚拟机创建成功
	VM_CREATE_FAILURE   = "vm_creation_failed"        // 虚拟机创建失败
	VM_EDIT_SUCCESS     = "vm_edited_successful"      // 虚拟机编辑成功
	VM_EDIT_FAILURE     = "vm_editing_failed"         // 虚拟机编辑失败
	VM_CLONE_SUCCESS    = "vm_cloned_successful"      // 虚拟机克隆成功
	VM_CLONE_FAILURE    = "vm_cloning_failed"         // 虚拟机克隆失败
	VM_IMPORT_SUCCESS   = "vm_imported_successful"    // 虚拟机导入成功
	VM_IMPORT_FAILURE   = "vm_importing_failed"       // 虚拟机导入失败
	VM_EXPORT_SUCCESS   = "vm_exported_successful"    // 虚拟机导出成功
	VM_EXPORT_FAILURE   = "vm_exporting_failed"       // 虚拟机导出失败

	// ================= 网络操作 ======================
	NETWORK_CREATE_SUCCESS = "network_created_successful" // 虚拟网络创建成功
	NETWORK_EDIT_SUCCESS   = "network_edited_successful"  // 虚拟网络编辑成功
	NETWORK_DELETE_SUCCESS = "network_deleted_successful" // 虚拟网络删除成功
	NETWORK_CONFIG_ERROR   = "network_config_failed"      // 网络配置错误

	// ================= 映像操作 =====================
	IMAGE_UPLOAD_SUCCESS = "image_upload_successful"  // 映像上传成功
	IMAGE_UPLOAD_FAILURE = "image_upload_failed"      // 映像上传失败
	IMAGE_DELETE_SUCCESS = "image_deleted_successful" // 映像删除成功
	IMAGE_DELETE_FAILURE = "image_deletion_failed"    // 映像删除失败
	IMAGE_EDIT_SUCCESS   = "image_edited_successful"  // 映像编辑成败
	IMAGE_EDIT_FAILURE   = "image_editing_failed"     // 映像编辑失败
)

const appName = "VirtualMachines"

var logPath = os.Getenv("ACTIONLOGPATH")
var aclog *actionlog.ActionLog

func init() {
	os.Mkdir(logPath, 0755)
	var err error
	aclog, err = actionlog.NewActionLog(etcd.BackendEtcdStore, logPath, appName)
	if err != nil {
		panic(err)
	}
	actionlog.SetDefaultWriter(aclog.AsWriter())
}

type Logger struct {
	taskName string
	user     string
}

func New(taskName string, ctx context.Context) *Logger {
	user, err := system.UserName(ctx)
	if err != nil {
		log.Warnf("invalid context type, expected gin.Context")
		return &Logger{
			taskName: taskName,
			user:     "unknown",
		}
	}

	return &Logger{
		taskName: taskName,
		user:     user,
	}
}

func (l *Logger) Info(key string, args ...string) {
	var trPairs []domain.TrPair
	if len(args)%2 != 0 {
		args = args[:len(args)-1]
	}
	for i := 0; i < len(args); i += 2 {
		trPairs = append(trPairs, domain.TrPair{
			Variable: args[i],
			Value:    args[i+1],
		})
	}
	actionlog.Info(l.taskName, actionlog.Translation{
		Section: appName,
		Key:     key,
		TrPairs: trPairs,
	}, l.user)
}

func (l *Logger) Warn(key string, args ...string) {
	var trPairs []domain.TrPair
	if len(args)%2 != 0 {
		args = args[:len(args)-1]
	}
	for i := 0; i < len(args); i += 2 {
		trPairs = append(trPairs, domain.TrPair{
			Variable: args[i],
			Value:    args[i+1],
		})
	}
	actionlog.Warn(l.taskName, actionlog.Translation{
		Section: appName,
		Key:     key,
		TrPairs: trPairs,
	}, l.user)
}

func (l *Logger) Error(key string, args ...string) {
	var trPairs []domain.TrPair
	if len(args)%2 != 0 {
		args = args[:len(args)-1]
	}
	for i := 0; i < len(args); i += 2 {
		trPairs = append(trPairs, domain.TrPair{
			Variable: args[i],
			Value:    args[i+1],
		})
	}
	actionlog.Error(l.taskName, actionlog.Translation{
		Section: appName,
		Key:     key,
		TrPairs: trPairs,
	}, l.user)
}

func (l *Logger) UnknownErr(errMsg string) {
	l.Error(fmt.Sprintf("%s_error", appName), "message", errMsg)
}
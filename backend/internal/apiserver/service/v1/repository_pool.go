// Package v1
/**
* @Project : terravirtualmachine
* @File    : network.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/20 17:03
**/

package v1

import (
	"context"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	uuid "github.com/satori/go.uuid"
	disk2 "github.com/shirou/gopsutil/v3/disk"
	"github.com/shopspring/decimal"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/golibrary/errors"
	"gitlab.local/golibrary/utils"
	"gopkg.in/ini.v1"
)

type RepositoryPoolSrv interface {
	Create(ctx context.Context, repo *domain.RepositoryPool, needImportData bool, opts metav1.CreateOptions) (*domain.RepositoryPool, error)
	Update(ctx context.Context, repoId string, repo *domain.RepositoryPool, opts metav1.UpdateOptions) error
	Get(ctx context.Context, repoId string, opts metav1.GetOptions) (*domain.RepositoryPool, error)
	List(ctx context.Context, opts metav1.ListOptions) (*dto.RepositoryPoolList, error)
	Delete(ctx context.Context, repoId string, needSaveData bool, opts metav1.DeleteOptions) (*domain.RepositoryPool, error)
	ListAvailVolumes(ctx context.Context, opts metav1.GetOptions) (map[string]dto.Volume, error)
	WhetherExistData(ctx context.Context, repo *domain.RepositoryPool, opts metav1.GetOptions) (*dto.RepoHostList, error)
	CheckVmsRunning(ctx context.Context, repoId string, opts metav1.GetOptions) ([]string, error)
	ListVmsInRepo(ctx context.Context, repo *domain.RepositoryPool, opts metav1.GetOptions) (*dto.RepoVMsDto, error)
}

const (
	VolumeDir            = "/tmp/volume"
	StorageDir           = "/tmp/storage"
	FlashCacheDir        = "/tmp/flashcache"
	TrimConfig           = "/etc/volume/trim.conf"
	RaidSyncSpeedConf    = "/etc/RaidSyncSpeed.ini"
	DiskDeActivationFile = "/etc/disk_deactivation.conf"
)

type VmRepoService interface {
	//列出存储池下所有虚拟机状态,返回[uuid]status
	ListVmStatus(ctx context.Context, vguestIds []string) (map[string]string, error)
	//该接口接受从配置文件中读出的虚拟机配置信息，更新存储池ID后，恢复虚拟机的etcd键值，同时将etcd值重新返还
	RestoreVmsConfig(ctx context.Context, repoId string, vguestId string, vguestCfg *string, vdiskCfg *string, vnicCfg *string) error
	//删除存储池下的所有虚拟机信息（删除vnic,vdisk等的相关所有etcd信息)，注意需要保存数据的情况下虚拟盘不能删
	DeleteVmList(ctx context.Context, repoId string, vguestIds []string, needSaveData bool) error
}

var _ VmRepoService = (*vmRepoService)(nil)

func (r *repositoryPoolService) Create(ctx context.Context, repo *domain.RepositoryPool, needImportData bool, opts metav1.CreateOptions) (*domain.RepositoryPool, error) {
	//判断是卷中否存在数据
	vmPath := path.Join(repo.LocationVolume, domain.RepoPathInVolume)

	hasVM, vMsList, err := r.hasFilesinDir(vmPath)
	if err != nil {
		log.Errorf("%s", readDirFailedLog(vmPath))
		return nil, err
	}

	if hasVM {
		//提示是否需要导入数据，将返回值传给needImportData
		if needImportData {
			//创建存储池
			repoId := uuid.Must(uuid.NewV4()).String()
			repo.RepoId = repoId

			err := r.store.RepositoryPool().Create(ctx, repoId, repo, opts)
			if err != nil {
				log.Errorf("%s", createRepoFailedLog(repo.Name))
				return nil, errors.WithCode(code.ErrEtcd, err.Error())
			}

			for _, vm := range vMsList {
				//掉用创建虚拟机接口，此时vnic的UUID，vdiskUUID等会重新生成
				vmFilePath := path.Join(repo.LocationVolume, domain.RepoPathInVolume, vm, domain.VguestConf)
				vmConfig, err := r.getConfigFile(vmFilePath)
				if err != nil {
					log.Warnf("%s", getCfgFailedLog(vmFilePath))
				}

				//导入数据,调用vnic等模块的更新接口，更新etcd
				vdiskFilePath := path.Join(repo.LocationVolume, domain.RepoPathInVolume, vm, domain.VdiskConf)
				vdiskConfig, err := r.getConfigFile(vdiskFilePath)
				if err != nil {
					log.Warnf("%s", getCfgFailedLog(vdiskFilePath))
				}

				//导入数据，调用vnic恢复接口
				vnicFilePath := path.Join(repo.LocationVolume, domain.RepoPathInVolume, vm, domain.VnicConf)
				vnicConfig, err := r.getConfigFile(vnicFilePath)
				if err != nil {
					log.Warnf("%s", getCfgFailedLog(vnicFilePath))
				}

				//调用restore接口
				err = r.vmService.RestoreVmsConfig(ctx, repoId, vm, &vmConfig, &vdiskConfig, &vnicConfig)
				if err != nil {
					log.Warnf("[Repository_Service] restore virtual machine %s failed: %s", vm, err.Error())
				}

				//此处需要更新配置文件
				if err := updateConfigFile(vmFilePath, vmConfig); err != nil {
					log.Warnf("%s", updateCfgFailedLog(vmFilePath))
				}

				if err := updateConfigFile(vdiskFilePath, vdiskConfig); err != nil {
					log.Warnf("%s", updateCfgFailedLog(vdiskFilePath))
				}

				if err := updateConfigFile(vdiskFilePath, vdiskConfig); err != nil {
					log.Warnf("%s", updateCfgFailedLog(vdiskFilePath))
				}
			}
		} else {
			//todo:删除虚拟盘
			err := r.vmService.DeleteVmList(ctx, "", vMsList, false)
			if err != nil {
				log.Errorf("[Repository_Service] virtual machines deletion failed - %s", err.Error())
			}

			//删除虚拟机配置
			configPath := path.Join(repo.LocationVolume, domain.RepoPathInVolume)
			entries, err := os.ReadDir(configPath)
			if err != nil {
				log.Errorf("%s", readDirFailedLog(vmPath))
			}

			for _, entry := range entries {
				if entry.IsDir() && entry.Name() != "." && entry.Name() != ".." {
					entryPath := fmt.Sprintf("%v/%v", configPath, entry.Name())
					os.RemoveAll(entryPath)
				}
			}

			//创建一个新的存储池
			repoId := uuid.Must(uuid.NewV4()).String()
			repo.RepoId = repoId

			err = r.store.RepositoryPool().Create(ctx, repoId, repo, opts)
			if err != nil {
				log.Errorf("%s", createRepoFailedLog(repo.Name))
				return nil, errors.WithCode(code.ErrEtcd, err.Error())
			}
		}

	} else {
		// 创建一个新的存储池
		repoId := uuid.Must(uuid.NewV4()).String()
		repo.RepoId = repoId

		err = r.store.RepositoryPool().Create(ctx, repoId, repo, opts)
		if err != nil {
			log.Errorf("%s", createRepoFailedLog(repo.Name))
			return nil, errors.WithCode(code.ErrEtcd, err.Error())
		}
	}

	log.Infof("[Repository_Service] repository creation succeeded - %s", repo.Name)
	return repo, nil
}

func (r *repositoryPoolService) Update(ctx context.Context, repoId string, repo *domain.RepositoryPool, opts metav1.UpdateOptions) error {
	err := r.store.RepositoryPool().Update(ctx, repoId, repo, opts)
	if err != nil {
		log.Errorf("[Repository_Service] repository updating failed - Backend storage failed to update %s", repo.Name)
		return errors.WithCode(code.ErrEtcd, err.Error())
	}
	log.Infof("[Repository_Service] repository updating succeeded - %s", repo.Name)
	return nil
}

func (r *repositoryPoolService) Get(ctx context.Context, repoId string, opts metav1.GetOptions) (*domain.RepositoryPool, error) {
	repo, err := r.store.RepositoryPool().Get(ctx, repoId, opts)
	if err != nil {
		log.Errorf("%s", getRepoFailedLog(repoId))
		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}
	log.Infof("[Repository_Service] repository retrieval succeeded - %s", repo.Name)
	return repo, nil
}

func (r *repositoryPoolService) List(ctx context.Context, opts metav1.ListOptions) (*dto.RepositoryPoolList, error) {
	repos, err := r.store.RepositoryPool().List(ctx, opts)
	if err != nil {
		log.Errorf("[Repository_Service] Backend storage failed to list repositories")
		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	//获取存储池基本信息
	var reposDto dto.RepositoryPoolList
	reposDto.Items = make(map[string]*dto.RepositoryPoolDto)
	for _, repo := range repos.Items {
		reposDto.TotalCount++
		reposDto.Items[repo.Name] = &dto.RepositoryPoolDto{
			RepoId:             repo.RepoId,             //存储池UUID
			Name:               repo.Name,               //存储空间名字
			HostID:             repo.HostID,             //主机唯一标识
			LocationVolume:     repo.LocationVolume,     //存储空间所在volume
			TotalSize:          repo.TotalSize,          //存储空间总容量
			HardLimit:          repo.HardLimit,          //空间不足阈值
			SoftLimit:          repo.SoftLimit,          //空间不足百分比阈值
			EnableLowerNotify:  repo.EnableLowerNotify,  //可用空间低于"空间不足"阈值时通知
			LastNotifyTreshold: repo.LastNotifyTreshold, //最后通知阈值
		}
	}

	//获取存储池容量使用情况
	repoUsed, err := r.redis.CollVolume().List(ctx, opts)
	if err != nil {
		log.Errorf("[Repository_Service] Backend redis failed to get usage")
		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	for _, item := range reposDto.Items {
		locationVolume := path.Base(item.LocationVolume)
		if _, ok := repoUsed.Storage[locationVolume]; !ok {
			continue
		}

		item.Used = repoUsed.Storage[locationVolume].SizeUsedByte
		item.PercentUsed = repoUsed.Storage[locationVolume].PercentUsed
		item.TotalSize = repoUsed.Storage[locationVolume].SizeFreeByte + repoUsed.Storage[locationVolume].SizeUsedByte
		softLimit := item.SoftLimit

		left := 100 - item.PercentUsed
		if left > softLimit {
			item.Status = domain.StatusNormal
		} else {
			item.Status = domain.StatusInsufficient
		}
	}

	log.Infof("[Repository_Service] list repositories succeeded")
	return &reposDto, nil
}

func (r *repositoryPoolService) Delete(ctx context.Context, repoId string, needSaveData bool, opts metav1.DeleteOptions) (*domain.RepositoryPool, error) {

	//获取待删除存储池的信息
	repo, _ := r.store.RepositoryPool().Get(ctx, repoId, metav1.GetOptions{})
	//判断是卷中否存在数据
	vmPath := path.Join(repo.LocationVolume, domain.RepoPathInVolume)
	hasVM, vMsList, err := r.hasFilesinDir(vmPath)
	if err != nil {
		log.Errorf("%s", readDirFailedLog(vmPath))
		return nil, err
	}
	if hasVM {
		err := r.vmService.DeleteVmList(ctx, "", vMsList, false)
		if err != nil {
			log.Errorf("[Repository_Service] virtual machine deletion failed - %s", err.Error())
		}

		if !needSaveData {
			configPath := path.Join(repo.LocationVolume, domain.RepoPathInVolume)
			entries, err := os.ReadDir(configPath)
			if err != nil {
				log.Errorf("%s", readDirFailedLog(vmPath))
			}
			for _, entry := range entries {
				if entry.IsDir() && entry.Name() != "." && entry.Name() != ".." {
					entryPath := fmt.Sprintf("%v/%v", configPath, entry.Name())
					os.RemoveAll(entryPath)
				}
			}
		}
	}

	//删除存储池
	err = r.store.RepositoryPool().Delete(ctx, repoId, opts)
	if err != nil {
		log.Errorf("[Repository_Service] repository deletion failed - Backend storage failed to delete %s", repoId)
		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	log.Infof("[Repository_Service] repository deletion succeeded - %s", repoId)
	return repo, nil
}

func (r *repositoryPoolService) CheckVmsRunning(ctx context.Context, repoId string, opts metav1.GetOptions) ([]string, error) {
	//Todo:调用虚拟机接口获取存储下的所有虚拟机运行状态，有虚拟机运行提示不能删除，并返回虚拟机名字
	repo, err := r.store.RepositoryPool().Get(ctx, repoId, metav1.GetOptions{})
	if err != nil {
		log.Errorf("%s", getRepoFailedLog(repoId))
		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	vmPath := path.Join(repo.LocationVolume, domain.RepoPathInVolume)
	_, vMsList, err := r.hasFilesinDir(vmPath)
	if err != nil {
		log.Errorf("%s", readDirFailedLog(vmPath))
		return nil, err
	}

	hasRunning, err := r.vmService.ListVmStatus(ctx, vMsList)
	if err != nil {
		log.Errorf("[Repository_Service] virtual machine status retrieval from VmService failed")
		return nil, err
	}

	var result []string
	for vmName, _ := range hasRunning {
		result = append(result, vmName)
	}
	return result, nil
}

func (r *repositoryPoolService) ListAvailVolumes(ctx context.Context, opts metav1.GetOptions) (map[string]dto.Volume, error) {
	result := make(map[string]dto.Volume)
	rule, _ := regexp.Compile(`/Volume\d+$`)
	dfs, err := disk2.Partitions(false)
	if err != nil {
		return nil, nil
	}
	for _, item := range dfs {
		if !rule.MatchString(item.Mountpoint) {
			continue
		}
		blockDevice, volumeConf, err := r.ParseBlkLvm(item.Device)
		if err != nil {
			continue
		}
		if blockDevice.UUID == "" {
			continue
		}
		box := dto.Volume{
			Name:       blockDevice.LvName,
			Device:     item.Device,
			FileSystem: item.Fstype,
			MntPath:    item.Mountpoint,
			VolumeType: blockDevice.Type, // 这个字段有待商榷
		}
		box.UUID = volumeConf.UUID
		box.ShowName = volumeConf.ShowName
		box.Sort = volumeConf.Sort
		box.Description = volumeConf.Description
		box.Compression = volumeConf.Compression
		usage, err := disk2.Usage(item.Mountpoint)
		if err != nil {
			continue
		}
		box.Total = dto.Capacity{Value: float64(usage.Total / 1024), Unit: "KB"}
		box.Used = dto.Capacity{Value: float64(usage.Used / 1024), Unit: "KB"}
		box.Available = dto.Capacity{Value: float64(usage.Free / 1024), Unit: "KB"}
		box.Usage = usage.UsedPercent
		// if item.Fstype == "xfs" {
		// 	box.HyperLockStatus = 1
		// 	//获取 HyperLock 有效期时间
		// 	box.HyperLockDay = hyperlock.GetHyperLockDay(item.Device)
		// 	// neverOverdue, this field is for deletion hyper-lock volume, and it seems can never delete the hyper-lock volume
		// 	box.IsHyperLockOverdue = false
		// }
		// //如果卷有缓存，则类型为 "lvm_cache"
		// vgName, lvName, err := lvm.ParseBlkPath(item.Device)
		// vgsInfo := parselvm.NewVgsInstance()
		// lvInfo := vgsInfo[vgName].LogicalVolumes[lvName]
		// for _, seg := range lvInfo.Segments {
		// 	if seg.Type == "cache" {
		// 		box.VolumeType = box.VolumeType + "_cache"
		// 		break
		// 	}
		// }
		//读取卷的状态  （这里读取的时卷是否存在数据清理、删除中、ssdtrim等状态。）
		box.Status = r.volumeStatus(blockDevice.LvName)
		box.DeFragment = r.deFragmentStatus(item.Mountpoint)
		if box.DeFragment != "" {
			box.Status = box.DeFragment
		}

		result[box.UUID] = box
	}

	//读取所有的存储池信息，将result中已经有存储池的卷去除
	var keysToDelete []string
	repos, err := r.store.RepositoryPool().List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Errorf("[Repository_Service] Backend storage failed to list repositories")
		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	for key, volume := range result {
		for _, repo := range repos.Items {
			//挂在点即存储池的所在卷路径
			if volume.MntPath == repo.LocationVolume {
				keysToDelete = append(keysToDelete, key)
			}
		}
	}

	//过滤所有卷信息中已创建了存储池的卷，即为可用卷
	for _, key := range keysToDelete {
		delete(result, key)
	}

	return result, nil
}

func (r *repositoryPoolService) CreateConfig(ctx context.Context, guestId string, repoId string, vguestCfg string, vdiskCfg string, vniCfg string) error {
	repo, err := r.store.RepositoryPool().Get(ctx, repoId, metav1.GetOptions{})
	if err != nil {
		log.Errorf("%s", getRepoFailedLog(repoId))
		return errors.WithCode(code.ErrEtcd, err.Error())
	}

	vmdirPath := path.Join(repo.LocationVolume, domain.RepoPathInVolume, guestId)
	vmFilePath := path.Join(vmdirPath, domain.VguestConf)
	err = os.MkdirAll(vmdirPath, 0755)
	if err != nil {
		log.Errorf("[Repository_Service] directory creation failed - %s", vmdirPath)
		return err

	}
	_, err = os.Stat(vmFilePath)
	if os.IsNotExist(err) {
		file, err := os.Create(vmFilePath)
		if err != nil {

		}
		file.Close()
	}

	if err := updateConfigFile(vmFilePath, vguestCfg); err != nil {
		log.Warnf("%s", updateCfgFailedLog(vmFilePath))
	}

	vdiskFilePath := path.Join(vmdirPath, domain.VdiskConf)
	_, err = os.Stat(vdiskFilePath)
	if os.IsNotExist(err) {
		file, err := os.Create(vdiskFilePath)
		if err != nil {

		}
		file.Close()
	}
	if err := updateConfigFile(vdiskFilePath, vdiskCfg); err != nil {
		log.Warnf("%s", updateCfgFailedLog(vdiskFilePath))
	}

	vnicFilePath := path.Join(vmdirPath, domain.VnicConf)
	_, err = os.Stat(vnicFilePath)
	if os.IsNotExist(err) {
		file, err := os.Create(vnicFilePath)
		if err != nil {

		}
		file.Close()
	}

	if err := updateConfigFile(vnicFilePath, vniCfg); err != nil {
		log.Warnf("%s", updateCfgFailedLog(vnicFilePath))
	}
	return nil

}

func (r *repositoryPoolService) UpdateConfig(ctx context.Context, guestId string, repoId string, vguestCfg string, vdiskCfg string, vniCfg string) error {
	repo, err := r.store.RepositoryPool().Get(ctx, repoId, metav1.GetOptions{})
	if err != nil {
		log.Errorf("%s ", getRepoFailedLog(repoId))
		return errors.WithCode(code.ErrEtcd, err.Error())
	}

	vmFilePath := path.Join(repo.LocationVolume, domain.RepoPathInVolume, guestId, domain.VguestConf)
	if err := updateConfigFile(vmFilePath, vguestCfg); err != nil {
		log.Warnf("%s", updateCfgFailedLog(vmFilePath))
	}

	vdiskFilePath := path.Join(repo.LocationVolume, domain.RepoPathInVolume, guestId, domain.VdiskConf)
	if err := updateConfigFile(vdiskFilePath, vdiskCfg); err != nil {
		log.Warnf("%s", updateCfgFailedLog(vdiskFilePath))
	}

	vnicFilePath := path.Join(repo.LocationVolume, domain.RepoPathInVolume, guestId, domain.VnicConf)
	if err := updateConfigFile(vnicFilePath, vniCfg); err != nil {
		log.Warnf("%s", updateCfgFailedLog(vnicFilePath))
	}

	return nil
}

func (r *repositoryPoolService) ListVmsInRepo(ctx context.Context, repo *domain.RepositoryPool, opts metav1.GetOptions) (*dto.RepoVMsDto, error) {
	vmPath := path.Join(repo.LocationVolume, domain.RepoPathInVolume)
	//存储池基本信息赋值
	var result *dto.RepoVMsDto = &dto.RepoVMsDto{
		EnableLowerNotify:  repo.EnableLowerNotify,
		Name:               repo.Name,
		HardLimit:          repo.HardLimit,
		HostID:             repo.HostID,
		VolumePath:         repo.LocationVolume,
		RepoId:             repo.RepoId,
		SoftLimit:          repo.SoftLimit,
		LastNotifyTreshold: repo.LastNotifyTreshold,
		TotalSize:          repo.TotalSize,
	}

	//vMsList即为存储池下所有虚拟机UUID的数组
	_, vMsList, err := r.hasFilesinDir(vmPath)
	if err != nil {
		log.Errorf("%s", readDirFailedLog(vmPath))
		return nil, err
	}

	vmUsage, err := r.redis.CollVMUsage().List(ctx, metav1.ListOptions{})
	if err != nil {
		log.L(ctx).Errorf("[Repository_Service] vm_usage retrieval from redis failed - %s", err.Error())
	}

	var vDisksTotalSize float64
	for _, vmId := range vMsList {
		vmInfo, err := r.store.VMs().Get(ctx, vmId, metav1.GetOptions{})
		if err != nil {
			log.Errorf("[Repository_Service] virtual machine retrieval failed - Backend storage failed to get - %s", vmId)
		}
		vmDiskInfo, _ := r.redis.CollVDiskUsage().Get(ctx, vmId, metav1.GetOptions{})

		result.GuestsInfo.Items[vmId] = &dto.GuestInfo{
			Name:            vmInfo.Name,
			Used:            vmDiskInfo.TotalUsed,
			Total:           vmDiskInfo.TotalSize,
			TotalIOPS:       vmUsage.Items[vmId].TotalIOPS,
			TotalThroughput: vmUsage.Items[vmId].TotalThroughput,
			TotalIOLatency:  vmUsage.Items[vmId].TotalIOLatency,
		}
		vDisksTotalSize += vmDiskInfo.TotalSize
	}
	result.VdisksTotalSize = strconv.FormatFloat(vDisksTotalSize, 'f', 2, 64)
	return result, nil
}

func (r *repositoryPoolService) volumeStatus(name string) (result string) {
	//是否正在执行ssdtrim
	isTrimFile := filepath.Join(VolumeDir, "trim", name)
	if utils.Exists(isTrimFile) {
		result = "${storage,SSDTrim_running}"
	}
	//是否正在执行文件系统碎片整理
	isDefragmentFile := filepath.Join(VolumeDir, "defragment", fmt.Sprintf("lv%s", name[2:]))
	if utils.Exists(isDefragmentFile) {
		result = "${storage,defragment_running}"
	}

	stateFile := filepath.Join(VolumeDir, fmt.Sprintf("lv%s", name[2:]))
	if !utils.Exists(stateFile) {
		return
	}
	b, err := os.ReadFile(stateFile)
	if err != nil {
		return
	}
	body := strings.Trim(string(b), "\n")
	if body == "" || !strings.HasPrefix(body, "stop") {
		_ = os.Remove(stateFile)
		return
	}
	fp, length := r.parseStringVolumeOperate(body)
	if length >= 2 && fp[length-2] == "0" {
		result = fmt.Sprintf("${storage,deletion} %s%%", fp[length-1])
	}

	return
}

func (r *repositoryPoolService) parseStringVolumeOperate(input string) (result []string, length int) {
	if input == "" {
		return
	}
	result = strings.Split(input, ":")
	length = len(result)
	return
}

// 碎片整理状态
func (r *repositoryPoolService) deFragmentStatus(path string) (result string) {
	name := filepath.Base(path)
	logFile := fmt.Sprintf("/tmp/defragment_%s", name)
	if !utils.Exists(logFile) {
		return
	}
	result = "${storage,defragment_running}"
	return
}

// ParseBlkLvm  替代 ParseBlockDevice
// 参数是： string, Example: /dev/mapper/vg0-lv0
// 返回 dto.BlockDevice,error
func (s *repositoryPoolService) ParseBlkLvm(name string) (dto.BlockDevice, dto.VolumeMember, error) {
	blkDevice := dto.BlockDevice{}
	volumeConf := dto.VolumeMember{}
	blkName := filepath.Base(name)
	re := regexp.MustCompile(`^(\w+)-(\w+)$`)
	matches := re.FindStringSubmatch(blkName) // []string{vg0-lv0, vg0, lv0}
	if matches == nil || len(matches) != 3 {
		err := fmt.Errorf("Invalid input format")
		return blkDevice, volumeConf, err
	}
	blkDevice.Storage = matches[1]
	blkDevice.LvName = matches[2]
	//适配新卷定义规则...  根据Blk(/dev/mapper/vg0-lv0)来获取卷的信息 dto.VolumeMember
	volumeConf, err := s.GetVolumeByBlk(name)
	if err != nil {
		return blkDevice, volumeConf, errors.New("ParseBlockDevice: invalid block device")
	}
	blkDevice.UUID = volumeConf.UUID
	return blkDevice, volumeConf, nil
}

func (r *repositoryPoolService) WhetherExistData(ctx context.Context, repo *domain.RepositoryPool, opts metav1.GetOptions) (*dto.RepoHostList, error) {
	//判断是卷中否存在数据
	var hostList dto.RepoHostList
	vmPath := path.Join(repo.LocationVolume, domain.RepoPathInVolume)

	hasVM, _, err := r.hasFilesinDir(vmPath)
	if err != nil {
		log.Errorf("%s", readDirFailedLog(vmPath))
		return nil, err
	}

	hostList.HasData = hasVM
	hosts, _ := r.store.Networks().ListHosts(ctx, metav1.ListOptions{})
	for _, host := range hosts.Items {
		hostList.Hosts = append(hostList.Hosts, host.HostID)
	}
	return &hostList, nil
}

func (Self *repositoryPoolService) GetVolumeByBlk(blk string) (result dto.VolumeMember, errResult error) {
	// 旧的ssd缓存逻辑
	//fc := diskutils.ParseFlashCache(blk)
	//if fc != nil {
	//	blk = fc.LvmDevice
	//}
	errResult = errors.New(fmt.Sprintf("invalid blk %s", blk))
	var uuid string
	if regexp.MustCompile(`/dev/md\d+$`).MatchString(blk) {
		raidName := filepath.Base(blk)
		ri, err := Self.RaidStatus(raidName)
		if err != nil {
			return
		}
		uuid = ri.UUID
	} else {
		lvmInfo := Self.lvm.GetLvInfoFromBackup(blk)
		if lvmInfo == nil {
			return
		}
		if lvmInfo.LvTag == "" {
			//uuid = diskutils.BlkId(blk, "UUID")
			uuid = LsBlk(blk, "UUID")
			if uuid != "" {
				lvmInfo.LvTag = uuid
				lvmInfo.LvTags = []string{uuid}
				err := Self.lvm.SetLvTag(blk, uuid)
				if err != nil {
					_, _ = fmt.Fprintln(os.Stderr, err)
				}
			}
		}
		uuid = lvmInfo.LvTag
	}

	if uuid != "" && utils.Exists(blk) {
		result, _ = Self.GetVolumeByUUID(uuid)
		errResult = nil
	}
	return
}

func (Self *repositoryPoolService) GetVolumeByUUID(uuid string) (result dto.VolumeMember, errResult error) {
	list := LoadConfig(dto.VolumeConfig)
	exists := false
	for _, item := range list {
		if item.UUID == uuid {
			result = item
			exists = true
			break
		}
	}
	if !exists {
		errResult = errors.Errorf("can't found uuid from %s", dto.VolumeConfig)
	}
	return
}

func LoadConfig(path string) []dto.VolumeMember {
	result := make([]dto.VolumeMember, 0)
	if !utils.Exists(path) {
		return result
	}
	cfg, err := ini.Load(path)
	if err != nil {
		return result
	}
	for _, section := range cfg.Sections() {
		if section.Name() == ini.DefaultSection {
			continue
		}
		box := dto.VolumeMember{UUID: section.Name()}
		if section.MapTo(&box) != nil {
			continue
		}
		if strings.HasPrefix(box.ShowName, "Volume") {
			box.ShowName = strings.ReplaceAll(box.ShowName, `Volume #`, `Volume`)
			box.ShowName = strings.ReplaceAll(box.ShowName, `Volume`, `${global,lvm}`)
		} else if strings.HasPrefix(box.ShowName, "StoragePool") {
			box.ShowName = strings.ReplaceAll(box.ShowName, `StoragePool #`, `StoragePool`)
			box.ShowName = strings.ReplaceAll(box.ShowName, `StoragePool`, `${global,storagepool}`)
		}
		result = append(result, box)
	}
	return result
}

// RaidStatus 从阵列基本信息中解析出必要的信息 example:md0,md1,...
func (Self *repositoryPoolService) RaidStatus(name string) (info dto.RaidInfo, errs error) {
	blk := fmt.Sprintf("/dev/%s", name)
	if !utils.Exists(blk) {
		errs = errors.New(fmt.Sprintf("[%s] isn't an block device!", blk))
		return
	}
	// 替换掉之前通过mdadm -D /dev/mdx获取的阵列信息
	//raid := Self.raidInfo(blk)
	info.Name = name
	info.Sort, _ = strconv.Atoi(name[2:])
	info.Level = utils.GetFirstLine(fmt.Sprintf("/sys/block/%s/md/level", name))
	if file, err := os.ReadFile("/etc/mdadm.conf"); err == nil {
		for _, line := range strings.Split(string(file), "\n") {
			if !strings.Contains(line, name) {
				continue
			}
			for _, v := range strings.Split(line, " ") {
				if strings.Contains(v, "UUID=") {
					if flags := strings.Split(v, "="); len(flags) > 1 {
						info.UUID = flags[1]
					}
				} else if strings.Contains(v, "name=") {
					if flags := strings.Split(v, ":"); len(flags) > 1 {
						info.Flag = flags[1]
					}
				}
			}
		}
	}
	// Array Size : 973337600 (928.25 GiB 996.70 GB)
	//sizeRule := regexp.MustCompile(`(\d+) \(`)
	//sizeString := sizeRule.FindStringSubmatch(raid["Array Size"]) // 可以 cat//sys/block/md0/size 扇区数量。一个扇区是512B
	arraySizeString := utils.GetFirstLine(fmt.Sprintf("/sys/block/%s/size", name)) // 扇区大小
	if arraySizeInt, err := strconv.Atoi(arraySizeString); err == nil {
		sizeValue := float64(arraySizeInt / 2)
		info.Size = dto.Capacity{
			Value: sizeValue,
			Unit:  "KB",
		}
	}
	devSizeString := utils.GetFirstLine(fmt.Sprintf("/sys/block/%s/md/component_size", name))
	if devSizeString != "" {
		devSizeValue, _ := strconv.ParseFloat(devSizeString, 64)
		info.DevSize = dto.Capacity{
			Value: devSizeValue,
			Unit:  "KB",
		}
	}
	info.TotalDisks, _ = strconv.Atoi(utils.GetFirstLine(fmt.Sprintf("/sys/block/%s/md/raid_disks", name)))
	info.UsedDisks, info.WorkDisks = func(md string) (totalUsed, work int) {
		root := fmt.Sprintf("/sys/block/%s/md/", md)
		list, _ := os.ReadDir(root)
		for _, item := range list {
			if !strings.HasPrefix(item.Name(), "dev-sd") {
				continue
			}
			totalUsed += 1
			target := filepath.Join(root, item.Name(), "block")
			link, err := os.Readlink(target)
			if err != nil {
				continue
			}
			blockTarget, _ := filepath.Abs(link)
			realPath := filepath.Join("/sys/devices", blockTarget)
			blockState := utils.GetFirstLine(filepath.Join(root, item.Name(), "state"))
			if !strings.Contains(blockState, "faulty") && utils.Exists(realPath) {
				work += 1
			}
		}
		return
	}(name)

	if info.TotalDisks == 1 && info.Level == "raid1" {
		info.Level = "single"
	}
	info.Health = 7
	// 依赖该文件的值来判断阵列是否开启bitmap "/sys/block/md0/md/bitmap/location" -> 未启用状态:none 或 启用状态:+8
	bitmapFlag := utils.GetFirstLine(fmt.Sprintf("/sys/block/%s/md/bitmap/location", name))
	if !strings.Contains(bitmapFlag, "none") {
		info.Bitmap = 1
	}
	//if _, ok := raid["Intent Bitmap"]; ok {
	//	info.Bitmap = 1
	//}
	//info.FsType = diskutils.BlkId(blk, "TYPE")
	info.FsType = LsBlk(blk, "FSTYPE")
	syncFile := fmt.Sprintf("/sys/block/%s/md/sync_action", name)
	syncStat := "idle"
	if utils.Exists(syncFile) {
		syncStat = utils.GetFirstLine(syncFile)
	}
	if syncStat != "idle" && syncStat != "check" {
		info.Process = syncStat
		info.Health = 3
		complete := utils.GetFirstLine(fmt.Sprintf("/sys/block/%s/md/sync_completed", name))
		if strings.Contains(complete, "/") {
			sp := strings.Split(complete, "/")
			completed, _ := strconv.ParseFloat(strings.TrimSpace(sp[0]), 64)
			totalsize, _ := strconv.ParseFloat(strings.TrimSpace(sp[1]), 64)
			info.Percent, _ = decimal.NewFromFloat(completed * 100 / totalsize).Round(2).Float64()
		} else {
			info.Percent = float64(0)
		}
	} else {
		switch info.Level {
		case "raid0", "linear", "single":
			if info.WorkDisks < info.TotalDisks {
				info.Health = 0 //损毁不可修复
			}
		case "raid1":
			if info.WorkDisks < info.TotalDisks {
				info.Health = 2 //缺失状态
				if info.TotalDisks == 1 {
					info.Health = 0
				}
			}
		case "raid5":
			if info.TotalDisks-info.WorkDisks > 1 {
				info.Health = 0
			} else if info.TotalDisks-info.WorkDisks == 1 {
				info.Health = 2
			}
		case "raid6":
			if info.TotalDisks-info.WorkDisks > 2 {
				info.Health = 0
			} else if info.TotalDisks-info.WorkDisks > 0 {
				info.Health = 2
			}
		case "raid10":
			if info.TotalDisks-info.WorkDisks > info.TotalDisks/2 {
				info.Health = 0
			} else if info.TotalDisks-info.WorkDisks > 0 {
				info.Health = 2
			}
		}
	}
	return info, nil
}

// lsblk --noheadings -o TYPE /dev/mapper/vg0-lv0
func LsBlk(device string, fields string) string {
	uuidStr, err := utils.ShellWithString(fmt.Sprintf("lsblk --noheadings -o %s %s", fields, device))
	if err != nil {
		return ""
	}
	lines := strings.Split(uuidStr, "\n")
	if len(lines) < 1 {
		return ""
	}
	return lines[0]
}

func (Self *repositoryPoolService) hasFilesinDir(dir string) (bool, []string, error) {
	Self.mu.RLock()
	defer Self.mu.RUnlock()
	entries, err := os.ReadDir(dir)
	if err != nil {
		return false, nil, err
	}
	var hasfiles bool = false
	var dirs []string
	for _, entry := range entries {
		if entry.IsDir() && entry.Name() != "." && entry.Name() != ".." {
			hasfiles = true
			dirs = append(dirs, entry.Name())
		}
	}
	return hasfiles, dirs, nil
}

func (Self *repositoryPoolService) getConfigFile(filePath string) (string, error) {
	Self.mu.RLock()
	defer Self.mu.RUnlock()
	jsonFile, err := os.Open(filePath)
	if err != nil {
		return "", err
	}

	defer jsonFile.Close()

	byteValue, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}

	result := string(byteValue)
	return result, nil
}

func updateConfigFile(filePath string, info string) error {
	file, err := os.OpenFile(filePath, os.O_WRONLY|os.O_TRUNC|os.O_CREATE, 0644)
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = file.WriteString(info)
	if err != nil {
		return err
	}
	return nil
}

type repositoryPoolService struct {
	store     store.Factory
	redis     store.RedisFactory
	lvm       *Vgs
	mu        sync.RWMutex
	vmService VmRepoService
}

var _ RepositoryPoolSrv = (*repositoryPoolService)(nil)

func newRepositoryPool(srv *service) *repositoryPoolService {
	var repoService repositoryPoolService
	repoService.store = srv.store
	repoService.vmService = newVmRepoService(srv)
	repoService.redis = srv.redis
	return &repoService
}

func createRepoFailedLog(repoName string) string {
	return fmt.Sprintf("[Repository_Service] repository creating failed - Backend storage failed to create %s", repoName)
}

func readDirFailedLog(dirPath string) string {
	return fmt.Sprintf("[Repository_Service] directory reading failed - %s", dirPath)
}

func getRepoFailedLog(repoId string) string {
	return fmt.Sprintf("[Repository_Service] repository retrieval failed - Backend storage failed to get %s", repoId)
}

func getCfgFailedLog(cfgPath string) string {
	return fmt.Sprintf("[Repository_Service] configuration file retrieval failed - %s", cfgPath)
}

func updateCfgFailedLog(cfgPath string) string {
	return fmt.Sprintf("[Repository_Service] configuration file updating failed - %s", cfgPath)
}

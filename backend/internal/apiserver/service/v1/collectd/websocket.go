package v1

import (
	"encoding/json"
	"sync"

	"github.com/gorilla/websocket"
)

type WebSocketService struct {
	mu    sync.RWMutex
	subs  map[string][]*websocket.Conn
	conns map[*websocket.Conn]bool // 新增连接管理
}

func NewWebSocketService() *WebSocketService {
	return &WebSocketService{
		subs:  make(map[string][]*websocket.Conn),
		conns: make(map[*websocket.Conn]bool),
	}
}

// 处理新连接
func (s *WebSocketService) HandleConnection(conn *websocket.Conn) {
	s.mu.Lock()
	s.conns[conn] = true
	s.mu.Unlock()

	conn.SetCloseHandler(func(code int, text string) error {
		s.CloseConnection(conn)
		return nil
	})
}

// 订阅主题
func (s *WebSocketService) Subscribe(topic string, conn *websocket.Conn) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 去重处理
	for _, c := range s.subs[topic] {
		if c == conn {
			return
		}
	}
	s.subs[topic] = append(s.subs[topic], conn)
}

// 取消订阅
func (s *WebSocketService) Unsubscribe(topic string, conn *websocket.Conn) {
	s.mu.Lock()
	defer s.mu.Unlock()

	subs := s.subs[topic]
	for i, c := range subs {
		if c == conn {
			s.subs[topic] = append(subs[:i], subs[i+1:]...)
			return
		}
	}
}

// 广播消息到主题
func (s *WebSocketService) Broadcast(topic string, message map[string]string) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 序列化消息为JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		// 处理序列化错误，可以记录日志
		return
	}

	for _, conn := range s.subs[topic] {
		if err := conn.WriteMessage(websocket.TextMessage, jsonData); err != nil {
			s.CloseConnection(conn)
		}
	}
}

// 关闭单个连接
func (s *WebSocketService) CloseConnection(conn *websocket.Conn) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 从所有主题中移除
	for topic := range s.subs {
		s.Unsubscribe(topic, conn)
	}
	conn.Close()
	delete(s.conns, conn)
}

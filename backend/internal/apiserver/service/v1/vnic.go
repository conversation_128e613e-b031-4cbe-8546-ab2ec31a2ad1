package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"os/exec"
	"strings"

	"github.com/digitalocean/go-openvswitch/ovs"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	uuid "github.com/satori/go.uuid"
	"github.com/vishvananda/netlink"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
)

type vNicService struct {
	store store.Factory
}

func newVNicService(s *service) *vNicService {
	return &vNicService{
		store: s.store,
	}
}

func (v *vNicService) Create(ctx context.Context, vnic *dto.VMInstanceRequestEdit, opts metav1.CreateOptions) ([]string, string, error) {
	var netIDs []string
	for _, data := range vnic.VNics.Add {
		//vnic基本信息赋值
		var vnicInfo domain.VNic
		vnicInfo.VNicID = uuid.Must(uuid.NewV4()).String()
		vnicInfo.PreferSRIOV = data.PreferSRIOV
		vnicInfo.VNicType = data.VNicType
		vnicInfo.MacAddress = data.Mac
		vnicInfo.NetworkID = data.NetworkID
		vnicInfo.GuestID = vnic.GuestID

		netIDs = append(netIDs, vnicInfo.VNicID)

		err := v.store.VNics().Create(ctx, &vnicInfo, opts)
		if err != nil {
			log.Info("create vnic to etcd failed!")
			return nil, "", err
		}
	}
	vnicList, err := v.store.VNics().List(ctx, vnic.GuestID, metav1.ListOptions{})
	if err != nil {
		log.Info("get vnic list from etcd failed!")
	}
	vnicCfg, err := json.Marshal(vnicList)
	if err != nil {

	}
	return netIDs, string(vnicCfg), nil
}

func (v *vNicService) Delete(ctx context.Context, vnic *dto.VMInstanceRequestEdit, opts metav1.DeleteOptions) error {
	for _, data := range vnic.VNics.Delete {
		//vnic基本信息赋值
		var vnicInfo domain.VNic
		vnicInfo.MacAddress = data.Mac
		vnicInfo.VNicID = data.VNicID
		err := v.store.VNics().Delete(ctx, &vnicInfo, metav1.DeleteOptions{})
		if err != nil {
			log.Info("Delete vnic from etcd failed!")
			return err
		}
	}
	return nil
}

func (v *vNicService) Update(ctx context.Context, vnic *dto.VMInstanceRequestEdit, opts metav1.UpdateOptions) error {
	for _, data := range vnic.VNics.Edit {
		//获取原vnic的信息
		vnicInfo, _ := v.store.VNics().Get(ctx, data.VNicID, metav1.GetOptions{})

		oldNetwork, _ := v.store.Networks().Get(ctx, vnicInfo.NetworkID, metav1.GetOptions{})
		newNetwork, _ := v.store.Networks().Get(ctx, data.NetworkID, metav1.GetOptions{})

		if vnic.Vmstate == "on" {
			for _, ifaces := range oldNetwork.Interfaces {
				for _, iface := range ifaces.Items {
					//与网络的接口断开连接
					ovsport := fmt.Sprintf("tap%v", strings.ReplaceAll(vnicInfo.MacAddress, ":", ""))
					err := exec.Command("ovs-vsctl", "del-port", iface.InterfaceName, ovsport).Run()
					if err != nil {
						log.Info("use ovs-vsctl delete port from OVS failed!")
					}
				}
			}

			vnicInfo.NetworkID = data.NetworkID
			//清空运行信息
			vnicInfo.Running[vnic.HostID].TotalCount = 0
			vnicInfo.Running[vnic.HostID].Items = nil

			//将虚拟网卡接入网络，并生成运行信息
			for _, ifaces := range newNetwork.Interfaces {
				for _, iface := range ifaces.Items {
					ovsport := fmt.Sprintf("tap%v", strings.ReplaceAll(vnicInfo.MacAddress, ":", ""))
					err := exec.Command("ovs-vsctl", "add-port", iface.InterfaceName, ovsport).Run()
					if err != nil {
						log.Info("use ovs-vsctl add port to OVS failed!")
					}
					vnicInfo.Running[vnic.HostID].TotalCount++
					runinginfo := &domain.VNicruning{InterfaceID: iface.InterfaceID, InterfaceName: iface.InterfaceName, OVSPort: ovsport}
					vnicInfo.Running[vnic.HostID].Items = append(vnicInfo.Running[iface.HostID].Items, runinginfo)
				}
			}
		} else {
			vnicInfo.NetworkID = data.NetworkID
		}
		err := v.store.VNics().Update(ctx, vnicInfo, opts)
		if err != nil {

		}
	}
	return nil
}

func (v *vNicService) List(ctx context.Context, GuestID string, opts metav1.ListOptions) (*domain.VNicList, error) {
	vnicList, err := v.store.VNics().List(ctx, GuestID, opts)
	if err != nil {
		log.Info("list virtual machine's vnics from etcd failed")
		return nil, err
	}
	return vnicList, err
}
func (v *vNicService) Recover(ctx context.Context, vnicList *domain.VNicList, repositoryUUID string, opts metav1.UpdateOptions) error {
	for _, vnic := range vnicList.Items {
		//清空运行信息
		vnic.Running = make(map[string]*domain.VNicruningList)
		//创建etcd信息
		err := v.store.VNics().Create(ctx, vnic, metav1.CreateOptions{})
		if err != nil {
			log.Info("create vnic to etcd failed")
		}
	}
	return nil
}
func (v *vNicService) Stop(ctx context.Context, vnics []string) error {
	type OptData struct {
		vnicInfo *domain.VNic
		network  *domain.Network
		ovsport  string
		link     netlink.Link
	}

	opts := make([]OptData, 0, len(vnics))
	defer func() {
		client := ovs.New()
		for _, opt := range opts {
			netlink.LinkDel(opt.link)

			if opt.network == nil {
				continue
			}
			for _, ifaces := range opt.network.Interfaces {
				for _, iface := range ifaces.Items {
					client.VSwitch.DeletePort(iface.InterfaceName, opt.ovsport)
				}
			}

			opt.vnicInfo.Running[opt.network.HostID].TotalCount = 0
			opt.vnicInfo.Running[opt.network.HostID].Items = nil
			v.store.VNics().Update(ctx, opt.vnicInfo, metav1.UpdateOptions{})
		}
	}()

	for _, vnicId := range vnics {
		opt := OptData{}

		vnicInfo, err := v.store.VNics().Get(ctx, vnicId, metav1.GetOptions{})
		if err != nil {
			log.Warnf("[VNic_Service] vnic stopping failed - Backend storage failed to get %s", vnicId)
			continue
		}
		ovsport := fmt.Sprintf("tap%v", strings.ReplaceAll(vnicInfo.MacAddress, ":", ""))

		link, err := netlink.LinkByName(ovsport)
		if err != nil {
			log.Warnf("[VNic_Service] vnic stopping failed - Net link failed to get %s", ovsport)
		}

		opt.network = nil
		if vnicInfo.NetworkID != "" {
			network, err := v.store.Networks().Get(ctx, vnicInfo.NetworkID, metav1.GetOptions{})
			if err != nil {
				log.Warnf("[VNic_Service] vnic stopping failed - Backend storage failed to get %s", vnicInfo.NetworkID)
			}
			opt.network = network
		}

		opt.ovsport = ovsport
		opt.link = link
		opts = append(opts, opt)
	}

	return nil
}

func (v *vNicService) Run(ctx context.Context, vnics []string) ([]string, error) {
	// 回滚操作
	availableIface := make(map[string]*domain.VNic)
	defer func() {
		for _, vnicInfo := range availableIface {
			if len(vnics) == len(availableIface) {
				//更新etcd信息
				v.store.VNics().Update(ctx, vnicInfo, metav1.UpdateOptions{})
				continue
			}

			ovsport := fmt.Sprintf("tap%v", strings.ReplaceAll(vnicInfo.MacAddress, ":", ""))
			link, _ := netlink.LinkByName(ovsport)
			netlink.LinkDel(link)
		}
	}()

	client := ovs.New()
	ifaceNames := make([]string, 0, len(vnics))
	for _, vnic := range vnics {
		vnicInfo, err := v.store.VNics().Get(ctx, vnic, metav1.GetOptions{})
		if err != nil {
			log.Info("get vnic information from etcd failed")
			return nil, err
		}
		if vnicInfo.Running == nil {
			vnicInfo.Running = make(map[string]*domain.VNicruningList)
		}

		ovsport := fmt.Sprintf("tap%v", strings.ReplaceAll(vnicInfo.MacAddress, ":", ""))
		mac, _ := net.ParseMAC(vnicInfo.MacAddress)
		link := &netlink.Tuntap{
			LinkAttrs: netlink.LinkAttrs{
				Name:         ovsport,
				HardwareAddr: mac,
			},
			Mode: netlink.TUNTAP_MODE_TAP,
		}

		client.VSwitch.ListBridges()
		if err := netlink.LinkAdd(link); err != nil {
			log.Errorf("failed to create tap device : %s", vnicInfo.MacAddress)
			return nil, err
		}

		ifaceNames = append(ifaceNames, ovsport)
		if vnicInfo.NetworkID != "" {
			network, err := v.store.Networks().Get(ctx, vnicInfo.NetworkID, metav1.GetOptions{})
			if err != nil {
				log.Info("get vnic information from etcd failed")
				return nil, err
			}

			//将虚拟网卡接入网络，并生成运行信息
			for _, ifaces := range network.Interfaces {
				for _, iface := range ifaces.Items {
					err := client.VSwitch.AddPort(iface.InterfaceName, ovsport)
					if err != nil {
						log.Info("use ovs-vsctl add port to OVS failed!")
						return nil, err
					}

					var vnicRnningList domain.VNicruningList
					vnicRnningList.TotalCount++
					runinginfo := &domain.VNicruning{InterfaceID: iface.InterfaceID, InterfaceName: iface.InterfaceName, OVSPort: ovsport}
					vnicRnningList.Items = append(vnicRnningList.Items, runinginfo)

					vnicInfo.Running[network.HostID] = &vnicRnningList
				}
			}
		}

		availableIface[vnic] = vnicInfo
	}
	return ifaceNames, nil
}

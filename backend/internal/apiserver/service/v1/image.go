package v1

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path"
	"sort"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	uuid "github.com/satori/go.uuid"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/golibrary/errors"
)

type ImageSrv interface {
	List(ctx context.Context, opts metav1.ListOptions) (*dto.ImageListDto, error)
	EditImage(ctx context.Context, img dto.ImageEdit) error
	GenerateUploadTask(ctx context.Context, img dto.ImageCreate) (domain.TaskID, error)
	UploadFile(ctx context.Context, taskId domain.TaskID) error
	Delete(ctx context.Context, imgId string) error
}

type asyncResult struct {
	Name       string
	RealName   string
	Id         string
	Type       domain.ImageType
	FileSize   uint64
	Status     domain.ImageState
	CreateTime int64
	ImageRepos []dto.ImageLocate
	Taskid     domain.TaskID
	Progress   int
}

type imageService struct {
	store store.Factory

	awaiting       map[domain.TaskID]chan asyncResult
	uploadTask     map[domain.TaskID]*createImageTask
	taskLastResult map[domain.TaskID]asyncResult
	taskRuningMut  sync.Mutex

	nextID chan domain.TaskID
	closed chan struct{}
}

func newImages(srv *service) *imageService {
	service := imageService{
		store:          srv.store,
		awaiting:       make(map[domain.TaskID]chan asyncResult),
		uploadTask:     make(map[domain.TaskID]*createImageTask),
		taskLastResult: make(map[domain.TaskID]asyncResult),
		nextID:         make(chan domain.TaskID),
		closed:         make(chan struct{}),
	}

	go service.idGenerator()
	return &service
}

func writeImageConf(uploadImg *domain.Image, wantUpateConfPaths []string) {
	for _, confPath := range wantUpateConfPaths {
		toConfFd, err := os.OpenFile(path.Join(confPath, domain.ImageConf), os.O_CREATE|os.O_WRONLY|os.O_EXCL, 0644)
		if err != nil {
			log.Warnf("[Image_Service] file creation failed - %s", path.Join(confPath, domain.ImageConf))
		}
		defer toConfFd.Close()

		encoder := json.NewEncoder(toConfFd)
		if err := encoder.Encode(uploadImg); err != nil {
			log.Warnf("[Image_Service] file writing failed - %s", path.Join(confPath, domain.ImageConf))
		}
	}
}

func (s *imageService) Delete(ctx context.Context, imgId string) error {
	image, err := s.store.Images().Get(ctx, imgId, metav1.GetOptions{})
	if err != nil {
		log.Errorf("[Image_Service] image deletion failed - Backend storage failed to get %s", imgId)
		return err
	}

	if err := s.store.Images().Delete(ctx, imgId, metav1.DeleteOptions{}); err != nil {
		log.Errorf("[Image_Service] image deletion failed - Backend storage failed to delete %s", imgId)
		return err
	}

	for _, repoId := range image.Repos {
		pool, _ := s.store.RepositoryPool().Get(ctx, repoId, metav1.GetOptions{})
		imgFile := image.LocationImageFile(imgId, pool.LocationVolume)
		imgDir := path.Dir(imgFile)

		os.RemoveAll(imgDir)
	}

	log.Infof("[Image_Service] image deletion succeeded")
	return nil
}

func (s *imageService) EditImage(ctx context.Context, img dto.ImageEdit) error {
	imgList, err := s.List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Errorf("[Image_Service] image editing failed - Backend storage failed to list")
		return err
	}

	var new dto.ImageDto
	var old dto.ImageDto
	var diskFileName string
	for _, l := range imgList.Items {
		if l.ImageId == img.Id {
			old = *l
			diskFileName = l.RealName
			break
		}
	}
	new.ImageId = img.Id
	new.ImageRepos = append(new.ImageRepos, func() []dto.ImageLocate {
		locates := make([]dto.ImageLocate, 0, len(img.RepoId))
		for _, repId := range img.RepoId {
			locates = append(locates, dto.ImageLocate{RepoId: repId})
		}
		return locates
	}()...)

	type replicate struct {
		from dto.ImageLocate // 编辑之前镜像的位置
		to   dto.ImageLocate // 编辑之后镜像的位置
	}

	// 计算新旧存储位置
	// 得出需要删除的镜像、复制的镜像
	replicateRules := make([]replicate, 0)
	{
		// 检查需要被复制的镜像
		for _, new_rep := range new.ImageRepos {
			tmpRep := replicate{to: dto.ImageLocate{RepoId: new_rep.RepoId}, from: dto.ImageLocate{}}
			isCp := true
			for _, old_rep := range old.ImageRepos {
				if old_rep.RepoId == new_rep.RepoId {
					isCp = false
					break
				}
			}
			if isCp {
				replicateRules = append(replicateRules, tmpRep)
			}
		}

		// 检查需要被删除的镜像
		for _, old_rep := range old.ImageRepos {
			tmpRep := replicate{to: dto.ImageLocate{}, from: dto.ImageLocate{RepoId: old_rep.RepoId}}
			isDel := true
			for _, new_rep := range new.ImageRepos {
				if old_rep.RepoId == new_rep.RepoId {
					isDel = false
					break
				}
			}
			if isDel {
				replicateRules = append(replicateRules, tmpRep)
			}
		}

		oldLocate := old.ImageRepos[0].RepoId
		for idx, _ := range replicateRules {
			if replicateRules[idx].from.RepoId == "" {
				// 将oldLocate存储的镜像复制到from.RepoId存储池中
				replicateRules[idx].from.RepoId = oldLocate
			}
		}
	}

	var uploadImg domain.Image
	uploadImg.Name = old.Name
	uploadImg.Type = old.Type
	uploadImg.RealFiles = old.RealName
	uploadImg.FileSize = old.FileSize
	uploadImg.CreatedAt = time.Unix(old.CreateTime, 0)
	uploadImg.Repos = append(uploadImg.Repos, func() []string {
		repos := make([]string, 0, len(old.ImageRepos))
		for _, rep := range old.ImageRepos {
			repos = append(repos, rep.RepoId)
		}
		return repos
	}()...)

	editImageId := img.Id
	wantUpateConfPaths := make([]string, 0)
	pool, err := s.store.RepositoryPool().List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Errorf("[Image_Service] image editing failed - Backend storage failed to list")
		return err
	}

	for _, rule := range replicateRules {
		to := rule.to
		from := rule.from

		var fromVolumeImagePath string
		var toVolumeImagePath string

		// 执行删除
		if to.RepoId == "" {
			rollbackRepos := make([]string, 0, len(uploadImg.Repos))
			tmpRepos := make([]string, 0, len(uploadImg.Repos))
			copy(rollbackRepos, uploadImg.Repos)

			for _, repoId := range uploadImg.Repos {
				if repoId == from.RepoId {
					continue
				}
				tmpRepos = append(tmpRepos, repoId)
			}
			uploadImg.Repos = tmpRepos

			var err error
			if len(uploadImg.Repos) == 0 {
				err = s.store.Images().Delete(ctx, editImageId, metav1.DeleteOptions{})
			} else {
				err = s.store.Images().Update(ctx, editImageId, &uploadImg, metav1.UpdateOptions{})
			}

			if err != nil {
				uploadImg.Repos = rollbackRepos
				writeImageConf(&uploadImg, wantUpateConfPaths)
				log.Errorf("[Image_Service] image editing failed - Backend storage failed to edit")
				return err
			}

			fromVolumeImagePath = path.Join(pool.Items[from.RepoId].LocationVolume, domain.ImageDiskVolumePath, editImageId)
			if err := os.RemoveAll(fromVolumeImagePath); err != nil {
				log.Warnf("[Image_Service] image editing failed - Error removing file %s", fromVolumeImagePath)
			}
			continue
		}

		// 执行新增
		fromVolumeImagePath = path.Join(pool.Items[from.RepoId].LocationVolume, domain.ImageDiskVolumePath, editImageId)
		toVolumeImagePath = path.Join(pool.Items[to.RepoId].LocationVolume, domain.ImageDiskVolumePath, editImageId)
		if _, err := os.Stat(toVolumeImagePath); os.IsNotExist(err) {
			err := os.MkdirAll(toVolumeImagePath, os.ModePerm)
			if err != nil {
				log.Errorf("[Image_Service] image editing failed - Error creating upload directory %s", toVolumeImagePath)
				return err
			}
		}

		fromFile := path.Join(fromVolumeImagePath, diskFileName)
		fromfd, err := os.Open(fromFile)
		if err != nil {
			return err
		}
		defer fromfd.Close()

		toFile := path.Join(toVolumeImagePath, diskFileName)
		tofd, err := os.OpenFile(toFile, os.O_CREATE|os.O_WRONLY|os.O_EXCL, 0644)
		if err != nil {
			return err
		}
		defer tofd.Close()

		if _, err := io.Copy(tofd, fromfd); err != nil {
			return err
		}

		uploadImg.Repos = append(uploadImg.Repos, to.RepoId)
		wantUpateConfPaths = append(wantUpateConfPaths, toVolumeImagePath)
		wantUpateConfPaths = append(wantUpateConfPaths, fromVolumeImagePath)
		if err := s.store.Images().Update(ctx, editImageId, &uploadImg, metav1.UpdateOptions{}); err != nil {
			log.Errorf("[Image_Service] image editing failed - Backend storage failed to edit")
			os.RemoveAll(toVolumeImagePath)
			uploadImg.Repos = uploadImg.Repos[0 : len(uploadImg.Repos)-1]
			writeImageConf(&uploadImg, wantUpateConfPaths)
			return err
		}
	}

	log.Infof("[Image_Service] image editing succeeded")
	writeImageConf(&uploadImg, wantUpateConfPaths)
	return nil
}

func (s *imageService) List(ctx context.Context, opts metav1.ListOptions) (*dto.ImageListDto, error) {
	// 获取所有的镜像数据
	images, err := s.store.Images().List(ctx, opts)
	if err != nil {
		log.Errorf("[Image_Service] image listing failed - Backend storage failed to list")
		return nil, errors.WithCode(code.ErrEtcd, err.Error())
	}

	var dtos dto.ImageListDto
	dtos.TotalCount = images.GetTotalCount()
	dtos.Items = make([]*dto.ImageDto, 0)

	for id, item := range images.Items {
		imgDto := &dto.ImageDto{}
		imgDto.FileSize = item.FileSize
		imgDto.Name = item.Name
		imgDto.Type = item.Type
		imgDto.ImageId = id
		imgDto.RealName = item.RealFiles
		imgDto.CreateTime = item.CreatedAt.Unix()

		if item.Type != "" && len(item.Repos) > 0 && item.FileSize > 0 {
			imgDto.Status = domain.StateAvailable
		} else if item.Name != "" {
			imgDto.Status = domain.StateCreatring
		} else {
			imgDto.Status = domain.StateUploading
		}

		var locate dto.ImageLocate
		imgDto.ImageRepos = make([]dto.ImageLocate, len(item.Repos))
		repos, err := s.store.RepositoryPool().List(context.Background(), metav1.ListOptions{})
		if err != nil {
			continue
		}

		for idx, repo := range item.Repos {
			locate.RepoId = repo
			if repo, ok := repos.Items[repo]; ok {
				locate.RepoName = repo.Name
			}

			imgDto.ImageRepos[idx] = locate
		}

		dtos.Items = append(dtos.Items, imgDto)
	}
	sort.Slice(dtos.Items, func(i, j int) bool {
		return dtos.Items[i].CreateTime < dtos.Items[j].CreateTime
	})

	proBegin := len(dtos.Items)
	listProgress, _ := s.listTaskProgress(ctx)
	for _, pro := range listProgress {
		imgDto := &dto.ImageDto{}
		imgDto.Status = pro.Status
		imgDto.ImageId = pro.Id
		imgDto.Name = pro.Name
		imgDto.Progress = pro.Progress
		imgDto.FileSize = pro.FileSize
		imgDto.CreateTime = pro.CreateTime
		imgDto.TaskId = pro.Taskid
		imgDto.Type = pro.Type
		imgDto.ImageRepos = pro.ImageRepos

		dtos.Items = append(dtos.Items, imgDto)
	}
	sort.Slice(dtos.Items[proBegin:], func(i, j int) bool {
		return dtos.Items[i].CreateTime < dtos.Items[j].CreateTime
	})

	log.Info("[Image_Service] image listing succeeded")
	return &dtos, nil
}

func websocketConnect(ctx context.Context) (*websocket.Conn, error) {
	// 升级请求为websocket连接
	var upgrader = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	var conn *websocket.Conn
	var err error
	if c, ok := ctx.(*gin.Context); ok {
		conn, err = upgrader.Upgrade(c.Writer, c.Request, nil)
		if err != nil {
			log.Errorf("[Image_Service] websocket upgrading failed - %s", err.Error())
			return nil, err
		}
	}

	return conn, nil
}

func (s *imageService) UploadFile(ctx context.Context, taskId domain.TaskID) error {
	s.taskRuningMut.Lock()
	task, ok := s.uploadTask[taskId]
	s.taskRuningMut.Unlock()

	if !ok {
		log.Errorf("[Image_Service] image uploading failed - invalid task id %s", taskId)
		return fmt.Errorf("please create a task ID first,then execute the task of uploading files")
	}

	conn, err := websocketConnect(ctx)
	if err != nil {
		return err
	}
	task.conn = conn

	go runCreateImageFile(task, s)
	log.Info("[Image_Service] image uploading succeeded")
	return nil
}

func (s *imageService) GenerateUploadTask(ctx context.Context, img dto.ImageCreate) (domain.TaskID, error) {
	if len(img.ImageRepos) <= 0 {
		log.Errorf("[Image_Service] image upload task generation failed")
		return domain.InValidTaskId, fmt.Errorf("no repository pool specified")
	}

	task := createImageTask{
		ctx:          ctx,
		Name:         img.Name,
		FileType:     img.Type,
		TaskId:       s.taskGenerator(),
		DiskFileName: img.DiskFileName,
	}

	pool, err := s.store.RepositoryPool().List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Errorf("[Image_Service] image upload task generation failed - Backend storage failed to list")
		return domain.InValidTaskId, err
	}

	for _, repo := range img.ImageRepos {
		if pool, ok := pool.Items[repo.RepoId]; ok {
			task.Volume = pool.LocationVolume
			task.ReposId = append(task.ReposId, repo.RepoId)
			task.ReposVolume = append(task.ReposVolume, pool.LocationVolume)
			task.ReposName = append(task.ReposName, pool.Name)
			continue
		}

		log.Errorf("[Image_Service] image upload task generation failed - Backend storage does not exist %s", repo.RepoId)
		return domain.InValidTaskId, fmt.Errorf("%s repository pool not exist", repo.RepoId)
	}

	task.CreateAt = time.Now().Unix()
	task.FileSize = img.FileSize
	task.ImageId = uuid.Must(uuid.NewV4()).String()

	s.taskRuningMut.Lock()
	s.uploadTask[task.TaskId] = &task
	s.taskRuningMut.Unlock()

	log.Infof("[Image_Service] image upload task generation succeeded")
	return task.TaskId, nil
}

func (s *imageService) listTaskProgress(ctx context.Context) ([]asyncResult, error) {
	listProgress := make([]asyncResult, 0)

	destroyTasks := make([]domain.TaskID, 0)
	timeout := time.Tick(500 * time.Millisecond)

	s.taskRuningMut.Lock()
	for taskId, result := range s.awaiting {
		select {
		case rlt := <-result:
			listProgress = append(listProgress, rlt)
			s.taskLastResult[taskId] = rlt
		case <-timeout:
		}

		if s.taskLastResult[taskId].Status == domain.StateError ||
			s.taskLastResult[taskId].Status == domain.StateAvailable {
			destroyTasks = append(destroyTasks, taskId)
		}

	}
	s.taskRuningMut.Unlock()

	for _, id := range destroyTasks {
		s.destroyTask(id)
	}

	return listProgress, nil
}

func (s *imageService) destroyTask(taskid domain.TaskID) {
	s.taskRuningMut.Lock()
	delete(s.awaiting, taskid)
	delete(s.uploadTask, taskid)
	delete(s.taskLastResult, taskid)
	s.taskRuningMut.Unlock()

	log.Infof("[Image_Service] image clearing task resources - %d", uint64(taskid))
}

func (s *imageService) taskGenerator() domain.TaskID {
	var id domain.TaskID
	select {
	case id = <-s.nextID:
	case <-s.closed:
	}

	s.taskRuningMut.Lock()
	if ch := s.awaiting[id]; ch != nil {
		log.Panicf("[Image_Service] id has already been used - %d", id)
	}
	rc := make(chan asyncResult, 1)
	s.awaiting[id] = rc
	s.taskRuningMut.Unlock()

	return id
}

func (s *imageService) idGenerator() {
	nextID := domain.TaskID(0x9A011E4C77)
	for {
		nextID = (nextID + 1)
		select {
		case s.nextID <- nextID:
		case <-s.closed:
			return
		}
	}
}

// 创建任务步骤
type step interface {
	Execute(task *createImageTask) error
	RollBack(task *createImageTask)
}

type createImageTask struct {
	ctx context.Context

	Name         string
	DiskFileName string
	Volume       string // 临时存储的卷
	FileType     domain.ImageType
	ReposVolume  []string
	ReposId      []string
	ReposName    []string
	TaskId       domain.TaskID
	CreateAt     int64
	FileSize     uint64

	// 不要初始化
	Status       int
	ImageId      string
	TempFilePath string
	conn         *websocket.Conn
}

const (
	STAGE_UPLOAD              = 1
	STAGE_WRITEBACKENDSTORAGE = 2
	STAGE_COPYTEMPFILE        = 3
)

// 创建文件时，文件的上传步骤
type uploadStep struct {
	*imageService
}

func (u *uploadStep) Execute(task *createImageTask) error {
	var err error
	// 执行文件上传
	if err = u.fileUpload(task); err != nil {
		return err
	}
	// 写入初始配置入存储后端
	if err = u.writeBackendStorageInfo(task); err != nil {
		return err
	}

	return nil
}

func (u *uploadStep) RollBack(task *createImageTask) {
	// 回滚，清楚存储后端的初始配置
	if task.Status >= STAGE_WRITEBACKENDSTORAGE {
		u.store.Images().Delete(task.ctx, task.ImageId, metav1.DeleteOptions{})
	}

	// 回滚，删除临时文件
	if task.Status >= STAGE_UPLOAD {
		os.Remove(task.TempFilePath)
	}
}

// 创建文件时，文件上传完成后，执行的落盘操作
type copyStep struct {
	*imageService
}

func (u *copyStep) Execute(task *createImageTask) error {
	err := u.copyTempFileToVolume(task)
	return err
}

func (u *copyStep) RollBack(task *createImageTask) {
	if task.Status >= STAGE_COPYTEMPFILE {
		for _, repo := range task.ReposVolume {
			os.Remove(path.Join(repo, task.ImageId))
		}

		u.imageService.store.Images().Delete(task.ctx, task.ImageId, metav1.DeleteOptions{})

		tempFile := path.Join(task.Volume, domain.VolumeTempDir, task.DiskFileName)
		if info, err := os.Stat(tempFile); err != nil {
			if info.Name() == task.DiskFileName {
				os.Remove(tempFile)
			}
		}
	}
}

func runPipeLine(t *createImageTask, steps []step) error {
	for i, step := range steps {
		if err := step.Execute(t); err != nil {
			for j := i; j >= 0; j-- {
				steps[j].RollBack(t)
			}
			return err
		}
	}
	return nil
}

func runCreateImageFile(t *createImageTask, img *imageService) (err error) {
	steps := []step{
		&uploadStep{img},
		&copyStep{img},
	}

	img.taskRuningMut.Lock()
	rc := img.awaiting[t.TaskId]
	img.taskRuningMut.Unlock()

	result := asyncResult{
		Name:       t.Name,
		Status:     domain.StateAvailable,
		Progress:   100,
		Type:       t.FileType,
		Id:         t.ImageId,
		RealName:   t.DiskFileName,
		CreateTime: t.CreateAt,
		FileSize:   t.FileSize,
		Taskid:     t.TaskId,
	}

	if err = runPipeLine(t, steps); err != nil {
		result.Status = domain.StateError
		log.Warnf("[Image_Service] image uploading failed - %d", t.TaskId)
	}

	// 写入最终处理结果
	for len(rc) > 0 {
		<-rc
	}
	rc <- result

	// 回收资源
	close(rc)
	return
}

// 上传文件
func (u *uploadStep) fileUpload(task *createImageTask) error {
	var fileName string
	var totalBlocks int
	var receivedBlocks int
	var async asyncResult

	async.Name = task.Name
	async.Status = domain.StateUploading
	async.Type = task.FileType
	async.Id = task.ImageId
	async.RealName = task.DiskFileName
	async.CreateTime = task.CreateAt
	async.FileSize = task.FileSize
	async.Taskid = task.TaskId
	for iname, jid := 0, 0; iname < len(task.ReposName) && jid < len(task.ReposId); {
		async.ImageRepos = append(async.ImageRepos,
			dto.ImageLocate{RepoId: task.ReposId[jid], RepoName: task.ReposName[iname]})

		iname++
		jid++
	}

	u.taskRuningMut.Lock()
	rc := u.awaiting[task.TaskId]
	u.taskRuningMut.Unlock()

	defer func() {
		// 连接关闭
		task.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, "File uploaded successfully!"))
		task.conn.Close()
	}()

	tempDir := path.Join(task.Volume, domain.VolumeTempDir)
	// 创建存储文件的临时目录
	if _, err := os.Stat(tempDir); os.IsNotExist(err) {
		err := os.MkdirAll(tempDir, os.ModePerm)
		if err != nil {
			log.Errorf("[Image_Service] image uploading failed - Error creating upload directory %s", tempDir)
			return err
		}
	}

	file, err := os.CreateTemp(tempDir, "upload.file_*")
	if err != nil {
		log.Errorf("[Image_Service] image uploading failed - Error openning file %s", file)
		return err
	}
	task.TempFilePath = file.Name()
	defer file.Close()

	// 超时检测
	go func() {
		pingInterval := time.Second * 3
		timeout := pingInterval * 2

		task.conn.SetPongHandler(func(appData string) error {
			task.conn.SetReadDeadline(time.Now().Add(timeout))
			return nil
		})

		for {
			ticker := time.NewTicker(pingInterval)
			defer ticker.Stop()

			select {
			case <-ticker.C:
				if err := task.conn.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
					return
				}
			}
		}
	}()

	for {

		// 读取 WebSocket 消息
		mt, msg, err := task.conn.ReadMessage()
		if err != nil {
			log.Errorf("[Image_Service] image uploading failed - Error reading websocket msg '%s'", err.Error())
			return err
		}

		if mt != websocket.TextMessage {
			log.Errorf("[Image_Service] image uploading failed - websocket read message not text")
			continue
		}

		// 解析文件块
		var chunk dto.IamgefileChunk
		if err := json.Unmarshal(msg, &chunk); err != nil {
			log.Errorf("[Image_Service] image uploading failed - Unmarshal error %s '%s'", msg, err.Error())
			return err
		}

		if fileName == "" {
			fileName = chunk.FileName
			totalBlocks = chunk.TotalBlocks
			task.Status = STAGE_UPLOAD

			// file come from nas
			if _, err := os.Stat(fileName); err == nil {
				log.Debugf("[Image_Service] file come from nas")

				srcFile, _ := os.Open(fileName)
				if _, err := io.Copy(file, srcFile); err != nil {
					log.Debugf("[Image_Service] image uploading failed - Error copying file %s to %s", fileName, file.Name())
					srcFile.Close()
					return err
				}
				break
			}
		}

		log.Debugf("[Image_Service] file context: %s", chunk.ChunkData[:100])
		fileContext, err := base64.StdEncoding.DecodeString(chunk.ChunkData)
		if err != nil {
			log.Errorf("[Image_Service] image uploading failed - Error encoding base64 %s", fileName)
			return err
		}
		calculatedHash := u.calculateHash(fileContext)

		// 计算当前块的哈希值并验证
		if calculatedHash != chunk.BlockHash {
			log.Errorf("[Image_Service] image uploading failed - Hash mismatch")
			// // 如果哈希值不匹配，要求客户端重新发送该块
			// var resendChunk dto.IamgefileChunk
			// resendChunk.BlockNumber = chunk.BlockNumber

			// b, _ := json.Marshal(resendChunk)
			// task.conn.WriteMessage(websocket.TextMessage, b)
			return fmt.Errorf("Hash mismatch for block %d, please resend.", chunk.BlockNumber)
		}

		_, err = file.WriteAt(fileContext, chunk.ChunkOffset)
		if err != nil {
			log.Infof("Error saving file: %s", err.Error())
			return err
		}

		// 计算并反馈进度
		receivedBlocks++
		progress := float64(receivedBlocks) / float64(totalBlocks) * 100
		async.Progress = int(progress - 0.5)

		select {
		case rc <- async:
		default:
			for len(rc) > 0 {
				<-rc
			}
		}

		// 如果所有文件块都接收到，保存文件并结束
		if receivedBlocks == totalBlocks {
			log.Infof("file %s,uploaded successfully", task.Name)
			break
		}
	}

	log.Infof("[Image_Service] image uploading succeeded - %s", task.Name)
	return nil
}

// 计算文件块的 SHA-256 哈希值
func (u *uploadStep) calculateHash(data []byte) string {
	hash := sha256.New()
	hash.Write(data)
	return hex.EncodeToString(hash.Sum(nil))
}

func (u *uploadStep) writeBackendStorageInfo(task *createImageTask) error {
	var img domain.Image
	img.Name = task.Name

	err := u.store.Images().Create(task.ctx, task.ImageId, &img, metav1.CreateOptions{})
	if err != nil {
		log.Errorf("[Image_Service] Backend storage failed to craete %s", task.Name)
		return err
	}

	task.Status = STAGE_WRITEBACKENDSTORAGE
	return nil
}

func (u *copyStep) copyTempFileToVolume(task *createImageTask) error {
	var tfSize int64
	var vfSize int64
	var result asyncResult

	u.taskRuningMut.Lock()
	rc := u.awaiting[task.TaskId]
	u.taskRuningMut.Unlock()

	// 复制临时文件到卷目录下
	srcFile, err := os.Open(task.TempFilePath)
	if err != nil {
		log.Errorf("[Image_Service] image uploading failed, Error openning file %s", task.Name)
		return err
	}
	info, err := srcFile.Stat()
	if err != nil {
		tfSize = 0
	} else {
		tfSize = info.Size()
	}
	defer srcFile.Close()

	for _, repo := range task.ReposVolume {
		volumePath := path.Join(repo, domain.ImageDiskVolumePath, task.ImageId)
		if err := os.MkdirAll(volumePath, 0777); err != nil {
			log.Errorf("[Image_Service] image uploading failed, Error making dir %s", volumePath)
			return err
		}

		volumeFile := path.Join(volumePath, task.DiskFileName)
		destFile, err := os.OpenFile(volumeFile, os.O_CREATE|os.O_WRONLY|os.O_EXCL, 0644|info.Mode()&0777)
		if err != nil {
			log.Errorf("[Image_Service] image uploading failed, Error openning file %s", destFile)
			return err
		}

		if _, err := io.Copy(destFile, srcFile); err != nil {
			destFile.Close()
			return err
		}
		srcFile.Seek(0, 0)
		defer destFile.Close()

		info, err := destFile.Stat()
		if err != nil {
			vfSize = 0
		} else {
			vfSize = info.Size()
		}

		// 进度跟踪
		vs := float32(len(task.ReposVolume))
		result.Progress = int(float32(vfSize) / (float32(tfSize) * vs) * 100)
		result.Name = task.Name
		result.Status = domain.StateCreatring

		select {
		case rc <- result:
		default:
		}

		// 写入配置项
		var image domain.Image
		image.Name = task.Name
		image.RealFiles = task.DiskFileName
		image.FileSize = uint64(tfSize)
		image.Repos = task.ReposId
		image.Type = task.FileType
		image.CreatedAt = time.Unix(task.CreateAt, 0)

		file, err := os.Create(path.Join(volumePath, domain.ImageConf))
		if err != nil {
			log.Errorf("[Image_Service] image uploading failed, Error creating file %s", path.Join(volumePath, domain.ImageConf))
			return err
		}
		defer file.Close()

		encoder := json.NewEncoder(file)
		err = encoder.Encode(image)
		if err != nil {
			log.Warnf("[Image_Service] image uploading failed, Error encoding file %s", path.Join(volumePath, domain.ImageConf))
		}

		err = u.imageService.store.Images().Create(task.ctx, task.ImageId, &image, metav1.CreateOptions{})
		if err != nil {
			log.Warnf("[Image_Service] image uploading failed - Backend storage failed to craete %s", task.Name)
			return err
		}
	}

	task.Status = STAGE_COPYTEMPFILE
	os.Remove(task.TempFilePath)
	return nil
}

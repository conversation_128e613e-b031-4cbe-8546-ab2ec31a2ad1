package dto

import (
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/domain/v1/image"
)

type Location struct {
	RepoName string `json:"repo_name"`
	RepoId   string `json:"repo_id"`
}

func LocateToRepos(locate []Location) []string {
	repos := make([]string, 0)
	for _, v := range locate {
		repos = append(repos, v.RepoId)
	}
	return repos
}

type UploadFileChunk struct {
	FileName    string `json:"file_name"`
	FileSize    int64  `json:"file_size"`
	ChunkData   string `json:"chunk_data"`
	ChunkOffset int64  `json:"chunk_offset"`
	BlockNumber int    `json:"block_number"`
	TotalBlocks int    `json:"total_blocks"`
	BlockHash   string `json:"block_hash"`
}

type BaseImage struct {
	Name         string          `json:"image_name"` // 映像名称
	Id           string          `json:"image_id"`
	DiskFileName string          `json:"file_name"` // 映像名称
	Type         image.ImageType `json:"type"`      // 映像的类型
	FileSize     uint64          `json:"file_size"` // 文件的大小
}

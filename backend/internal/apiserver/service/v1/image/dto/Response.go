package dto

import (
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domain_image "gitlab.local/TOS5_APP/terravirtualmachine/internal/domain/v1/image"
)

type Location struct {
	RepoName string `json:"repo_name"`
	RepoId   string `json:"repo_id"`
}

type Response struct {
	BaseImage
	Status       string     `json:"status"`        // 映像状态
	StatusCode   int        `json:"status_code"`   // 映像错误码
	StatusReason string     `json:"status_reason"` // 映像错误理由
	CreateTime   int64      `json:"create_time"`   // 映像创建时间
	ImageRepos   []Location `json:"repos"`         // 映像存储位置
	Progress     int        `json:"progress"`      // 创建进度
	TaskId       uint64     `json:"task_id"`       // 上传任务ID
}

type ResponseList struct {
	metav1.ListMeta `json:",inline"` // 标准列表元数据
	Items           []*Response      `json:"items"` // 网络列表
}

func BuildResponse(imgId string, img domain_image.Image, poolNames []string) (*Response, error) {
	r := &Response{
		Name:         img.Name,
		FileSize:     img.FileSize,
		Type:         img.Type,
		Id:           imgId,
		DiskFileName: img.RealFiles,
		Status:       img.QuerryState(),
		StatusCode:   0,
		StatusReason: "",
		CreateTime:   img.CreatedAt.Unix(),
		ImageRepos:   make([]Location, 0),
		Progress:     0,
		TaskId:       0,
	}

	for idx, repo := range img.Repos {
		locate := Location{
			RepoId:   repo,
			RepoName: poolNames[idx],
		}
		r.ImageRepos = append(r.ImageRepos, locate)
	}

	return r, nil
}

func (r *Response) SetState(state string) {
	r.Status = state
}

func (r *Response) SetProgress(progress int) {
	r.Progress = progress
}

func (r *Response) SetStatusCode(code int) {
	r.StatusCode = code
}

func (r *Response) SetStatusReason(reason string) {
	r.StatusReason = reason
}

func (r *Response) SetTaskId(taskId uint64) {
	r.TaskId = taskId
}

func ToResponse(imgs domain_image.ImageList,
	listPoolNames func([]string) ([]string, error),
) (map[string]Response, error) {
	list := make(map[string]*Response)

	for id, item := range imgs.Items {
		poolNames, _ := listPoolNames(item.Repos)
		response, err := BuildResponse(id, *item, poolNames)
		if err != nil {
			continue
		}
		list[id] = response
	}

	result := make(map[string]Response)
	for k, v := range list {
		result[k] = *v
	}

	return result, nil
}

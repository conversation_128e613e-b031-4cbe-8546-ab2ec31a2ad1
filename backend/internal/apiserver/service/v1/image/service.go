package image_service

import (
	"context"
	"fmt"
	"os"
	"path"
	"sort"
	"sync"
	"time"

	"github.com/gogf/gf/errors/gcode"
	"github.com/gogf/gf/errors/gerror"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/common"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/common/utils"
	domain_image "gitlab.local/TOS5_APP/terravirtualmachine/internal/domain/v1/image"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/aclog"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/image/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/store"
)

var (
	ImageUploadError      = gcode.New(5001, "image upload error", nil)
	ImageDeleteError      = gcode.New(5002, "image delete error", nil)
	ImageDeleteInUseImage = gcode.New(5003, "image delete in use", nil)
	ImageNotFound         = gcode.New(5004, "image not found", nil)
	ImageEditedError      = gcode.New(5005, "image edit error", nil)
)

type ImageService struct {
	store          store.ImageStore
	vguestSrv      VGuestInternelSrv
	repoSrv        RepositoryInternelSrv
	awaiting       map[uint64]chan asyncResult
	uploadTask     map[uint64]*createImageTask
	taskLastResult map[uint64]asyncResult
	taskRuningMut  sync.Mutex
	nextID         chan uint64
	closed         chan struct{}
}

func NewImageService(store store.ImageStore, vguestSrv VGuestInternelSrv, repoSrv RepositoryInternelSrv) *ImageService {
	service := &ImageService{
		store:          store,
		awaiting:       make(map[uint64]chan asyncResult),
		uploadTask:     make(map[uint64]*createImageTask),
		taskLastResult: make(map[uint64]asyncResult),
		nextID:         make(chan uint64),
		closed:         make(chan struct{}),
		vguestSrv:      vguestSrv,
		repoSrv:        repoSrv,
	}

	go service.idGenerator()
	return service
}

func (s *ImageService) Delete(ctx context.Context, imgId string) error {
	image, err := s.store.Get(ctx, imgId, metav1.GetOptions{})
	if err != nil {
		log.Errorf("[Image_Service] image deletion failed - Backend storage failed to get %s", imgId)
		aclog.New("image", ctx).Error(aclog.IMAGE_DELETE_FAILURE, "name", imgId, "msg", err.Error())
		return gerror.WrapCode(ImageDeleteError, gerror.Newf("Backend storage failed to get %s", imgId))
	}

	if err := s.vguestSrv.UmountImageByImage(imgId); err != nil {
		log.Errorf("[Image_Service] image deletion failed - umount image %s", imgId)
		aclog.New("image", ctx).Error(aclog.IMAGE_DELETE_FAILURE, "name", image.Name, "msg", err.Error())
		return gerror.WrapCode(ImageDeleteError, gerror.Newf("umount image %s", imgId))
	}

	if err := s.store.Delete(ctx, imgId, metav1.DeleteOptions{}); err != nil {
		log.Errorf("[Image_Service] image deletion failed - Backend storage failed to delete %s", imgId)
		aclog.New("image", ctx).Error(aclog.IMAGE_DELETE_FAILURE, "name", image.Name, "msg", err.Error())
		return gerror.WrapCode(ImageDeleteError, gerror.Newf("Backend storage failed to delete %s", imgId))
	}

	for _, repoId := range image.Repos {
		location, err := s.repoSrv.LocatePoolPath(repoId)
		if err != nil {
			log.Errorf("[Image_Service] image deletion failed - Backend storage failed to locate %s", repoId)
			continue
		}

		imgFile := image.LocateImageFile(imgId, location)
		imgDir := path.Dir(imgFile)
		os.RemoveAll(imgDir)
	}

	aclog.New("image", ctx).Info(aclog.IMAGE_DELETE_SUCCESS, "name", image.Name)
	return nil
}

func difference(left []string, right []string) (dels []string, adds []string, keeps []string) {
	leftSet := common.InitSetUseList(left...)
	rightSet := common.InitSetUseList(right...)
	leftSet.Difference(rightSet).Do(func(value string) { dels = append(dels, value) })
	rightSet.Difference(leftSet).Do(func(value string) { adds = append(adds, value) })
	leftSet.Intersection(rightSet).Do(func(value string) { keeps = append(keeps, value) })
	return
}

func (s *ImageService) EditImage(ctx context.Context, request dto.EditRequest) error {
	image, err := s.store.Get(ctx, request.Id, metav1.GetOptions{})
	if err != nil {
		log.Errorf("[Image_Service] image editing failed - Backend storage failed to list")
		aclog.New("image", ctx).Error(aclog.IMAGE_EDIT_FAILURE, "name", request.Name, "msg", err.Error())
		return err
	}
	if len(image.Repos) <= 0 {
		log.Errorf("[Image_Service] image editing failed - image file does not exist")
		aclog.New("image", ctx).Error(aclog.IMAGE_EDIT_FAILURE, "name", request.Name, "msg", err.Error())
		return gerror.New("image file does not exist")
	}
	srcVolume, err := image.FindAvailableRepository(s.repoSrv.LocatePoolPath)
	if err != nil {
		log.Errorf("[Image_Service] image editing failed - not available repository")
		aclog.New("image", ctx).Error(aclog.IMAGE_EDIT_FAILURE, "name", request.Name, "msg", err.Error())
		return err
	}

	delImgs, addImgs, _ := difference(image.Repos, request.Repos)

	var (
		srcFiles, destTmpFiles, destTmpDirs, destFinalFiles, delDirs []string
		editingImage                                                 *domain_image.Image
	)
	imageId := request.Id
	editingImage = image.DeepCopy()
	editingImage.Repos = request.Repos
	for _, dest := range addImgs {
		volume, err := s.repoSrv.LocatePoolPath(dest)
		if err != nil {
			log.Warnf("[Image_Service] image editing warned - copy to volume ('%s') failed", dest)
			continue
		}

		tmpDir := editingImage.LocateTmpDir(volume)
		srcFiles = append(srcFiles, []string{
			editingImage.LocateImageConfig(imageId, srcVolume),
			editingImage.LocateImageFile(imageId, srcVolume),
		}...)
		destTmpFiles = append(destTmpFiles, []string{
			editingImage.LocateImageConfig(imageId, tmpDir),
			editingImage.LocateImageFile(imageId, tmpDir),
		}...)
		destFinalFiles = append(destFinalFiles, []string{
			path.Join(editingImage.LocateImageDir(imageId, volume), path.Base(editingImage.LocateImageConfig(imageId, volume))),
			path.Join(editingImage.LocateImageDir(imageId, volume), path.Base(editingImage.LocateImageFile(imageId, volume))),
		}...)

		destTmpDirs = append(destTmpDirs, tmpDir)
	}
	for _, dest := range delImgs {
		volume, err := s.repoSrv.LocatePoolPath(dest)
		if err != nil {
			log.Warnf("[Image_Service] image editing warned - del image failed ('%s')", dest)
			continue
		}

		delDirs = append(delDirs, editingImage.LocateImageDir(imageId, volume))
	}

	var result asyncResult
	editResultChan := s.taskChan()
	result = result.fromImage(imageId, editingImage)
	result.Status = domain_image.ImageStateEditing
	result.Taskid = 0

	editingFailed := func(err error) {
		result.Progress = -1
		result.StatusCode = ImageEditedError.Code()
		result.Status = domain_image.ImageStateError
		result.StatusReason = err.Error()
		for _, tmpDir := range destTmpDirs {
			os.RemoveAll(tmpDir)
		}

		log.Errorf("[Image_Service] image editing failed - %v", err)
		aclog.New("image", ctx).Error(aclog.IMAGE_EDIT_FAILURE, "name", result.Name, "msg", err.Error())
		utils.NonBlockWriteChan(editResultChan, result)
	}

	go utils.CopyFileWithProgress(srcFiles, destTmpFiles, func(progress int, isFinish bool, err error) {
		result.Progress = progress
		result.StatusCode = 0

		if err != nil {
			editingFailed(err)
			return
		}

		if !isFinish {
			utils.NonBlockWriteChan(editResultChan, result)
			return
		}

		result.Status = domain_image.ImageStateAvailable
		if err := s.store.Update(ctx, imageId, editingImage, metav1.UpdateOptions{}); err != nil {
			editingFailed(err)
			return
		}

		for i := 0; i < len(destTmpFiles); i++ {
			os.MkdirAll(path.Dir(destFinalFiles[i]), 0755)
			if err := os.Rename(destTmpFiles[i], destFinalFiles[i]); err != nil {
				log.Warnf("[Image_Service] file renaming failed - %v", err)
			}
		}
		for i := 0; i < len(delImgs); i++ {
			os.RemoveAll(delDirs[i])
		}
		utils.NonBlockWriteChan(editResultChan, result)
		aclog.New("image", ctx).Info(aclog.IMAGE_EDIT_SUCCESS, "name", request.Name, "msg")
	})

	return nil
}

func (s *ImageService) listTaskProgress() ([]asyncResult, error) {
	listProgress := make([]asyncResult, 0)
	destroyTasks := make([]uint64, 0)
	timeout := time.Tick(10 * time.Millisecond)

	s.taskRuningMut.Lock()
	for taskId, result := range s.awaiting {
		select {
		case rlt := <-result:
			listProgress = append(listProgress, rlt)
			s.taskLastResult[taskId] = rlt
		case <-timeout:
			if _, ok := s.taskLastResult[taskId]; !ok {
				continue
			}
			listProgress = append(listProgress, s.taskLastResult[taskId])
		}

		if _, ok := s.taskLastResult[taskId]; ok && (s.taskLastResult[taskId].Status == domain_image.ImageStateError ||
			s.taskLastResult[taskId].Status == domain_image.ImageStateAvailable) {
			destroyTasks = append(destroyTasks, taskId)
		}
	}
	s.taskRuningMut.Unlock()

	for _, id := range destroyTasks {
		s.destroyTask(id)
	}

	return listProgress, nil
}

func (s *ImageService) destroyTask(taskid uint64) {
	s.taskRuningMut.Lock()
	delete(s.awaiting, taskid)
	delete(s.uploadTask, taskid)
	delete(s.taskLastResult, taskid)
	s.taskRuningMut.Unlock()

	log.Infof("[Image_Service] image clearing task resources - %d", uint64(taskid))
}

func (s *ImageService) List(ctx context.Context, opts metav1.ListOptions) (dto.ResponseList, error) {
	images, err := s.store.List(ctx, opts)
	if err != nil {
		log.Errorf("[Image_Service] image listing failed - Backend storage failed to list")
		return nil, err
	}

	responses, _ := dto.ToRespons(images, s.repoSrv.ListPoolNames)
	listProgress, _ := s.listTaskProgress()
	for _, pro := range listProgress {
		response := pro.ToResponse()
		responses[response.Id] = response
	}

	list := &dto.ResponseList{}
	list.Items = make([]*dto.Response, 0, len(responses))
	for _, res := range responses {
		list.Items = append(list.Items, res)
	}
	sort.Slice(list.Items, func(i, j int) bool {
		return list.Items[i].CreateTime < list.Items[j].CreateTime
	})

	list.TotalCount = int64(len(list.Items))
	return list, nil
}

func (s *ImageService) ListVMs(ctx context.Context, imgId string) ([]string, error) {
	vguests, err := s.vguestSrv.ListvguestNamesByImage(imgId)
	if err != nil {
		return nil, gerror.WrapCode(ImageDeleteError, nil)
	}

	var (
		names        []string
		runningNames []string
	)
	for name, state := range vguests {
		names = append(names, name)

		if s.vguestSrv.IsRunning(state) {
			runningNames = append(runningNames, name)
		}
	}

	if len(runningNames) > 0 {
		return runningNames, gerror.WrapCode(ImageDeleteInUseImage, fmt.Errorf("%s", imgId))
	}
	return names, nil
}

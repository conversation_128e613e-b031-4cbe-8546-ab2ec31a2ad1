package image_service

import (
	"context"
	"fmt"
	"io"
	"math/rand"
	"os"
	"path"
	"sort"
	"sync"
	"testing"
	"time"

	"github.com/gogf/gf/errors/gerror"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	domain_image "gitlab.local/TOS5_APP/terravirtualmachine/internal/domain/v1/image"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/image/dto"
)

type mockStore struct {
	mock.Mock
	mu    sync.Mutex
	store map[string]*domain_image.Image
}

func newMockStore() *mockStore {
	return &mockStore{
		store: make(map[string]*domain_image.Image),
	}
}

func (m *mockStore) Create(ctx context.Context, imageID string, image *domain_image.Image, opts metav1.CreateOptions) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.store[imageID] = image.DeepCopy()
	return nil
}

func (m *mockStore) Update(ctx context.Context, imageID string, image *domain_image.Image, opts metav1.UpdateOptions) error {
	if len(m.Mock.ExpectedCalls) != 0 {
		args := m.Called()
		if args.Get(0) != nil {
			return args.Error(0)
		}
		return nil
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	m.store[imageID] = image.DeepCopy()
	return nil
}

func (m *mockStore) List(ctx context.Context, opts metav1.ListOptions) (*domain_image.ImageList, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	list := &domain_image.ImageList{}
	for _, img := range m.store {
		list.Items = append(list.Items, *img)
	}
	return list, nil
}

func (m *mockStore) Delete(ctx context.Context, imageID string, opts metav1.DeleteOptions) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	delete(m.store, imageID)
	return nil
}

func (m *mockStore) Get(ctx context.Context, imageID string, opts metav1.GetOptions) (*domain_image.Image, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if img, ok := m.store[imageID]; ok {
		return img.DeepCopy(), nil
	}
	return nil, fmt.Errorf("image not found")
}

var poolPath = "/tmp/test_image_edit"

func (m *mockStore) LocatePoolPath(repoID string) (string, error) {
	return path.Join(poolPath, repoID), nil
}

func (m *mockStore) ListPoolNames(repoIDs []string) ([]string, error) {
	return []string{"pool0", "pool1", "pool2", "pool3", "pool4", "pool5", "pool6", "pool7", "pool8", "pool9", "pool10"}, nil
}

func createFile(name string, size int64) {
	os.MkdirAll(path.Dir(name), 0666)
	fd, err := os.OpenFile(name, os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		panic(err)
	}
	defer fd.Close()

	_, err = io.CopyN(fd, rand.New(rand.NewSource(time.Now().UnixNano())), size)
	if err != nil {
		panic(err)
	}
}

func deleteFile(name string) {
	os.RemoveAll(name)
}

func isFileExist(name string) bool {
	if _, err := os.Open(name); os.IsNotExist(err) {
		return false
	}
	return true
}

func TestImageEdit(t *testing.T) {
	mockStore := newMockStore()
	service := NewImageService(mockStore, nil, mockStore)

	// Setup test data
	mockStore.Create(context.Background(), "test0", &domain_image.Image{
		ObjectMeta: metav1.ObjectMeta{
			CreatedAt: time.Now(),
		},
		Name:      "image",
		Type:      domain_image.ISO,
		FileSize:  1024 * 1024 * 1024,
		Repos:     []string{"1", "2", "4", "7"},
		RealFiles: "iso",
	}, metav1.CreateOptions{})

	request := dto.EditRequest{
		Id:    "test0",
		Repos: []string{"1", "2", "3", "5"},
	}

	deleteFile(poolPath)

	t.Run("should be correct for difference using set", func(t *testing.T) {
		old := []string{"1", "2", "4", "8", "11", "15"}
		new := []string{"1", "2", "3", "8", "12"}

		del, add, keeps := difference(old, new)

		sort.Strings(del)
		sort.Strings(add)
		sort.Strings(keeps)
		assert.Equal(t, []string{"11", "15", "4"}, del)
		assert.Equal(t, []string{"12", "3"}, add)
		assert.Equal(t, []string{"1", "2", "8"}, keeps)
	})

	createImageFiles := func(image *domain_image.Image, size int64) ([]string, []string, []string) {
		imageConfs := make([]string, 0)
		isos := make([]string, 0)
		fileDirs := make([]string, 0)

		for _, repo := range image.Repos {
			vPath, _ := mockStore.LocatePoolPath(repo)

			imageConfs = append(imageConfs, image.LocateImageConfig("test0", vPath))
			isos = append(isos, path.Join(image.LocateImageFile("test0", vPath)))
			fileDirs = append(fileDirs, image.LocateImageDir("test0", vPath))

			createFile(imageConfs[len(imageConfs)-1], size)
			createFile(isos[len(isos)-1], size)
		}

		return imageConfs, isos, fileDirs
	}

	t.Run("should continue exec nor block when edit large file", func(t *testing.T) {
		resume, _ := mockStore.Get(context.Background(), "test0", metav1.GetOptions{})
		resumeCopy := resume.DeepCopy()
		imageConfs, isos, _ := createImageFiles(resumeCopy, 512*1024*1024)

		defer func() {
			mockStore.Update(context.Background(), "test0", resumeCopy, metav1.UpdateOptions{})
			deleteFile(poolPath)
		}()

		t.Run("should have iso file in dir", func(t *testing.T) {
			for _, conf := range imageConfs {
				assert.True(t, isFileExist(conf))
			}
			for _, iso := range isos {
				assert.True(t, isFileExist(iso))
			}
		})

		t.Run("should modify image state to editing when edit", func(t *testing.T) {
			err := service.EditImage(context.Background(), request)
			assert.NoError(t, err)

			for {
				response, _ := service.List(context.Background(), metav1.ListOptions{})
				time.Sleep(100 * time.Millisecond)

				assert.Equal(t, 1, len(response.Items))
				if response.Items[0].Status != domain_image.StateEditing {
					break
				}
				fmt.Printf("editing progress - %d\n", response.Items[0].Progress)
			}
		})

		t.Run("should be modify for repository pool locations", func(t *testing.T) {
			img, _ := mockStore.Get(context.Background(), "test0", metav1.GetOptions{})
			sort.Strings(img.Repos)
			assert.Equal(t, []string{"1", "2", "3", "5"}, img.Repos)
		})

		dels, adds, _ := difference(resumeCopy.Repos, request.Repos)
		t.Run("should be add new files after edit image", func(t *testing.T) {
			for _, repo := range adds {
				vPath, _ := mockStore.LocatePoolPath(repo)
				cfg := resumeCopy.LocateImageConfig("test0", vPath)
				iso := resumeCopy.LocateImageFile("test0", vPath)
				assert.True(t, isFileExist(cfg))
				assert.True(t, isFileExist(iso))
			}
		})

		t.Run("should be del after edit image", func(t *testing.T) {
			for _, repo := range dels {
				vPath, _ := mockStore.LocatePoolPath(repo)
				assert.False(t, isFileExist(resumeCopy.LocateImageDir("test0", vPath)))
			}
		})
	})

	t.Run("should be error when update etcd data", func(t *testing.T) {
		resume, _ := mockStore.Get(context.Background(), "test0", metav1.GetOptions{})
		defer func() {
			mockStore.Create(context.Background(), "test0", resume, metav1.CreateOptions{})
			deleteFile(poolPath)
		}()

		imageConfs, isos, _ := createImageFiles(resume, 10*1024*1024)

		mockStore.On("Update", mock.Anything).Return(gerror.New("error"))
		err := service.EditImage(context.Background(), request)
		assert.NoError(t, err)

		var response *dto.ResponseList
		for {
			response, _ = service.List(context.Background(), metav1.ListOptions{})
			time.Sleep(100 * time.Millisecond)
			if response.Items[0].Status == domain_image.StateError {
				break
			}
		}

		newImg, _ := mockStore.Get(context.Background(), "test0", metav1.GetOptions{})
		assert.Equal(t, ImageEditedError.Code(), response.Items[0].StatusCode)
		assert.Equal(t, "error", response.Items[0].StatusReason)
		assert.Equal(t, resume.Repos, newImg.Repos)

		for _, conf := range imageConfs {
			assert.True(t, isFileExist(conf))
		}
		for _, iso := range isos {
			assert.True(t, isFileExist(iso))
		}
	})
}

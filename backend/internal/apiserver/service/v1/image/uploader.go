package image_service

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/errors/gerror"
	"github.com/gorilla/websocket"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/log"
	uuid "github.com/satori/go.uuid"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/common/utils"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/domain/v1"
	domain_image "gitlab.local/TOS5_APP/terravirtualmachine/internal/domain/v1/image"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/aclog"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/service/v1/image/dto"
)

const (
	InvalidTaskID = ^uint64(0)
)

type AsyncResult struct {
	Name         string
	RealName     string
	ID           string
	Type         domain_image.ImageType
	FileSize     uint64
	Status       domain_image.ImageState
	StatusCode   int
	StatusReason string
	CreateTime   int64
	ImageRepos   []dto.Location
	TaskID       uint64
	Progress     int
}

func (r AsyncResult) ToResponse() *dto.Response {
	response := &dto.Response{
		Status:       r.Status.ToString(),
		StatusCode:   r.StatusCode,
		StatusReason: r.StatusReason,
		ID:           r.ID,
		Name:         r.Name,
		Progress:     r.Progress,
		FileSize:     r.FileSize,
		CreateTime:   r.CreateTime,
		TaskID:       r.TaskID,
		Type:         r.Type,
		ImageRepos:   r.ImageRepos,
	}
	return response
}

func (r AsyncResult) FromImage(id string, img *domain_image.Image) AsyncResult {
	result := AsyncResult{
		Name:        img.Name,
		RealName:    img.RealFiles,
		ID:          id,
		Type:        img.Type,
		FileSize:    img.FileSize,
		Status:      domain_image.ImageStateAvailable,
		StatusCode:  0,
		StatusReason: "",
		CreateTime:  img.CreatedAt.Unix(),
		TaskID:      InvalidTaskID,
		Progress:    0,
	}

	result.ImageRepos = make([]dto.Location, 0)
	for _, repo := range img.Repos {
		result.ImageRepos = append(result.ImageRepos, dto.Location{RepoID: repo})
	}
	return result
}

func websocketConnect(ctx context.Context) (*websocket.Conn, error) {
	var upgrader = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	if c, ok := ctx.(*gin.Context); ok {
		conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
		if err != nil {
			log.Errorf("[Image_Service] websocket upgrading failed - %s", err.Error())
			return nil, err
		}
		return conn, nil
	}

	return nil, fmt.Errorf("invalid context type")
}

func (s *ImageService) UploadFile(ctx context.Context, taskID uint64) error {
	s.taskRuningMut.Lock()
	task, ok := s.uploadTask[taskID]
	s.taskRuningMut.Unlock()

	if !ok {
		log.Errorf("[Image_Service] image uploading failed - invalid task id %d", taskID)
		aclog.New("image", ctx).Error(aclog.IMAGE_UPLOAD_FAILURE, "name", "name:Unknown")
		return fmt.Errorf("please create a task ID first, then execute the task of uploading files")
	}

	conn, err := websocketConnect(ctx)
	if err != nil {
		return err
	}
	task.conn = conn

	s.taskRuningMut.Lock()
	rc := s.awaiting[taskID]
	s.taskRuningMut.Unlock()

	var async AsyncResult
	async.Name = task.Name
	async.Status = domain_image.ImageStateUploading
	async.Type = task.FileType
	async.ID = task.ImageID
	async.RealName = task.DiskFileName
	async.CreateTime = task.CreateAt
	async.FileSize = task.FileSize
	async.TaskID = task.TaskID
	for iName, jID := 0, 0; iName < len(task.ReposName) && jID < len(task.ReposID); {
		async.ImageRepos = append(async.ImageRepos,
			dto.Location{RepoID: task.ReposID[jID], RepoName: task.ReposName[iName]})
		iName++
		jID++
	}

	rc <- async

	go runCreateImageFile(task, s)
	return nil
}

func (s *ImageService) GenerateUploadTask(ctx context.Context, img dto.CreateRequest) (uint64, error) {
	if len(img.ImageRepos) <= 0 {
		aclog.New("image", ctx).Info(aclog.IMAGE_UPLOAD_FAILURE, "name", img.Name)
		log.Errorf("[Image_Service] image upload task generation failed")
		return InvalidTaskID, fmt.Errorf("no repository pool specified")
	}

	task := &createImageTask{
		ctx:          ctx,
		Name:        img.Name,
		FileType:    img.Type,
		TaskID:      s.taskGenerator(),
		DiskFileName: img.DiskFileName,
	}

	var repoIDs []string
	var locations []string
	for _, repo := range img.ImageRepos {
		repoIDs = append(repoIDs, repo.RepoID)
		location, err := s.repoSrv.LocatePoolPath(repo.RepoID)
		if err != nil {
			aclog.New("image", ctx).Info(aclog.IMAGE_UPLOAD_FAILURE, "name", img.Name)
			log.Errorf("[Image_Service] image upload task generation failed - Backend storage does not exist %s", repo.RepoID)
			return InvalidTaskID, fmt.Errorf("%s repository pool not exist", repo.RepoID)
		}
		locations = append(locations, location)
	}

	names, err := s.repoSrv.ListPoolNames(repoIDs)
	if err != nil {
		aclog.New("image", ctx).Info(aclog.IMAGE_UPLOAD_FAILURE, "name", img.Name)
		log.Errorf("[Image_Service] image upload task generation failed - Backend storage does not exist")
		return InvalidTaskID, fmt.Errorf("repository pool not exist")
	}

	task.Volume = locations[0]
	task.ReposID = append(task.ReposID, repoIDs...)
	task.ReposVolume = append(task.ReposVolume, locations...)
	task.ReposName = append(task.ReposName, names...)

	task.CreateAt = time.Now().Unix()
	task.FileSize = img.FileSize
	task.ImageID = uuid.Must(uuid.NewV4()).String()

	s.taskRuningMut.Lock()
	s.uploadTask[task.TaskID] = task
	s.taskRuningMut.Unlock()

	return task.TaskID, nil
}

func (s *ImageService) taskChan() chan AsyncResult {
	id := s.taskGenerator()
	s.taskRuningMut.Lock()
	ch := s.awaiting[id]
	s.taskRuningMut.Unlock()
	return ch
}

func (s *ImageService) taskGenerator() uint64 {
	var id uint64
	select {
	case id = <-s.nextID:
	case <-s.closed:
		return 0
	}

	s.taskRuningMut.Lock()
	if ch := s.awaiting[id]; ch != nil {
		log.Panicf("[Image_Service] id has already been used - %d", id)
	}
	s.awaiting[id] = make(chan AsyncResult, 1)
	s.taskRuningMut.Unlock()

	return id
}

func (s *ImageService) idGenerator() {
	nextID := uint64(0x9A011E4C77)
	for {
		nextID++
		select {
		case s.nextID <- nextID:
		case <-s.closed:
			return
		}
	}
}

// 创建任务步骤
type Step interface {
	Execute(task *createImageTask) error
	RollBack(task *createImageTask)
}

type createImageTask struct {
	ctx context.Context

	Name        string
	DiskFileName string
	Volume      string // 临时存储的卷
	FileType    domain_image.ImageType
	ReposVolume []string
	ReposID     []string
	ReposName   []string
	TaskID      uint64
	CreateAt    int64
	FileSize    uint64

	// 不要初始化
	Status       int
	ImageID      string
	TempFilePath string
	conn         *websocket.Conn
}

const (
	StageUpload             = 1
	StageWriteBackendStorage = 2
	StageCopyTempFile       = 3
)

// 创建文件时，文件的上传步骤
type uploadStep struct {
	*ImageService
}

func (u *uploadStep) Execute(task *createImageTask) error {
	if err := u.fileUpload(task); err != nil {
		return err
	}
	if err := u.writeBackendStorageInfo(task); err != nil {
		return err
	}
	return nil
}

func (u *uploadStep) RollBack(task *createImageTask) {
	if task.Status >= StageWriteBackendStorage {
		u.store.Delete(task.ctx, task.ImageID, metav1.DeleteOptions{})
	}

	if task.Status >= StageUpload {
		os.Remove(task.TempFilePath)
	}
}

type copyStep struct {
	*ImageService
}

func (u *copyStep) Execute(task *createImageTask) error {
	return u.copyTempFileToVolume(task)
}

func (u *copyStep) RollBack(task *createImageTask) {
	if task.Status >= StageCopyTempFile {
		for _, repo := range task.ReposVolume {
			os.Remove(path.Join(repo, task.ImageID))
		}

		u.ImageService.store.Delete(task.ctx, task.ImageID, metav1.DeleteOptions{})

		tempFile := path.Join(task.Volume, domain.VolumeTempDir, task.DiskFileName)
		if info, err := os.Stat(tempFile); err != nil {
			if info.Name() == task.DiskFileName {
				os.Remove(tempFile)
			}
		}
	}
}

func runPipeLine(t *createImageTask, steps []Step) error {
	for i, step := range steps {
		if err := step.Execute(t); err != nil {
			for j := i; j >= 0; j-- {
				steps[j].RollBack(t)
			}
			return err
		}
	}
	return nil
}

func runCreateImageFile(t *createImageTask, img *ImageService) error {
	steps := []Step{
		&uploadStep{img},
		&copyStep{img},
	}

	img.taskRuningMut.Lock()
	rc := img.awaiting[t.TaskID]
	img.taskRuningMut.Unlock()

	result := AsyncResult{
		Name:       t.Name,
		Status:     domain_image.ImageStateAvailable,
		Progress:   100,
		Type:       t.FileType,
		ID:         t.ImageID,
		RealName:   t.DiskFileName,
		CreateTime: t.CreateAt,
		FileSize:   t.FileSize,
		TaskID:     t.TaskID,
	}

	for iName, jID := 0, 0; iName < len(t.ReposName) && jID < len(t.ReposID); {
		result.ImageRepos = append(result.ImageRepos,
			dto.Location{RepoID: t.ReposID[jID], RepoName: t.ReposName[iName]})
		iName++
		jID++
	}

	userLogState := aclog.IMAGE_UPLOAD_SUCCESS
	if err := runPipeLine(t, steps); err != nil {
		result.Status = domain_image.ImageStateError
		result.StatusCode = ImageUploadError.Code()
		result.StatusReason = err.Error()
		userLogState = aclog.IMAGE_UPLOAD_FAILURE
		log.Warnf("[Image_Service] image uploading failed - %d", t.TaskID)
	}

	for len(rc) > 0 {
		<-rc
	}
	rc <- result
	close(rc)

	aclog.New("image", t.ctx).Info(userLogState, "name", t.Name)
	return nil
}

// 上传文件
func (u *uploadStep) fileUpload(task *createImageTask) error {
	var fileName string
	var totalBlocks int
	var receivedBlocks int
	var async AsyncResult

	async.Name = task.Name
	async.Status = domain_image.ImageStateUploading
	async.Type = task.FileType
	async.ID = task.ImageID
	async.RealName = task.DiskFileName
	async.CreateTime = task.CreateAt
	async.FileSize = task.FileSize
	async.TaskID = task.TaskID
	for iName, jID := 0, iName < len(task.ReposName) && jID < len(task.ReposID); {
		async.ImageRepos = append(async.ImageRepos,
			dto.Location{RepoID: task.ReposID[jID], RepoName: task.ReposName[iName]})
		iName++
		jID++
	}

	u.taskRuningMut.Lock()
	rc := u.awaiting[task.TaskID]
	u.taskRuningMut.Unlock()

	defer func() {
		task.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, "File uploaded successfully!"))
		task.conn.Close()
	}()

	tempDir := path.Join(task.Volume, domain.VolumeTempDir)
	if _, err := os.Stat(tempDir); os.IsNotExist(err) {
		err := os.MkdirAll(tempDir, os.ModePerm)
		if err != nil {
			log.Errorf("[Image_Service] image uploading failed - Error creating upload directory %s", tempDir)
			return err
		}
	}

	file, err := os.CreateTemp(tempDir, "upload.file_*")
	if err != nil {
		log.Errorf("[Image_Service] image uploading failed - Error opening file %s", file)
		return err
	}
	task.TempFilePath = file.Name()
	defer file.Close()

	go func() {
		pingInterval := 3 * time.Second
		timeout := pingInterval * 2

		task.conn.SetPongHandler(func(appData string) error {
			task.conn.SetReadDeadline(time.Now().Add(timeout))
			return nil
		})

		ticker := time.NewTicker(pingInterval)
		defer ticker.Stop()

		for range ticker.C {
			if err := task.conn.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
				return
			}
		}
	}()

	for {
		mt, msg, err := task.conn.ReadMessage()
		if err != nil {
			log.Errorf("[Image_Service] image uploading failed - Error reading websocket msg '%s'", err.Error())
			return err
		}

		if mt != websocket.TextMessage {
			log.Errorf("[Image_Service] image uploading failed - websocket read message not text")
			continue
		}

		var chunk dto.UploadFileChunk
		if err := json.Unmarshal(msg, &chunk); err != nil {
			log.Errorf("[Image_Service] image uploading failed - Unmarshal error '%s' '%s'", msg, err.Error())
			return err
		}

		if fileName == "" {
			fileName = chunk.FileName
			totalBlocks = chunk.TotalBlocks
			task.Status = StageUpload

			if _, err := os.Stat(fileName); err == nil {
				var copyErr error
				log.Debugf("[Image_Service] image file come from TNAS - %d", file.Name())
				utils.CopyFileWithProgress([]string{fileName}, []string{file.Name()}, func(progress int, isFinish bool, err error) {
					copyErr = err
					async.Progress = progress
					utils.NonBlockWriteChan(rc, async)
				})
				return copyErr
			}
		}

		log.Debugf("[Image_Service] file context: %s", chunk.ChunkData[:100])
		fileContext, err := base64.StdEncoding.DecodeString(chunk.ChunkData)
		if err != nil {
			log.Errorf("[Image_Service] image uploading failed - Error encoding base64 %s", fileName)
			return err
		}
		calculatedHash := u.calculateHash(fileContext)

		if calculatedHash != chunk.BlockHash {
			log.Errorf("[Image_Service] image uploading failed - Hash mismatch")
			return fmt.Errorf("Hash mismatch for block %d, please resend.", chunk.BlockNumber)
		}

		_, err = file.WriteAt(fileContext, chunk.ChunkOffset)
		if err != nil {
			log.Infof("[Image_Service] Error saving file: %s", err.Error())
			return err
		}

		receivedBlocks++
		progress := float64(receivedBlocks) / float64(totalBlocks) * 100
		async.Progress = int(progress - 0.5)

		utils.NonBlockWriteChan(rc, async)

		if receivedBlocks == totalBlocks {
			log.Infof("[Image_Service] file %s uploaded successfully", task.Name)
			break
		}

		if _, err := os.Stat(file.Name()); os.IsNotExist(err) {
			return gerror.WrapCode(ImageUploadError, gerror.New("temporary file are lost"))
		}
	}

	return nil
}

func (u *uploadStep) calculateHash(data []byte) string {
	hash := sha256.New()
	hash.Write(data)
	return hex.EncodeToString(hash.Sum(nil))
}

func (u *uploadStep) writeBackendStorageInfo(task *createImageTask) error {
	var img domain_image.Image
	img.Name = task.Name

	err := u.store.Create(task.ctx, task.ImageID, &img, metav1.CreateOptions{})
	if err != nil {
		log.Errorf("[Image_Service] Backend storage failed to create %s", task.Name)
		return err
	}

	task.Status = StageWriteBackendStorage
	return nil
}

func (u *copyStep) copyTempFileToVolume(task *createImageTask) error {
	var tfSize int64
	var vfSize int64
	var result AsyncResult

	u.taskRuningMut.Lock()
	rc := u.awaiting[task.TaskID]
	u.taskRuningMut.Unlock()

	// 复制临时文件到卷目录下
	srcFile, err := os.Open(task.TempFilePath)
	if err != nil {
		log.Errorf("[Image_Service] image uploading failed, Error opening file %s", task.Name)
		return err
	}
	info, err := srcFile.Stat()
	if err != nil {
		tfSize = 0
	} else {
		tfSize = info.Size()
	}
	defer srcFile.Close()

	for _, repo := range task.ReposVolume {
		volumePath := path.Join(repo, domain_image.ImageDiskVolumePath, task.ImageID)
		if err := os.MkdirAll(volumePath, 0777); err != nil {
			log.Errorf("[Image_Service] image uploading failed, Error making dir %s", volumePath)
			return err
		}

		volumeFile := path.Join(volumePath, task.DiskFileName)
		destFile, err := os.OpenFile(volumeFile, os.O_CREATE|os.O_WRONLY|os.O_EXCL, 0644|info.Mode()&0777)
		if err != nil {
			log.Errorf("[Image_Service] image uploading failed, Error opening file %s", destFile)
			return err
		}

		if _, err := io.Copy(destFile, srcFile); err != nil {
			destFile.Close()
			return err
		}
		srcFile.Seek(0, 0)
		defer destFile.Close()

		info, err := destFile.Stat()
		if err != nil {
			vfSize = 0
		} else {
			vfSize = info.Size()
		}

		// 进度跟踪
		vs := float32(len(task.ReposVolume))
		result.Progress = int(float32(vfSize) / (float32(tfSize) * vs) * 100)
		result.Name = task.Name
		result.Status = domain_image.ImageStateCreating

		select {
		case rc <- result:
		default:
		}

		// 写入配置项
		var image domain_image.Image
		image.Name = task.Name
		image.RealFiles = task.DiskFileName
		image.FileSize = uint64(tfSize)
		image.Type = task.FileType
		image.CreatedAt = time.Unix(task.CreateAt, 0)

		file, err := os.Create(path.Join(volumePath, domain_image.ImageConf))
		if err != nil {
			log.Errorf("[Image_Service] image uploading failed, Error creating file %s", path.Join(volumePath, domain_image.ImageConf))
			return err
		}
		defer file.Close()

		encoder := json.NewEncoder(file)
		err = encoder.Encode(image)
		if err != nil {
			log.Warnf("[Image_Service] image uploading failed, Error encoding file %s", path.Join(volumePath, domain_image.ImageConf))
		}

		image.Repos = task.ReposID
		err = u.ImageService.store.Create(task.ctx, task.ImageID, &image, metav1.CreateOptions{})
		if err != nil {
			log.Warnf("[Image_Service] image uploading failed - Backend storage failed to create %s", task.Name)
			return err
		}
	}

	task.Status = StageCopyTempFile
	os.Remove(task.TempFilePath)
	return nil
}
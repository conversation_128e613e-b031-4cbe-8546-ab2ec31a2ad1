package image_service

import (
	"context"
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path"
	"path/filepath"
	"strings"

	"github.com/gogf/gf/errors/gerror"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	domain_image "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/image"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/store"
)

// ImageInternalSrv provides internal image related services
type ImageInternalSrv struct {
	store           store.ImageStore
	repoInternalSrv RepositoryInternalSrv
}

// NewImageInternalSrv creates a new ImageInternalSrv instance
func NewImageInternalSrv(store store.ImageStore, repoInternalSrv RepositoryInternalSrv) *ImageInternalSrv {
	return &ImageInternalSrv{
		store:           store,
		repoInternalSrv: repoInternalSrv,
	}
}

// GetImageFilePath returns the file paths for given image IDs
func (srv *ImageInternalSrv) GetImageFilePath(imageIDs []string) ([]string, error) {
	imgList, err := srv.store.List(context.Background(), metav1.ListOptions{})
	if err != nil {
		return nil, err
	}

	configISOs := make([]string, 0, len(imageIDs))
	for _, iso := range imageIDs {
		// Handle special case for "not used" device
		if strings.EqualFold(iso, domain.DeviceNotUse) {
			configISOs = append(configISOs, "")
			continue
		}

		// Check if path exists directly
		if _, err := os.Stat(iso); err == nil {
			configISOs = append(configISOs, iso)
			continue
		}

		// Lookup in stored images
		found := false
		isoImage, ok := imgList.Items[iso]
		if !ok {
			return nil, gerror.WrapCode(ImageNotFound, fmt.Errorf("backend storage image [%s] not found", iso))
		}

		// Try to locate image in repositories
		for _, repo := range isoImage.Repos {
			volumeLocation, err := srv.repoInternalSrv.LocatePoolPath(repo)
			if err != nil {
				continue
			}

			imgFilePath := isoImage.LocateImageFile(iso, volumeLocation)
			if _, err := os.Stat(imgFilePath); err == nil {
				configISOs = append(configISOs, imgFilePath)
				found = true
				break
			}
		}

		if !found {
			return nil, gerror.WrapCode(ImageNotFound, fmt.Errorf("image file not found"))
		}
	}

	return configISOs, nil
}

// DeleteImageList deletes images from repository and optionally from storage
func (srv *ImageInternalSrv) DeleteImageList(repoID string, locationVolume string, needSaveData bool) error {
	lists, err := srv.store.List(context.Background(), metav1.ListOptions{})
	if err != nil {
		log.Errorf("[Image_Service] image deleting failed - Backend storage failed to list: %v", err)
		return err
	}

	delImages := make([]string, 0)
	// First pass: find images to delete and update repos
	for id, img := range lists.Items {
		repos := make([]string, 0)
		for _, repo := range img.Repos {
			if repo == repoID {
				delImages = append(delImages, id)
				continue
			}
			repos = append(repos, repo)
		}
		img.Repos = repos
	}

	// Second pass: update or delete images
	for id, img := range lists.Items {
		if len(img.Repos) == 0 {
			if err := srv.store.Delete(context.Background(), id, metav1.DeleteOptions{}); err != nil {
				log.Warnf("[Image_Service] image deleting - Backend storage failed to delete (image id %s): %v", id, err)
			}
			continue
		}

		if err := srv.store.Update(context.Background(), id, img, metav1.UpdateOptions{}); err != nil {
			log.Warnf("[Image_Service] image deleting - Backend storage failed to update (image id %s): %v", id, err)
		}
	}

	// If we need to save data, return early
	if needSaveData {
		return nil
	}

	// Delete image directories
	for _, id := range delImages {
		imgDir := path.Join(locationVolume, domain_image.ImageDiskVolumePath, id)
		if err := os.RemoveAll(imgDir); err != nil {
			log.Warnf("[Image_Service] file removing failed: %v", err)
		}
	}
	return nil
}

// RestoreImageList restores images from storage to repository
func (srv *ImageInternalSrv) RestoreImageList(repoID string, needImportData bool) error {
	location, err := srv.repoInternalSrv.LocatePoolPath(repoID)
	if err != nil {
		log.Errorf("[Image_Service] repository pool getting failed - Backend storage failed to get (repository pool id %s): %v", repoID, err)
		return err
	}

	imgDir := path.Join(location, domain_image.ImageDiskVolumePath)

	err = filepath.WalkDir(imgDir, func(filePath string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// Clean up directories if not importing data
		if d.IsDir() && !needImportData && filePath != imgDir {
			return os.RemoveAll(filePath)
		}

		// Process image config files
		if d.Type().IsRegular() && path.Base(filePath) == domain_image.ImageConf {
			imgID := path.Base(path.Dir(filePath))
			existImage, err := srv.store.Get(context.Background(), imgID, metav1.GetOptions{})
			if existImage != nil && err == nil {
				// Image exists, just add repo
				existImage.Repos = append(existImage.Repos, repoID)
				if err := srv.store.Update(context.Background(), imgID, existImage, metav1.UpdateOptions{}); err != nil {
					log.Warnf("[Image_Service] image restoring failed - (image id %s): %v", imgID, err)
				}
				return nil
			}

			// New image, read config and create
			image := domain_image.Image{}
			imgData, err := os.ReadFile(filePath)
			if err != nil {
				log.Warnf("[Image_Service] image restoring failed - (image id %s): %v", imgID, err)
				return nil
			}

			if err := json.Unmarshal(imgData, &image); err != nil {
				log.Warnf("[Image_Service] image restoring failed - (image id %s): %v", imgID, err)
				return nil
			}

			image.Repos = append(image.Repos, repoID)
			if err := srv.store.Create(context.Background(), imgID, &image, metav1.CreateOptions{}); err != nil {
				log.Warnf("[Image_Service] image restoring failed - (image id %s): %v", imgID, err)
			}
		}

		return nil
	})

	return err
}

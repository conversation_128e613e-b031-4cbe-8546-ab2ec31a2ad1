header:
  license:
    spdx-id: Apache-2.0
    copyright-owner: ByteDance Inc.

  paths:
    - '**/*.go'
    - '**/*.s'

  paths-ignore:
    - 'ast/asm.s'                                   # empty file
    - 'decoder/asm.s'                               # empty file
    - 'encoder/asm.s'                               # empty file
    - 'internal/caching/asm.s'                      # empty file
    - 'internal/jit/asm.s'                          # empty file
    - 'internal/native/avx/native_amd64.s'          # auto-generated by asm2asm
    - 'internal/native/avx/native_subr_amd64.go'    # auto-generated by asm2asm
    - 'internal/native/avx2/native_amd64.s'         # auto-generated by asm2asm
    - 'internal/native/avx2/native_subr_amd64.go'   # auto-generated by asm2asm
    - 'internal/resolver/asm.s'                     # empty file
    - 'internal/rt/asm.s'                           # empty file
    - 'internal/loader/asm.s'                       # empty file

  comment: on-failure
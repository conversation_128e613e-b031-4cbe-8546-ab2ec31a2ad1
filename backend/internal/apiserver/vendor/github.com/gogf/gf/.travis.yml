os: linux
arch: arm64-graviton2

language: go

go:
  - "1.14.x"
  - "1.15.x"
  - "1.16.x"

branches:
  only:
  - master
  - develop
  - staging

env:
- TZ=Asia/Shanghai GF_DEBUG=1 GO111MODULE=on

services:
- mysql
- redis-server
- postgresql

addons:
  postgresql: "9.4"
  hosts:
  - local

before_install:
  - mysql -e 'CREATE DATABASE IF NOT EXISTS test;'

install:
- cat /etc/hosts

before_script:
- find . -name "*.go" | xargs gofmt -w
- git diff --name-only --exit-code || exit 1
- echo "UPDATE mysql.user SET authentication_string=PASSWORD('12345678') WHERE user='root';\nFLUSH PRIVILEGES;\n" | mysql -u root
- psql -c 'create database travis_ci_test;' -U postgres

script:
- GOARCH=386 go test -v ./... || exit 1
- GOARCH=amd64 go test -v ./... -race -coverprofile=coverage.txt -covermode=atomic

after_success:
- bash <(curl -s https://codecov.io/bash)




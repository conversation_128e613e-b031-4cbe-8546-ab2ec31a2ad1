// Copyright GoFrame Author(https://goframe.org). All Rights Reserved.
//
// This Source Code Form is subject to the terms of the MIT License.
// If a copy of the MIT was not distributed with this file,
// You can obtain one at https://gitee.com/johng/gp.

// Package gparser provides convenient API for accessing/converting variable and JSON/XML/YAML/TOML.
package gparser

import (
	"github.com/gogf/gf/encoding/gjson"
)

// Parser is actually alias of gjson.Json.
type Parser = gjson.Json

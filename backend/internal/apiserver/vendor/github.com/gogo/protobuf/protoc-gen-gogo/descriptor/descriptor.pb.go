// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: descriptor.proto

package descriptor

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type FieldDescriptorProto_Type int32

const (
	// 0 is reserved for errors.
	// Order is weird for historical reasons.
	FieldDescriptorProto_TYPE_DOUBLE FieldDescriptorProto_Type = 1
	FieldDescriptorProto_TYPE_FLOAT  FieldDescriptorProto_Type = 2
	// Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT64 if
	// negative values are likely.
	FieldDescriptorProto_TYPE_INT64  FieldDescriptorProto_Type = 3
	FieldDescriptorProto_TYPE_UINT64 FieldDescriptorProto_Type = 4
	// Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT32 if
	// negative values are likely.
	FieldDescriptorProto_TYPE_INT32   FieldDescriptorProto_Type = 5
	FieldDescriptorProto_TYPE_FIXED64 FieldDescriptorProto_Type = 6
	FieldDescriptorProto_TYPE_FIXED32 FieldDescriptorProto_Type = 7
	FieldDescriptorProto_TYPE_BOOL    FieldDescriptorProto_Type = 8
	FieldDescriptorProto_TYPE_STRING  FieldDescriptorProto_Type = 9
	// Tag-delimited aggregate.
	// Group type is deprecated and not supported in proto3. However, Proto3
	// implementations should still be able to parse the group wire format and
	// treat group fields as unknown fields.
	FieldDescriptorProto_TYPE_GROUP   FieldDescriptorProto_Type = 10
	FieldDescriptorProto_TYPE_MESSAGE FieldDescriptorProto_Type = 11
	// New in version 2.
	FieldDescriptorProto_TYPE_BYTES    FieldDescriptorProto_Type = 12
	FieldDescriptorProto_TYPE_UINT32   FieldDescriptorProto_Type = 13
	FieldDescriptorProto_TYPE_ENUM     FieldDescriptorProto_Type = 14
	FieldDescriptorProto_TYPE_SFIXED32 FieldDescriptorProto_Type = 15
	FieldDescriptorProto_TYPE_SFIXED64 FieldDescriptorProto_Type = 16
	FieldDescriptorProto_TYPE_SINT32   FieldDescriptorProto_Type = 17
	FieldDescriptorProto_TYPE_SINT64   FieldDescriptorProto_Type = 18
)

var FieldDescriptorProto_Type_name = map[int32]string{
	1:  "TYPE_DOUBLE",
	2:  "TYPE_FLOAT",
	3:  "TYPE_INT64",
	4:  "TYPE_UINT64",
	5:  "TYPE_INT32",
	6:  "TYPE_FIXED64",
	7:  "TYPE_FIXED32",
	8:  "TYPE_BOOL",
	9:  "TYPE_STRING",
	10: "TYPE_GROUP",
	11: "TYPE_MESSAGE",
	12: "TYPE_BYTES",
	13: "TYPE_UINT32",
	14: "TYPE_ENUM",
	15: "TYPE_SFIXED32",
	16: "TYPE_SFIXED64",
	17: "TYPE_SINT32",
	18: "TYPE_SINT64",
}

var FieldDescriptorProto_Type_value = map[string]int32{
	"TYPE_DOUBLE":   1,
	"TYPE_FLOAT":    2,
	"TYPE_INT64":    3,
	"TYPE_UINT64":   4,
	"TYPE_INT32":    5,
	"TYPE_FIXED64":  6,
	"TYPE_FIXED32":  7,
	"TYPE_BOOL":     8,
	"TYPE_STRING":   9,
	"TYPE_GROUP":    10,
	"TYPE_MESSAGE":  11,
	"TYPE_BYTES":    12,
	"TYPE_UINT32":   13,
	"TYPE_ENUM":     14,
	"TYPE_SFIXED32": 15,
	"TYPE_SFIXED64": 16,
	"TYPE_SINT32":   17,
	"TYPE_SINT64":   18,
}

func (x FieldDescriptorProto_Type) Enum() *FieldDescriptorProto_Type {
	p := new(FieldDescriptorProto_Type)
	*p = x
	return p
}

func (x FieldDescriptorProto_Type) String() string {
	return proto.EnumName(FieldDescriptorProto_Type_name, int32(x))
}

func (x *FieldDescriptorProto_Type) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FieldDescriptorProto_Type_value, data, "FieldDescriptorProto_Type")
	if err != nil {
		return err
	}
	*x = FieldDescriptorProto_Type(value)
	return nil
}

func (FieldDescriptorProto_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{4, 0}
}

type FieldDescriptorProto_Label int32

const (
	// 0 is reserved for errors
	FieldDescriptorProto_LABEL_OPTIONAL FieldDescriptorProto_Label = 1
	FieldDescriptorProto_LABEL_REQUIRED FieldDescriptorProto_Label = 2
	FieldDescriptorProto_LABEL_REPEATED FieldDescriptorProto_Label = 3
)

var FieldDescriptorProto_Label_name = map[int32]string{
	1: "LABEL_OPTIONAL",
	2: "LABEL_REQUIRED",
	3: "LABEL_REPEATED",
}

var FieldDescriptorProto_Label_value = map[string]int32{
	"LABEL_OPTIONAL": 1,
	"LABEL_REQUIRED": 2,
	"LABEL_REPEATED": 3,
}

func (x FieldDescriptorProto_Label) Enum() *FieldDescriptorProto_Label {
	p := new(FieldDescriptorProto_Label)
	*p = x
	return p
}

func (x FieldDescriptorProto_Label) String() string {
	return proto.EnumName(FieldDescriptorProto_Label_name, int32(x))
}

func (x *FieldDescriptorProto_Label) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FieldDescriptorProto_Label_value, data, "FieldDescriptorProto_Label")
	if err != nil {
		return err
	}
	*x = FieldDescriptorProto_Label(value)
	return nil
}

func (FieldDescriptorProto_Label) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{4, 1}
}

// Generated classes can be optimized for speed or code size.
type FileOptions_OptimizeMode int32

const (
	FileOptions_SPEED FileOptions_OptimizeMode = 1
	// etc.
	FileOptions_CODE_SIZE    FileOptions_OptimizeMode = 2
	FileOptions_LITE_RUNTIME FileOptions_OptimizeMode = 3
)

var FileOptions_OptimizeMode_name = map[int32]string{
	1: "SPEED",
	2: "CODE_SIZE",
	3: "LITE_RUNTIME",
}

var FileOptions_OptimizeMode_value = map[string]int32{
	"SPEED":        1,
	"CODE_SIZE":    2,
	"LITE_RUNTIME": 3,
}

func (x FileOptions_OptimizeMode) Enum() *FileOptions_OptimizeMode {
	p := new(FileOptions_OptimizeMode)
	*p = x
	return p
}

func (x FileOptions_OptimizeMode) String() string {
	return proto.EnumName(FileOptions_OptimizeMode_name, int32(x))
}

func (x *FileOptions_OptimizeMode) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FileOptions_OptimizeMode_value, data, "FileOptions_OptimizeMode")
	if err != nil {
		return err
	}
	*x = FileOptions_OptimizeMode(value)
	return nil
}

func (FileOptions_OptimizeMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{10, 0}
}

type FieldOptions_CType int32

const (
	// Default mode.
	FieldOptions_STRING       FieldOptions_CType = 0
	FieldOptions_CORD         FieldOptions_CType = 1
	FieldOptions_STRING_PIECE FieldOptions_CType = 2
)

var FieldOptions_CType_name = map[int32]string{
	0: "STRING",
	1: "CORD",
	2: "STRING_PIECE",
}

var FieldOptions_CType_value = map[string]int32{
	"STRING":       0,
	"CORD":         1,
	"STRING_PIECE": 2,
}

func (x FieldOptions_CType) Enum() *FieldOptions_CType {
	p := new(FieldOptions_CType)
	*p = x
	return p
}

func (x FieldOptions_CType) String() string {
	return proto.EnumName(FieldOptions_CType_name, int32(x))
}

func (x *FieldOptions_CType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FieldOptions_CType_value, data, "FieldOptions_CType")
	if err != nil {
		return err
	}
	*x = FieldOptions_CType(value)
	return nil
}

func (FieldOptions_CType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{12, 0}
}

type FieldOptions_JSType int32

const (
	// Use the default type.
	FieldOptions_JS_NORMAL FieldOptions_JSType = 0
	// Use JavaScript strings.
	FieldOptions_JS_STRING FieldOptions_JSType = 1
	// Use JavaScript numbers.
	FieldOptions_JS_NUMBER FieldOptions_JSType = 2
)

var FieldOptions_JSType_name = map[int32]string{
	0: "JS_NORMAL",
	1: "JS_STRING",
	2: "JS_NUMBER",
}

var FieldOptions_JSType_value = map[string]int32{
	"JS_NORMAL": 0,
	"JS_STRING": 1,
	"JS_NUMBER": 2,
}

func (x FieldOptions_JSType) Enum() *FieldOptions_JSType {
	p := new(FieldOptions_JSType)
	*p = x
	return p
}

func (x FieldOptions_JSType) String() string {
	return proto.EnumName(FieldOptions_JSType_name, int32(x))
}

func (x *FieldOptions_JSType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FieldOptions_JSType_value, data, "FieldOptions_JSType")
	if err != nil {
		return err
	}
	*x = FieldOptions_JSType(value)
	return nil
}

func (FieldOptions_JSType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{12, 1}
}

// Is this method side-effect-free (or safe in HTTP parlance), or idempotent,
// or neither? HTTP based RPC implementation may choose GET verb for safe
// methods, and PUT verb for idempotent methods instead of the default POST.
type MethodOptions_IdempotencyLevel int32

const (
	MethodOptions_IDEMPOTENCY_UNKNOWN MethodOptions_IdempotencyLevel = 0
	MethodOptions_NO_SIDE_EFFECTS     MethodOptions_IdempotencyLevel = 1
	MethodOptions_IDEMPOTENT          MethodOptions_IdempotencyLevel = 2
)

var MethodOptions_IdempotencyLevel_name = map[int32]string{
	0: "IDEMPOTENCY_UNKNOWN",
	1: "NO_SIDE_EFFECTS",
	2: "IDEMPOTENT",
}

var MethodOptions_IdempotencyLevel_value = map[string]int32{
	"IDEMPOTENCY_UNKNOWN": 0,
	"NO_SIDE_EFFECTS":     1,
	"IDEMPOTENT":          2,
}

func (x MethodOptions_IdempotencyLevel) Enum() *MethodOptions_IdempotencyLevel {
	p := new(MethodOptions_IdempotencyLevel)
	*p = x
	return p
}

func (x MethodOptions_IdempotencyLevel) String() string {
	return proto.EnumName(MethodOptions_IdempotencyLevel_name, int32(x))
}

func (x *MethodOptions_IdempotencyLevel) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MethodOptions_IdempotencyLevel_value, data, "MethodOptions_IdempotencyLevel")
	if err != nil {
		return err
	}
	*x = MethodOptions_IdempotencyLevel(value)
	return nil
}

func (MethodOptions_IdempotencyLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{17, 0}
}

// The protocol compiler can output a FileDescriptorSet containing the .proto
// files it parses.
type FileDescriptorSet struct {
	File                 []*FileDescriptorProto `protobuf:"bytes,1,rep,name=file" json:"file,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *FileDescriptorSet) Reset()         { *m = FileDescriptorSet{} }
func (m *FileDescriptorSet) String() string { return proto.CompactTextString(m) }
func (*FileDescriptorSet) ProtoMessage()    {}
func (*FileDescriptorSet) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{0}
}
func (m *FileDescriptorSet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FileDescriptorSet.Unmarshal(m, b)
}
func (m *FileDescriptorSet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FileDescriptorSet.Marshal(b, m, deterministic)
}
func (m *FileDescriptorSet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FileDescriptorSet.Merge(m, src)
}
func (m *FileDescriptorSet) XXX_Size() int {
	return xxx_messageInfo_FileDescriptorSet.Size(m)
}
func (m *FileDescriptorSet) XXX_DiscardUnknown() {
	xxx_messageInfo_FileDescriptorSet.DiscardUnknown(m)
}

var xxx_messageInfo_FileDescriptorSet proto.InternalMessageInfo

func (m *FileDescriptorSet) GetFile() []*FileDescriptorProto {
	if m != nil {
		return m.File
	}
	return nil
}

// Describes a complete .proto file.
type FileDescriptorProto struct {
	Name    *string `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	Package *string `protobuf:"bytes,2,opt,name=package" json:"package,omitempty"`
	// Names of files imported by this file.
	Dependency []string `protobuf:"bytes,3,rep,name=dependency" json:"dependency,omitempty"`
	// Indexes of the public imported files in the dependency list above.
	PublicDependency []int32 `protobuf:"varint,10,rep,name=public_dependency,json=publicDependency" json:"public_dependency,omitempty"`
	// Indexes of the weak imported files in the dependency list.
	// For Google-internal migration only. Do not use.
	WeakDependency []int32 `protobuf:"varint,11,rep,name=weak_dependency,json=weakDependency" json:"weak_dependency,omitempty"`
	// All top-level definitions in this file.
	MessageType []*DescriptorProto        `protobuf:"bytes,4,rep,name=message_type,json=messageType" json:"message_type,omitempty"`
	EnumType    []*EnumDescriptorProto    `protobuf:"bytes,5,rep,name=enum_type,json=enumType" json:"enum_type,omitempty"`
	Service     []*ServiceDescriptorProto `protobuf:"bytes,6,rep,name=service" json:"service,omitempty"`
	Extension   []*FieldDescriptorProto   `protobuf:"bytes,7,rep,name=extension" json:"extension,omitempty"`
	Options     *FileOptions              `protobuf:"bytes,8,opt,name=options" json:"options,omitempty"`
	// This field contains optional information about the original source code.
	// You may safely remove this entire field without harming runtime
	// functionality of the descriptors -- the information is needed only by
	// development tools.
	SourceCodeInfo *SourceCodeInfo `protobuf:"bytes,9,opt,name=source_code_info,json=sourceCodeInfo" json:"source_code_info,omitempty"`
	// The syntax of the proto file.
	// The supported values are "proto2" and "proto3".
	Syntax               *string  `protobuf:"bytes,12,opt,name=syntax" json:"syntax,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FileDescriptorProto) Reset()         { *m = FileDescriptorProto{} }
func (m *FileDescriptorProto) String() string { return proto.CompactTextString(m) }
func (*FileDescriptorProto) ProtoMessage()    {}
func (*FileDescriptorProto) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{1}
}
func (m *FileDescriptorProto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FileDescriptorProto.Unmarshal(m, b)
}
func (m *FileDescriptorProto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FileDescriptorProto.Marshal(b, m, deterministic)
}
func (m *FileDescriptorProto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FileDescriptorProto.Merge(m, src)
}
func (m *FileDescriptorProto) XXX_Size() int {
	return xxx_messageInfo_FileDescriptorProto.Size(m)
}
func (m *FileDescriptorProto) XXX_DiscardUnknown() {
	xxx_messageInfo_FileDescriptorProto.DiscardUnknown(m)
}

var xxx_messageInfo_FileDescriptorProto proto.InternalMessageInfo

func (m *FileDescriptorProto) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *FileDescriptorProto) GetPackage() string {
	if m != nil && m.Package != nil {
		return *m.Package
	}
	return ""
}

func (m *FileDescriptorProto) GetDependency() []string {
	if m != nil {
		return m.Dependency
	}
	return nil
}

func (m *FileDescriptorProto) GetPublicDependency() []int32 {
	if m != nil {
		return m.PublicDependency
	}
	return nil
}

func (m *FileDescriptorProto) GetWeakDependency() []int32 {
	if m != nil {
		return m.WeakDependency
	}
	return nil
}

func (m *FileDescriptorProto) GetMessageType() []*DescriptorProto {
	if m != nil {
		return m.MessageType
	}
	return nil
}

func (m *FileDescriptorProto) GetEnumType() []*EnumDescriptorProto {
	if m != nil {
		return m.EnumType
	}
	return nil
}

func (m *FileDescriptorProto) GetService() []*ServiceDescriptorProto {
	if m != nil {
		return m.Service
	}
	return nil
}

func (m *FileDescriptorProto) GetExtension() []*FieldDescriptorProto {
	if m != nil {
		return m.Extension
	}
	return nil
}

func (m *FileDescriptorProto) GetOptions() *FileOptions {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *FileDescriptorProto) GetSourceCodeInfo() *SourceCodeInfo {
	if m != nil {
		return m.SourceCodeInfo
	}
	return nil
}

func (m *FileDescriptorProto) GetSyntax() string {
	if m != nil && m.Syntax != nil {
		return *m.Syntax
	}
	return ""
}

// Describes a message type.
type DescriptorProto struct {
	Name           *string                           `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	Field          []*FieldDescriptorProto           `protobuf:"bytes,2,rep,name=field" json:"field,omitempty"`
	Extension      []*FieldDescriptorProto           `protobuf:"bytes,6,rep,name=extension" json:"extension,omitempty"`
	NestedType     []*DescriptorProto                `protobuf:"bytes,3,rep,name=nested_type,json=nestedType" json:"nested_type,omitempty"`
	EnumType       []*EnumDescriptorProto            `protobuf:"bytes,4,rep,name=enum_type,json=enumType" json:"enum_type,omitempty"`
	ExtensionRange []*DescriptorProto_ExtensionRange `protobuf:"bytes,5,rep,name=extension_range,json=extensionRange" json:"extension_range,omitempty"`
	OneofDecl      []*OneofDescriptorProto           `protobuf:"bytes,8,rep,name=oneof_decl,json=oneofDecl" json:"oneof_decl,omitempty"`
	Options        *MessageOptions                   `protobuf:"bytes,7,opt,name=options" json:"options,omitempty"`
	ReservedRange  []*DescriptorProto_ReservedRange  `protobuf:"bytes,9,rep,name=reserved_range,json=reservedRange" json:"reserved_range,omitempty"`
	// Reserved field names, which may not be used by fields in the same message.
	// A given name may only be reserved once.
	ReservedName         []string `protobuf:"bytes,10,rep,name=reserved_name,json=reservedName" json:"reserved_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DescriptorProto) Reset()         { *m = DescriptorProto{} }
func (m *DescriptorProto) String() string { return proto.CompactTextString(m) }
func (*DescriptorProto) ProtoMessage()    {}
func (*DescriptorProto) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{2}
}
func (m *DescriptorProto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DescriptorProto.Unmarshal(m, b)
}
func (m *DescriptorProto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DescriptorProto.Marshal(b, m, deterministic)
}
func (m *DescriptorProto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescriptorProto.Merge(m, src)
}
func (m *DescriptorProto) XXX_Size() int {
	return xxx_messageInfo_DescriptorProto.Size(m)
}
func (m *DescriptorProto) XXX_DiscardUnknown() {
	xxx_messageInfo_DescriptorProto.DiscardUnknown(m)
}

var xxx_messageInfo_DescriptorProto proto.InternalMessageInfo

func (m *DescriptorProto) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *DescriptorProto) GetField() []*FieldDescriptorProto {
	if m != nil {
		return m.Field
	}
	return nil
}

func (m *DescriptorProto) GetExtension() []*FieldDescriptorProto {
	if m != nil {
		return m.Extension
	}
	return nil
}

func (m *DescriptorProto) GetNestedType() []*DescriptorProto {
	if m != nil {
		return m.NestedType
	}
	return nil
}

func (m *DescriptorProto) GetEnumType() []*EnumDescriptorProto {
	if m != nil {
		return m.EnumType
	}
	return nil
}

func (m *DescriptorProto) GetExtensionRange() []*DescriptorProto_ExtensionRange {
	if m != nil {
		return m.ExtensionRange
	}
	return nil
}

func (m *DescriptorProto) GetOneofDecl() []*OneofDescriptorProto {
	if m != nil {
		return m.OneofDecl
	}
	return nil
}

func (m *DescriptorProto) GetOptions() *MessageOptions {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *DescriptorProto) GetReservedRange() []*DescriptorProto_ReservedRange {
	if m != nil {
		return m.ReservedRange
	}
	return nil
}

func (m *DescriptorProto) GetReservedName() []string {
	if m != nil {
		return m.ReservedName
	}
	return nil
}

type DescriptorProto_ExtensionRange struct {
	Start                *int32                 `protobuf:"varint,1,opt,name=start" json:"start,omitempty"`
	End                  *int32                 `protobuf:"varint,2,opt,name=end" json:"end,omitempty"`
	Options              *ExtensionRangeOptions `protobuf:"bytes,3,opt,name=options" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *DescriptorProto_ExtensionRange) Reset()         { *m = DescriptorProto_ExtensionRange{} }
func (m *DescriptorProto_ExtensionRange) String() string { return proto.CompactTextString(m) }
func (*DescriptorProto_ExtensionRange) ProtoMessage()    {}
func (*DescriptorProto_ExtensionRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{2, 0}
}
func (m *DescriptorProto_ExtensionRange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DescriptorProto_ExtensionRange.Unmarshal(m, b)
}
func (m *DescriptorProto_ExtensionRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DescriptorProto_ExtensionRange.Marshal(b, m, deterministic)
}
func (m *DescriptorProto_ExtensionRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescriptorProto_ExtensionRange.Merge(m, src)
}
func (m *DescriptorProto_ExtensionRange) XXX_Size() int {
	return xxx_messageInfo_DescriptorProto_ExtensionRange.Size(m)
}
func (m *DescriptorProto_ExtensionRange) XXX_DiscardUnknown() {
	xxx_messageInfo_DescriptorProto_ExtensionRange.DiscardUnknown(m)
}

var xxx_messageInfo_DescriptorProto_ExtensionRange proto.InternalMessageInfo

func (m *DescriptorProto_ExtensionRange) GetStart() int32 {
	if m != nil && m.Start != nil {
		return *m.Start
	}
	return 0
}

func (m *DescriptorProto_ExtensionRange) GetEnd() int32 {
	if m != nil && m.End != nil {
		return *m.End
	}
	return 0
}

func (m *DescriptorProto_ExtensionRange) GetOptions() *ExtensionRangeOptions {
	if m != nil {
		return m.Options
	}
	return nil
}

// Range of reserved tag numbers. Reserved tag numbers may not be used by
// fields or extension ranges in the same message. Reserved ranges may
// not overlap.
type DescriptorProto_ReservedRange struct {
	Start                *int32   `protobuf:"varint,1,opt,name=start" json:"start,omitempty"`
	End                  *int32   `protobuf:"varint,2,opt,name=end" json:"end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DescriptorProto_ReservedRange) Reset()         { *m = DescriptorProto_ReservedRange{} }
func (m *DescriptorProto_ReservedRange) String() string { return proto.CompactTextString(m) }
func (*DescriptorProto_ReservedRange) ProtoMessage()    {}
func (*DescriptorProto_ReservedRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{2, 1}
}
func (m *DescriptorProto_ReservedRange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DescriptorProto_ReservedRange.Unmarshal(m, b)
}
func (m *DescriptorProto_ReservedRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DescriptorProto_ReservedRange.Marshal(b, m, deterministic)
}
func (m *DescriptorProto_ReservedRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescriptorProto_ReservedRange.Merge(m, src)
}
func (m *DescriptorProto_ReservedRange) XXX_Size() int {
	return xxx_messageInfo_DescriptorProto_ReservedRange.Size(m)
}
func (m *DescriptorProto_ReservedRange) XXX_DiscardUnknown() {
	xxx_messageInfo_DescriptorProto_ReservedRange.DiscardUnknown(m)
}

var xxx_messageInfo_DescriptorProto_ReservedRange proto.InternalMessageInfo

func (m *DescriptorProto_ReservedRange) GetStart() int32 {
	if m != nil && m.Start != nil {
		return *m.Start
	}
	return 0
}

func (m *DescriptorProto_ReservedRange) GetEnd() int32 {
	if m != nil && m.End != nil {
		return *m.End
	}
	return 0
}

type ExtensionRangeOptions struct {
	// The parser stores options it doesn't recognize here. See above.
	UninterpretedOption          []*UninterpretedOption `protobuf:"bytes,999,rep,name=uninterpreted_option,json=uninterpretedOption" json:"uninterpreted_option,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}               `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *ExtensionRangeOptions) Reset()         { *m = ExtensionRangeOptions{} }
func (m *ExtensionRangeOptions) String() string { return proto.CompactTextString(m) }
func (*ExtensionRangeOptions) ProtoMessage()    {}
func (*ExtensionRangeOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{3}
}

var extRange_ExtensionRangeOptions = []proto.ExtensionRange{
	{Start: 1000, End: 536870911},
}

func (*ExtensionRangeOptions) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_ExtensionRangeOptions
}

func (m *ExtensionRangeOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExtensionRangeOptions.Unmarshal(m, b)
}
func (m *ExtensionRangeOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExtensionRangeOptions.Marshal(b, m, deterministic)
}
func (m *ExtensionRangeOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtensionRangeOptions.Merge(m, src)
}
func (m *ExtensionRangeOptions) XXX_Size() int {
	return xxx_messageInfo_ExtensionRangeOptions.Size(m)
}
func (m *ExtensionRangeOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtensionRangeOptions.DiscardUnknown(m)
}

var xxx_messageInfo_ExtensionRangeOptions proto.InternalMessageInfo

func (m *ExtensionRangeOptions) GetUninterpretedOption() []*UninterpretedOption {
	if m != nil {
		return m.UninterpretedOption
	}
	return nil
}

// Describes a field within a message.
type FieldDescriptorProto struct {
	Name   *string                     `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	Number *int32                      `protobuf:"varint,3,opt,name=number" json:"number,omitempty"`
	Label  *FieldDescriptorProto_Label `protobuf:"varint,4,opt,name=label,enum=google.protobuf.FieldDescriptorProto_Label" json:"label,omitempty"`
	// If type_name is set, this need not be set.  If both this and type_name
	// are set, this must be one of TYPE_ENUM, TYPE_MESSAGE or TYPE_GROUP.
	Type *FieldDescriptorProto_Type `protobuf:"varint,5,opt,name=type,enum=google.protobuf.FieldDescriptorProto_Type" json:"type,omitempty"`
	// For message and enum types, this is the name of the type.  If the name
	// starts with a '.', it is fully-qualified.  Otherwise, C++-like scoping
	// rules are used to find the type (i.e. first the nested types within this
	// message are searched, then within the parent, on up to the root
	// namespace).
	TypeName *string `protobuf:"bytes,6,opt,name=type_name,json=typeName" json:"type_name,omitempty"`
	// For extensions, this is the name of the type being extended.  It is
	// resolved in the same manner as type_name.
	Extendee *string `protobuf:"bytes,2,opt,name=extendee" json:"extendee,omitempty"`
	// For numeric types, contains the original text representation of the value.
	// For booleans, "true" or "false".
	// For strings, contains the default text contents (not escaped in any way).
	// For bytes, contains the C escaped value.  All bytes >= 128 are escaped.
	// TODO(kenton):  Base-64 encode?
	DefaultValue *string `protobuf:"bytes,7,opt,name=default_value,json=defaultValue" json:"default_value,omitempty"`
	// If set, gives the index of a oneof in the containing type's oneof_decl
	// list.  This field is a member of that oneof.
	OneofIndex *int32 `protobuf:"varint,9,opt,name=oneof_index,json=oneofIndex" json:"oneof_index,omitempty"`
	// JSON name of this field. The value is set by protocol compiler. If the
	// user has set a "json_name" option on this field, that option's value
	// will be used. Otherwise, it's deduced from the field's name by converting
	// it to camelCase.
	JsonName             *string       `protobuf:"bytes,10,opt,name=json_name,json=jsonName" json:"json_name,omitempty"`
	Options              *FieldOptions `protobuf:"bytes,8,opt,name=options" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *FieldDescriptorProto) Reset()         { *m = FieldDescriptorProto{} }
func (m *FieldDescriptorProto) String() string { return proto.CompactTextString(m) }
func (*FieldDescriptorProto) ProtoMessage()    {}
func (*FieldDescriptorProto) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{4}
}
func (m *FieldDescriptorProto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FieldDescriptorProto.Unmarshal(m, b)
}
func (m *FieldDescriptorProto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FieldDescriptorProto.Marshal(b, m, deterministic)
}
func (m *FieldDescriptorProto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FieldDescriptorProto.Merge(m, src)
}
func (m *FieldDescriptorProto) XXX_Size() int {
	return xxx_messageInfo_FieldDescriptorProto.Size(m)
}
func (m *FieldDescriptorProto) XXX_DiscardUnknown() {
	xxx_messageInfo_FieldDescriptorProto.DiscardUnknown(m)
}

var xxx_messageInfo_FieldDescriptorProto proto.InternalMessageInfo

func (m *FieldDescriptorProto) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *FieldDescriptorProto) GetNumber() int32 {
	if m != nil && m.Number != nil {
		return *m.Number
	}
	return 0
}

func (m *FieldDescriptorProto) GetLabel() FieldDescriptorProto_Label {
	if m != nil && m.Label != nil {
		return *m.Label
	}
	return FieldDescriptorProto_LABEL_OPTIONAL
}

func (m *FieldDescriptorProto) GetType() FieldDescriptorProto_Type {
	if m != nil && m.Type != nil {
		return *m.Type
	}
	return FieldDescriptorProto_TYPE_DOUBLE
}

func (m *FieldDescriptorProto) GetTypeName() string {
	if m != nil && m.TypeName != nil {
		return *m.TypeName
	}
	return ""
}

func (m *FieldDescriptorProto) GetExtendee() string {
	if m != nil && m.Extendee != nil {
		return *m.Extendee
	}
	return ""
}

func (m *FieldDescriptorProto) GetDefaultValue() string {
	if m != nil && m.DefaultValue != nil {
		return *m.DefaultValue
	}
	return ""
}

func (m *FieldDescriptorProto) GetOneofIndex() int32 {
	if m != nil && m.OneofIndex != nil {
		return *m.OneofIndex
	}
	return 0
}

func (m *FieldDescriptorProto) GetJsonName() string {
	if m != nil && m.JsonName != nil {
		return *m.JsonName
	}
	return ""
}

func (m *FieldDescriptorProto) GetOptions() *FieldOptions {
	if m != nil {
		return m.Options
	}
	return nil
}

// Describes a oneof.
type OneofDescriptorProto struct {
	Name                 *string       `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	Options              *OneofOptions `protobuf:"bytes,2,opt,name=options" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OneofDescriptorProto) Reset()         { *m = OneofDescriptorProto{} }
func (m *OneofDescriptorProto) String() string { return proto.CompactTextString(m) }
func (*OneofDescriptorProto) ProtoMessage()    {}
func (*OneofDescriptorProto) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{5}
}
func (m *OneofDescriptorProto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneofDescriptorProto.Unmarshal(m, b)
}
func (m *OneofDescriptorProto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneofDescriptorProto.Marshal(b, m, deterministic)
}
func (m *OneofDescriptorProto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneofDescriptorProto.Merge(m, src)
}
func (m *OneofDescriptorProto) XXX_Size() int {
	return xxx_messageInfo_OneofDescriptorProto.Size(m)
}
func (m *OneofDescriptorProto) XXX_DiscardUnknown() {
	xxx_messageInfo_OneofDescriptorProto.DiscardUnknown(m)
}

var xxx_messageInfo_OneofDescriptorProto proto.InternalMessageInfo

func (m *OneofDescriptorProto) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *OneofDescriptorProto) GetOptions() *OneofOptions {
	if m != nil {
		return m.Options
	}
	return nil
}

// Describes an enum type.
type EnumDescriptorProto struct {
	Name    *string                     `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	Value   []*EnumValueDescriptorProto `protobuf:"bytes,2,rep,name=value" json:"value,omitempty"`
	Options *EnumOptions                `protobuf:"bytes,3,opt,name=options" json:"options,omitempty"`
	// Range of reserved numeric values. Reserved numeric values may not be used
	// by enum values in the same enum declaration. Reserved ranges may not
	// overlap.
	ReservedRange []*EnumDescriptorProto_EnumReservedRange `protobuf:"bytes,4,rep,name=reserved_range,json=reservedRange" json:"reserved_range,omitempty"`
	// Reserved enum value names, which may not be reused. A given name may only
	// be reserved once.
	ReservedName         []string `protobuf:"bytes,5,rep,name=reserved_name,json=reservedName" json:"reserved_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnumDescriptorProto) Reset()         { *m = EnumDescriptorProto{} }
func (m *EnumDescriptorProto) String() string { return proto.CompactTextString(m) }
func (*EnumDescriptorProto) ProtoMessage()    {}
func (*EnumDescriptorProto) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{6}
}
func (m *EnumDescriptorProto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnumDescriptorProto.Unmarshal(m, b)
}
func (m *EnumDescriptorProto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnumDescriptorProto.Marshal(b, m, deterministic)
}
func (m *EnumDescriptorProto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnumDescriptorProto.Merge(m, src)
}
func (m *EnumDescriptorProto) XXX_Size() int {
	return xxx_messageInfo_EnumDescriptorProto.Size(m)
}
func (m *EnumDescriptorProto) XXX_DiscardUnknown() {
	xxx_messageInfo_EnumDescriptorProto.DiscardUnknown(m)
}

var xxx_messageInfo_EnumDescriptorProto proto.InternalMessageInfo

func (m *EnumDescriptorProto) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *EnumDescriptorProto) GetValue() []*EnumValueDescriptorProto {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *EnumDescriptorProto) GetOptions() *EnumOptions {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *EnumDescriptorProto) GetReservedRange() []*EnumDescriptorProto_EnumReservedRange {
	if m != nil {
		return m.ReservedRange
	}
	return nil
}

func (m *EnumDescriptorProto) GetReservedName() []string {
	if m != nil {
		return m.ReservedName
	}
	return nil
}

// Range of reserved numeric values. Reserved values may not be used by
// entries in the same enum. Reserved ranges may not overlap.
//
// Note that this is distinct from DescriptorProto.ReservedRange in that it
// is inclusive such that it can appropriately represent the entire int32
// domain.
type EnumDescriptorProto_EnumReservedRange struct {
	Start                *int32   `protobuf:"varint,1,opt,name=start" json:"start,omitempty"`
	End                  *int32   `protobuf:"varint,2,opt,name=end" json:"end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EnumDescriptorProto_EnumReservedRange) Reset()         { *m = EnumDescriptorProto_EnumReservedRange{} }
func (m *EnumDescriptorProto_EnumReservedRange) String() string { return proto.CompactTextString(m) }
func (*EnumDescriptorProto_EnumReservedRange) ProtoMessage()    {}
func (*EnumDescriptorProto_EnumReservedRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{6, 0}
}
func (m *EnumDescriptorProto_EnumReservedRange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnumDescriptorProto_EnumReservedRange.Unmarshal(m, b)
}
func (m *EnumDescriptorProto_EnumReservedRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnumDescriptorProto_EnumReservedRange.Marshal(b, m, deterministic)
}
func (m *EnumDescriptorProto_EnumReservedRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnumDescriptorProto_EnumReservedRange.Merge(m, src)
}
func (m *EnumDescriptorProto_EnumReservedRange) XXX_Size() int {
	return xxx_messageInfo_EnumDescriptorProto_EnumReservedRange.Size(m)
}
func (m *EnumDescriptorProto_EnumReservedRange) XXX_DiscardUnknown() {
	xxx_messageInfo_EnumDescriptorProto_EnumReservedRange.DiscardUnknown(m)
}

var xxx_messageInfo_EnumDescriptorProto_EnumReservedRange proto.InternalMessageInfo

func (m *EnumDescriptorProto_EnumReservedRange) GetStart() int32 {
	if m != nil && m.Start != nil {
		return *m.Start
	}
	return 0
}

func (m *EnumDescriptorProto_EnumReservedRange) GetEnd() int32 {
	if m != nil && m.End != nil {
		return *m.End
	}
	return 0
}

// Describes a value within an enum.
type EnumValueDescriptorProto struct {
	Name                 *string           `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	Number               *int32            `protobuf:"varint,2,opt,name=number" json:"number,omitempty"`
	Options              *EnumValueOptions `protobuf:"bytes,3,opt,name=options" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *EnumValueDescriptorProto) Reset()         { *m = EnumValueDescriptorProto{} }
func (m *EnumValueDescriptorProto) String() string { return proto.CompactTextString(m) }
func (*EnumValueDescriptorProto) ProtoMessage()    {}
func (*EnumValueDescriptorProto) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{7}
}
func (m *EnumValueDescriptorProto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnumValueDescriptorProto.Unmarshal(m, b)
}
func (m *EnumValueDescriptorProto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnumValueDescriptorProto.Marshal(b, m, deterministic)
}
func (m *EnumValueDescriptorProto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnumValueDescriptorProto.Merge(m, src)
}
func (m *EnumValueDescriptorProto) XXX_Size() int {
	return xxx_messageInfo_EnumValueDescriptorProto.Size(m)
}
func (m *EnumValueDescriptorProto) XXX_DiscardUnknown() {
	xxx_messageInfo_EnumValueDescriptorProto.DiscardUnknown(m)
}

var xxx_messageInfo_EnumValueDescriptorProto proto.InternalMessageInfo

func (m *EnumValueDescriptorProto) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *EnumValueDescriptorProto) GetNumber() int32 {
	if m != nil && m.Number != nil {
		return *m.Number
	}
	return 0
}

func (m *EnumValueDescriptorProto) GetOptions() *EnumValueOptions {
	if m != nil {
		return m.Options
	}
	return nil
}

// Describes a service.
type ServiceDescriptorProto struct {
	Name                 *string                  `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	Method               []*MethodDescriptorProto `protobuf:"bytes,2,rep,name=method" json:"method,omitempty"`
	Options              *ServiceOptions          `protobuf:"bytes,3,opt,name=options" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ServiceDescriptorProto) Reset()         { *m = ServiceDescriptorProto{} }
func (m *ServiceDescriptorProto) String() string { return proto.CompactTextString(m) }
func (*ServiceDescriptorProto) ProtoMessage()    {}
func (*ServiceDescriptorProto) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{8}
}
func (m *ServiceDescriptorProto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServiceDescriptorProto.Unmarshal(m, b)
}
func (m *ServiceDescriptorProto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServiceDescriptorProto.Marshal(b, m, deterministic)
}
func (m *ServiceDescriptorProto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServiceDescriptorProto.Merge(m, src)
}
func (m *ServiceDescriptorProto) XXX_Size() int {
	return xxx_messageInfo_ServiceDescriptorProto.Size(m)
}
func (m *ServiceDescriptorProto) XXX_DiscardUnknown() {
	xxx_messageInfo_ServiceDescriptorProto.DiscardUnknown(m)
}

var xxx_messageInfo_ServiceDescriptorProto proto.InternalMessageInfo

func (m *ServiceDescriptorProto) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *ServiceDescriptorProto) GetMethod() []*MethodDescriptorProto {
	if m != nil {
		return m.Method
	}
	return nil
}

func (m *ServiceDescriptorProto) GetOptions() *ServiceOptions {
	if m != nil {
		return m.Options
	}
	return nil
}

// Describes a method of a service.
type MethodDescriptorProto struct {
	Name *string `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	// Input and output type names.  These are resolved in the same way as
	// FieldDescriptorProto.type_name, but must refer to a message type.
	InputType  *string        `protobuf:"bytes,2,opt,name=input_type,json=inputType" json:"input_type,omitempty"`
	OutputType *string        `protobuf:"bytes,3,opt,name=output_type,json=outputType" json:"output_type,omitempty"`
	Options    *MethodOptions `protobuf:"bytes,4,opt,name=options" json:"options,omitempty"`
	// Identifies if client streams multiple client messages
	ClientStreaming *bool `protobuf:"varint,5,opt,name=client_streaming,json=clientStreaming,def=0" json:"client_streaming,omitempty"`
	// Identifies if server streams multiple server messages
	ServerStreaming      *bool    `protobuf:"varint,6,opt,name=server_streaming,json=serverStreaming,def=0" json:"server_streaming,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MethodDescriptorProto) Reset()         { *m = MethodDescriptorProto{} }
func (m *MethodDescriptorProto) String() string { return proto.CompactTextString(m) }
func (*MethodDescriptorProto) ProtoMessage()    {}
func (*MethodDescriptorProto) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{9}
}
func (m *MethodDescriptorProto) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MethodDescriptorProto.Unmarshal(m, b)
}
func (m *MethodDescriptorProto) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MethodDescriptorProto.Marshal(b, m, deterministic)
}
func (m *MethodDescriptorProto) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MethodDescriptorProto.Merge(m, src)
}
func (m *MethodDescriptorProto) XXX_Size() int {
	return xxx_messageInfo_MethodDescriptorProto.Size(m)
}
func (m *MethodDescriptorProto) XXX_DiscardUnknown() {
	xxx_messageInfo_MethodDescriptorProto.DiscardUnknown(m)
}

var xxx_messageInfo_MethodDescriptorProto proto.InternalMessageInfo

const Default_MethodDescriptorProto_ClientStreaming bool = false
const Default_MethodDescriptorProto_ServerStreaming bool = false

func (m *MethodDescriptorProto) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *MethodDescriptorProto) GetInputType() string {
	if m != nil && m.InputType != nil {
		return *m.InputType
	}
	return ""
}

func (m *MethodDescriptorProto) GetOutputType() string {
	if m != nil && m.OutputType != nil {
		return *m.OutputType
	}
	return ""
}

func (m *MethodDescriptorProto) GetOptions() *MethodOptions {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *MethodDescriptorProto) GetClientStreaming() bool {
	if m != nil && m.ClientStreaming != nil {
		return *m.ClientStreaming
	}
	return Default_MethodDescriptorProto_ClientStreaming
}

func (m *MethodDescriptorProto) GetServerStreaming() bool {
	if m != nil && m.ServerStreaming != nil {
		return *m.ServerStreaming
	}
	return Default_MethodDescriptorProto_ServerStreaming
}

type FileOptions struct {
	// Sets the Java package where classes generated from this .proto will be
	// placed.  By default, the proto package is used, but this is often
	// inappropriate because proto packages do not normally start with backwards
	// domain names.
	JavaPackage *string `protobuf:"bytes,1,opt,name=java_package,json=javaPackage" json:"java_package,omitempty"`
	// If set, all the classes from the .proto file are wrapped in a single
	// outer class with the given name.  This applies to both Proto1
	// (equivalent to the old "--one_java_file" option) and Proto2 (where
	// a .proto always translates to a single class, but you may want to
	// explicitly choose the class name).
	JavaOuterClassname *string `protobuf:"bytes,8,opt,name=java_outer_classname,json=javaOuterClassname" json:"java_outer_classname,omitempty"`
	// If set true, then the Java code generator will generate a separate .java
	// file for each top-level message, enum, and service defined in the .proto
	// file.  Thus, these types will *not* be nested inside the outer class
	// named by java_outer_classname.  However, the outer class will still be
	// generated to contain the file's getDescriptor() method as well as any
	// top-level extensions defined in the file.
	JavaMultipleFiles *bool `protobuf:"varint,10,opt,name=java_multiple_files,json=javaMultipleFiles,def=0" json:"java_multiple_files,omitempty"`
	// This option does nothing.
	JavaGenerateEqualsAndHash *bool `protobuf:"varint,20,opt,name=java_generate_equals_and_hash,json=javaGenerateEqualsAndHash" json:"java_generate_equals_and_hash,omitempty"` // Deprecated: Do not use.
	// If set true, then the Java2 code generator will generate code that
	// throws an exception whenever an attempt is made to assign a non-UTF-8
	// byte sequence to a string field.
	// Message reflection will do the same.
	// However, an extension field still accepts non-UTF-8 byte sequences.
	// This option has no effect on when used with the lite runtime.
	JavaStringCheckUtf8 *bool                     `protobuf:"varint,27,opt,name=java_string_check_utf8,json=javaStringCheckUtf8,def=0" json:"java_string_check_utf8,omitempty"`
	OptimizeFor         *FileOptions_OptimizeMode `protobuf:"varint,9,opt,name=optimize_for,json=optimizeFor,enum=google.protobuf.FileOptions_OptimizeMode,def=1" json:"optimize_for,omitempty"`
	// Sets the Go package where structs generated from this .proto will be
	// placed. If omitted, the Go package will be derived from the following:
	//   - The basename of the package import path, if provided.
	//   - Otherwise, the package statement in the .proto file, if present.
	//   - Otherwise, the basename of the .proto file, without extension.
	GoPackage *string `protobuf:"bytes,11,opt,name=go_package,json=goPackage" json:"go_package,omitempty"`
	// Should generic services be generated in each language?  "Generic" services
	// are not specific to any particular RPC system.  They are generated by the
	// main code generators in each language (without additional plugins).
	// Generic services were the only kind of service generation supported by
	// early versions of google.protobuf.
	//
	// Generic services are now considered deprecated in favor of using plugins
	// that generate code specific to your particular RPC system.  Therefore,
	// these default to false.  Old code which depends on generic services should
	// explicitly set them to true.
	CcGenericServices   *bool `protobuf:"varint,16,opt,name=cc_generic_services,json=ccGenericServices,def=0" json:"cc_generic_services,omitempty"`
	JavaGenericServices *bool `protobuf:"varint,17,opt,name=java_generic_services,json=javaGenericServices,def=0" json:"java_generic_services,omitempty"`
	PyGenericServices   *bool `protobuf:"varint,18,opt,name=py_generic_services,json=pyGenericServices,def=0" json:"py_generic_services,omitempty"`
	PhpGenericServices  *bool `protobuf:"varint,42,opt,name=php_generic_services,json=phpGenericServices,def=0" json:"php_generic_services,omitempty"`
	// Is this file deprecated?
	// Depending on the target platform, this can emit Deprecated annotations
	// for everything in the file, or it will be completely ignored; in the very
	// least, this is a formalization for deprecating files.
	Deprecated *bool `protobuf:"varint,23,opt,name=deprecated,def=0" json:"deprecated,omitempty"`
	// Enables the use of arenas for the proto messages in this file. This applies
	// only to generated classes for C++.
	CcEnableArenas *bool `protobuf:"varint,31,opt,name=cc_enable_arenas,json=ccEnableArenas,def=0" json:"cc_enable_arenas,omitempty"`
	// Sets the objective c class prefix which is prepended to all objective c
	// generated classes from this .proto. There is no default.
	ObjcClassPrefix *string `protobuf:"bytes,36,opt,name=objc_class_prefix,json=objcClassPrefix" json:"objc_class_prefix,omitempty"`
	// Namespace for generated classes; defaults to the package.
	CsharpNamespace *string `protobuf:"bytes,37,opt,name=csharp_namespace,json=csharpNamespace" json:"csharp_namespace,omitempty"`
	// By default Swift generators will take the proto package and CamelCase it
	// replacing '.' with underscore and use that to prefix the types/symbols
	// defined. When this options is provided, they will use this value instead
	// to prefix the types/symbols defined.
	SwiftPrefix *string `protobuf:"bytes,39,opt,name=swift_prefix,json=swiftPrefix" json:"swift_prefix,omitempty"`
	// Sets the php class prefix which is prepended to all php generated classes
	// from this .proto. Default is empty.
	PhpClassPrefix *string `protobuf:"bytes,40,opt,name=php_class_prefix,json=phpClassPrefix" json:"php_class_prefix,omitempty"`
	// Use this option to change the namespace of php generated classes. Default
	// is empty. When this option is empty, the package name will be used for
	// determining the namespace.
	PhpNamespace *string `protobuf:"bytes,41,opt,name=php_namespace,json=phpNamespace" json:"php_namespace,omitempty"`
	// Use this option to change the namespace of php generated metadata classes.
	// Default is empty. When this option is empty, the proto file name will be
	// used for determining the namespace.
	PhpMetadataNamespace *string `protobuf:"bytes,44,opt,name=php_metadata_namespace,json=phpMetadataNamespace" json:"php_metadata_namespace,omitempty"`
	// Use this option to change the package of ruby generated classes. Default
	// is empty. When this option is not set, the package name will be used for
	// determining the ruby package.
	RubyPackage *string `protobuf:"bytes,45,opt,name=ruby_package,json=rubyPackage" json:"ruby_package,omitempty"`
	// The parser stores options it doesn't recognize here.
	// See the documentation for the "Options" section above.
	UninterpretedOption          []*UninterpretedOption `protobuf:"bytes,999,rep,name=uninterpreted_option,json=uninterpretedOption" json:"uninterpreted_option,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}               `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *FileOptions) Reset()         { *m = FileOptions{} }
func (m *FileOptions) String() string { return proto.CompactTextString(m) }
func (*FileOptions) ProtoMessage()    {}
func (*FileOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{10}
}

var extRange_FileOptions = []proto.ExtensionRange{
	{Start: 1000, End: 536870911},
}

func (*FileOptions) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_FileOptions
}

func (m *FileOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FileOptions.Unmarshal(m, b)
}
func (m *FileOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FileOptions.Marshal(b, m, deterministic)
}
func (m *FileOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FileOptions.Merge(m, src)
}
func (m *FileOptions) XXX_Size() int {
	return xxx_messageInfo_FileOptions.Size(m)
}
func (m *FileOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_FileOptions.DiscardUnknown(m)
}

var xxx_messageInfo_FileOptions proto.InternalMessageInfo

const Default_FileOptions_JavaMultipleFiles bool = false
const Default_FileOptions_JavaStringCheckUtf8 bool = false
const Default_FileOptions_OptimizeFor FileOptions_OptimizeMode = FileOptions_SPEED
const Default_FileOptions_CcGenericServices bool = false
const Default_FileOptions_JavaGenericServices bool = false
const Default_FileOptions_PyGenericServices bool = false
const Default_FileOptions_PhpGenericServices bool = false
const Default_FileOptions_Deprecated bool = false
const Default_FileOptions_CcEnableArenas bool = false

func (m *FileOptions) GetJavaPackage() string {
	if m != nil && m.JavaPackage != nil {
		return *m.JavaPackage
	}
	return ""
}

func (m *FileOptions) GetJavaOuterClassname() string {
	if m != nil && m.JavaOuterClassname != nil {
		return *m.JavaOuterClassname
	}
	return ""
}

func (m *FileOptions) GetJavaMultipleFiles() bool {
	if m != nil && m.JavaMultipleFiles != nil {
		return *m.JavaMultipleFiles
	}
	return Default_FileOptions_JavaMultipleFiles
}

// Deprecated: Do not use.
func (m *FileOptions) GetJavaGenerateEqualsAndHash() bool {
	if m != nil && m.JavaGenerateEqualsAndHash != nil {
		return *m.JavaGenerateEqualsAndHash
	}
	return false
}

func (m *FileOptions) GetJavaStringCheckUtf8() bool {
	if m != nil && m.JavaStringCheckUtf8 != nil {
		return *m.JavaStringCheckUtf8
	}
	return Default_FileOptions_JavaStringCheckUtf8
}

func (m *FileOptions) GetOptimizeFor() FileOptions_OptimizeMode {
	if m != nil && m.OptimizeFor != nil {
		return *m.OptimizeFor
	}
	return Default_FileOptions_OptimizeFor
}

func (m *FileOptions) GetGoPackage() string {
	if m != nil && m.GoPackage != nil {
		return *m.GoPackage
	}
	return ""
}

func (m *FileOptions) GetCcGenericServices() bool {
	if m != nil && m.CcGenericServices != nil {
		return *m.CcGenericServices
	}
	return Default_FileOptions_CcGenericServices
}

func (m *FileOptions) GetJavaGenericServices() bool {
	if m != nil && m.JavaGenericServices != nil {
		return *m.JavaGenericServices
	}
	return Default_FileOptions_JavaGenericServices
}

func (m *FileOptions) GetPyGenericServices() bool {
	if m != nil && m.PyGenericServices != nil {
		return *m.PyGenericServices
	}
	return Default_FileOptions_PyGenericServices
}

func (m *FileOptions) GetPhpGenericServices() bool {
	if m != nil && m.PhpGenericServices != nil {
		return *m.PhpGenericServices
	}
	return Default_FileOptions_PhpGenericServices
}

func (m *FileOptions) GetDeprecated() bool {
	if m != nil && m.Deprecated != nil {
		return *m.Deprecated
	}
	return Default_FileOptions_Deprecated
}

func (m *FileOptions) GetCcEnableArenas() bool {
	if m != nil && m.CcEnableArenas != nil {
		return *m.CcEnableArenas
	}
	return Default_FileOptions_CcEnableArenas
}

func (m *FileOptions) GetObjcClassPrefix() string {
	if m != nil && m.ObjcClassPrefix != nil {
		return *m.ObjcClassPrefix
	}
	return ""
}

func (m *FileOptions) GetCsharpNamespace() string {
	if m != nil && m.CsharpNamespace != nil {
		return *m.CsharpNamespace
	}
	return ""
}

func (m *FileOptions) GetSwiftPrefix() string {
	if m != nil && m.SwiftPrefix != nil {
		return *m.SwiftPrefix
	}
	return ""
}

func (m *FileOptions) GetPhpClassPrefix() string {
	if m != nil && m.PhpClassPrefix != nil {
		return *m.PhpClassPrefix
	}
	return ""
}

func (m *FileOptions) GetPhpNamespace() string {
	if m != nil && m.PhpNamespace != nil {
		return *m.PhpNamespace
	}
	return ""
}

func (m *FileOptions) GetPhpMetadataNamespace() string {
	if m != nil && m.PhpMetadataNamespace != nil {
		return *m.PhpMetadataNamespace
	}
	return ""
}

func (m *FileOptions) GetRubyPackage() string {
	if m != nil && m.RubyPackage != nil {
		return *m.RubyPackage
	}
	return ""
}

func (m *FileOptions) GetUninterpretedOption() []*UninterpretedOption {
	if m != nil {
		return m.UninterpretedOption
	}
	return nil
}

type MessageOptions struct {
	// Set true to use the old proto1 MessageSet wire format for extensions.
	// This is provided for backwards-compatibility with the MessageSet wire
	// format.  You should not use this for any other reason:  It's less
	// efficient, has fewer features, and is more complicated.
	//
	// The message must be defined exactly as follows:
	//   message Foo {
	//     option message_set_wire_format = true;
	//     extensions 4 to max;
	//   }
	// Note that the message cannot have any defined fields; MessageSets only
	// have extensions.
	//
	// All extensions of your type must be singular messages; e.g. they cannot
	// be int32s, enums, or repeated messages.
	//
	// Because this is an option, the above two restrictions are not enforced by
	// the protocol compiler.
	MessageSetWireFormat *bool `protobuf:"varint,1,opt,name=message_set_wire_format,json=messageSetWireFormat,def=0" json:"message_set_wire_format,omitempty"`
	// Disables the generation of the standard "descriptor()" accessor, which can
	// conflict with a field of the same name.  This is meant to make migration
	// from proto1 easier; new code should avoid fields named "descriptor".
	NoStandardDescriptorAccessor *bool `protobuf:"varint,2,opt,name=no_standard_descriptor_accessor,json=noStandardDescriptorAccessor,def=0" json:"no_standard_descriptor_accessor,omitempty"`
	// Is this message deprecated?
	// Depending on the target platform, this can emit Deprecated annotations
	// for the message, or it will be completely ignored; in the very least,
	// this is a formalization for deprecating messages.
	Deprecated *bool `protobuf:"varint,3,opt,name=deprecated,def=0" json:"deprecated,omitempty"`
	// Whether the message is an automatically generated map entry type for the
	// maps field.
	//
	// For maps fields:
	//     map<KeyType, ValueType> map_field = 1;
	// The parsed descriptor looks like:
	//     message MapFieldEntry {
	//         option map_entry = true;
	//         optional KeyType key = 1;
	//         optional ValueType value = 2;
	//     }
	//     repeated MapFieldEntry map_field = 1;
	//
	// Implementations may choose not to generate the map_entry=true message, but
	// use a native map in the target language to hold the keys and values.
	// The reflection APIs in such implementations still need to work as
	// if the field is a repeated message field.
	//
	// NOTE: Do not set the option in .proto files. Always use the maps syntax
	// instead. The option should only be implicitly set by the proto compiler
	// parser.
	MapEntry *bool `protobuf:"varint,7,opt,name=map_entry,json=mapEntry" json:"map_entry,omitempty"`
	// The parser stores options it doesn't recognize here. See above.
	UninterpretedOption          []*UninterpretedOption `protobuf:"bytes,999,rep,name=uninterpreted_option,json=uninterpretedOption" json:"uninterpreted_option,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}               `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *MessageOptions) Reset()         { *m = MessageOptions{} }
func (m *MessageOptions) String() string { return proto.CompactTextString(m) }
func (*MessageOptions) ProtoMessage()    {}
func (*MessageOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{11}
}

var extRange_MessageOptions = []proto.ExtensionRange{
	{Start: 1000, End: 536870911},
}

func (*MessageOptions) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_MessageOptions
}

func (m *MessageOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MessageOptions.Unmarshal(m, b)
}
func (m *MessageOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MessageOptions.Marshal(b, m, deterministic)
}
func (m *MessageOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MessageOptions.Merge(m, src)
}
func (m *MessageOptions) XXX_Size() int {
	return xxx_messageInfo_MessageOptions.Size(m)
}
func (m *MessageOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_MessageOptions.DiscardUnknown(m)
}

var xxx_messageInfo_MessageOptions proto.InternalMessageInfo

const Default_MessageOptions_MessageSetWireFormat bool = false
const Default_MessageOptions_NoStandardDescriptorAccessor bool = false
const Default_MessageOptions_Deprecated bool = false

func (m *MessageOptions) GetMessageSetWireFormat() bool {
	if m != nil && m.MessageSetWireFormat != nil {
		return *m.MessageSetWireFormat
	}
	return Default_MessageOptions_MessageSetWireFormat
}

func (m *MessageOptions) GetNoStandardDescriptorAccessor() bool {
	if m != nil && m.NoStandardDescriptorAccessor != nil {
		return *m.NoStandardDescriptorAccessor
	}
	return Default_MessageOptions_NoStandardDescriptorAccessor
}

func (m *MessageOptions) GetDeprecated() bool {
	if m != nil && m.Deprecated != nil {
		return *m.Deprecated
	}
	return Default_MessageOptions_Deprecated
}

func (m *MessageOptions) GetMapEntry() bool {
	if m != nil && m.MapEntry != nil {
		return *m.MapEntry
	}
	return false
}

func (m *MessageOptions) GetUninterpretedOption() []*UninterpretedOption {
	if m != nil {
		return m.UninterpretedOption
	}
	return nil
}

type FieldOptions struct {
	// The ctype option instructs the C++ code generator to use a different
	// representation of the field than it normally would.  See the specific
	// options below.  This option is not yet implemented in the open source
	// release -- sorry, we'll try to include it in a future version!
	Ctype *FieldOptions_CType `protobuf:"varint,1,opt,name=ctype,enum=google.protobuf.FieldOptions_CType,def=0" json:"ctype,omitempty"`
	// The packed option can be enabled for repeated primitive fields to enable
	// a more efficient representation on the wire. Rather than repeatedly
	// writing the tag and type for each element, the entire array is encoded as
	// a single length-delimited blob. In proto3, only explicit setting it to
	// false will avoid using packed encoding.
	Packed *bool `protobuf:"varint,2,opt,name=packed" json:"packed,omitempty"`
	// The jstype option determines the JavaScript type used for values of the
	// field.  The option is permitted only for 64 bit integral and fixed types
	// (int64, uint64, sint64, fixed64, sfixed64).  A field with jstype JS_STRING
	// is represented as JavaScript string, which avoids loss of precision that
	// can happen when a large value is converted to a floating point JavaScript.
	// Specifying JS_NUMBER for the jstype causes the generated JavaScript code to
	// use the JavaScript "number" type.  The behavior of the default option
	// JS_NORMAL is implementation dependent.
	//
	// This option is an enum to permit additional types to be added, e.g.
	// goog.math.Integer.
	Jstype *FieldOptions_JSType `protobuf:"varint,6,opt,name=jstype,enum=google.protobuf.FieldOptions_JSType,def=0" json:"jstype,omitempty"`
	// Should this field be parsed lazily?  Lazy applies only to message-type
	// fields.  It means that when the outer message is initially parsed, the
	// inner message's contents will not be parsed but instead stored in encoded
	// form.  The inner message will actually be parsed when it is first accessed.
	//
	// This is only a hint.  Implementations are free to choose whether to use
	// eager or lazy parsing regardless of the value of this option.  However,
	// setting this option true suggests that the protocol author believes that
	// using lazy parsing on this field is worth the additional bookkeeping
	// overhead typically needed to implement it.
	//
	// This option does not affect the public interface of any generated code;
	// all method signatures remain the same.  Furthermore, thread-safety of the
	// interface is not affected by this option; const methods remain safe to
	// call from multiple threads concurrently, while non-const methods continue
	// to require exclusive access.
	//
	//
	// Note that implementations may choose not to check required fields within
	// a lazy sub-message.  That is, calling IsInitialized() on the outer message
	// may return true even if the inner message has missing required fields.
	// This is necessary because otherwise the inner message would have to be
	// parsed in order to perform the check, defeating the purpose of lazy
	// parsing.  An implementation which chooses not to check required fields
	// must be consistent about it.  That is, for any particular sub-message, the
	// implementation must either *always* check its required fields, or *never*
	// check its required fields, regardless of whether or not the message has
	// been parsed.
	Lazy *bool `protobuf:"varint,5,opt,name=lazy,def=0" json:"lazy,omitempty"`
	// Is this field deprecated?
	// Depending on the target platform, this can emit Deprecated annotations
	// for accessors, or it will be completely ignored; in the very least, this
	// is a formalization for deprecating fields.
	Deprecated *bool `protobuf:"varint,3,opt,name=deprecated,def=0" json:"deprecated,omitempty"`
	// For Google-internal migration only. Do not use.
	Weak *bool `protobuf:"varint,10,opt,name=weak,def=0" json:"weak,omitempty"`
	// The parser stores options it doesn't recognize here. See above.
	UninterpretedOption          []*UninterpretedOption `protobuf:"bytes,999,rep,name=uninterpreted_option,json=uninterpretedOption" json:"uninterpreted_option,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}               `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *FieldOptions) Reset()         { *m = FieldOptions{} }
func (m *FieldOptions) String() string { return proto.CompactTextString(m) }
func (*FieldOptions) ProtoMessage()    {}
func (*FieldOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{12}
}

var extRange_FieldOptions = []proto.ExtensionRange{
	{Start: 1000, End: 536870911},
}

func (*FieldOptions) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_FieldOptions
}

func (m *FieldOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FieldOptions.Unmarshal(m, b)
}
func (m *FieldOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FieldOptions.Marshal(b, m, deterministic)
}
func (m *FieldOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FieldOptions.Merge(m, src)
}
func (m *FieldOptions) XXX_Size() int {
	return xxx_messageInfo_FieldOptions.Size(m)
}
func (m *FieldOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_FieldOptions.DiscardUnknown(m)
}

var xxx_messageInfo_FieldOptions proto.InternalMessageInfo

const Default_FieldOptions_Ctype FieldOptions_CType = FieldOptions_STRING
const Default_FieldOptions_Jstype FieldOptions_JSType = FieldOptions_JS_NORMAL
const Default_FieldOptions_Lazy bool = false
const Default_FieldOptions_Deprecated bool = false
const Default_FieldOptions_Weak bool = false

func (m *FieldOptions) GetCtype() FieldOptions_CType {
	if m != nil && m.Ctype != nil {
		return *m.Ctype
	}
	return Default_FieldOptions_Ctype
}

func (m *FieldOptions) GetPacked() bool {
	if m != nil && m.Packed != nil {
		return *m.Packed
	}
	return false
}

func (m *FieldOptions) GetJstype() FieldOptions_JSType {
	if m != nil && m.Jstype != nil {
		return *m.Jstype
	}
	return Default_FieldOptions_Jstype
}

func (m *FieldOptions) GetLazy() bool {
	if m != nil && m.Lazy != nil {
		return *m.Lazy
	}
	return Default_FieldOptions_Lazy
}

func (m *FieldOptions) GetDeprecated() bool {
	if m != nil && m.Deprecated != nil {
		return *m.Deprecated
	}
	return Default_FieldOptions_Deprecated
}

func (m *FieldOptions) GetWeak() bool {
	if m != nil && m.Weak != nil {
		return *m.Weak
	}
	return Default_FieldOptions_Weak
}

func (m *FieldOptions) GetUninterpretedOption() []*UninterpretedOption {
	if m != nil {
		return m.UninterpretedOption
	}
	return nil
}

type OneofOptions struct {
	// The parser stores options it doesn't recognize here. See above.
	UninterpretedOption          []*UninterpretedOption `protobuf:"bytes,999,rep,name=uninterpreted_option,json=uninterpretedOption" json:"uninterpreted_option,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}               `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *OneofOptions) Reset()         { *m = OneofOptions{} }
func (m *OneofOptions) String() string { return proto.CompactTextString(m) }
func (*OneofOptions) ProtoMessage()    {}
func (*OneofOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{13}
}

var extRange_OneofOptions = []proto.ExtensionRange{
	{Start: 1000, End: 536870911},
}

func (*OneofOptions) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_OneofOptions
}

func (m *OneofOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneofOptions.Unmarshal(m, b)
}
func (m *OneofOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneofOptions.Marshal(b, m, deterministic)
}
func (m *OneofOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneofOptions.Merge(m, src)
}
func (m *OneofOptions) XXX_Size() int {
	return xxx_messageInfo_OneofOptions.Size(m)
}
func (m *OneofOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_OneofOptions.DiscardUnknown(m)
}

var xxx_messageInfo_OneofOptions proto.InternalMessageInfo

func (m *OneofOptions) GetUninterpretedOption() []*UninterpretedOption {
	if m != nil {
		return m.UninterpretedOption
	}
	return nil
}

type EnumOptions struct {
	// Set this option to true to allow mapping different tag names to the same
	// value.
	AllowAlias *bool `protobuf:"varint,2,opt,name=allow_alias,json=allowAlias" json:"allow_alias,omitempty"`
	// Is this enum deprecated?
	// Depending on the target platform, this can emit Deprecated annotations
	// for the enum, or it will be completely ignored; in the very least, this
	// is a formalization for deprecating enums.
	Deprecated *bool `protobuf:"varint,3,opt,name=deprecated,def=0" json:"deprecated,omitempty"`
	// The parser stores options it doesn't recognize here. See above.
	UninterpretedOption          []*UninterpretedOption `protobuf:"bytes,999,rep,name=uninterpreted_option,json=uninterpretedOption" json:"uninterpreted_option,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}               `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *EnumOptions) Reset()         { *m = EnumOptions{} }
func (m *EnumOptions) String() string { return proto.CompactTextString(m) }
func (*EnumOptions) ProtoMessage()    {}
func (*EnumOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{14}
}

var extRange_EnumOptions = []proto.ExtensionRange{
	{Start: 1000, End: 536870911},
}

func (*EnumOptions) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_EnumOptions
}

func (m *EnumOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnumOptions.Unmarshal(m, b)
}
func (m *EnumOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnumOptions.Marshal(b, m, deterministic)
}
func (m *EnumOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnumOptions.Merge(m, src)
}
func (m *EnumOptions) XXX_Size() int {
	return xxx_messageInfo_EnumOptions.Size(m)
}
func (m *EnumOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_EnumOptions.DiscardUnknown(m)
}

var xxx_messageInfo_EnumOptions proto.InternalMessageInfo

const Default_EnumOptions_Deprecated bool = false

func (m *EnumOptions) GetAllowAlias() bool {
	if m != nil && m.AllowAlias != nil {
		return *m.AllowAlias
	}
	return false
}

func (m *EnumOptions) GetDeprecated() bool {
	if m != nil && m.Deprecated != nil {
		return *m.Deprecated
	}
	return Default_EnumOptions_Deprecated
}

func (m *EnumOptions) GetUninterpretedOption() []*UninterpretedOption {
	if m != nil {
		return m.UninterpretedOption
	}
	return nil
}

type EnumValueOptions struct {
	// Is this enum value deprecated?
	// Depending on the target platform, this can emit Deprecated annotations
	// for the enum value, or it will be completely ignored; in the very least,
	// this is a formalization for deprecating enum values.
	Deprecated *bool `protobuf:"varint,1,opt,name=deprecated,def=0" json:"deprecated,omitempty"`
	// The parser stores options it doesn't recognize here. See above.
	UninterpretedOption          []*UninterpretedOption `protobuf:"bytes,999,rep,name=uninterpreted_option,json=uninterpretedOption" json:"uninterpreted_option,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}               `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *EnumValueOptions) Reset()         { *m = EnumValueOptions{} }
func (m *EnumValueOptions) String() string { return proto.CompactTextString(m) }
func (*EnumValueOptions) ProtoMessage()    {}
func (*EnumValueOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{15}
}

var extRange_EnumValueOptions = []proto.ExtensionRange{
	{Start: 1000, End: 536870911},
}

func (*EnumValueOptions) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_EnumValueOptions
}

func (m *EnumValueOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnumValueOptions.Unmarshal(m, b)
}
func (m *EnumValueOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnumValueOptions.Marshal(b, m, deterministic)
}
func (m *EnumValueOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnumValueOptions.Merge(m, src)
}
func (m *EnumValueOptions) XXX_Size() int {
	return xxx_messageInfo_EnumValueOptions.Size(m)
}
func (m *EnumValueOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_EnumValueOptions.DiscardUnknown(m)
}

var xxx_messageInfo_EnumValueOptions proto.InternalMessageInfo

const Default_EnumValueOptions_Deprecated bool = false

func (m *EnumValueOptions) GetDeprecated() bool {
	if m != nil && m.Deprecated != nil {
		return *m.Deprecated
	}
	return Default_EnumValueOptions_Deprecated
}

func (m *EnumValueOptions) GetUninterpretedOption() []*UninterpretedOption {
	if m != nil {
		return m.UninterpretedOption
	}
	return nil
}

type ServiceOptions struct {
	// Is this service deprecated?
	// Depending on the target platform, this can emit Deprecated annotations
	// for the service, or it will be completely ignored; in the very least,
	// this is a formalization for deprecating services.
	Deprecated *bool `protobuf:"varint,33,opt,name=deprecated,def=0" json:"deprecated,omitempty"`
	// The parser stores options it doesn't recognize here. See above.
	UninterpretedOption          []*UninterpretedOption `protobuf:"bytes,999,rep,name=uninterpreted_option,json=uninterpretedOption" json:"uninterpreted_option,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}               `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *ServiceOptions) Reset()         { *m = ServiceOptions{} }
func (m *ServiceOptions) String() string { return proto.CompactTextString(m) }
func (*ServiceOptions) ProtoMessage()    {}
func (*ServiceOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{16}
}

var extRange_ServiceOptions = []proto.ExtensionRange{
	{Start: 1000, End: 536870911},
}

func (*ServiceOptions) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_ServiceOptions
}

func (m *ServiceOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServiceOptions.Unmarshal(m, b)
}
func (m *ServiceOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServiceOptions.Marshal(b, m, deterministic)
}
func (m *ServiceOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServiceOptions.Merge(m, src)
}
func (m *ServiceOptions) XXX_Size() int {
	return xxx_messageInfo_ServiceOptions.Size(m)
}
func (m *ServiceOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_ServiceOptions.DiscardUnknown(m)
}

var xxx_messageInfo_ServiceOptions proto.InternalMessageInfo

const Default_ServiceOptions_Deprecated bool = false

func (m *ServiceOptions) GetDeprecated() bool {
	if m != nil && m.Deprecated != nil {
		return *m.Deprecated
	}
	return Default_ServiceOptions_Deprecated
}

func (m *ServiceOptions) GetUninterpretedOption() []*UninterpretedOption {
	if m != nil {
		return m.UninterpretedOption
	}
	return nil
}

type MethodOptions struct {
	// Is this method deprecated?
	// Depending on the target platform, this can emit Deprecated annotations
	// for the method, or it will be completely ignored; in the very least,
	// this is a formalization for deprecating methods.
	Deprecated       *bool                           `protobuf:"varint,33,opt,name=deprecated,def=0" json:"deprecated,omitempty"`
	IdempotencyLevel *MethodOptions_IdempotencyLevel `protobuf:"varint,34,opt,name=idempotency_level,json=idempotencyLevel,enum=google.protobuf.MethodOptions_IdempotencyLevel,def=0" json:"idempotency_level,omitempty"`
	// The parser stores options it doesn't recognize here. See above.
	UninterpretedOption          []*UninterpretedOption `protobuf:"bytes,999,rep,name=uninterpreted_option,json=uninterpretedOption" json:"uninterpreted_option,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}               `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *MethodOptions) Reset()         { *m = MethodOptions{} }
func (m *MethodOptions) String() string { return proto.CompactTextString(m) }
func (*MethodOptions) ProtoMessage()    {}
func (*MethodOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{17}
}

var extRange_MethodOptions = []proto.ExtensionRange{
	{Start: 1000, End: 536870911},
}

func (*MethodOptions) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_MethodOptions
}

func (m *MethodOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MethodOptions.Unmarshal(m, b)
}
func (m *MethodOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MethodOptions.Marshal(b, m, deterministic)
}
func (m *MethodOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MethodOptions.Merge(m, src)
}
func (m *MethodOptions) XXX_Size() int {
	return xxx_messageInfo_MethodOptions.Size(m)
}
func (m *MethodOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_MethodOptions.DiscardUnknown(m)
}

var xxx_messageInfo_MethodOptions proto.InternalMessageInfo

const Default_MethodOptions_Deprecated bool = false
const Default_MethodOptions_IdempotencyLevel MethodOptions_IdempotencyLevel = MethodOptions_IDEMPOTENCY_UNKNOWN

func (m *MethodOptions) GetDeprecated() bool {
	if m != nil && m.Deprecated != nil {
		return *m.Deprecated
	}
	return Default_MethodOptions_Deprecated
}

func (m *MethodOptions) GetIdempotencyLevel() MethodOptions_IdempotencyLevel {
	if m != nil && m.IdempotencyLevel != nil {
		return *m.IdempotencyLevel
	}
	return Default_MethodOptions_IdempotencyLevel
}

func (m *MethodOptions) GetUninterpretedOption() []*UninterpretedOption {
	if m != nil {
		return m.UninterpretedOption
	}
	return nil
}

// A message representing a option the parser does not recognize. This only
// appears in options protos created by the compiler::Parser class.
// DescriptorPool resolves these when building Descriptor objects. Therefore,
// options protos in descriptor objects (e.g. returned by Descriptor::options(),
// or produced by Descriptor::CopyTo()) will never have UninterpretedOptions
// in them.
type UninterpretedOption struct {
	Name []*UninterpretedOption_NamePart `protobuf:"bytes,2,rep,name=name" json:"name,omitempty"`
	// The value of the uninterpreted option, in whatever type the tokenizer
	// identified it as during parsing. Exactly one of these should be set.
	IdentifierValue      *string  `protobuf:"bytes,3,opt,name=identifier_value,json=identifierValue" json:"identifier_value,omitempty"`
	PositiveIntValue     *uint64  `protobuf:"varint,4,opt,name=positive_int_value,json=positiveIntValue" json:"positive_int_value,omitempty"`
	NegativeIntValue     *int64   `protobuf:"varint,5,opt,name=negative_int_value,json=negativeIntValue" json:"negative_int_value,omitempty"`
	DoubleValue          *float64 `protobuf:"fixed64,6,opt,name=double_value,json=doubleValue" json:"double_value,omitempty"`
	StringValue          []byte   `protobuf:"bytes,7,opt,name=string_value,json=stringValue" json:"string_value,omitempty"`
	AggregateValue       *string  `protobuf:"bytes,8,opt,name=aggregate_value,json=aggregateValue" json:"aggregate_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UninterpretedOption) Reset()         { *m = UninterpretedOption{} }
func (m *UninterpretedOption) String() string { return proto.CompactTextString(m) }
func (*UninterpretedOption) ProtoMessage()    {}
func (*UninterpretedOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{18}
}
func (m *UninterpretedOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UninterpretedOption.Unmarshal(m, b)
}
func (m *UninterpretedOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UninterpretedOption.Marshal(b, m, deterministic)
}
func (m *UninterpretedOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UninterpretedOption.Merge(m, src)
}
func (m *UninterpretedOption) XXX_Size() int {
	return xxx_messageInfo_UninterpretedOption.Size(m)
}
func (m *UninterpretedOption) XXX_DiscardUnknown() {
	xxx_messageInfo_UninterpretedOption.DiscardUnknown(m)
}

var xxx_messageInfo_UninterpretedOption proto.InternalMessageInfo

func (m *UninterpretedOption) GetName() []*UninterpretedOption_NamePart {
	if m != nil {
		return m.Name
	}
	return nil
}

func (m *UninterpretedOption) GetIdentifierValue() string {
	if m != nil && m.IdentifierValue != nil {
		return *m.IdentifierValue
	}
	return ""
}

func (m *UninterpretedOption) GetPositiveIntValue() uint64 {
	if m != nil && m.PositiveIntValue != nil {
		return *m.PositiveIntValue
	}
	return 0
}

func (m *UninterpretedOption) GetNegativeIntValue() int64 {
	if m != nil && m.NegativeIntValue != nil {
		return *m.NegativeIntValue
	}
	return 0
}

func (m *UninterpretedOption) GetDoubleValue() float64 {
	if m != nil && m.DoubleValue != nil {
		return *m.DoubleValue
	}
	return 0
}

func (m *UninterpretedOption) GetStringValue() []byte {
	if m != nil {
		return m.StringValue
	}
	return nil
}

func (m *UninterpretedOption) GetAggregateValue() string {
	if m != nil && m.AggregateValue != nil {
		return *m.AggregateValue
	}
	return ""
}

// The name of the uninterpreted option.  Each string represents a segment in
// a dot-separated name.  is_extension is true iff a segment represents an
// extension (denoted with parentheses in options specs in .proto files).
// E.g.,{ ["foo", false], ["bar.baz", true], ["qux", false] } represents
// "foo.(bar.baz).qux".
type UninterpretedOption_NamePart struct {
	NamePart             *string  `protobuf:"bytes,1,req,name=name_part,json=namePart" json:"name_part,omitempty"`
	IsExtension          *bool    `protobuf:"varint,2,req,name=is_extension,json=isExtension" json:"is_extension,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UninterpretedOption_NamePart) Reset()         { *m = UninterpretedOption_NamePart{} }
func (m *UninterpretedOption_NamePart) String() string { return proto.CompactTextString(m) }
func (*UninterpretedOption_NamePart) ProtoMessage()    {}
func (*UninterpretedOption_NamePart) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{18, 0}
}
func (m *UninterpretedOption_NamePart) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UninterpretedOption_NamePart.Unmarshal(m, b)
}
func (m *UninterpretedOption_NamePart) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UninterpretedOption_NamePart.Marshal(b, m, deterministic)
}
func (m *UninterpretedOption_NamePart) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UninterpretedOption_NamePart.Merge(m, src)
}
func (m *UninterpretedOption_NamePart) XXX_Size() int {
	return xxx_messageInfo_UninterpretedOption_NamePart.Size(m)
}
func (m *UninterpretedOption_NamePart) XXX_DiscardUnknown() {
	xxx_messageInfo_UninterpretedOption_NamePart.DiscardUnknown(m)
}

var xxx_messageInfo_UninterpretedOption_NamePart proto.InternalMessageInfo

func (m *UninterpretedOption_NamePart) GetNamePart() string {
	if m != nil && m.NamePart != nil {
		return *m.NamePart
	}
	return ""
}

func (m *UninterpretedOption_NamePart) GetIsExtension() bool {
	if m != nil && m.IsExtension != nil {
		return *m.IsExtension
	}
	return false
}

// Encapsulates information about the original source file from which a
// FileDescriptorProto was generated.
type SourceCodeInfo struct {
	// A Location identifies a piece of source code in a .proto file which
	// corresponds to a particular definition.  This information is intended
	// to be useful to IDEs, code indexers, documentation generators, and similar
	// tools.
	//
	// For example, say we have a file like:
	//   message Foo {
	//     optional string foo = 1;
	//   }
	// Let's look at just the field definition:
	//   optional string foo = 1;
	//   ^       ^^     ^^  ^  ^^^
	//   a       bc     de  f  ghi
	// We have the following locations:
	//   span   path               represents
	//   [a,i)  [ 4, 0, 2, 0 ]     The whole field definition.
	//   [a,b)  [ 4, 0, 2, 0, 4 ]  The label (optional).
	//   [c,d)  [ 4, 0, 2, 0, 5 ]  The type (string).
	//   [e,f)  [ 4, 0, 2, 0, 1 ]  The name (foo).
	//   [g,h)  [ 4, 0, 2, 0, 3 ]  The number (1).
	//
	// Notes:
	// - A location may refer to a repeated field itself (i.e. not to any
	//   particular index within it).  This is used whenever a set of elements are
	//   logically enclosed in a single code segment.  For example, an entire
	//   extend block (possibly containing multiple extension definitions) will
	//   have an outer location whose path refers to the "extensions" repeated
	//   field without an index.
	// - Multiple locations may have the same path.  This happens when a single
	//   logical declaration is spread out across multiple places.  The most
	//   obvious example is the "extend" block again -- there may be multiple
	//   extend blocks in the same scope, each of which will have the same path.
	// - A location's span is not always a subset of its parent's span.  For
	//   example, the "extendee" of an extension declaration appears at the
	//   beginning of the "extend" block and is shared by all extensions within
	//   the block.
	// - Just because a location's span is a subset of some other location's span
	//   does not mean that it is a descendant.  For example, a "group" defines
	//   both a type and a field in a single declaration.  Thus, the locations
	//   corresponding to the type and field and their components will overlap.
	// - Code which tries to interpret locations should probably be designed to
	//   ignore those that it doesn't understand, as more types of locations could
	//   be recorded in the future.
	Location             []*SourceCodeInfo_Location `protobuf:"bytes,1,rep,name=location" json:"location,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *SourceCodeInfo) Reset()         { *m = SourceCodeInfo{} }
func (m *SourceCodeInfo) String() string { return proto.CompactTextString(m) }
func (*SourceCodeInfo) ProtoMessage()    {}
func (*SourceCodeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{19}
}
func (m *SourceCodeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SourceCodeInfo.Unmarshal(m, b)
}
func (m *SourceCodeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SourceCodeInfo.Marshal(b, m, deterministic)
}
func (m *SourceCodeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SourceCodeInfo.Merge(m, src)
}
func (m *SourceCodeInfo) XXX_Size() int {
	return xxx_messageInfo_SourceCodeInfo.Size(m)
}
func (m *SourceCodeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SourceCodeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SourceCodeInfo proto.InternalMessageInfo

func (m *SourceCodeInfo) GetLocation() []*SourceCodeInfo_Location {
	if m != nil {
		return m.Location
	}
	return nil
}

type SourceCodeInfo_Location struct {
	// Identifies which part of the FileDescriptorProto was defined at this
	// location.
	//
	// Each element is a field number or an index.  They form a path from
	// the root FileDescriptorProto to the place where the definition.  For
	// example, this path:
	//   [ 4, 3, 2, 7, 1 ]
	// refers to:
	//   file.message_type(3)  // 4, 3
	//       .field(7)         // 2, 7
	//       .name()           // 1
	// This is because FileDescriptorProto.message_type has field number 4:
	//   repeated DescriptorProto message_type = 4;
	// and DescriptorProto.field has field number 2:
	//   repeated FieldDescriptorProto field = 2;
	// and FieldDescriptorProto.name has field number 1:
	//   optional string name = 1;
	//
	// Thus, the above path gives the location of a field name.  If we removed
	// the last element:
	//   [ 4, 3, 2, 7 ]
	// this path refers to the whole field declaration (from the beginning
	// of the label to the terminating semicolon).
	Path []int32 `protobuf:"varint,1,rep,packed,name=path" json:"path,omitempty"`
	// Always has exactly three or four elements: start line, start column,
	// end line (optional, otherwise assumed same as start line), end column.
	// These are packed into a single field for efficiency.  Note that line
	// and column numbers are zero-based -- typically you will want to add
	// 1 to each before displaying to a user.
	Span []int32 `protobuf:"varint,2,rep,packed,name=span" json:"span,omitempty"`
	// If this SourceCodeInfo represents a complete declaration, these are any
	// comments appearing before and after the declaration which appear to be
	// attached to the declaration.
	//
	// A series of line comments appearing on consecutive lines, with no other
	// tokens appearing on those lines, will be treated as a single comment.
	//
	// leading_detached_comments will keep paragraphs of comments that appear
	// before (but not connected to) the current element. Each paragraph,
	// separated by empty lines, will be one comment element in the repeated
	// field.
	//
	// Only the comment content is provided; comment markers (e.g. //) are
	// stripped out.  For block comments, leading whitespace and an asterisk
	// will be stripped from the beginning of each line other than the first.
	// Newlines are included in the output.
	//
	// Examples:
	//
	//   optional int32 foo = 1;  // Comment attached to foo.
	//   // Comment attached to bar.
	//   optional int32 bar = 2;
	//
	//   optional string baz = 3;
	//   // Comment attached to baz.
	//   // Another line attached to baz.
	//
	//   // Comment attached to qux.
	//   //
	//   // Another line attached to qux.
	//   optional double qux = 4;
	//
	//   // Detached comment for corge. This is not leading or trailing comments
	//   // to qux or corge because there are blank lines separating it from
	//   // both.
	//
	//   // Detached comment for corge paragraph 2.
	//
	//   optional string corge = 5;
	//   /* Block comment attached
	//    * to corge.  Leading asterisks
	//    * will be removed. */
	//   /* Block comment attached to
	//    * grault. */
	//   optional int32 grault = 6;
	//
	//   // ignored detached comments.
	LeadingComments         *string  `protobuf:"bytes,3,opt,name=leading_comments,json=leadingComments" json:"leading_comments,omitempty"`
	TrailingComments        *string  `protobuf:"bytes,4,opt,name=trailing_comments,json=trailingComments" json:"trailing_comments,omitempty"`
	LeadingDetachedComments []string `protobuf:"bytes,6,rep,name=leading_detached_comments,json=leadingDetachedComments" json:"leading_detached_comments,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *SourceCodeInfo_Location) Reset()         { *m = SourceCodeInfo_Location{} }
func (m *SourceCodeInfo_Location) String() string { return proto.CompactTextString(m) }
func (*SourceCodeInfo_Location) ProtoMessage()    {}
func (*SourceCodeInfo_Location) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{19, 0}
}
func (m *SourceCodeInfo_Location) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SourceCodeInfo_Location.Unmarshal(m, b)
}
func (m *SourceCodeInfo_Location) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SourceCodeInfo_Location.Marshal(b, m, deterministic)
}
func (m *SourceCodeInfo_Location) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SourceCodeInfo_Location.Merge(m, src)
}
func (m *SourceCodeInfo_Location) XXX_Size() int {
	return xxx_messageInfo_SourceCodeInfo_Location.Size(m)
}
func (m *SourceCodeInfo_Location) XXX_DiscardUnknown() {
	xxx_messageInfo_SourceCodeInfo_Location.DiscardUnknown(m)
}

var xxx_messageInfo_SourceCodeInfo_Location proto.InternalMessageInfo

func (m *SourceCodeInfo_Location) GetPath() []int32 {
	if m != nil {
		return m.Path
	}
	return nil
}

func (m *SourceCodeInfo_Location) GetSpan() []int32 {
	if m != nil {
		return m.Span
	}
	return nil
}

func (m *SourceCodeInfo_Location) GetLeadingComments() string {
	if m != nil && m.LeadingComments != nil {
		return *m.LeadingComments
	}
	return ""
}

func (m *SourceCodeInfo_Location) GetTrailingComments() string {
	if m != nil && m.TrailingComments != nil {
		return *m.TrailingComments
	}
	return ""
}

func (m *SourceCodeInfo_Location) GetLeadingDetachedComments() []string {
	if m != nil {
		return m.LeadingDetachedComments
	}
	return nil
}

// Describes the relationship between generated code and its original source
// file. A GeneratedCodeInfo message is associated with only one generated
// source file, but may contain references to different source .proto files.
type GeneratedCodeInfo struct {
	// An Annotation connects some span of text in generated code to an element
	// of its generating .proto file.
	Annotation           []*GeneratedCodeInfo_Annotation `protobuf:"bytes,1,rep,name=annotation" json:"annotation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GeneratedCodeInfo) Reset()         { *m = GeneratedCodeInfo{} }
func (m *GeneratedCodeInfo) String() string { return proto.CompactTextString(m) }
func (*GeneratedCodeInfo) ProtoMessage()    {}
func (*GeneratedCodeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{20}
}
func (m *GeneratedCodeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeneratedCodeInfo.Unmarshal(m, b)
}
func (m *GeneratedCodeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeneratedCodeInfo.Marshal(b, m, deterministic)
}
func (m *GeneratedCodeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeneratedCodeInfo.Merge(m, src)
}
func (m *GeneratedCodeInfo) XXX_Size() int {
	return xxx_messageInfo_GeneratedCodeInfo.Size(m)
}
func (m *GeneratedCodeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GeneratedCodeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GeneratedCodeInfo proto.InternalMessageInfo

func (m *GeneratedCodeInfo) GetAnnotation() []*GeneratedCodeInfo_Annotation {
	if m != nil {
		return m.Annotation
	}
	return nil
}

type GeneratedCodeInfo_Annotation struct {
	// Identifies the element in the original source .proto file. This field
	// is formatted the same as SourceCodeInfo.Location.path.
	Path []int32 `protobuf:"varint,1,rep,packed,name=path" json:"path,omitempty"`
	// Identifies the filesystem path to the original source .proto.
	SourceFile *string `protobuf:"bytes,2,opt,name=source_file,json=sourceFile" json:"source_file,omitempty"`
	// Identifies the starting offset in bytes in the generated code
	// that relates to the identified object.
	Begin *int32 `protobuf:"varint,3,opt,name=begin" json:"begin,omitempty"`
	// Identifies the ending offset in bytes in the generated code that
	// relates to the identified offset. The end offset should be one past
	// the last relevant byte (so the length of the text = end - begin).
	End                  *int32   `protobuf:"varint,4,opt,name=end" json:"end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GeneratedCodeInfo_Annotation) Reset()         { *m = GeneratedCodeInfo_Annotation{} }
func (m *GeneratedCodeInfo_Annotation) String() string { return proto.CompactTextString(m) }
func (*GeneratedCodeInfo_Annotation) ProtoMessage()    {}
func (*GeneratedCodeInfo_Annotation) Descriptor() ([]byte, []int) {
	return fileDescriptor_308767df5ffe18af, []int{20, 0}
}
func (m *GeneratedCodeInfo_Annotation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeneratedCodeInfo_Annotation.Unmarshal(m, b)
}
func (m *GeneratedCodeInfo_Annotation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeneratedCodeInfo_Annotation.Marshal(b, m, deterministic)
}
func (m *GeneratedCodeInfo_Annotation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeneratedCodeInfo_Annotation.Merge(m, src)
}
func (m *GeneratedCodeInfo_Annotation) XXX_Size() int {
	return xxx_messageInfo_GeneratedCodeInfo_Annotation.Size(m)
}
func (m *GeneratedCodeInfo_Annotation) XXX_DiscardUnknown() {
	xxx_messageInfo_GeneratedCodeInfo_Annotation.DiscardUnknown(m)
}

var xxx_messageInfo_GeneratedCodeInfo_Annotation proto.InternalMessageInfo

func (m *GeneratedCodeInfo_Annotation) GetPath() []int32 {
	if m != nil {
		return m.Path
	}
	return nil
}

func (m *GeneratedCodeInfo_Annotation) GetSourceFile() string {
	if m != nil && m.SourceFile != nil {
		return *m.SourceFile
	}
	return ""
}

func (m *GeneratedCodeInfo_Annotation) GetBegin() int32 {
	if m != nil && m.Begin != nil {
		return *m.Begin
	}
	return 0
}

func (m *GeneratedCodeInfo_Annotation) GetEnd() int32 {
	if m != nil && m.End != nil {
		return *m.End
	}
	return 0
}

func init() {
	proto.RegisterEnum("google.protobuf.FieldDescriptorProto_Type", FieldDescriptorProto_Type_name, FieldDescriptorProto_Type_value)
	proto.RegisterEnum("google.protobuf.FieldDescriptorProto_Label", FieldDescriptorProto_Label_name, FieldDescriptorProto_Label_value)
	proto.RegisterEnum("google.protobuf.FileOptions_OptimizeMode", FileOptions_OptimizeMode_name, FileOptions_OptimizeMode_value)
	proto.RegisterEnum("google.protobuf.FieldOptions_CType", FieldOptions_CType_name, FieldOptions_CType_value)
	proto.RegisterEnum("google.protobuf.FieldOptions_JSType", FieldOptions_JSType_name, FieldOptions_JSType_value)
	proto.RegisterEnum("google.protobuf.MethodOptions_IdempotencyLevel", MethodOptions_IdempotencyLevel_name, MethodOptions_IdempotencyLevel_value)
	proto.RegisterType((*FileDescriptorSet)(nil), "google.protobuf.FileDescriptorSet")
	proto.RegisterType((*FileDescriptorProto)(nil), "google.protobuf.FileDescriptorProto")
	proto.RegisterType((*DescriptorProto)(nil), "google.protobuf.DescriptorProto")
	proto.RegisterType((*DescriptorProto_ExtensionRange)(nil), "google.protobuf.DescriptorProto.ExtensionRange")
	proto.RegisterType((*DescriptorProto_ReservedRange)(nil), "google.protobuf.DescriptorProto.ReservedRange")
	proto.RegisterType((*ExtensionRangeOptions)(nil), "google.protobuf.ExtensionRangeOptions")
	proto.RegisterType((*FieldDescriptorProto)(nil), "google.protobuf.FieldDescriptorProto")
	proto.RegisterType((*OneofDescriptorProto)(nil), "google.protobuf.OneofDescriptorProto")
	proto.RegisterType((*EnumDescriptorProto)(nil), "google.protobuf.EnumDescriptorProto")
	proto.RegisterType((*EnumDescriptorProto_EnumReservedRange)(nil), "google.protobuf.EnumDescriptorProto.EnumReservedRange")
	proto.RegisterType((*EnumValueDescriptorProto)(nil), "google.protobuf.EnumValueDescriptorProto")
	proto.RegisterType((*ServiceDescriptorProto)(nil), "google.protobuf.ServiceDescriptorProto")
	proto.RegisterType((*MethodDescriptorProto)(nil), "google.protobuf.MethodDescriptorProto")
	proto.RegisterType((*FileOptions)(nil), "google.protobuf.FileOptions")
	proto.RegisterType((*MessageOptions)(nil), "google.protobuf.MessageOptions")
	proto.RegisterType((*FieldOptions)(nil), "google.protobuf.FieldOptions")
	proto.RegisterType((*OneofOptions)(nil), "google.protobuf.OneofOptions")
	proto.RegisterType((*EnumOptions)(nil), "google.protobuf.EnumOptions")
	proto.RegisterType((*EnumValueOptions)(nil), "google.protobuf.EnumValueOptions")
	proto.RegisterType((*ServiceOptions)(nil), "google.protobuf.ServiceOptions")
	proto.RegisterType((*MethodOptions)(nil), "google.protobuf.MethodOptions")
	proto.RegisterType((*UninterpretedOption)(nil), "google.protobuf.UninterpretedOption")
	proto.RegisterType((*UninterpretedOption_NamePart)(nil), "google.protobuf.UninterpretedOption.NamePart")
	proto.RegisterType((*SourceCodeInfo)(nil), "google.protobuf.SourceCodeInfo")
	proto.RegisterType((*SourceCodeInfo_Location)(nil), "google.protobuf.SourceCodeInfo.Location")
	proto.RegisterType((*GeneratedCodeInfo)(nil), "google.protobuf.GeneratedCodeInfo")
	proto.RegisterType((*GeneratedCodeInfo_Annotation)(nil), "google.protobuf.GeneratedCodeInfo.Annotation")
}

func init() { proto.RegisterFile("descriptor.proto", fileDescriptor_308767df5ffe18af) }

var fileDescriptor_308767df5ffe18af = []byte{
	// 2522 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x59, 0xcd, 0x6f, 0xdb, 0xc8,
	0x15, 0x5f, 0x7d, 0x5a, 0x7a, 0x92, 0x65, 0x7a, 0xec, 0x75, 0x18, 0xef, 0x47, 0x1c, 0xed, 0x66,
	0xe3, 0x24, 0xbb, 0xca, 0xc2, 0x49, 0x9c, 0xac, 0x53, 0x6c, 0x2b, 0x4b, 0x8c, 0x57, 0xa9, 0xbe,
	0x4a, 0xc9, 0xdd, 0x64, 0x8b, 0x82, 0x18, 0x93, 0x23, 0x89, 0x09, 0x45, 0x72, 0x49, 0x2a, 0x89,
	0x83, 0x1e, 0x02, 0xf4, 0x54, 0xa0, 0x7f, 0x40, 0x51, 0x14, 0x3d, 0xf4, 0xb2, 0x40, 0xff, 0x80,
	0x02, 0xed, 0xbd, 0xd7, 0x02, 0xbd, 0xf7, 0x50, 0xa0, 0x05, 0xda, 0x3f, 0xa1, 0xc7, 0x62, 0x66,
	0x48, 0x8a, 0xd4, 0x47, 0xe2, 0x5d, 0x20, 0xd9, 0x93, 0x3d, 0xef, 0xfd, 0xde, 0x9b, 0x37, 0x8f,
	0xbf, 0x79, 0xf3, 0x66, 0x04, 0x82, 0x46, 0x5c, 0xd5, 0xd1, 0x6d, 0xcf, 0x72, 0x2a, 0xb6, 0x63,
	0x79, 0x16, 0x5a, 0x1b, 0x5a, 0xd6, 0xd0, 0x20, 0x7c, 0x74, 0x32, 0x19, 0x94, 0x5b, 0xb0, 0x7e,
	0x4f, 0x37, 0x48, 0x3d, 0x04, 0xf6, 0x88, 0x87, 0xee, 0x40, 0x7a, 0xa0, 0x1b, 0x44, 0x4c, 0xec,
	0xa4, 0x76, 0x0b, 0x7b, 0x1f, 0x56, 0x66, 0x8c, 0x2a, 0x71, 0x8b, 0x2e, 0x15, 0xcb, 0xcc, 0xa2,
	0xfc, 0xef, 0x34, 0x6c, 0x2c, 0xd0, 0x22, 0x04, 0x69, 0x13, 0x8f, 0xa9, 0xc7, 0xc4, 0x6e, 0x5e,
	0x66, 0xff, 0x23, 0x11, 0x56, 0x6c, 0xac, 0x3e, 0xc6, 0x43, 0x22, 0x26, 0x99, 0x38, 0x18, 0xa2,
	0xf7, 0x01, 0x34, 0x62, 0x13, 0x53, 0x23, 0xa6, 0x7a, 0x2a, 0xa6, 0x76, 0x52, 0xbb, 0x79, 0x39,
	0x22, 0x41, 0xd7, 0x60, 0xdd, 0x9e, 0x9c, 0x18, 0xba, 0xaa, 0x44, 0x60, 0xb0, 0x93, 0xda, 0xcd,
	0xc8, 0x02, 0x57, 0xd4, 0xa7, 0xe0, 0xcb, 0xb0, 0xf6, 0x94, 0xe0, 0xc7, 0x51, 0x68, 0x81, 0x41,
	0x4b, 0x54, 0x1c, 0x01, 0xd6, 0xa0, 0x38, 0x26, 0xae, 0x8b, 0x87, 0x44, 0xf1, 0x4e, 0x6d, 0x22,
	0xa6, 0xd9, 0xea, 0x77, 0xe6, 0x56, 0x3f, 0xbb, 0xf2, 0x82, 0x6f, 0xd5, 0x3f, 0xb5, 0x09, 0xaa,
	0x42, 0x9e, 0x98, 0x93, 0x31, 0xf7, 0x90, 0x59, 0x92, 0x3f, 0xc9, 0x9c, 0x8c, 0x67, 0xbd, 0xe4,
	0xa8, 0x99, 0xef, 0x62, 0xc5, 0x25, 0xce, 0x13, 0x5d, 0x25, 0x62, 0x96, 0x39, 0xb8, 0x3c, 0xe7,
	0xa0, 0xc7, 0xf5, 0xb3, 0x3e, 0x02, 0x3b, 0x54, 0x83, 0x3c, 0x79, 0xe6, 0x11, 0xd3, 0xd5, 0x2d,
	0x53, 0x5c, 0x61, 0x4e, 0x2e, 0x2d, 0xf8, 0x8a, 0xc4, 0xd0, 0x66, 0x5d, 0x4c, 0xed, 0xd0, 0x3e,
	0xac, 0x58, 0xb6, 0xa7, 0x5b, 0xa6, 0x2b, 0xe6, 0x76, 0x12, 0xbb, 0x85, 0xbd, 0x77, 0x17, 0x12,
	0xa1, 0xc3, 0x31, 0x72, 0x00, 0x46, 0x0d, 0x10, 0x5c, 0x6b, 0xe2, 0xa8, 0x44, 0x51, 0x2d, 0x8d,
	0x28, 0xba, 0x39, 0xb0, 0xc4, 0x3c, 0x73, 0x70, 0x61, 0x7e, 0x21, 0x0c, 0x58, 0xb3, 0x34, 0xd2,
	0x30, 0x07, 0x96, 0x5c, 0x72, 0x63, 0x63, 0xb4, 0x05, 0x59, 0xf7, 0xd4, 0xf4, 0xf0, 0x33, 0xb1,
	0xc8, 0x18, 0xe2, 0x8f, 0xca, 0x7f, 0xce, 0xc2, 0xda, 0x59, 0x28, 0x76, 0x17, 0x32, 0x03, 0xba,
	0x4a, 0x31, 0xf9, 0x6d, 0x72, 0xc0, 0x6d, 0xe2, 0x49, 0xcc, 0x7e, 0xc7, 0x24, 0x56, 0xa1, 0x60,
	0x12, 0xd7, 0x23, 0x1a, 0x67, 0x44, 0xea, 0x8c, 0x9c, 0x02, 0x6e, 0x34, 0x4f, 0xa9, 0xf4, 0x77,
	0xa2, 0xd4, 0x03, 0x58, 0x0b, 0x43, 0x52, 0x1c, 0x6c, 0x0e, 0x03, 0x6e, 0x5e, 0x7f, 0x55, 0x24,
	0x15, 0x29, 0xb0, 0x93, 0xa9, 0x99, 0x5c, 0x22, 0xb1, 0x31, 0xaa, 0x03, 0x58, 0x26, 0xb1, 0x06,
	0x8a, 0x46, 0x54, 0x43, 0xcc, 0x2d, 0xc9, 0x52, 0x87, 0x42, 0xe6, 0xb2, 0x64, 0x71, 0xa9, 0x6a,
	0xa0, 0xcf, 0xa6, 0x54, 0x5b, 0x59, 0xc2, 0x94, 0x16, 0xdf, 0x64, 0x73, 0x6c, 0x3b, 0x86, 0x92,
	0x43, 0x28, 0xef, 0x89, 0xe6, 0xaf, 0x2c, 0xcf, 0x82, 0xa8, 0xbc, 0x72, 0x65, 0xb2, 0x6f, 0xc6,
	0x17, 0xb6, 0xea, 0x44, 0x87, 0xe8, 0x03, 0x08, 0x05, 0x0a, 0xa3, 0x15, 0xb0, 0x2a, 0x54, 0x0c,
	0x84, 0x6d, 0x3c, 0x26, 0xdb, 0xcf, 0xa1, 0x14, 0x4f, 0x0f, 0xda, 0x84, 0x8c, 0xeb, 0x61, 0xc7,
	0x63, 0x2c, 0xcc, 0xc8, 0x7c, 0x80, 0x04, 0x48, 0x11, 0x53, 0x63, 0x55, 0x2e, 0x23, 0xd3, 0x7f,
	0xd1, 0x8f, 0xa6, 0x0b, 0x4e, 0xb1, 0x05, 0x7f, 0x34, 0xff, 0x45, 0x63, 0x9e, 0x67, 0xd7, 0xbd,
	0x7d, 0x1b, 0x56, 0x63, 0x0b, 0x38, 0xeb, 0xd4, 0xe5, 0x5f, 0xc0, 0xdb, 0x0b, 0x5d, 0xa3, 0x07,
	0xb0, 0x39, 0x31, 0x75, 0xd3, 0x23, 0x8e, 0xed, 0x10, 0xca, 0x58, 0x3e, 0x95, 0xf8, 0x9f, 0x95,
	0x25, 0x9c, 0x3b, 0x8e, 0xa2, 0xb9, 0x17, 0x79, 0x63, 0x32, 0x2f, 0xbc, 0x9a, 0xcf, 0xfd, 0x77,
	0x45, 0x78, 0xf1, 0xe2, 0xc5, 0x8b, 0x64, 0xf9, 0x37, 0x59, 0xd8, 0x5c, 0xb4, 0x67, 0x16, 0x6e,
	0xdf, 0x2d, 0xc8, 0x9a, 0x93, 0xf1, 0x09, 0x71, 0x58, 0x92, 0x32, 0xb2, 0x3f, 0x42, 0x55, 0xc8,
	0x18, 0xf8, 0x84, 0x18, 0x62, 0x7a, 0x27, 0xb1, 0x5b, 0xda, 0xbb, 0x76, 0xa6, 0x5d, 0x59, 0x69,
	0x52, 0x13, 0x99, 0x5b, 0xa2, 0xcf, 0x21, 0xed, 0x97, 0x68, 0xea, 0xe1, 0xea, 0xd9, 0x3c, 0xd0,
	0xbd, 0x24, 0x33, 0x3b, 0xf4, 0x0e, 0xe4, 0xe9, 0x5f, 0xce, 0x8d, 0x2c, 0x8b, 0x39, 0x47, 0x05,
	0x94, 0x17, 0x68, 0x1b, 0x72, 0x6c, 0x9b, 0x68, 0x24, 0x38, 0xda, 0xc2, 0x31, 0x25, 0x96, 0x46,
	0x06, 0x78, 0x62, 0x78, 0xca, 0x13, 0x6c, 0x4c, 0x08, 0x23, 0x7c, 0x5e, 0x2e, 0xfa, 0xc2, 0x9f,
	0x52, 0x19, 0xba, 0x00, 0x05, 0xbe, 0xab, 0x74, 0x53, 0x23, 0xcf, 0x58, 0xf5, 0xcc, 0xc8, 0x7c,
	0xa3, 0x35, 0xa8, 0x84, 0x4e, 0xff, 0xc8, 0xb5, 0xcc, 0x80, 0x9a, 0x6c, 0x0a, 0x2a, 0x60, 0xd3,
	0xdf, 0x9e, 0x2d, 0xdc, 0xef, 0x2d, 0x5e, 0xde, 0x2c, 0xa7, 0xca, 0x7f, 0x4a, 0x42, 0x9a, 0xd5,
	0x8b, 0x35, 0x28, 0xf4, 0x1f, 0x76, 0x25, 0xa5, 0xde, 0x39, 0x3e, 0x6c, 0x4a, 0x42, 0x02, 0x95,
	0x00, 0x98, 0xe0, 0x5e, 0xb3, 0x53, 0xed, 0x0b, 0xc9, 0x70, 0xdc, 0x68, 0xf7, 0xf7, 0x6f, 0x0a,
	0xa9, 0xd0, 0xe0, 0x98, 0x0b, 0xd2, 0x51, 0xc0, 0x8d, 0x3d, 0x21, 0x83, 0x04, 0x28, 0x72, 0x07,
	0x8d, 0x07, 0x52, 0x7d, 0xff, 0xa6, 0x90, 0x8d, 0x4b, 0x6e, 0xec, 0x09, 0x2b, 0x68, 0x15, 0xf2,
	0x4c, 0x72, 0xd8, 0xe9, 0x34, 0x85, 0x5c, 0xe8, 0xb3, 0xd7, 0x97, 0x1b, 0xed, 0x23, 0x21, 0x1f,
	0xfa, 0x3c, 0x92, 0x3b, 0xc7, 0x5d, 0x01, 0x42, 0x0f, 0x2d, 0xa9, 0xd7, 0xab, 0x1e, 0x49, 0x42,
	0x21, 0x44, 0x1c, 0x3e, 0xec, 0x4b, 0x3d, 0xa1, 0x18, 0x0b, 0xeb, 0xc6, 0x9e, 0xb0, 0x1a, 0x4e,
	0x21, 0xb5, 0x8f, 0x5b, 0x42, 0x09, 0xad, 0xc3, 0x2a, 0x9f, 0x22, 0x08, 0x62, 0x6d, 0x46, 0xb4,
	0x7f, 0x53, 0x10, 0xa6, 0x81, 0x70, 0x2f, 0xeb, 0x31, 0xc1, 0xfe, 0x4d, 0x01, 0x95, 0x6b, 0x90,
	0x61, 0xec, 0x42, 0x08, 0x4a, 0xcd, 0xea, 0xa1, 0xd4, 0x54, 0x3a, 0xdd, 0x7e, 0xa3, 0xd3, 0xae,
	0x36, 0x85, 0xc4, 0x54, 0x26, 0x4b, 0x3f, 0x39, 0x6e, 0xc8, 0x52, 0x5d, 0x48, 0x46, 0x65, 0x5d,
	0xa9, 0xda, 0x97, 0xea, 0x42, 0xaa, 0xac, 0xc2, 0xe6, 0xa2, 0x3a, 0xb9, 0x70, 0x67, 0x44, 0x3e,
	0x71, 0x72, 0xc9, 0x27, 0x66, 0xbe, 0xe6, 0x3e, 0xf1, 0xbf, 0x92, 0xb0, 0xb1, 0xe0, 0xac, 0x58,
	0x38, 0xc9, 0x0f, 0x21, 0xc3, 0x29, 0xca, 0x4f, 0xcf, 0x2b, 0x0b, 0x0f, 0x1d, 0x46, 0xd8, 0xb9,
	0x13, 0x94, 0xd9, 0x45, 0x3b, 0x88, 0xd4, 0x92, 0x0e, 0x82, 0xba, 0x98, 0xab, 0xe9, 0x3f, 0x9f,
	0xab, 0xe9, 0xfc, 0xd8, 0xdb, 0x3f, 0xcb, 0xb1, 0xc7, 0x64, 0xdf, 0xae, 0xb6, 0x67, 0x16, 0xd4,
	0xf6, 0xbb, 0xb0, 0x3e, 0xe7, 0xe8, 0xcc, 0x35, 0xf6, 0x97, 0x09, 0x10, 0x97, 0x25, 0xe7, 0x15,
	0x95, 0x2e, 0x19, 0xab, 0x74, 0x77, 0x67, 0x33, 0x78, 0x71, 0xf9, 0x47, 0x98, 0xfb, 0xd6, 0xdf,
	0x24, 0x60, 0x6b, 0x71, 0xa7, 0xb8, 0x30, 0x86, 0xcf, 0x21, 0x3b, 0x26, 0xde, 0xc8, 0x0a, 0xba,
	0xa5, 0x8f, 0x16, 0x9c, 0xc1, 0x54, 0x3d, 0xfb, 0xb1, 0x7d, 0xab, 0xe8, 0x21, 0x9e, 0x5a, 0xd6,
	0xee, 0xf1, 0x68, 0xe6, 0x22, 0xfd, 0x55, 0x12, 0xde, 0x5e, 0xe8, 0x7c, 0x61, 0xa0, 0xef, 0x01,
	0xe8, 0xa6, 0x3d, 0xf1, 0x78, 0x47, 0xc4, 0x0b, 0x6c, 0x9e, 0x49, 0x58, 0xf1, 0xa2, 0xc5, 0x73,
	0xe2, 0x85, 0xfa, 0x14, 0xd3, 0x03, 0x17, 0x31, 0xc0, 0x9d, 0x69, 0xa0, 0x69, 0x16, 0xe8, 0xfb,
	0x4b, 0x56, 0x3a, 0x47, 0xcc, 0x4f, 0x41, 0x50, 0x0d, 0x9d, 0x98, 0x9e, 0xe2, 0x7a, 0x0e, 0xc1,
	0x63, 0xdd, 0x1c, 0xb2, 0x13, 0x24, 0x77, 0x90, 0x19, 0x60, 0xc3, 0x25, 0xf2, 0x1a, 0x57, 0xf7,
	0x02, 0x2d, 0xb5, 0x60, 0x04, 0x72, 0x22, 0x16, 0xd9, 0x98, 0x05, 0x57, 0x87, 0x16, 0xe5, 0x5f,
	0xe7, 0xa1, 0x10, 0xe9, 0xab, 0xd1, 0x45, 0x28, 0x3e, 0xc2, 0x4f, 0xb0, 0x12, 0xdc, 0x95, 0x78,
	0x26, 0x0a, 0x54, 0xd6, 0xf5, 0xef, 0x4b, 0x9f, 0xc2, 0x26, 0x83, 0x58, 0x13, 0x8f, 0x38, 0x8a,
	0x6a, 0x60, 0xd7, 0x65, 0x49, 0xcb, 0x31, 0x28, 0xa2, 0xba, 0x0e, 0x55, 0xd5, 0x02, 0x0d, 0xba,
	0x05, 0x1b, 0xcc, 0x62, 0x3c, 0x31, 0x3c, 0xdd, 0x36, 0x88, 0x42, 0x6f, 0x6f, 0x2e, 0x3b, 0x49,
	0xc2, 0xc8, 0xd6, 0x29, 0xa2, 0xe5, 0x03, 0x68, 0x44, 0x2e, 0xaa, 0xc3, 0x7b, 0xcc, 0x6c, 0x48,
	0x4c, 0xe2, 0x60, 0x8f, 0x28, 0xe4, 0xeb, 0x09, 0x36, 0x5c, 0x05, 0x9b, 0x9a, 0x32, 0xc2, 0xee,
	0x48, 0xdc, 0xa4, 0x0e, 0x0e, 0x93, 0x62, 0x42, 0x3e, 0x4f, 0x81, 0x47, 0x3e, 0x4e, 0x62, 0xb0,
	0xaa, 0xa9, 0x7d, 0x81, 0xdd, 0x11, 0x3a, 0x80, 0x2d, 0xe6, 0xc5, 0xf5, 0x1c, 0xdd, 0x1c, 0x2a,
	0xea, 0x88, 0xa8, 0x8f, 0x95, 0x89, 0x37, 0xb8, 0x23, 0xbe, 0x13, 0x9d, 0x9f, 0x45, 0xd8, 0x63,
	0x98, 0x1a, 0x85, 0x1c, 0x7b, 0x83, 0x3b, 0xa8, 0x07, 0x45, 0xfa, 0x31, 0xc6, 0xfa, 0x73, 0xa2,
	0x0c, 0x2c, 0x87, 0x1d, 0x8d, 0xa5, 0x05, 0xa5, 0x29, 0x92, 0xc1, 0x4a, 0xc7, 0x37, 0x68, 0x59,
	0x1a, 0x39, 0xc8, 0xf4, 0xba, 0x92, 0x54, 0x97, 0x0b, 0x81, 0x97, 0x7b, 0x96, 0x43, 0x09, 0x35,
	0xb4, 0xc2, 0x04, 0x17, 0x38, 0xa1, 0x86, 0x56, 0x90, 0xde, 0x5b, 0xb0, 0xa1, 0xaa, 0x7c, 0xcd,
	0xba, 0xaa, 0xf8, 0x77, 0x2c, 0x57, 0x14, 0x62, 0xc9, 0x52, 0xd5, 0x23, 0x0e, 0xf0, 0x39, 0xee,
	0xa2, 0xcf, 0xe0, 0xed, 0x69, 0xb2, 0xa2, 0x86, 0xeb, 0x73, 0xab, 0x9c, 0x35, 0xbd, 0x05, 0x1b,
	0xf6, 0xe9, 0xbc, 0x21, 0x8a, 0xcd, 0x68, 0x9f, 0xce, 0x9a, 0xdd, 0x86, 0x4d, 0x7b, 0x64, 0xcf,
	0xdb, 0x5d, 0x8d, 0xda, 0x21, 0x7b, 0x64, 0xcf, 0x1a, 0x5e, 0x62, 0x17, 0x6e, 0x87, 0xa8, 0xd8,
	0x23, 0x9a, 0x78, 0x2e, 0x0a, 0x8f, 0x28, 0xd0, 0x75, 0x10, 0x54, 0x55, 0x21, 0x26, 0x3e, 0x31,
	0x88, 0x82, 0x1d, 0x62, 0x62, 0x57, 0xbc, 0x10, 0x05, 0x97, 0x54, 0x55, 0x62, 0xda, 0x2a, 0x53,
	0xa2, 0xab, 0xb0, 0x6e, 0x9d, 0x3c, 0x52, 0x39, 0x25, 0x15, 0xdb, 0x21, 0x03, 0xfd, 0x99, 0xf8,
	0x21, 0xcb, 0xef, 0x1a, 0x55, 0x30, 0x42, 0x76, 0x99, 0x18, 0x5d, 0x01, 0x41, 0x75, 0x47, 0xd8,
	0xb1, 0x59, 0x4d, 0x76, 0x6d, 0xac, 0x12, 0xf1, 0x12, 0x87, 0x72, 0x79, 0x3b, 0x10, 0xd3, 0x2d,
	0xe1, 0x3e, 0xd5, 0x07, 0x5e, 0xe0, 0xf1, 0x32, 0xdf, 0x12, 0x4c, 0xe6, 0x7b, 0xdb, 0x05, 0x81,
	0xa6, 0x22, 0x36, 0xf1, 0x2e, 0x83, 0x95, 0xec, 0x91, 0x1d, 0x9d, 0xf7, 0x03, 0x58, 0xa5, 0xc8,
	0xe9, 0xa4, 0x57, 0x78, 0x43, 0x66, 0x8f, 0x22, 0x33, 0xde, 0x84, 0x2d, 0x0a, 0x1a, 0x13, 0x0f,
	0x6b, 0xd8, 0xc3, 0x11, 0xf4, 0xc7, 0x0c, 0x4d, 0xf3, 0xde, 0xf2, 0x95, 0xb1, 0x38, 0x9d, 0xc9,
	0xc9, 0x69, 0xc8, 0xac, 0x4f, 0x78, 0x9c, 0x54, 0x16, 0x70, 0xeb, 0xb5, 0x35, 0xdd, 0xe5, 0x03,
	0x28, 0x46, 0x89, 0x8f, 0xf2, 0xc0, 0xa9, 0x2f, 0x24, 0x68, 0x17, 0x54, 0xeb, 0xd4, 0x69, 0xff,
	0xf2, 0x95, 0x24, 0x24, 0x69, 0x1f, 0xd5, 0x6c, 0xf4, 0x25, 0x45, 0x3e, 0x6e, 0xf7, 0x1b, 0x2d,
	0x49, 0x48, 0x45, 0x1b, 0xf6, 0xbf, 0x26, 0xa1, 0x14, 0xbf, 0x7b, 0xa1, 0x1f, 0xc0, 0xb9, 0xe0,
	0xa1, 0xc4, 0x25, 0x9e, 0xf2, 0x54, 0x77, 0xd8, 0x5e, 0x1c, 0x63, 0x7e, 0x2e, 0x86, 0x6c, 0xd8,
	0xf4, 0x51, 0x3d, 0xe2, 0x7d, 0xa9, 0x3b, 0x74, 0xa7, 0x8d, 0xb1, 0x87, 0x9a, 0x70, 0xc1, 0xb4,
	0x14, 0xd7, 0xc3, 0xa6, 0x86, 0x1d, 0x4d, 0x99, 0x3e, 0x51, 0x29, 0x58, 0x55, 0x89, 0xeb, 0x5a,
	0xfc, 0x0c, 0x0c, 0xbd, 0xbc, 0x6b, 0x5a, 0x3d, 0x1f, 0x3c, 0x3d, 0x1c, 0xaa, 0x3e, 0x74, 0x86,
	0xb9, 0xa9, 0x65, 0xcc, 0x7d, 0x07, 0xf2, 0x63, 0x6c, 0x2b, 0xc4, 0xf4, 0x9c, 0x53, 0xd6, 0x71,
	0xe7, 0xe4, 0xdc, 0x18, 0xdb, 0x12, 0x1d, 0xbf, 0x99, 0x8b, 0xcf, 0x3f, 0x52, 0x50, 0x8c, 0x76,
	0xdd, 0xf4, 0x12, 0xa3, 0xb2, 0x03, 0x2a, 0xc1, 0x4a, 0xd8, 0x07, 0x2f, 0xed, 0xd1, 0x2b, 0x35,
	0x7a, 0x72, 0x1d, 0x64, 0x79, 0x2f, 0x2c, 0x73, 0x4b, 0xda, 0x35, 0x50, 0x6a, 0x11, 0xde, 0x7b,
	0xe4, 0x64, 0x7f, 0x84, 0x8e, 0x20, 0xfb, 0xc8, 0x65, 0xbe, 0xb3, 0xcc, 0xf7, 0x87, 0x2f, 0xf7,
	0x7d, 0xbf, 0xc7, 0x9c, 0xe7, 0xef, 0xf7, 0x94, 0x76, 0x47, 0x6e, 0x55, 0x9b, 0xb2, 0x6f, 0x8e,
	0xce, 0x43, 0xda, 0xc0, 0xcf, 0x4f, 0xe3, 0x67, 0x1c, 0x13, 0x9d, 0x35, 0xf1, 0xe7, 0x21, 0xfd,
	0x94, 0xe0, 0xc7, 0xf1, 0x93, 0x85, 0x89, 0x5e, 0x23, 0xf5, 0xaf, 0x43, 0x86, 0xe5, 0x0b, 0x01,
	0xf8, 0x19, 0x13, 0xde, 0x42, 0x39, 0x48, 0xd7, 0x3a, 0x32, 0xa5, 0xbf, 0x00, 0x45, 0x2e, 0x55,
	0xba, 0x0d, 0xa9, 0x26, 0x09, 0xc9, 0xf2, 0x2d, 0xc8, 0xf2, 0x24, 0xd0, 0xad, 0x11, 0xa6, 0x41,
	0x78, 0xcb, 0x1f, 0xfa, 0x3e, 0x12, 0x81, 0xf6, 0xb8, 0x75, 0x28, 0xc9, 0x42, 0x32, 0xfa, 0x79,
	0x5d, 0x28, 0x46, 0x1b, 0xee, 0x37, 0xc3, 0xa9, 0xbf, 0x24, 0xa0, 0x10, 0x69, 0xa0, 0x69, 0xe7,
	0x83, 0x0d, 0xc3, 0x7a, 0xaa, 0x60, 0x43, 0xc7, 0xae, 0x4f, 0x0a, 0x60, 0xa2, 0x2a, 0x95, 0x9c,
	0xf5, 0xa3, 0xbd, 0x91, 0xe0, 0x7f, 0x9f, 0x00, 0x61, 0xb6, 0x77, 0x9d, 0x09, 0x30, 0xf1, 0xbd,
	0x06, 0xf8, 0xbb, 0x04, 0x94, 0xe2, 0x0d, 0xeb, 0x4c, 0x78, 0x17, 0xbf, 0xd7, 0xf0, 0xfe, 0x99,
	0x84, 0xd5, 0x58, 0x9b, 0x7a, 0xd6, 0xe8, 0xbe, 0x86, 0x75, 0x5d, 0x23, 0x63, 0xdb, 0xf2, 0x88,
	0xa9, 0x9e, 0x2a, 0x06, 0x79, 0x42, 0x0c, 0xb1, 0xcc, 0x0a, 0xc5, 0xf5, 0x97, 0x37, 0xc2, 0x95,
	0xc6, 0xd4, 0xae, 0x49, 0xcd, 0x0e, 0x36, 0x1a, 0x75, 0xa9, 0xd5, 0xed, 0xf4, 0xa5, 0x76, 0xed,
	0xa1, 0x72, 0xdc, 0xfe, 0x71, 0xbb, 0xf3, 0x65, 0x5b, 0x16, 0xf4, 0x19, 0xd8, 0x6b, 0xdc, 0xea,
	0x5d, 0x10, 0x66, 0x83, 0x42, 0xe7, 0x60, 0x51, 0x58, 0xc2, 0x5b, 0x68, 0x03, 0xd6, 0xda, 0x1d,
	0xa5, 0xd7, 0xa8, 0x4b, 0x8a, 0x74, 0xef, 0x9e, 0x54, 0xeb, 0xf7, 0xf8, 0xd3, 0x46, 0x88, 0xee,
	0xc7, 0x37, 0xf5, 0x6f, 0x53, 0xb0, 0xb1, 0x20, 0x12, 0x54, 0xf5, 0x2f, 0x25, 0xfc, 0x9e, 0xf4,
	0xc9, 0x59, 0xa2, 0xaf, 0xd0, 0xae, 0xa0, 0x8b, 0x1d, 0xcf, 0xbf, 0xc3, 0x5c, 0x01, 0x9a, 0x25,
	0xd3, 0xd3, 0x07, 0x3a, 0x71, 0xfc, 0x97, 0x20, 0x7e, 0x53, 0x59, 0x9b, 0xca, 0xf9, 0x63, 0xd0,
	0xc7, 0x80, 0x6c, 0xcb, 0xd5, 0x3d, 0xfd, 0x09, 0x51, 0x74, 0x33, 0x78, 0x36, 0xa2, 0x37, 0x97,
	0xb4, 0x2c, 0x04, 0x9a, 0x86, 0xe9, 0x85, 0x68, 0x93, 0x0c, 0xf1, 0x0c, 0x9a, 0x16, 0xf0, 0x94,
	0x2c, 0x04, 0x9a, 0x10, 0x7d, 0x11, 0x8a, 0x9a, 0x35, 0xa1, 0xed, 0x1c, 0xc7, 0xd1, 0xf3, 0x22,
	0x21, 0x17, 0xb8, 0x2c, 0x84, 0xf8, 0x8d, 0xfa, 0xf4, 0xbd, 0xaa, 0x28, 0x17, 0xb8, 0x8c, 0x43,
	0x2e, 0xc3, 0x1a, 0x1e, 0x0e, 0x1d, 0xea, 0x3c, 0x70, 0xc4, 0xaf, 0x1e, 0xa5, 0x50, 0xcc, 0x80,
	0xdb, 0xf7, 0x21, 0x17, 0xe4, 0x81, 0x1e, 0xc9, 0x34, 0x13, 0x8a, 0xcd, 0xef, 0xd3, 0xc9, 0xdd,
	0xbc, 0x9c, 0x33, 0x03, 0xe5, 0x45, 0x28, 0xea, 0xae, 0x32, 0x7d, 0x7e, 0x4f, 0xee, 0x24, 0x77,
	0x73, 0x72, 0x41, 0x77, 0xc3, 0xa7, 0xcb, 0xf2, 0x37, 0x49, 0x28, 0xc5, 0x7f, 0x3e, 0x40, 0x75,
	0xc8, 0x19, 0x96, 0x8a, 0x19, 0xb5, 0xf8, 0x6f, 0x57, 0xbb, 0xaf, 0xf8, 0xc5, 0xa1, 0xd2, 0xf4,
	0xf1, 0x72, 0x68, 0xb9, 0xfd, 0xb7, 0x04, 0xe4, 0x02, 0x31, 0xda, 0x82, 0xb4, 0x8d, 0xbd, 0x11,
	0x73, 0x97, 0x39, 0x4c, 0x0a, 0x09, 0x99, 0x8d, 0xa9, 0xdc, 0xb5, 0xb1, 0xc9, 0x28, 0xe0, 0xcb,
	0xe9, 0x98, 0x7e, 0x57, 0x83, 0x60, 0x8d, 0xdd, 0x6b, 0xac, 0xf1, 0x98, 0x98, 0x9e, 0x1b, 0x7c,
	0x57, 0x5f, 0x5e, 0xf3, 0xc5, 0xe8, 0x1a, 0xac, 0x7b, 0x0e, 0xd6, 0x8d, 0x18, 0x36, 0xcd, 0xb0,
	0x42, 0xa0, 0x08, 0xc1, 0x07, 0x70, 0x3e, 0xf0, 0xab, 0x11, 0x0f, 0xab, 0x23, 0xa2, 0x4d, 0x8d,
	0xb2, 0xec, 0xfd, 0xe2, 0x9c, 0x0f, 0xa8, 0xfb, 0xfa, 0xc0, 0xb6, 0xfc, 0xf7, 0x04, 0xac, 0x07,
	0x37, 0x31, 0x2d, 0x4c, 0x56, 0x0b, 0x00, 0x9b, 0xa6, 0xe5, 0x45, 0xd3, 0x35, 0x4f, 0xe5, 0x39,
	0xbb, 0x4a, 0x35, 0x34, 0x92, 0x23, 0x0e, 0xb6, 0xc7, 0x00, 0x53, 0xcd, 0xd2, 0xb4, 0x5d, 0x80,
	0x82, 0xff, 0xdb, 0x10, 0xfb, 0x81, 0x91, 0xdf, 0xdd, 0x81, 0x8b, 0xe8, 0x95, 0x0d, 0x6d, 0x42,
	0xe6, 0x84, 0x0c, 0x75, 0xd3, 0x7f, 0xf1, 0xe5, 0x83, 0xe0, 0x85, 0x25, 0x1d, 0xbe, 0xb0, 0x1c,
	0xfe, 0x0c, 0x36, 0x54, 0x6b, 0x3c, 0x1b, 0xee, 0xa1, 0x30, 0xf3, 0x7e, 0xe0, 0x7e, 0x91, 0xf8,
	0x0a, 0xa6, 0x2d, 0xe6, 0xff, 0x12, 0x89, 0x3f, 0x24, 0x53, 0x47, 0xdd, 0xc3, 0x3f, 0x26, 0xb7,
	0x8f, 0xb8, 0x69, 0x37, 0x58, 0xa9, 0x4c, 0x06, 0x06, 0x51, 0x69, 0xf4, 0xff, 0x0f, 0x00, 0x00,
	0xff, 0xff, 0x88, 0x17, 0xc1, 0xbe, 0x38, 0x1d, 0x00, 0x00,
}

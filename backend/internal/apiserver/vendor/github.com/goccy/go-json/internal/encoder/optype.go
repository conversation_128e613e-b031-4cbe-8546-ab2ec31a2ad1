// Code generated by internal/cmd/generator. DO NOT EDIT!
package encoder

import (
	"strings"
)

type CodeType int

const (
	CodeOp          CodeType = 0
	CodeArrayHead   CodeType = 1
	CodeArrayElem   CodeType = 2
	CodeSliceHead   CodeType = 3
	CodeSliceElem   CodeType = 4
	CodeMapHead     CodeType = 5
	CodeMapKey      CodeType = 6
	CodeMapValue    CodeType = 7
	CodeMapEnd      CodeType = 8
	CodeRecursive   CodeType = 9
	CodeStructField CodeType = 10
	CodeStructEnd   CodeType = 11
)

var opTypeStrings = [400]string{
	"End",
	"Interface",
	"Ptr",
	"SliceElem",
	"SliceEnd",
	"ArrayElem",
	"ArrayEnd",
	"MapKey",
	"MapValue",
	"MapEnd",
	"Recursive",
	"RecursivePtr",
	"RecursiveEnd",
	"InterfaceEnd",
	"Int",
	"Uint",
	"Float32",
	"Float64",
	"Bool",
	"String",
	"Bytes",
	"Number",
	"Array",
	"Map",
	"Slice",
	"Struct",
	"MarshalJSON",
	"MarshalText",
	"IntString",
	"UintString",
	"Float32String",
	"Float64String",
	"BoolString",
	"StringString",
	"NumberString",
	"IntPtr",
	"UintPtr",
	"Float32Ptr",
	"Float64Ptr",
	"BoolPtr",
	"StringPtr",
	"BytesPtr",
	"NumberPtr",
	"ArrayPtr",
	"MapPtr",
	"SlicePtr",
	"MarshalJSONPtr",
	"MarshalTextPtr",
	"InterfacePtr",
	"IntPtrString",
	"UintPtrString",
	"Float32PtrString",
	"Float64PtrString",
	"BoolPtrString",
	"StringPtrString",
	"NumberPtrString",
	"StructHeadInt",
	"StructHeadOmitEmptyInt",
	"StructPtrHeadInt",
	"StructPtrHeadOmitEmptyInt",
	"StructHeadUint",
	"StructHeadOmitEmptyUint",
	"StructPtrHeadUint",
	"StructPtrHeadOmitEmptyUint",
	"StructHeadFloat32",
	"StructHeadOmitEmptyFloat32",
	"StructPtrHeadFloat32",
	"StructPtrHeadOmitEmptyFloat32",
	"StructHeadFloat64",
	"StructHeadOmitEmptyFloat64",
	"StructPtrHeadFloat64",
	"StructPtrHeadOmitEmptyFloat64",
	"StructHeadBool",
	"StructHeadOmitEmptyBool",
	"StructPtrHeadBool",
	"StructPtrHeadOmitEmptyBool",
	"StructHeadString",
	"StructHeadOmitEmptyString",
	"StructPtrHeadString",
	"StructPtrHeadOmitEmptyString",
	"StructHeadBytes",
	"StructHeadOmitEmptyBytes",
	"StructPtrHeadBytes",
	"StructPtrHeadOmitEmptyBytes",
	"StructHeadNumber",
	"StructHeadOmitEmptyNumber",
	"StructPtrHeadNumber",
	"StructPtrHeadOmitEmptyNumber",
	"StructHeadArray",
	"StructHeadOmitEmptyArray",
	"StructPtrHeadArray",
	"StructPtrHeadOmitEmptyArray",
	"StructHeadMap",
	"StructHeadOmitEmptyMap",
	"StructPtrHeadMap",
	"StructPtrHeadOmitEmptyMap",
	"StructHeadSlice",
	"StructHeadOmitEmptySlice",
	"StructPtrHeadSlice",
	"StructPtrHeadOmitEmptySlice",
	"StructHeadStruct",
	"StructHeadOmitEmptyStruct",
	"StructPtrHeadStruct",
	"StructPtrHeadOmitEmptyStruct",
	"StructHeadMarshalJSON",
	"StructHeadOmitEmptyMarshalJSON",
	"StructPtrHeadMarshalJSON",
	"StructPtrHeadOmitEmptyMarshalJSON",
	"StructHeadMarshalText",
	"StructHeadOmitEmptyMarshalText",
	"StructPtrHeadMarshalText",
	"StructPtrHeadOmitEmptyMarshalText",
	"StructHeadIntString",
	"StructHeadOmitEmptyIntString",
	"StructPtrHeadIntString",
	"StructPtrHeadOmitEmptyIntString",
	"StructHeadUintString",
	"StructHeadOmitEmptyUintString",
	"StructPtrHeadUintString",
	"StructPtrHeadOmitEmptyUintString",
	"StructHeadFloat32String",
	"StructHeadOmitEmptyFloat32String",
	"StructPtrHeadFloat32String",
	"StructPtrHeadOmitEmptyFloat32String",
	"StructHeadFloat64String",
	"StructHeadOmitEmptyFloat64String",
	"StructPtrHeadFloat64String",
	"StructPtrHeadOmitEmptyFloat64String",
	"StructHeadBoolString",
	"StructHeadOmitEmptyBoolString",
	"StructPtrHeadBoolString",
	"StructPtrHeadOmitEmptyBoolString",
	"StructHeadStringString",
	"StructHeadOmitEmptyStringString",
	"StructPtrHeadStringString",
	"StructPtrHeadOmitEmptyStringString",
	"StructHeadNumberString",
	"StructHeadOmitEmptyNumberString",
	"StructPtrHeadNumberString",
	"StructPtrHeadOmitEmptyNumberString",
	"StructHeadIntPtr",
	"StructHeadOmitEmptyIntPtr",
	"StructPtrHeadIntPtr",
	"StructPtrHeadOmitEmptyIntPtr",
	"StructHeadUintPtr",
	"StructHeadOmitEmptyUintPtr",
	"StructPtrHeadUintPtr",
	"StructPtrHeadOmitEmptyUintPtr",
	"StructHeadFloat32Ptr",
	"StructHeadOmitEmptyFloat32Ptr",
	"StructPtrHeadFloat32Ptr",
	"StructPtrHeadOmitEmptyFloat32Ptr",
	"StructHeadFloat64Ptr",
	"StructHeadOmitEmptyFloat64Ptr",
	"StructPtrHeadFloat64Ptr",
	"StructPtrHeadOmitEmptyFloat64Ptr",
	"StructHeadBoolPtr",
	"StructHeadOmitEmptyBoolPtr",
	"StructPtrHeadBoolPtr",
	"StructPtrHeadOmitEmptyBoolPtr",
	"StructHeadStringPtr",
	"StructHeadOmitEmptyStringPtr",
	"StructPtrHeadStringPtr",
	"StructPtrHeadOmitEmptyStringPtr",
	"StructHeadBytesPtr",
	"StructHeadOmitEmptyBytesPtr",
	"StructPtrHeadBytesPtr",
	"StructPtrHeadOmitEmptyBytesPtr",
	"StructHeadNumberPtr",
	"StructHeadOmitEmptyNumberPtr",
	"StructPtrHeadNumberPtr",
	"StructPtrHeadOmitEmptyNumberPtr",
	"StructHeadArrayPtr",
	"StructHeadOmitEmptyArrayPtr",
	"StructPtrHeadArrayPtr",
	"StructPtrHeadOmitEmptyArrayPtr",
	"StructHeadMapPtr",
	"StructHeadOmitEmptyMapPtr",
	"StructPtrHeadMapPtr",
	"StructPtrHeadOmitEmptyMapPtr",
	"StructHeadSlicePtr",
	"StructHeadOmitEmptySlicePtr",
	"StructPtrHeadSlicePtr",
	"StructPtrHeadOmitEmptySlicePtr",
	"StructHeadMarshalJSONPtr",
	"StructHeadOmitEmptyMarshalJSONPtr",
	"StructPtrHeadMarshalJSONPtr",
	"StructPtrHeadOmitEmptyMarshalJSONPtr",
	"StructHeadMarshalTextPtr",
	"StructHeadOmitEmptyMarshalTextPtr",
	"StructPtrHeadMarshalTextPtr",
	"StructPtrHeadOmitEmptyMarshalTextPtr",
	"StructHeadInterfacePtr",
	"StructHeadOmitEmptyInterfacePtr",
	"StructPtrHeadInterfacePtr",
	"StructPtrHeadOmitEmptyInterfacePtr",
	"StructHeadIntPtrString",
	"StructHeadOmitEmptyIntPtrString",
	"StructPtrHeadIntPtrString",
	"StructPtrHeadOmitEmptyIntPtrString",
	"StructHeadUintPtrString",
	"StructHeadOmitEmptyUintPtrString",
	"StructPtrHeadUintPtrString",
	"StructPtrHeadOmitEmptyUintPtrString",
	"StructHeadFloat32PtrString",
	"StructHeadOmitEmptyFloat32PtrString",
	"StructPtrHeadFloat32PtrString",
	"StructPtrHeadOmitEmptyFloat32PtrString",
	"StructHeadFloat64PtrString",
	"StructHeadOmitEmptyFloat64PtrString",
	"StructPtrHeadFloat64PtrString",
	"StructPtrHeadOmitEmptyFloat64PtrString",
	"StructHeadBoolPtrString",
	"StructHeadOmitEmptyBoolPtrString",
	"StructPtrHeadBoolPtrString",
	"StructPtrHeadOmitEmptyBoolPtrString",
	"StructHeadStringPtrString",
	"StructHeadOmitEmptyStringPtrString",
	"StructPtrHeadStringPtrString",
	"StructPtrHeadOmitEmptyStringPtrString",
	"StructHeadNumberPtrString",
	"StructHeadOmitEmptyNumberPtrString",
	"StructPtrHeadNumberPtrString",
	"StructPtrHeadOmitEmptyNumberPtrString",
	"StructHead",
	"StructHeadOmitEmpty",
	"StructPtrHead",
	"StructPtrHeadOmitEmpty",
	"StructFieldInt",
	"StructFieldOmitEmptyInt",
	"StructEndInt",
	"StructEndOmitEmptyInt",
	"StructFieldUint",
	"StructFieldOmitEmptyUint",
	"StructEndUint",
	"StructEndOmitEmptyUint",
	"StructFieldFloat32",
	"StructFieldOmitEmptyFloat32",
	"StructEndFloat32",
	"StructEndOmitEmptyFloat32",
	"StructFieldFloat64",
	"StructFieldOmitEmptyFloat64",
	"StructEndFloat64",
	"StructEndOmitEmptyFloat64",
	"StructFieldBool",
	"StructFieldOmitEmptyBool",
	"StructEndBool",
	"StructEndOmitEmptyBool",
	"StructFieldString",
	"StructFieldOmitEmptyString",
	"StructEndString",
	"StructEndOmitEmptyString",
	"StructFieldBytes",
	"StructFieldOmitEmptyBytes",
	"StructEndBytes",
	"StructEndOmitEmptyBytes",
	"StructFieldNumber",
	"StructFieldOmitEmptyNumber",
	"StructEndNumber",
	"StructEndOmitEmptyNumber",
	"StructFieldArray",
	"StructFieldOmitEmptyArray",
	"StructEndArray",
	"StructEndOmitEmptyArray",
	"StructFieldMap",
	"StructFieldOmitEmptyMap",
	"StructEndMap",
	"StructEndOmitEmptyMap",
	"StructFieldSlice",
	"StructFieldOmitEmptySlice",
	"StructEndSlice",
	"StructEndOmitEmptySlice",
	"StructFieldStruct",
	"StructFieldOmitEmptyStruct",
	"StructEndStruct",
	"StructEndOmitEmptyStruct",
	"StructFieldMarshalJSON",
	"StructFieldOmitEmptyMarshalJSON",
	"StructEndMarshalJSON",
	"StructEndOmitEmptyMarshalJSON",
	"StructFieldMarshalText",
	"StructFieldOmitEmptyMarshalText",
	"StructEndMarshalText",
	"StructEndOmitEmptyMarshalText",
	"StructFieldIntString",
	"StructFieldOmitEmptyIntString",
	"StructEndIntString",
	"StructEndOmitEmptyIntString",
	"StructFieldUintString",
	"StructFieldOmitEmptyUintString",
	"StructEndUintString",
	"StructEndOmitEmptyUintString",
	"StructFieldFloat32String",
	"StructFieldOmitEmptyFloat32String",
	"StructEndFloat32String",
	"StructEndOmitEmptyFloat32String",
	"StructFieldFloat64String",
	"StructFieldOmitEmptyFloat64String",
	"StructEndFloat64String",
	"StructEndOmitEmptyFloat64String",
	"StructFieldBoolString",
	"StructFieldOmitEmptyBoolString",
	"StructEndBoolString",
	"StructEndOmitEmptyBoolString",
	"StructFieldStringString",
	"StructFieldOmitEmptyStringString",
	"StructEndStringString",
	"StructEndOmitEmptyStringString",
	"StructFieldNumberString",
	"StructFieldOmitEmptyNumberString",
	"StructEndNumberString",
	"StructEndOmitEmptyNumberString",
	"StructFieldIntPtr",
	"StructFieldOmitEmptyIntPtr",
	"StructEndIntPtr",
	"StructEndOmitEmptyIntPtr",
	"StructFieldUintPtr",
	"StructFieldOmitEmptyUintPtr",
	"StructEndUintPtr",
	"StructEndOmitEmptyUintPtr",
	"StructFieldFloat32Ptr",
	"StructFieldOmitEmptyFloat32Ptr",
	"StructEndFloat32Ptr",
	"StructEndOmitEmptyFloat32Ptr",
	"StructFieldFloat64Ptr",
	"StructFieldOmitEmptyFloat64Ptr",
	"StructEndFloat64Ptr",
	"StructEndOmitEmptyFloat64Ptr",
	"StructFieldBoolPtr",
	"StructFieldOmitEmptyBoolPtr",
	"StructEndBoolPtr",
	"StructEndOmitEmptyBoolPtr",
	"StructFieldStringPtr",
	"StructFieldOmitEmptyStringPtr",
	"StructEndStringPtr",
	"StructEndOmitEmptyStringPtr",
	"StructFieldBytesPtr",
	"StructFieldOmitEmptyBytesPtr",
	"StructEndBytesPtr",
	"StructEndOmitEmptyBytesPtr",
	"StructFieldNumberPtr",
	"StructFieldOmitEmptyNumberPtr",
	"StructEndNumberPtr",
	"StructEndOmitEmptyNumberPtr",
	"StructFieldArrayPtr",
	"StructFieldOmitEmptyArrayPtr",
	"StructEndArrayPtr",
	"StructEndOmitEmptyArrayPtr",
	"StructFieldMapPtr",
	"StructFieldOmitEmptyMapPtr",
	"StructEndMapPtr",
	"StructEndOmitEmptyMapPtr",
	"StructFieldSlicePtr",
	"StructFieldOmitEmptySlicePtr",
	"StructEndSlicePtr",
	"StructEndOmitEmptySlicePtr",
	"StructFieldMarshalJSONPtr",
	"StructFieldOmitEmptyMarshalJSONPtr",
	"StructEndMarshalJSONPtr",
	"StructEndOmitEmptyMarshalJSONPtr",
	"StructFieldMarshalTextPtr",
	"StructFieldOmitEmptyMarshalTextPtr",
	"StructEndMarshalTextPtr",
	"StructEndOmitEmptyMarshalTextPtr",
	"StructFieldInterfacePtr",
	"StructFieldOmitEmptyInterfacePtr",
	"StructEndInterfacePtr",
	"StructEndOmitEmptyInterfacePtr",
	"StructFieldIntPtrString",
	"StructFieldOmitEmptyIntPtrString",
	"StructEndIntPtrString",
	"StructEndOmitEmptyIntPtrString",
	"StructFieldUintPtrString",
	"StructFieldOmitEmptyUintPtrString",
	"StructEndUintPtrString",
	"StructEndOmitEmptyUintPtrString",
	"StructFieldFloat32PtrString",
	"StructFieldOmitEmptyFloat32PtrString",
	"StructEndFloat32PtrString",
	"StructEndOmitEmptyFloat32PtrString",
	"StructFieldFloat64PtrString",
	"StructFieldOmitEmptyFloat64PtrString",
	"StructEndFloat64PtrString",
	"StructEndOmitEmptyFloat64PtrString",
	"StructFieldBoolPtrString",
	"StructFieldOmitEmptyBoolPtrString",
	"StructEndBoolPtrString",
	"StructEndOmitEmptyBoolPtrString",
	"StructFieldStringPtrString",
	"StructFieldOmitEmptyStringPtrString",
	"StructEndStringPtrString",
	"StructEndOmitEmptyStringPtrString",
	"StructFieldNumberPtrString",
	"StructFieldOmitEmptyNumberPtrString",
	"StructEndNumberPtrString",
	"StructEndOmitEmptyNumberPtrString",
	"StructField",
	"StructFieldOmitEmpty",
	"StructEnd",
	"StructEndOmitEmpty",
}

type OpType uint16

const (
	OpEnd                                    OpType = 0
	OpInterface                              OpType = 1
	OpPtr                                    OpType = 2
	OpSliceElem                              OpType = 3
	OpSliceEnd                               OpType = 4
	OpArrayElem                              OpType = 5
	OpArrayEnd                               OpType = 6
	OpMapKey                                 OpType = 7
	OpMapValue                               OpType = 8
	OpMapEnd                                 OpType = 9
	OpRecursive                              OpType = 10
	OpRecursivePtr                           OpType = 11
	OpRecursiveEnd                           OpType = 12
	OpInterfaceEnd                           OpType = 13
	OpInt                                    OpType = 14
	OpUint                                   OpType = 15
	OpFloat32                                OpType = 16
	OpFloat64                                OpType = 17
	OpBool                                   OpType = 18
	OpString                                 OpType = 19
	OpBytes                                  OpType = 20
	OpNumber                                 OpType = 21
	OpArray                                  OpType = 22
	OpMap                                    OpType = 23
	OpSlice                                  OpType = 24
	OpStruct                                 OpType = 25
	OpMarshalJSON                            OpType = 26
	OpMarshalText                            OpType = 27
	OpIntString                              OpType = 28
	OpUintString                             OpType = 29
	OpFloat32String                          OpType = 30
	OpFloat64String                          OpType = 31
	OpBoolString                             OpType = 32
	OpStringString                           OpType = 33
	OpNumberString                           OpType = 34
	OpIntPtr                                 OpType = 35
	OpUintPtr                                OpType = 36
	OpFloat32Ptr                             OpType = 37
	OpFloat64Ptr                             OpType = 38
	OpBoolPtr                                OpType = 39
	OpStringPtr                              OpType = 40
	OpBytesPtr                               OpType = 41
	OpNumberPtr                              OpType = 42
	OpArrayPtr                               OpType = 43
	OpMapPtr                                 OpType = 44
	OpSlicePtr                               OpType = 45
	OpMarshalJSONPtr                         OpType = 46
	OpMarshalTextPtr                         OpType = 47
	OpInterfacePtr                           OpType = 48
	OpIntPtrString                           OpType = 49
	OpUintPtrString                          OpType = 50
	OpFloat32PtrString                       OpType = 51
	OpFloat64PtrString                       OpType = 52
	OpBoolPtrString                          OpType = 53
	OpStringPtrString                        OpType = 54
	OpNumberPtrString                        OpType = 55
	OpStructHeadInt                          OpType = 56
	OpStructHeadOmitEmptyInt                 OpType = 57
	OpStructPtrHeadInt                       OpType = 58
	OpStructPtrHeadOmitEmptyInt              OpType = 59
	OpStructHeadUint                         OpType = 60
	OpStructHeadOmitEmptyUint                OpType = 61
	OpStructPtrHeadUint                      OpType = 62
	OpStructPtrHeadOmitEmptyUint             OpType = 63
	OpStructHeadFloat32                      OpType = 64
	OpStructHeadOmitEmptyFloat32             OpType = 65
	OpStructPtrHeadFloat32                   OpType = 66
	OpStructPtrHeadOmitEmptyFloat32          OpType = 67
	OpStructHeadFloat64                      OpType = 68
	OpStructHeadOmitEmptyFloat64             OpType = 69
	OpStructPtrHeadFloat64                   OpType = 70
	OpStructPtrHeadOmitEmptyFloat64          OpType = 71
	OpStructHeadBool                         OpType = 72
	OpStructHeadOmitEmptyBool                OpType = 73
	OpStructPtrHeadBool                      OpType = 74
	OpStructPtrHeadOmitEmptyBool             OpType = 75
	OpStructHeadString                       OpType = 76
	OpStructHeadOmitEmptyString              OpType = 77
	OpStructPtrHeadString                    OpType = 78
	OpStructPtrHeadOmitEmptyString           OpType = 79
	OpStructHeadBytes                        OpType = 80
	OpStructHeadOmitEmptyBytes               OpType = 81
	OpStructPtrHeadBytes                     OpType = 82
	OpStructPtrHeadOmitEmptyBytes            OpType = 83
	OpStructHeadNumber                       OpType = 84
	OpStructHeadOmitEmptyNumber              OpType = 85
	OpStructPtrHeadNumber                    OpType = 86
	OpStructPtrHeadOmitEmptyNumber           OpType = 87
	OpStructHeadArray                        OpType = 88
	OpStructHeadOmitEmptyArray               OpType = 89
	OpStructPtrHeadArray                     OpType = 90
	OpStructPtrHeadOmitEmptyArray            OpType = 91
	OpStructHeadMap                          OpType = 92
	OpStructHeadOmitEmptyMap                 OpType = 93
	OpStructPtrHeadMap                       OpType = 94
	OpStructPtrHeadOmitEmptyMap              OpType = 95
	OpStructHeadSlice                        OpType = 96
	OpStructHeadOmitEmptySlice               OpType = 97
	OpStructPtrHeadSlice                     OpType = 98
	OpStructPtrHeadOmitEmptySlice            OpType = 99
	OpStructHeadStruct                       OpType = 100
	OpStructHeadOmitEmptyStruct              OpType = 101
	OpStructPtrHeadStruct                    OpType = 102
	OpStructPtrHeadOmitEmptyStruct           OpType = 103
	OpStructHeadMarshalJSON                  OpType = 104
	OpStructHeadOmitEmptyMarshalJSON         OpType = 105
	OpStructPtrHeadMarshalJSON               OpType = 106
	OpStructPtrHeadOmitEmptyMarshalJSON      OpType = 107
	OpStructHeadMarshalText                  OpType = 108
	OpStructHeadOmitEmptyMarshalText         OpType = 109
	OpStructPtrHeadMarshalText               OpType = 110
	OpStructPtrHeadOmitEmptyMarshalText      OpType = 111
	OpStructHeadIntString                    OpType = 112
	OpStructHeadOmitEmptyIntString           OpType = 113
	OpStructPtrHeadIntString                 OpType = 114
	OpStructPtrHeadOmitEmptyIntString        OpType = 115
	OpStructHeadUintString                   OpType = 116
	OpStructHeadOmitEmptyUintString          OpType = 117
	OpStructPtrHeadUintString                OpType = 118
	OpStructPtrHeadOmitEmptyUintString       OpType = 119
	OpStructHeadFloat32String                OpType = 120
	OpStructHeadOmitEmptyFloat32String       OpType = 121
	OpStructPtrHeadFloat32String             OpType = 122
	OpStructPtrHeadOmitEmptyFloat32String    OpType = 123
	OpStructHeadFloat64String                OpType = 124
	OpStructHeadOmitEmptyFloat64String       OpType = 125
	OpStructPtrHeadFloat64String             OpType = 126
	OpStructPtrHeadOmitEmptyFloat64String    OpType = 127
	OpStructHeadBoolString                   OpType = 128
	OpStructHeadOmitEmptyBoolString          OpType = 129
	OpStructPtrHeadBoolString                OpType = 130
	OpStructPtrHeadOmitEmptyBoolString       OpType = 131
	OpStructHeadStringString                 OpType = 132
	OpStructHeadOmitEmptyStringString        OpType = 133
	OpStructPtrHeadStringString              OpType = 134
	OpStructPtrHeadOmitEmptyStringString     OpType = 135
	OpStructHeadNumberString                 OpType = 136
	OpStructHeadOmitEmptyNumberString        OpType = 137
	OpStructPtrHeadNumberString              OpType = 138
	OpStructPtrHeadOmitEmptyNumberString     OpType = 139
	OpStructHeadIntPtr                       OpType = 140
	OpStructHeadOmitEmptyIntPtr              OpType = 141
	OpStructPtrHeadIntPtr                    OpType = 142
	OpStructPtrHeadOmitEmptyIntPtr           OpType = 143
	OpStructHeadUintPtr                      OpType = 144
	OpStructHeadOmitEmptyUintPtr             OpType = 145
	OpStructPtrHeadUintPtr                   OpType = 146
	OpStructPtrHeadOmitEmptyUintPtr          OpType = 147
	OpStructHeadFloat32Ptr                   OpType = 148
	OpStructHeadOmitEmptyFloat32Ptr          OpType = 149
	OpStructPtrHeadFloat32Ptr                OpType = 150
	OpStructPtrHeadOmitEmptyFloat32Ptr       OpType = 151
	OpStructHeadFloat64Ptr                   OpType = 152
	OpStructHeadOmitEmptyFloat64Ptr          OpType = 153
	OpStructPtrHeadFloat64Ptr                OpType = 154
	OpStructPtrHeadOmitEmptyFloat64Ptr       OpType = 155
	OpStructHeadBoolPtr                      OpType = 156
	OpStructHeadOmitEmptyBoolPtr             OpType = 157
	OpStructPtrHeadBoolPtr                   OpType = 158
	OpStructPtrHeadOmitEmptyBoolPtr          OpType = 159
	OpStructHeadStringPtr                    OpType = 160
	OpStructHeadOmitEmptyStringPtr           OpType = 161
	OpStructPtrHeadStringPtr                 OpType = 162
	OpStructPtrHeadOmitEmptyStringPtr        OpType = 163
	OpStructHeadBytesPtr                     OpType = 164
	OpStructHeadOmitEmptyBytesPtr            OpType = 165
	OpStructPtrHeadBytesPtr                  OpType = 166
	OpStructPtrHeadOmitEmptyBytesPtr         OpType = 167
	OpStructHeadNumberPtr                    OpType = 168
	OpStructHeadOmitEmptyNumberPtr           OpType = 169
	OpStructPtrHeadNumberPtr                 OpType = 170
	OpStructPtrHeadOmitEmptyNumberPtr        OpType = 171
	OpStructHeadArrayPtr                     OpType = 172
	OpStructHeadOmitEmptyArrayPtr            OpType = 173
	OpStructPtrHeadArrayPtr                  OpType = 174
	OpStructPtrHeadOmitEmptyArrayPtr         OpType = 175
	OpStructHeadMapPtr                       OpType = 176
	OpStructHeadOmitEmptyMapPtr              OpType = 177
	OpStructPtrHeadMapPtr                    OpType = 178
	OpStructPtrHeadOmitEmptyMapPtr           OpType = 179
	OpStructHeadSlicePtr                     OpType = 180
	OpStructHeadOmitEmptySlicePtr            OpType = 181
	OpStructPtrHeadSlicePtr                  OpType = 182
	OpStructPtrHeadOmitEmptySlicePtr         OpType = 183
	OpStructHeadMarshalJSONPtr               OpType = 184
	OpStructHeadOmitEmptyMarshalJSONPtr      OpType = 185
	OpStructPtrHeadMarshalJSONPtr            OpType = 186
	OpStructPtrHeadOmitEmptyMarshalJSONPtr   OpType = 187
	OpStructHeadMarshalTextPtr               OpType = 188
	OpStructHeadOmitEmptyMarshalTextPtr      OpType = 189
	OpStructPtrHeadMarshalTextPtr            OpType = 190
	OpStructPtrHeadOmitEmptyMarshalTextPtr   OpType = 191
	OpStructHeadInterfacePtr                 OpType = 192
	OpStructHeadOmitEmptyInterfacePtr        OpType = 193
	OpStructPtrHeadInterfacePtr              OpType = 194
	OpStructPtrHeadOmitEmptyInterfacePtr     OpType = 195
	OpStructHeadIntPtrString                 OpType = 196
	OpStructHeadOmitEmptyIntPtrString        OpType = 197
	OpStructPtrHeadIntPtrString              OpType = 198
	OpStructPtrHeadOmitEmptyIntPtrString     OpType = 199
	OpStructHeadUintPtrString                OpType = 200
	OpStructHeadOmitEmptyUintPtrString       OpType = 201
	OpStructPtrHeadUintPtrString             OpType = 202
	OpStructPtrHeadOmitEmptyUintPtrString    OpType = 203
	OpStructHeadFloat32PtrString             OpType = 204
	OpStructHeadOmitEmptyFloat32PtrString    OpType = 205
	OpStructPtrHeadFloat32PtrString          OpType = 206
	OpStructPtrHeadOmitEmptyFloat32PtrString OpType = 207
	OpStructHeadFloat64PtrString             OpType = 208
	OpStructHeadOmitEmptyFloat64PtrString    OpType = 209
	OpStructPtrHeadFloat64PtrString          OpType = 210
	OpStructPtrHeadOmitEmptyFloat64PtrString OpType = 211
	OpStructHeadBoolPtrString                OpType = 212
	OpStructHeadOmitEmptyBoolPtrString       OpType = 213
	OpStructPtrHeadBoolPtrString             OpType = 214
	OpStructPtrHeadOmitEmptyBoolPtrString    OpType = 215
	OpStructHeadStringPtrString              OpType = 216
	OpStructHeadOmitEmptyStringPtrString     OpType = 217
	OpStructPtrHeadStringPtrString           OpType = 218
	OpStructPtrHeadOmitEmptyStringPtrString  OpType = 219
	OpStructHeadNumberPtrString              OpType = 220
	OpStructHeadOmitEmptyNumberPtrString     OpType = 221
	OpStructPtrHeadNumberPtrString           OpType = 222
	OpStructPtrHeadOmitEmptyNumberPtrString  OpType = 223
	OpStructHead                             OpType = 224
	OpStructHeadOmitEmpty                    OpType = 225
	OpStructPtrHead                          OpType = 226
	OpStructPtrHeadOmitEmpty                 OpType = 227
	OpStructFieldInt                         OpType = 228
	OpStructFieldOmitEmptyInt                OpType = 229
	OpStructEndInt                           OpType = 230
	OpStructEndOmitEmptyInt                  OpType = 231
	OpStructFieldUint                        OpType = 232
	OpStructFieldOmitEmptyUint               OpType = 233
	OpStructEndUint                          OpType = 234
	OpStructEndOmitEmptyUint                 OpType = 235
	OpStructFieldFloat32                     OpType = 236
	OpStructFieldOmitEmptyFloat32            OpType = 237
	OpStructEndFloat32                       OpType = 238
	OpStructEndOmitEmptyFloat32              OpType = 239
	OpStructFieldFloat64                     OpType = 240
	OpStructFieldOmitEmptyFloat64            OpType = 241
	OpStructEndFloat64                       OpType = 242
	OpStructEndOmitEmptyFloat64              OpType = 243
	OpStructFieldBool                        OpType = 244
	OpStructFieldOmitEmptyBool               OpType = 245
	OpStructEndBool                          OpType = 246
	OpStructEndOmitEmptyBool                 OpType = 247
	OpStructFieldString                      OpType = 248
	OpStructFieldOmitEmptyString             OpType = 249
	OpStructEndString                        OpType = 250
	OpStructEndOmitEmptyString               OpType = 251
	OpStructFieldBytes                       OpType = 252
	OpStructFieldOmitEmptyBytes              OpType = 253
	OpStructEndBytes                         OpType = 254
	OpStructEndOmitEmptyBytes                OpType = 255
	OpStructFieldNumber                      OpType = 256
	OpStructFieldOmitEmptyNumber             OpType = 257
	OpStructEndNumber                        OpType = 258
	OpStructEndOmitEmptyNumber               OpType = 259
	OpStructFieldArray                       OpType = 260
	OpStructFieldOmitEmptyArray              OpType = 261
	OpStructEndArray                         OpType = 262
	OpStructEndOmitEmptyArray                OpType = 263
	OpStructFieldMap                         OpType = 264
	OpStructFieldOmitEmptyMap                OpType = 265
	OpStructEndMap                           OpType = 266
	OpStructEndOmitEmptyMap                  OpType = 267
	OpStructFieldSlice                       OpType = 268
	OpStructFieldOmitEmptySlice              OpType = 269
	OpStructEndSlice                         OpType = 270
	OpStructEndOmitEmptySlice                OpType = 271
	OpStructFieldStruct                      OpType = 272
	OpStructFieldOmitEmptyStruct             OpType = 273
	OpStructEndStruct                        OpType = 274
	OpStructEndOmitEmptyStruct               OpType = 275
	OpStructFieldMarshalJSON                 OpType = 276
	OpStructFieldOmitEmptyMarshalJSON        OpType = 277
	OpStructEndMarshalJSON                   OpType = 278
	OpStructEndOmitEmptyMarshalJSON          OpType = 279
	OpStructFieldMarshalText                 OpType = 280
	OpStructFieldOmitEmptyMarshalText        OpType = 281
	OpStructEndMarshalText                   OpType = 282
	OpStructEndOmitEmptyMarshalText          OpType = 283
	OpStructFieldIntString                   OpType = 284
	OpStructFieldOmitEmptyIntString          OpType = 285
	OpStructEndIntString                     OpType = 286
	OpStructEndOmitEmptyIntString            OpType = 287
	OpStructFieldUintString                  OpType = 288
	OpStructFieldOmitEmptyUintString         OpType = 289
	OpStructEndUintString                    OpType = 290
	OpStructEndOmitEmptyUintString           OpType = 291
	OpStructFieldFloat32String               OpType = 292
	OpStructFieldOmitEmptyFloat32String      OpType = 293
	OpStructEndFloat32String                 OpType = 294
	OpStructEndOmitEmptyFloat32String        OpType = 295
	OpStructFieldFloat64String               OpType = 296
	OpStructFieldOmitEmptyFloat64String      OpType = 297
	OpStructEndFloat64String                 OpType = 298
	OpStructEndOmitEmptyFloat64String        OpType = 299
	OpStructFieldBoolString                  OpType = 300
	OpStructFieldOmitEmptyBoolString         OpType = 301
	OpStructEndBoolString                    OpType = 302
	OpStructEndOmitEmptyBoolString           OpType = 303
	OpStructFieldStringString                OpType = 304
	OpStructFieldOmitEmptyStringString       OpType = 305
	OpStructEndStringString                  OpType = 306
	OpStructEndOmitEmptyStringString         OpType = 307
	OpStructFieldNumberString                OpType = 308
	OpStructFieldOmitEmptyNumberString       OpType = 309
	OpStructEndNumberString                  OpType = 310
	OpStructEndOmitEmptyNumberString         OpType = 311
	OpStructFieldIntPtr                      OpType = 312
	OpStructFieldOmitEmptyIntPtr             OpType = 313
	OpStructEndIntPtr                        OpType = 314
	OpStructEndOmitEmptyIntPtr               OpType = 315
	OpStructFieldUintPtr                     OpType = 316
	OpStructFieldOmitEmptyUintPtr            OpType = 317
	OpStructEndUintPtr                       OpType = 318
	OpStructEndOmitEmptyUintPtr              OpType = 319
	OpStructFieldFloat32Ptr                  OpType = 320
	OpStructFieldOmitEmptyFloat32Ptr         OpType = 321
	OpStructEndFloat32Ptr                    OpType = 322
	OpStructEndOmitEmptyFloat32Ptr           OpType = 323
	OpStructFieldFloat64Ptr                  OpType = 324
	OpStructFieldOmitEmptyFloat64Ptr         OpType = 325
	OpStructEndFloat64Ptr                    OpType = 326
	OpStructEndOmitEmptyFloat64Ptr           OpType = 327
	OpStructFieldBoolPtr                     OpType = 328
	OpStructFieldOmitEmptyBoolPtr            OpType = 329
	OpStructEndBoolPtr                       OpType = 330
	OpStructEndOmitEmptyBoolPtr              OpType = 331
	OpStructFieldStringPtr                   OpType = 332
	OpStructFieldOmitEmptyStringPtr          OpType = 333
	OpStructEndStringPtr                     OpType = 334
	OpStructEndOmitEmptyStringPtr            OpType = 335
	OpStructFieldBytesPtr                    OpType = 336
	OpStructFieldOmitEmptyBytesPtr           OpType = 337
	OpStructEndBytesPtr                      OpType = 338
	OpStructEndOmitEmptyBytesPtr             OpType = 339
	OpStructFieldNumberPtr                   OpType = 340
	OpStructFieldOmitEmptyNumberPtr          OpType = 341
	OpStructEndNumberPtr                     OpType = 342
	OpStructEndOmitEmptyNumberPtr            OpType = 343
	OpStructFieldArrayPtr                    OpType = 344
	OpStructFieldOmitEmptyArrayPtr           OpType = 345
	OpStructEndArrayPtr                      OpType = 346
	OpStructEndOmitEmptyArrayPtr             OpType = 347
	OpStructFieldMapPtr                      OpType = 348
	OpStructFieldOmitEmptyMapPtr             OpType = 349
	OpStructEndMapPtr                        OpType = 350
	OpStructEndOmitEmptyMapPtr               OpType = 351
	OpStructFieldSlicePtr                    OpType = 352
	OpStructFieldOmitEmptySlicePtr           OpType = 353
	OpStructEndSlicePtr                      OpType = 354
	OpStructEndOmitEmptySlicePtr             OpType = 355
	OpStructFieldMarshalJSONPtr              OpType = 356
	OpStructFieldOmitEmptyMarshalJSONPtr     OpType = 357
	OpStructEndMarshalJSONPtr                OpType = 358
	OpStructEndOmitEmptyMarshalJSONPtr       OpType = 359
	OpStructFieldMarshalTextPtr              OpType = 360
	OpStructFieldOmitEmptyMarshalTextPtr     OpType = 361
	OpStructEndMarshalTextPtr                OpType = 362
	OpStructEndOmitEmptyMarshalTextPtr       OpType = 363
	OpStructFieldInterfacePtr                OpType = 364
	OpStructFieldOmitEmptyInterfacePtr       OpType = 365
	OpStructEndInterfacePtr                  OpType = 366
	OpStructEndOmitEmptyInterfacePtr         OpType = 367
	OpStructFieldIntPtrString                OpType = 368
	OpStructFieldOmitEmptyIntPtrString       OpType = 369
	OpStructEndIntPtrString                  OpType = 370
	OpStructEndOmitEmptyIntPtrString         OpType = 371
	OpStructFieldUintPtrString               OpType = 372
	OpStructFieldOmitEmptyUintPtrString      OpType = 373
	OpStructEndUintPtrString                 OpType = 374
	OpStructEndOmitEmptyUintPtrString        OpType = 375
	OpStructFieldFloat32PtrString            OpType = 376
	OpStructFieldOmitEmptyFloat32PtrString   OpType = 377
	OpStructEndFloat32PtrString              OpType = 378
	OpStructEndOmitEmptyFloat32PtrString     OpType = 379
	OpStructFieldFloat64PtrString            OpType = 380
	OpStructFieldOmitEmptyFloat64PtrString   OpType = 381
	OpStructEndFloat64PtrString              OpType = 382
	OpStructEndOmitEmptyFloat64PtrString     OpType = 383
	OpStructFieldBoolPtrString               OpType = 384
	OpStructFieldOmitEmptyBoolPtrString      OpType = 385
	OpStructEndBoolPtrString                 OpType = 386
	OpStructEndOmitEmptyBoolPtrString        OpType = 387
	OpStructFieldStringPtrString             OpType = 388
	OpStructFieldOmitEmptyStringPtrString    OpType = 389
	OpStructEndStringPtrString               OpType = 390
	OpStructEndOmitEmptyStringPtrString      OpType = 391
	OpStructFieldNumberPtrString             OpType = 392
	OpStructFieldOmitEmptyNumberPtrString    OpType = 393
	OpStructEndNumberPtrString               OpType = 394
	OpStructEndOmitEmptyNumberPtrString      OpType = 395
	OpStructField                            OpType = 396
	OpStructFieldOmitEmpty                   OpType = 397
	OpStructEnd                              OpType = 398
	OpStructEndOmitEmpty                     OpType = 399
)

func (t OpType) String() string {
	if int(t) >= 400 {
		return ""
	}
	return opTypeStrings[int(t)]
}

func (t OpType) CodeType() CodeType {
	if strings.Contains(t.String(), "Struct") {
		if strings.Contains(t.String(), "End") {
			return CodeStructEnd
		}
		return CodeStructField
	}
	switch t {
	case OpArray, OpArrayPtr:
		return CodeArrayHead
	case OpArrayElem:
		return CodeArrayElem
	case OpSlice, OpSlicePtr:
		return CodeSliceHead
	case OpSliceElem:
		return CodeSliceElem
	case OpMap, OpMapPtr:
		return CodeMapHead
	case OpMapKey:
		return CodeMapKey
	case OpMapValue:
		return CodeMapValue
	case OpMapEnd:
		return CodeMapEnd
	}

	return CodeOp
}

func (t OpType) HeadToPtrHead() OpType {
	if strings.Index(t.String(), "PtrHead") > 0 {
		return t
	}

	idx := strings.Index(t.String(), "Head")
	if idx == -1 {
		return t
	}
	suffix := "PtrHead" + t.String()[idx+len("Head"):]

	const toPtrOffset = 2
	if strings.Contains(OpType(int(t)+toPtrOffset).String(), suffix) {
		return OpType(int(t) + toPtrOffset)
	}
	return t
}

func (t OpType) HeadToOmitEmptyHead() OpType {
	const toOmitEmptyOffset = 1
	if strings.Contains(OpType(int(t)+toOmitEmptyOffset).String(), "OmitEmpty") {
		return OpType(int(t) + toOmitEmptyOffset)
	}

	return t
}

func (t OpType) PtrHeadToHead() OpType {
	idx := strings.Index(t.String(), "PtrHead")
	if idx == -1 {
		return t
	}
	suffix := t.String()[idx+len("Ptr"):]

	const toPtrOffset = 2
	if strings.Contains(OpType(int(t)-toPtrOffset).String(), suffix) {
		return OpType(int(t) - toPtrOffset)
	}
	return t
}

func (t OpType) FieldToEnd() OpType {
	idx := strings.Index(t.String(), "Field")
	if idx == -1 {
		return t
	}
	suffix := t.String()[idx+len("Field"):]
	if suffix == "" || suffix == "OmitEmpty" {
		return t
	}
	const toEndOffset = 2
	if strings.Contains(OpType(int(t)+toEndOffset).String(), "End"+suffix) {
		return OpType(int(t) + toEndOffset)
	}
	return t
}

func (t OpType) FieldToOmitEmptyField() OpType {
	const toOmitEmptyOffset = 1
	if strings.Contains(OpType(int(t)+toOmitEmptyOffset).String(), "OmitEmpty") {
		return OpType(int(t) + toOmitEmptyOffset)
	}
	return t
}

package store

import (
	"context"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

type RepositoryPoolStore interface {
	Create(ctx context.Context, repoId string, repo *domain.RepositoryPool, opts metav1.CreateOptions) error
	Update(ctx context.Context, repoId string, repo *domain.RepositoryPool, opts metav1.UpdateOptions) error
	Get(ctx context.Context, repoId string, opts metav1.GetOptions) (*domain.RepositoryPool, error)
	List(ctx context.Context, opts metav1.ListOptions) (*domain.RepositoryPoolList, error)
	Delete(ctx context.Context, repoId string, opts metav1.DeleteOptions) error
	ListStatus(ctx context.Context, opts metav1.ListOptions) ([]domain.StatusEntry, error)
}

// Package store
/**
* @Project : terravirtualmachine
* @File    : network.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/21 09:16
**/

package store

import (
	"context"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

type NetworkStore interface {
	Create(ctx context.Context, network *domain.Network, opts metav1.CreateOptions) error
	Update(ctx context.Context, network *domain.Network, opts metav1.UpdateOptions) error
	Delete(ctx context.Context, networkID string, opts metav1.DeleteOptions) (*domain.Network, error)
	Get(ctx context.Context, networkID string, opts metav1.GetOptions) (*domain.Network, error)
	GetInterface(ctx context.Context, hostID string, ifaceID string, opts metav1.GetOptions) (*domain.Interface, error)
	List(ctx context.Context, opts metav1.ListOptions) (*domain.NetworkList, error)
	ListHosts(ctx context.Context, opts metav1.ListOptions) (*domain.NetworkHostList, error)
	ListInterfaces(ctx context.Context, hostID string, opts metav1.ListOptions) (*domain.InterfaceList, error)
	ListVNics(ctx context.Context, opts metav1.ListOptions) (*domain.VNicList, error)
}

package store

import (
	"context"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

type Log interface {
	Create(ctx context.Context, disk *domain.LogEntry, opts metav1.CreateOptions) error
	Clear(ctx context.Context, del *domain.LogDel, opts metav1.DeleteOptions) error
	List(ctx context.Context, opts metav1.ListOptions) (*domain.LogList, error)
	GetAddLen(ctx context.Context, opts metav1.GetOptions) (int, error)
	GetDelLen(ctx context.Context, opts metav1.GetOptions) (int, error)
	GetAddLog(ctx context.Context, idx string, opts metav1.GetOptions) (*domain.LogEntry, error)
	GetDelLog(ctx context.Context, idx string, opts metav1.GetOptions) (*domain.LogDel, error)
}

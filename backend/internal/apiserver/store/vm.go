package store

import (
	"context"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

type VMStore interface {
	List(ctx context.Context, opts metav1.ListOptions) (*domain.VMInstanceList, error)
	Get(ctx context.Context, guestId string, opts metav1.GetOptions) (*domain.VMInstance, error)
	Create(ctx context.Context, guestId string, ins *domain.VMInstance, opts metav1.CreateOptions) error
	Update(ctx context.Context, guestId string, ins *domain.VMInstance, opts metav1.CreateOptions) error
	Delete(ctx context.Context, guestId string, opts metav1.DeleteOptions) error

	ListStatus(ctx context.Context, opts metav1.ListOptions) ([]domain.StatusEntry, error)
	// PostStatus(ctx context.Context, status *domain.StatusEntry, opts metav1.ListOptions) ([]domain.StatusEntry, error)
}

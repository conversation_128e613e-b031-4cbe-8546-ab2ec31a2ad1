package etcd

import (
	"context"
	"os"
	"os/exec"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

var testKeyPrefix = "/twm/test1"

func TestMain(m *testing.M) {
	err := os.Setenv("ETCDCTL_API", "3")
	if err != nil {
		panic(err)
	}
	os.Exit(m.Run())
}

func TestGetEtcdFactoryOr(t *testing.T) {
	tests := []struct {
		name               string
		opts               *options.EtcdOptions
		onKeepaliveFailure func()
	}{
		{
			name: "test1",
			opts: &options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      testKeyPrefix,
				RequestTimeout: 2,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			etcd, err := GetEtcdFactoryOr(tt.opts, tt.onKeepaliveFailure)
			assert.NoError(t, err)
			assert.NotNil(t, etcd)
		})
	}
}

func TestDatastore_Put(t *testing.T) {
	tests := []struct {
		name  string
		key   string
		val   string
		after func(t *testing.T)
	}{
		{
			name: "test1",
			key:  "/name",
			val:  "tvm",
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", testKeyPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			etcd, err := GetEtcdFactoryOr(getTestEtcdOptions(), nil)
			assert.NoError(t, err)

			ds, ok := etcd.(*datastore)
			assert.True(t, ok)

			err = ds.Put(context.Background(), tt.key, tt.val)
			assert.NoError(t, err)

			out, err := exec.Command("etcdctl",
				"--endpoints=http://127.0.0.1:2379",
				"get", testKeyPrefix+tt.key, "--prefix").Output()
			assert.NoError(t, err)

			lines := strings.Split(string(out), "\n")
			if len(lines) == 3 {
				actual := strings.TrimSpace(lines[1])
				assert.Equal(t, tt.val, actual)
			} else {
				t.Errorf("Unexpected output format: %s", string(out))
			}

			defer tt.after(t)
		})
	}
}

func TestDatastore_PutSession(t *testing.T) {
	tests := []struct {
		name  string
		key   string
		val   string
		after func(t *testing.T)
	}{
		{
			name: "test1",
			key:  "/name",
			val:  "tvm",
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", testKeyPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			etcd, err := GetEtcdFactoryOr(getTestEtcdOptions(), nil)
			assert.NoError(t, err)

			ds, ok := etcd.(*datastore)
			assert.True(t, ok)

			err = ds.PutSession(context.Background(), tt.key, tt.val)
			assert.NoError(t, err)

			// 1、验证数据是否正确存储
			out, err := exec.Command("etcdctl",
				"--endpoints=http://127.0.0.1:2379",
				"get", testKeyPrefix+tt.key, "--prefix").Output()
			assert.NoError(t, err)

			lines := strings.Split(string(out), "\n")
			if len(lines) == 3 {
				actual := strings.TrimSpace(lines[1])
				assert.Equal(t, tt.val, actual)
			} else {
				t.Errorf("Unexpected output format: %s", string(out))
			}

			// 2、验证租约到期后，数据是否被删除
			// 因为有开启自动续约，所以租约到期后，数据应该还是存在的
			defaultLeaseExpire := 5
			time.Sleep(time.Duration(defaultLeaseExpire+2) * time.Second)
			out, err = exec.Command("etcdctl",
				"--endpoints=http://127.0.0.1:2379",
				"get", testKeyPrefix+tt.key, "--prefix").Output()
			assert.NoError(t, err)

			lines = strings.Split(string(out), "\n")
			if len(lines) == 3 {
				actual := strings.TrimSpace(lines[1])
				assert.Equal(t, tt.val, actual)
			} else {
				t.Errorf("Unexpected output format: %s", string(out))
			}

			defer tt.after(t)
		})
	}
}

func TestDatastore_PutWithLease(t *testing.T) {
	tests := []struct {
		name       string
		key        string
		val        string
		ttlSeconds int64
		after      func(t *testing.T)
	}{
		{
			name:       "test1",
			key:        "/name",
			val:        "tvm",
			ttlSeconds: 3,
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", testKeyPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			etcd, err := GetEtcdFactoryOr(getTestEtcdOptions(), nil)
			assert.NoError(t, err)

			ds, ok := etcd.(*datastore)
			assert.True(t, ok)

			err = ds.PutWithLease(context.Background(), tt.key, tt.val, tt.ttlSeconds)
			assert.NoError(t, err)

			// 1、验证数据是否正确存储
			out, err := exec.Command("etcdctl",
				"--endpoints=http://127.0.0.1:2379",
				"get", testKeyPrefix+tt.key, "--prefix").Output()
			assert.NoError(t, err)

			lines := strings.Split(string(out), "\n")
			if len(lines) == 3 {
				actual := strings.TrimSpace(lines[1])
				assert.Equal(t, tt.val, actual)
			} else {
				t.Errorf("Unexpected output format: %s", string(out))
			}

			// 2、验证租约到期后，数据是否被删除
			// 因为使用独立租约，所以租约到期后，数据应该被删除
			time.Sleep(time.Duration(tt.ttlSeconds+2) * time.Second)
			out, err = exec.Command("etcdctl",
				"--endpoints=http://127.0.0.1:2379",
				"get", testKeyPrefix+tt.key, "--prefix").Output()
			assert.NoError(t, err)
			assert.Empty(t, out)

			defer tt.after(t)
		})
	}
}

func TestDatastore_Get(t *testing.T) {
	tests := []struct {
		name   string
		key    string
		val    string
		before func(t *testing.T)
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			key:  "/name",
			val:  "tvm",
			before: func(t *testing.T) {
				// 清理可能存在的脏数据
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", testKeyPrefix, "--prefix").Run()
				// 准备测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", testKeyPrefix+"/name", "tvm").Run()
				assert.NoError(t, err)
			},
			after: func(t *testing.T) {
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", testKeyPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.before(t)
			defer tt.after(t)

			etcd, err := GetEtcdFactoryOr(getTestEtcdOptions(), nil)
			assert.NoError(t, err)

			ds, ok := etcd.(*datastore)
			assert.True(t, ok)

			res, err := ds.Get(context.Background(), tt.key)
			assert.NoError(t, err)
			assert.Equal(t, tt.val, string(res))
		})
	}
}

func TestDatastore_List(t *testing.T) {
	tests := []struct {
		name   string
		kvs    map[string]string
		before func(t *testing.T, kvs map[string]string)
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			kvs: map[string]string{
				"/name": "tvm",
				"/age":  "18",
			},
			before: func(t *testing.T, kvs map[string]string) {
				// 清理可能存在的脏数据
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", testKeyPrefix, "--prefix").Run()
				// 准备测试数据
				for k, v := range kvs {
					err := exec.Command("etcdctl",
						"--endpoints=http://127.0.0.1:2379",
						"put", testKeyPrefix+k, v).Run()
					assert.NoError(t, err)
				}
			},
			after: func(t *testing.T) {
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", testKeyPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.before(t, tt.kvs)
			defer tt.after(t)

			etcd, err := GetEtcdFactoryOr(getTestEtcdOptions(), nil)
			assert.NoError(t, err)

			ds, ok := etcd.(*datastore)
			assert.True(t, ok)

			items, err := ds.List(context.Background(), "")
			assert.NoError(t, err)
			assert.Len(t, items, len(tt.kvs))
			for _, kv := range items {
				val, ok := tt.kvs[kv.Key]
				assert.True(t, ok)
				assert.Equal(t, val, string(kv.Val))
			}
		})
	}
}

func TestDatastore_Delete(t *testing.T) {
	tests := []struct {
		name   string
		key    string
		val    string
		before func(t *testing.T)
	}{
		{
			name: "test1",
			key:  "/name",
			val:  "tvm",
			before: func(t *testing.T) {
				// 清理可能存在的脏数据
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", testKeyPrefix, "--prefix").Run()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.before(t)

			etcd, err := GetEtcdFactoryOr(getTestEtcdOptions(), nil)
			assert.NoError(t, err)

			ds, ok := etcd.(*datastore)
			assert.True(t, ok)

			// 准备测试数据
			err = exec.Command("etcdctl",
				"--endpoints=http://127.0.0.1:2379",
				"put", testKeyPrefix+tt.key, tt.val).Run()
			assert.NoError(t, err)

			val, err := ds.Delete(context.Background(), tt.key)
			assert.NoError(t, err)
			assert.Equal(t, tt.val, string(val))
		})
	}
}

func TestDatastore_Watch(t *testing.T) {
	etcd, err := GetEtcdFactoryOr(getTestEtcdOptions(), nil)
	assert.NoError(t, err)

	ds, ok := etcd.(*datastore)
	assert.True(t, ok)

	type watchEvent struct {
		op     string // "create", "update", "delete"
		key    string
		val    string
		oldVal string
	}

	var (
		eventCh  = make(chan watchEvent)
		wg       sync.WaitGroup
		testData = []watchEvent{
			{
				op:     "create",
				key:    "/name",
				val:    "tvm",
				oldVal: "",
			},
			{
				op:     "update",
				key:    "/name",
				val:    "tvm2",
				oldVal: "tvm",
			},
			{
				op:     "delete",
				key:    "/name",
				val:    "",
				oldVal: "",
			},
		}
	)

	// 注册监听器
	onCreate := func(ctx context.Context, key, val []byte) {
		event := watchEvent{
			op:     "create",
			key:    string(key),
			val:    string(val),
			oldVal: "",
		}
		t.Log(event)
		eventCh <- event
	}
	onUpdate := func(ctx context.Context, key, oldVal, val []byte) {
		event := watchEvent{
			op:     "update",
			key:    string(key),
			val:    string(val),
			oldVal: string(oldVal),
		}
		t.Log(event)
		eventCh <- event
	}
	onDelete := func(ctx context.Context, key []byte) {
		event := watchEvent{
			op:     "delete",
			key:    string(key),
			val:    "",
			oldVal: "",
		}
		t.Log(event)
		eventCh <- event
	}
	// 启动监听器
	err = ds.Watch(context.Background(), "", onCreate, onUpdate, onDelete)
	assert.NoError(t, err)

	wg.Add(1)
	go func() {
		defer wg.Done()
		expectedIndex := 0

		for {
			select {
			case event := <-eventCh:
				if expectedIndex >= len(testData) {
					t.Error("收到超出预期的额外事件")
					return
				}

				expected := testData[expectedIndex]
				assert.Equal(t, expected.op, event.op)
				assert.Equal(t, expected.key, event.key)

				switch expected.op {
				case "create":
					assert.Equal(t, expected.val, event.val)
				case "update":
					assert.Equal(t, expected.oldVal, event.oldVal)
					assert.Equal(t, expected.val, event.val)
				case "delete":
					assert.Empty(t, event.val)
				}

				expectedIndex++
				if expectedIndex == len(testData) {
					return
				}

			case <-time.After(3 * time.Second):
				t.Error("等待事件超时")
				return
			}
		}
	}()

	// 触发事件
	time.Sleep(500 * time.Millisecond) // 等待监听器就绪

	// 创建操作
	err = ds.Put(context.Background(), "/name", "tvm")
	assert.NoError(t, err)

	// 更新操作
	err = ds.Put(context.Background(), "/name", "tvm2")
	assert.NoError(t, err)

	// 删除操作
	_, err = ds.Delete(context.Background(), "/name")
	assert.NoError(t, err)

	wg.Wait()
}

func getTestEtcdOptions() *options.EtcdOptions {
	opts := options.NewEtcdOptions()
	opts.Endpoints = []string{"http://127.0.0.1:2379"}
	opts.Namespace = testKeyPrefix
	return opts
}

package etcd

import (
	"context"
	"os/exec"
	"testing"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	uuid "github.com/satori/go.uuid"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

func TestVM_CreateAndList(t *testing.T) {
	_ = exec.Command("etcdctl",
		"--endpoints=http://127.0.0.1:2379",
		"del", "/twm/live_cluster/", "--prefix").Run()

	etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
		Endpoints:      []string{"http://127.0.0.1:2379"},
		Timeout:        5,
		Namespace:      KeyPrefix,
		RequestTimeout: 2,
	}, nil)
	assert.NoError(t, err)

	vm := domain.VMInstance{
		AutoSwitch:      0,
		Autorun:         0,
		BootFrom:        "disk",
		CPUPassthru:     false,
		CPUPinNum:       2,
		CPUWeight:       256,
		CreatingHost:    "",
		Desc:            "",
		HostID:          "9b5910c9-557c-42a7-b592-7996d606dcc0",
		HypervEnlighten: false,
		IsGeneralVM:     true,
		ISOImages:       []string{"8f7ea322-4933-420b-84d6-06c05011a596", "8f7ea322-4933-420b-84d6-06c05011a596"},
		KBLayout:        "en-gb",
		MachineType:     "q35",
		Name:            "ubuntu",
		RepositoryID:    "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
		SerialConsole:   true,
		ShutdownReason:  10,
		State:           "saved",
		StateValue:      10,
		USBVersion:      2,
		USBs:            []string{"002:003:058f:6387", "unmounted", "unmounted", "unmounted"},
		UseOVMF:         false,
		UTCOffset:       5,
		VCPUNum:         2,
		VDisks:          []string{"80f416fa-3119-47e4-ab34-927cb1996145", "bfd0f937-fce2-498e-afd0-7400fb4af503", "da7b87f9-bcd1-4a50-bfbf-50433bd8746d", "aa03c5b3-2890-44d3-872b-87bdadc219db", "4b257ca5-f6cf-4f74-b99e-b7ae887192b3"},
		VideoCard:       "cirrus",
		VNics:           []string{"218deca1-176c-4e29-a4e9-080c829d6849", "b5036451-f0cd-4fc2-b9df-820d515876e9", "491269c5-6b7e-4616-b25c-c0ef5f735a07", "22c9e1fa-1ddf-4798-8004-1c9eb8b682e4"},
		VRAMSize:        2048,
	}

	id := uuid.Must(uuid.NewV4()).String()
	err = etcd.VMs().Create(context.Background(), id, &vm, metav1.CreateOptions{})
	assert.NoError(t, err)

	list, err := etcd.VMs().List(context.Background(), metav1.ListOptions{})
	assert.NoError(t, err)

	found := false
	for guestid, item := range list.Items {
		if guestid == id {
			found = true

			assert.Equal(t, *item, vm)
		}
	}
	assert.True(t, found)
}

package etcd

import (
	"context"
	"encoding/json"
	"fmt"
	"path"
	"strings"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/component-base/pkg/util/jsonutil"
	"github.com/marmotedu/errors"

	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

type Host struct {
	ds *datastore
}

func (h *Host) ListStatus(ctx context.Context, opts metav1.ListOptions) ([]domain.StatusEntry, error) {
	panic("no finished")
}

func (h *Host) Create(ctx context.Context, hostId string, host *domain.Host, opts metav1.CreateOptions) error {
	return h.ds.Put(ctx, h.getHostBaseInfoKey(hostId), jsonutil.ToString(host))
}

func (h *Host) List(ctx context.Context, opts metav1.ListOptions) (*domain.HostList, error) {
	kvs, err := h.ds.List(ctx, domain.HostPrefix)
	if err != nil {
		return nil, err
	}

	res := &domain.HostList{
		ListMeta: metav1.ListMeta{
			//TotalCount: int64(len(kvs)),  //totalCount默认初始化，因为同一HostId的子目录包含nics和base_info等,显然不能以len(kvs)赋值
		},
		Items: make(map[string]*domain.Host),
	}

	for _, kv := range kvs {
		var host domain.Host

		paths := h.ds.SplitEtcdKeyPath(kv.Key)
		if len(paths) <= 0 {
			return nil, fmt.Errorf("failed to obtain the host id")
		}

		//List返回的key基于传入参数"/"，若入参后以"/"结束，则list返回key开头无"/"。入参不以"/"结束，则list返回key带"/"
		hostId := paths[0]
		baseInfo := strings.Join([]string{hostId, domain.HostBaseInfo}, domain.HostSep)
		if kv.Key != baseInfo {
			continue
		}
		if err := json.Unmarshal(kv.Val, &host); err != nil {
			return nil, err
		}

		res.TotalCount++
		res.Items[hostId] = &host
	}

	return res, nil
}

func (h *Host) Get(ctx context.Context, HostId string, opts metav1.GetOptions) (*domain.Host, error) {
	resp, err := h.ds.Get(ctx, h.getHostBaseInfoKey(HostId))
	if err != nil {
		return nil, err
	}

	var host domain.Host
	if err := json.Unmarshal(resp, &host); err != nil {
		return nil, err
	}

	return &host, nil
}

func (h *Host) Delete(ctx context.Context, HostId string, opts metav1.DeleteOptions) error {
	_, err := h.ds.Delete(ctx, h.getHostKey(HostId))
	return err
}

func (h *Host) IfaceGet(ctx context.Context, hostId string, ifaceId string, opts metav1.GetOptions) (*domain.HostNetIface, error) {
	var iface domain.HostNetIface
	resp, err := h.ds.Get(ctx, h.getIfaceKey(hostId, ifaceId))
	if err != nil {
		return nil, err
	}

	if err := json.Unmarshal(resp, &iface); err != nil {
		return nil, err
	}

	return &iface, nil
}

func (h *Host) IfaceCreate(ctx context.Context, HostId string, ifaceId string, iface *domain.HostNetIface, opts metav1.CreateOptions) error {
	return h.ds.Put(ctx, h.getIfaceKey(HostId, ifaceId), jsonutil.ToString(iface))
}

func (h *Host) IfaceDelete(ctx context.Context, hostID string, ifaceId string, opts metav1.DeleteOptions) error {
	_, err := h.ds.Delete(ctx, h.getIfaceKey(hostID, ifaceId))
	return err
}

func (h *Host) ListIfaceOfHost(ctx context.Context, hostId string, opts metav1.ListOptions) (*domain.HostNetIfaceList, error) {
	kvs, err := h.ds.List(ctx, h.getIfaceKey(hostId, ""))
	if err != nil {
		return nil, err
	}

	res := &domain.HostNetIfaceList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(kvs)),
		},
		Items: make(map[string]*domain.HostNetIface),
	}

	for _, kv := range kvs {
		var hostNetIface domain.HostNetIface
		if err := json.Unmarshal(kv.Val, &hostNetIface); err != nil {
			return nil, errors.Wrap(err, "unmarshal to HostNetIface struct failed")
		}
		res.Items[kv.Key] = &hostNetIface
	}
	return res, nil
}

func newHost(ds *datastore) *Host {
	return &Host{ds: ds}
}

func (h *Host) getHostKey(hostId string) string {
	//return strings.Join([]string{domain.HostPrefix, hostId}, domain.HostSep)
	return path.Join(domain.HostPrefix, hostId)
}

func (h *Host) getHostBaseInfoKey(hostId string) string {
	//return strings.Join([]string{h.getHostKey(hostId), domain.HostBaseInfo}, domain.HostSep)
	return path.Join(h.getHostKey(hostId), domain.HostBaseInfo)
}

func (h *Host) getIfaceKey(hostId string, ifaceId string) string {
	//return strings.Join([]string{h.getHostKey(hostId), domain.HostIface}, domain.HostSep)
	return path.Join(h.getHostKey(hostId), domain.HostIface)
}

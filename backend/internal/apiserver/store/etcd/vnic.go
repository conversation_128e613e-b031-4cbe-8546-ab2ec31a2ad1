// Package etcd
/**
* @Project : terravirtualmachine
* @File    : network.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/21 08:54
**/

package etcd

import (
	"context"
	"encoding/json"
	"fmt"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/component-base/pkg/util/jsonutil"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/golibrary/errors"
)

var _ store.VNicStore = (*vnics)(nil)

type vnics struct {
	ds *datastore
}

// Listvnics 查询特定虚拟机的的虚拟网络接口列表
func (v *vnics) List(ctx context.Context, GuestID string, opts metav1.ListOptions) (*domain.VNicList, error) {
	kvs, err := v.ds.List(ctx, v.getNetworkVNicKey(""))
	if err != nil {
		return nil, err
	}

	res := &domain.VNicList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(kvs)),
		},
	}

	for _, kv := range kvs {
		var vIface domain.VNic
		if err := json.Unmarshal(kv.Val, &vIface); err != nil {
			return nil, errors.Wrap(err, "unmarshal to VNic struct failed")
		}
		if vIface.GuestID == GuestID {
			res.Items = append(res.Items, &vIface)
		} else {
			res.TotalCount--
		}

	}

	return res, nil
}

// Get 查询虚拟机的指定虚拟接口
// etcdctl get /twm/live_cluster/virtual_interfaces/{interface_id}
func (v *vnics) Get(ctx context.Context, vInterfaceID string, opts metav1.GetOptions) (*domain.VNic, error) {
	resp, err := v.ds.Get(ctx, v.getNetworkVNicKey(vInterfaceID))
	if err != nil {
		return nil, err
	}

	var vinterface domain.VNic
	if err := json.Unmarshal(resp, &vinterface); err != nil {
		return nil, errors.Wrap(err, "unmarshal to VNic struct failed")
	}

	return &vinterface, nil
}

// 配置单个虚拟网卡
func (v *vnics) Create(ctx context.Context, vInterface *domain.VNic, opts metav1.CreateOptions) error {
	return v.ds.Put(ctx, v.getNetworkVNicKey(vInterface.VNicID), jsonutil.ToString(vInterface))
}

// 配置虚拟机的所有网卡
func (v *vnics) CreateList(ctx context.Context, vInterfaces *domain.VNicList, opts metav1.CreateOptions) error {
	for _, vinterface := range vInterfaces.Items {
		err := v.Create(ctx, vinterface, opts)
		if err != nil {
			return err
		}
	}
	return nil
}

// 更新etcd中虚拟接口的信息
func (v *vnics) Update(ctx context.Context, vInterface *domain.VNic, opts metav1.UpdateOptions) error {
	return v.ds.Put(ctx, v.getNetworkVNicKey(vInterface.VNicID), jsonutil.ToString(vInterface))
}

// 更新etcd中虚拟接口的信息
func (v *vnics) Delete(ctx context.Context, vInterface *domain.VNic, opts metav1.DeleteOptions) error {
	_, err := v.ds.Delete(ctx, v.getNetworkVNicKey(vInterface.VNicID))
	return err
}

func newVNics(ds *datastore) *vnics {
	return &vnics{ds: ds}
}

func VMsDisconnect2Network() {

}

func (v *vnics) getNetworkVNicKey(vIfaceID string) string {
	return fmt.Sprintf(keyNetworkVNics, vIfaceID)
}

package etcd

import (
	"context"
	"fmt"
	"os/exec"
	"testing"
	"time"

	"github.com/marmotedu/component-base/pkg/json"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	uuid "github.com/satori/go.uuid"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

func TestNetworks_ListHosts(t *testing.T) {
	tests := []struct {
		name   string
		before func(t *testing.T) []string
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) (res []string) {
				// 清理可能存在的脏数据
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/hosts/", "--prefix").Run()
				// 准备测试数据
				for i := 0; i < 3; i++ {
					hostID := uuid.Must(uuid.NewV4()).String()
					res = append(res, hostID)
					err := exec.Command("etcdctl",
						"--endpoints=http://127.0.0.1:2379",
						"put", "/twm/live_cluster/hosts/"+hostID, "true").Run()
					assert.NoError(t, err)
				}
				return res
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/hosts/", "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wanthosts := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      KeyPrefix,
				RequestTimeout: 2,
			}, nil)
			assert.NoError(t, err)

			hosts, err := etcd.Networks().ListHosts(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)
			assert.Equal(t, int64(len(wanthosts)), hosts.TotalCount)
			for _, host := range hosts.Items {
				assert.Contains(t, wanthosts, host.HostID)
			}

			defer tt.after(t)
		})
	}
}

func TestNetworks_ListInterfaces(t *testing.T) {
	tests := []struct {
		name   string
		before func(t *testing.T) map[string][]domain.Interface
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) map[string][]domain.Interface {
				// 清理可能存在的脏数据
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/hosts/", "--prefix").Run()
				interfaces := make(map[string][]domain.Interface)
				// 准备测试数据
				for i := 0; i < 3; i++ {
					hostID := uuid.Must(uuid.NewV4()).String()
					interfaces[hostID] = []domain.Interface{}
					for j := 0; j < 2; j++ {
						interfaceID := uuid.Must(uuid.NewV4()).String()
						iface := domain.Interface{
							HostID:        hostID,
							HostName:      hostID,
							InterfaceID:   interfaceID,
							InterfaceName: fmt.Sprintf("ovs_eth%d", j),
							IP:            []string{"127.0.0.1"},
							Mask:          []string{"*************"},
							SpeedMbs:      1000,
							Status:        domain.CONNECTED,
						}
						jsonData, err := json.Marshal(iface)
						assert.NoError(t, err)
						key := fmt.Sprintf("/twm/live_cluster/hosts/%s/interfaces/%s", hostID, interfaceID)
						err = exec.Command("etcdctl",
							"--endpoints=http://127.0.0.1:2379",
							"put", key, string(jsonData),
						).Run()
						assert.NoError(t, err)
						interfaces[hostID] = append(interfaces[hostID], iface)
					}
				}
				return interfaces
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/hosts/", "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantInterfaces := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      KeyPrefix,
				RequestTimeout: 2,
			}, nil)
			assert.NoError(t, err)

			for host, ifaces := range wantInterfaces {
				interfaces, err := etcd.Networks().ListInterfaces(context.Background(), host, metav1.ListOptions{})
				assert.NoError(t, err)
				assert.Equal(t, int64(len(ifaces)), interfaces.TotalCount)
				for _, iface := range interfaces.Items {
					found := false
					for _, wantIface := range ifaces {
						if iface.InterfaceID == wantIface.InterfaceID {
							found = true
							// 验证接口详细信息
							assert.Equal(t, wantIface.HostID, iface.HostID)
							assert.Equal(t, wantIface.HostName, iface.HostName)
							assert.Equal(t, wantIface.InterfaceName, iface.InterfaceName)
							assert.Equal(t, wantIface.IP, iface.IP)
							assert.Equal(t, wantIface.Mask, iface.Mask)
							assert.Equal(t, wantIface.SpeedMbs, iface.SpeedMbs)
							assert.Equal(t, wantIface.Status, iface.Status)
							break
						}
					}
					assert.True(t, found)
				}
			}

			defer tt.after(t)
		})
	}
}

func TestNetworks_List(t *testing.T) {
	tests := []struct {
		name   string
		before func(t *testing.T) domain.NetworkList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.NetworkList {
				// 清理可能存在的脏数据
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				// 准备测试数据
				networks := domain.NetworkList{
					ListMeta: metav1.ListMeta{
						TotalCount: 1,
					},
					Items: []*domain.Network{
						{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "Default VM Network",
								CreatedAt: time.Now(),
								UpdatedAt: time.Now(),
							},
							NetworkID: "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
							HostID:    "",
							HostName:  "",
							Interfaces: map[string]*domain.InterfaceList{
								"9b5910c9-557c-42a7-b592-7996d606dcc0": {
									ListMeta: metav1.ListMeta{
										TotalCount: 2,
									},
									Items: []*domain.Interface{
										{
											Checked:     true,
											HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
											InterfaceID: "00000000-0000-0000-0000-9009d05e8271",
											// 其他字段需要查询来更新
										},
										{
											Checked:     false,
											HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
											InterfaceID: "00000000-0000-0000-0000-9009d05e8272",
										},
									},
								},
							},
							NumGuests:     0, // 这个是需要查询来更新的
							NumHosts:      1,
							NumInterfaces: 2,
							Type:          domain.EXTERNAL,
							VLANID:        0,
						},
					},
				}

				for _, network := range networks.Items {
					jsonData, err := json.Marshal(network)
					assert.NoError(t, err)
					err = exec.Command("etcdctl",
						"--endpoints=http://127.0.0.1:2379",
						"put", "/twm/live_cluster/networks/"+network.NetworkID, string(jsonData)).Run()
					assert.NoError(t, err)
				}

				return networks
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				assert.NoError(t, err)
			},
		},
		{
			name: "test2",
			before: func(t *testing.T) domain.NetworkList {
				// 清理可能存在的脏数据
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				// 准备测试数据
				networks := domain.NetworkList{
					ListMeta: metav1.ListMeta{
						TotalCount: 3,
					},
					Items: []*domain.Network{
						{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "Default VM Network",
								CreatedAt: time.Now(),
								UpdatedAt: time.Now(),
							},
							NetworkID: "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
							HostID:    "",
							HostName:  "",
							Interfaces: map[string]*domain.InterfaceList{
								"9b5910c9-557c-42a7-b592-7996d606dcc0": {
									ListMeta: metav1.ListMeta{
										TotalCount: 2,
									},
									Items: []*domain.Interface{
										{
											Checked:     true,
											HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
											InterfaceID: "00000000-0000-0000-0000-9009d05e8271",
											// 其他字段需要查询来更新
										},
										{
											Checked:     false,
											HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
											InterfaceID: "00000000-0000-0000-0000-9009d05e8272",
										},
									},
								},
							},
							NumGuests:     0, // 这个是需要查询来更新的
							NumHosts:      1,
							NumInterfaces: 2,
							Type:          domain.EXTERNAL,
							VLANID:        0,
						}, // end of network1
						{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "private",
								CreatedAt: time.Now(),
								UpdatedAt: time.Now(),
							},
							NetworkID: "bfffe844-3dee-46fd-a1cc-00d2cae7b768",
							HostID:    "79e0750a-29b2-4e1b-9a34-73fe4d4143c1",
							HostName:  "local",
							Interfaces: map[string]*domain.InterfaceList{
								"79e0750a-29b2-4e1b-9a34-73fe4d4143c1": {
									ListMeta: metav1.ListMeta{
										TotalCount: 0,
									},
									Items: []*domain.Interface{},
								},
							},
							NumGuests:     0,
							NumHosts:      1,
							NumInterfaces: 0,
							Type:          domain.PRIVATE,
							VLANID:        0,
						}, // end of network2
						{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "external vlan 100",
								CreatedAt: time.Now(),
								UpdatedAt: time.Now(),
							},
							NetworkID: "bfffe844-3dee-46fd-a1cc-00d2cae7b769",
							HostID:    "",
							HostName:  "",
							Interfaces: map[string]*domain.InterfaceList{
								"9b5910c9-557c-42a7-b592-7996d606dcc0": {
									ListMeta: metav1.ListMeta{
										TotalCount: 2,
									},
									Items: []*domain.Interface{
										{
											Checked:       false,
											HostID:        "9b5910c9-557c-42a7-b592-7996d606dcc0",
											HostName:      "local",
											InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
											InterfaceName: "ovs_eth0",
											IP:            []string{"**********"},
											Mask:          []string{"*************"},
											SpeedMbs:      1000,
											Status:        domain.CONNECTED,
										},
										{
											Checked:       false,
											HostID:        "9b5910c9-557c-42a7-b592-7996d606dcc0",
											HostName:      "local",
											InterfaceID:   "00000000-0000-0000-0000-9009d05e8272",
											InterfaceName: "ovs_eth1",
											IP:            []string{"***************"},
											Mask:          []string{"*************"},
											SpeedMbs:      -1,
											Status:        domain.DISCONNECTED,
										},
									},
								},
							},
							NumGuests:     0,
							NumHosts:      1,
							NumInterfaces: 2,
							Type:          domain.EXTERNAL,
							VLANID:        100,
						}, // end of network3
					},
				} // end of networks

				for _, network := range networks.Items {
					jsonData, err := json.Marshal(network)
					assert.NoError(t, err)
					err = exec.Command("etcdctl",
						"--endpoints=http://127.0.0.1:2379",
						"put", "/twm/live_cluster/networks/"+network.NetworkID, string(jsonData)).Run()
					assert.NoError(t, err)
				}

				return networks
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantNetworks := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      KeyPrefix,
				RequestTimeout: 2,
			}, nil)
			assert.NoError(t, err)

			networks, err := etcd.Networks().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)

			assert.Equal(t, wantNetworks.TotalCount, networks.TotalCount)
			assert.Equal(t, len(wantNetworks.Items), len(networks.Items))

			for _, wantNetwork := range wantNetworks.Items {
				foundNetwork := false
				for _, network := range networks.Items {
					if wantNetwork.NetworkID == network.NetworkID {
						foundNetwork = true

						// 断言网络的元数据
						assert.Equal(t, wantNetwork.Name, network.Name)
						// 去除单调时钟偏移量后比较时间 否则无法通过测试
						// assert.Equal(t, wantNetwork.CreatedAt.Round(0), network.CreatedAt.Round(0))
						// assert.Equal(t, wantNetwork.UpdatedAt.Round(0), network.UpdatedAt.Round(0))

						// 断言网络的基本信息
						assert.Equal(t, wantNetwork.NetworkID, network.NetworkID)
						assert.Equal(t, wantNetwork.HostID, network.HostID)
						// assert.Equal(t, wantNetwork.HostName, network.HostName) // 需要查询来更新
						// assert.Equal(t, wantNetwork.NumGuests, network.NumGuests) // 需要查询来更新
						assert.Equal(t, wantNetwork.NumHosts, network.NumHosts)
						assert.Equal(t, wantNetwork.NumInterfaces, network.NumInterfaces)
						assert.Equal(t, wantNetwork.Type, network.Type)
						assert.Equal(t, wantNetwork.VLANID, network.VLANID)

						// 断言网络的网络接口信息
						assert.Equal(t, len(wantNetwork.Interfaces), len(network.Interfaces))
						for hostID, wantInterfaceList := range wantNetwork.Interfaces {
							ifaceList, ok := network.Interfaces[hostID]
							assert.True(t, ok)
							assert.Equal(t, wantInterfaceList.TotalCount, network.Interfaces[hostID].TotalCount)
							assert.Equal(t, len(wantInterfaceList.Items), len(ifaceList.Items))
							for _, wantInterface := range wantInterfaceList.Items {
								foundInterface := false
								for _, iface := range ifaceList.Items {
									if wantInterface.InterfaceID == iface.InterfaceID {
										foundInterface = true
										// 断言网络接口的详细信息
										assert.Equal(t, wantInterface.Checked, iface.Checked)
										assert.Equal(t, wantInterface.HostID, iface.HostID)
										assert.Equal(t, wantInterface.InterfaceID, iface.InterfaceID)
										// 其他字段需要查询来更新
									}
								} // end of range ifaceList.Items
								assert.True(t, foundInterface)
							} // end of range wantInterfaceList.Items
						} // end of range wantNetwork.Interfaces
					}
				} // end of range networks.Items
				assert.True(t, foundNetwork)
			} // end of range wantNetworks.Items

			defer tt.after(t)
		})
	}
}

func TestNetworks_Get(t *testing.T) {
	tests := []struct {
		name   string
		before func(t *testing.T) domain.Network
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.Network {
				// 清理可能存在的脏数据
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				// 准备测试数据
				network := domain.Network{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "Default VM Network",
						CreatedAt: time.Now(),
						UpdatedAt: time.Now(),
					},
					NetworkID: "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
					HostID:    "",
					HostName:  "",
					Interfaces: map[string]*domain.InterfaceList{
						"9b5910c9-557c-42a7-b592-7996d606dcc0": {
							ListMeta: metav1.ListMeta{
								TotalCount: 2,
							},
							Items: []*domain.Interface{
								{
									Checked:     true,
									HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
									InterfaceID: "00000000-0000-0000-0000-9009d05e8271",
								},
								{
									Checked:     false,
									HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
									InterfaceID: "00000000-0000-0000-0000-9009d05e8272",
								},
							},
						},
					},
					NumGuests:     1,
					NumHosts:      1,
					NumInterfaces: 2,
					Type:          domain.EXTERNAL,
					VLANID:        0,
				}

				jsonData, err := json.Marshal(network)
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/networks/"+network.NetworkID, string(jsonData)).Run()
				assert.NoError(t, err)

				return network
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantNetwork := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      KeyPrefix,
				RequestTimeout: 2,
			}, nil)
			assert.NoError(t, err)

			network, err := etcd.Networks().Get(context.Background(), wantNetwork.NetworkID, metav1.GetOptions{})
			assert.NoError(t, err)

			assert.Equal(t, wantNetwork.NetworkID, network.NetworkID)
			assert.Equal(t, wantNetwork.HostID, network.HostID)
			assert.Equal(t, wantNetwork.HostName, network.HostName)
			assert.Equal(t, wantNetwork.NumGuests, network.NumGuests)
			assert.Equal(t, wantNetwork.NumHosts, network.NumHosts)
			assert.Equal(t, wantNetwork.NumInterfaces, network.NumInterfaces)
			assert.Equal(t, wantNetwork.Type, network.Type)
			assert.Equal(t, wantNetwork.VLANID, network.VLANID)

			for hostID, wantInterfaceList := range wantNetwork.Interfaces {
				ifaceList, ok := network.Interfaces[hostID]
				assert.True(t, ok)
				assert.Equal(t, len(wantInterfaceList.Items), len(ifaceList.Items))
				for _, wantInterface := range wantInterfaceList.Items {
					foundInterface := false
					for _, iface := range ifaceList.Items {
						if wantInterface.InterfaceID == iface.InterfaceID {
							foundInterface = true
							assert.Equal(t, wantInterface.Checked, iface.Checked)
							assert.Equal(t, wantInterface.HostID, iface.HostID)
							assert.Equal(t, wantInterface.HostName, iface.HostName)
							assert.Equal(t, wantInterface.InterfaceID, iface.InterfaceID)
							assert.Equal(t, wantInterface.InterfaceName, iface.InterfaceName)
							assert.Equal(t, wantInterface.IP, iface.IP)
							assert.Equal(t, wantInterface.Mask, iface.Mask)
							assert.Equal(t, wantInterface.SpeedMbs, iface.SpeedMbs)
							assert.Equal(t, wantInterface.Status, iface.Status)
						}
					}
					assert.True(t, foundInterface)
				}
			}

			defer tt.after(t)
		})
	}
}

func TestNetworks_Create(t *testing.T) {
	tests := []struct {
		name    string
		network *domain.Network
		after   func(t *testing.T)
	}{
		{
			name: "test1",
			network: &domain.Network{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "Default VM Network",
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				},
				NetworkID: "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
				HostID:    "",
				HostName:  "",
				Interfaces: map[string]*domain.InterfaceList{
					"9b5910c9-557c-42a7-b592-7996d606dcc0": {
						ListMeta: metav1.ListMeta{
							TotalCount: 2,
						},
						Items: []*domain.Interface{
							{
								Checked:     true,
								HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
								InterfaceID: "00000000-0000-0000-0000-9009d05e8271",
							},
							{
								Checked:     false,
								HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
								InterfaceID: "00000000-0000-0000-0000-9009d05e8272",
							},
						},
					},
				},
				NumGuests:     0,
				NumHosts:      1,
				NumInterfaces: 2,
				Type:          domain.EXTERNAL,
				VLANID:        0,
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      KeyPrefix,
				RequestTimeout: 2,
			}, nil)
			assert.NoError(t, err)

			err = etcd.Networks().Create(context.Background(), tt.network, metav1.CreateOptions{})
			assert.NoError(t, err)

			defer tt.after(t)
		})
	}
}

func TestNetworks_Delete(t *testing.T) {
	tests := []struct {
		name   string
		before func(t *testing.T) *domain.Network
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) *domain.Network {
				// 清理可能存在的脏数据
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				// 准备测试数据
				network := &domain.Network{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "Default VM Network",
						CreatedAt: time.Now(),
						UpdatedAt: time.Now(),
					},
					NetworkID: "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
					HostID:    "",
					HostName:  "",
					Interfaces: map[string]*domain.InterfaceList{
						"9b5910c9-557c-42a7-b592-7996d606dcc0": {
							ListMeta: metav1.ListMeta{
								TotalCount: 2,
							},
							Items: []*domain.Interface{
								{
									Checked:     true,
									HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
									InterfaceID: "00000000-0000-0000-0000-9009d05e8271",
								},
								{
									Checked:     false,
									HostID:      "9b5910c9-557c-42a7-b592-7996d606dcc0",
									InterfaceID: "00000000-0000-0000-0000-9009d05e8272",
								},
							},
						},
					},
					NumGuests:     0,
					NumHosts:      1,
					NumInterfaces: 2,
					Type:          domain.EXTERNAL,
					VLANID:        0,
				}

				jsonData, err := json.Marshal(network)
				assert.NoError(t, err)

				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", "/twm/live_cluster/networks/"+network.NetworkID, string(jsonData)).Run()
				assert.NoError(t, err)

				return network
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/networks/", "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantNetwork := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      KeyPrefix,
				RequestTimeout: 2,
			}, nil)
			assert.NoError(t, err)

			network, err := etcd.Networks().Delete(context.Background(), wantNetwork.NetworkID, metav1.DeleteOptions{})
			assert.NoError(t, err)

			assert.Equal(t, wantNetwork.NetworkID, network.NetworkID)
			assert.Equal(t, wantNetwork.HostID, network.HostID)
			assert.Equal(t, wantNetwork.HostName, network.HostName)
			assert.Equal(t, wantNetwork.NumGuests, network.NumGuests)
			assert.Equal(t, wantNetwork.NumHosts, network.NumHosts)
			assert.Equal(t, wantNetwork.NumInterfaces, network.NumInterfaces)
			assert.Equal(t, wantNetwork.Type, network.Type)
			assert.Equal(t, wantNetwork.VLANID, network.VLANID)

			for hostID, wantInterfaceList := range wantNetwork.Interfaces {
				ifaceList, ok := network.Interfaces[hostID]
				assert.True(t, ok)
				assert.Equal(t, len(wantInterfaceList.Items), len(ifaceList.Items))
				for _, wantInterface := range wantInterfaceList.Items {
					foundInterface := false
					for _, iface := range ifaceList.Items {
						if wantInterface.InterfaceID == iface.InterfaceID {
							foundInterface = true
							assert.Equal(t, wantInterface.Checked, iface.Checked)
							assert.Equal(t, wantInterface.HostID, iface.HostID)
							assert.Equal(t, wantInterface.HostName, iface.HostName)
							assert.Equal(t, wantInterface.InterfaceID, iface.InterfaceID)
							assert.Equal(t, wantInterface.InterfaceName, iface.InterfaceName)
							assert.Equal(t, wantInterface.IP, iface.IP)
							assert.Equal(t, wantInterface.Mask, iface.Mask)
							assert.Equal(t, wantInterface.SpeedMbs, iface.SpeedMbs)
							assert.Equal(t, wantInterface.Status, iface.Status)
						}
					}
					assert.True(t, foundInterface)
				}
			}

			defer tt.after(t)
		})
	}
}

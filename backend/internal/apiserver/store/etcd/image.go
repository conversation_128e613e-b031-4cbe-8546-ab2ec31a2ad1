package etcd

import (
	"context"
	"encoding/json"
	"fmt"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/component-base/pkg/util/jsonutil"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/golibrary/errors"
)

var _ store.ImageStore = (*Image)(nil)

type Image struct {
	ds *datastore
}

func (img *Image) Create(ctx context.Context, imageId string, image *domain.Image, opts metav1.CreateOptions) error {
	return img.ds.Put(ctx, img.getImageKey(imageId), jsonutil.ToString(image))
}

func (img *Image) Update(ctx context.Context, imageId string, image *domain.Image, opts metav1.UpdateOptions) error {
	return img.ds.Put(ctx, img.getImageKey(imageId), jsonutil.ToString(image))
}

func (img *Image) List(ctx context.Context, opts metav1.ListOptions) (*domain.ImageList, error) {
	kvs, err := img.ds.List(ctx, domain.ImagePrefix)
	if err != nil {
		return nil, err
	}

	res := &domain.ImageList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(kvs)),
		},
	}

	res.Items = make(map[string]*domain.Image)
	for _, kv := range kvs {
		var image domain.Image

		paths := img.ds.SplitEtcdKeyPath(kv.Key)
		if err := json.Unmarshal(kv.Val, &image); err != nil {
			return nil, errors.Wrap(err, "unmarshal to image struct failed")
		}

		if len(paths) <= 0 {
			log.Info("failed to obtain the image id,etcd path have problem")
			return nil, errors.Wrap(err, "failed to obtain the image Id")
		}
		res.Items[paths[0]] = &image
	}

	return res, nil
}

func (img *Image) Delete(ctx context.Context, imageId string, opts metav1.DeleteOptions) error {
	_, err := img.ds.Delete(ctx, img.getImageKey(imageId))
	return err
}

func (img *Image) Get(ctx context.Context, imageId string, opts metav1.GetOptions) (*domain.Image, error) {
	resp, err := img.ds.Get(ctx, img.getImageKey(imageId))
	if err != nil {
		return nil, err
	}

	var retImage domain.Image
	if err := json.Unmarshal(resp, &retImage); err != nil {
		return nil, errors.Wrap(err, "unmarshal to VNic struct failed")
	}

	return &retImage, nil
}

func newImage(ds *datastore) *Image {
	return &Image{ds: ds}
}

func (img *Image) getImageKey(imgId string) string {
	return fmt.Sprintf(domain.ImagePrefix+"%v", imgId)
}

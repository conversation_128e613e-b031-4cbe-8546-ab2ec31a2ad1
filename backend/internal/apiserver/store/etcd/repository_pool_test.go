package etcd

import (
	"context"
	"encoding/json"
	"os/exec"
	"strconv"
	"testing"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	uuid "github.com/satori/go.uuid"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

func TestRepositoryPool_List(t *testing.T) {
	testNumber := 6
	tests := []struct {
		name   string
		before func(t *testing.T) domain.RepositoryPoolList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.RepositoryPoolList {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				pools := domain.RepositoryPoolList{}
				pools.ListMeta.TotalCount = int64(testNumber)
				pools.Items = make(map[string]*domain.RepositoryPool)

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()

					pools.Items[id] = &domain.RepositoryPool{
						Name:               "twmStorage" + "-" + strconv.Itoa(i),
						HostID:             id,
						LocationVolume:     "/Volume" + strconv.Itoa(i),
						TotalSize:          0.0,
						HardLimit:          0.0,
						SoftLimit:          0.0,
						EnableLowerNotify:  true,
						LastNotifyTreshold: strconv.Itoa(100 * i),
					}
				}

				for id, pool := range pools.Items {
					jsonData, err := json.Marshal(pool)
					assert.NoError(t, err)
					etcddir := repoPrefix + id
					err = exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
						"put", etcddir, string(jsonData)).Run()
					assert.NoError(t, err)

				}

				return pools
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", repoPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantPools := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      KeyPrefix,
				RequestTimeout: 2,
			}, nil)
			assert.NoError(t, err)

			pools, err := etcd.RepositoryPool().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)

			assert.Equal(t, wantPools.TotalCount, pools.TotalCount)
			assert.Equal(t, len(wantPools.Items), len(pools.Items))
			for id, wantPool := range wantPools.Items {
				pool, ok := pools.Items[id]

				assert.True(t, ok)
				assert.Equal(t, wantPool.Name, pool.Name)
				assert.Equal(t, wantPool.HostID, pool.HostID)
				assert.Equal(t, wantPool.LocationVolume, pool.LocationVolume)
				assert.Equal(t, wantPool.HardLimit, pool.HardLimit)
				assert.Equal(t, wantPool.SoftLimit, pool.SoftLimit)
				assert.Equal(t, wantPool.EnableLowerNotify, pool.EnableLowerNotify)
				assert.Equal(t, wantPool.LastNotifyTreshold, pool.LastNotifyTreshold)
			}

			//defer tt.after(t)
		})
	}
}

func TestRepositoryPool_Create(t *testing.T) {
	testNumber := 6
	tests := []struct {
		name   string
		before func(t *testing.T) domain.RepositoryPoolList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.RepositoryPoolList {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
					Endpoints:      []string{"http://127.0.0.1:2379"},
					Timeout:        5,
					Namespace:      KeyPrefix,
					RequestTimeout: 2,
				}, nil)
				assert.NoError(t, err)

				pools := domain.RepositoryPoolList{}
				pools.ListMeta.TotalCount = int64(testNumber)
				pools.Items = make(map[string]*domain.RepositoryPool)

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()

					pools.Items[id] = &domain.RepositoryPool{
						Name:               "twmStorage" + "-" + strconv.Itoa(i),
						HostID:             id,
						LocationVolume:     "/Volume" + strconv.Itoa(i),
						TotalSize:          0.0,
						HardLimit:          0.0,
						SoftLimit:          0.0,
						EnableLowerNotify:  true,
						LastNotifyTreshold: strconv.Itoa(100 * i),
					}
				}

				for id, pool := range pools.Items {
					err = etcd.RepositoryPool().Create(context.Background(), id, pool, metav1.CreateOptions{})
					assert.NoError(t, err)

				}

				return pools
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", repoPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantPools := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      KeyPrefix,
				RequestTimeout: 2,
			}, nil)
			assert.NoError(t, err)

			pools, err := etcd.RepositoryPool().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)

			assert.Equal(t, wantPools.TotalCount, pools.TotalCount)
			assert.Equal(t, len(wantPools.Items), len(pools.Items))
			for id, wantPool := range wantPools.Items {
				pool, ok := pools.Items[id]

				assert.True(t, ok)
				assert.Equal(t, wantPool.Name, pool.Name)
				assert.Equal(t, wantPool.HostID, pool.HostID)
				assert.Equal(t, wantPool.LocationVolume, pool.LocationVolume)
				assert.Equal(t, wantPool.HardLimit, pool.HardLimit)
				assert.Equal(t, wantPool.SoftLimit, pool.SoftLimit)
				assert.Equal(t, wantPool.EnableLowerNotify, pool.EnableLowerNotify)
				assert.Equal(t, wantPool.LastNotifyTreshold, pool.LastNotifyTreshold)
			}

			defer tt.after(t)
		})
	}
}

func TestRepositoryPool_Get(t *testing.T) {
	testNumber := 6
	tests := []struct {
		name   string
		before func(t *testing.T) domain.RepositoryPoolList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.RepositoryPoolList {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
					Endpoints:      []string{"http://127.0.0.1:2379"},
					Timeout:        5,
					Namespace:      KeyPrefix,
					RequestTimeout: 2,
				}, nil)
				assert.NoError(t, err)

				pools := domain.RepositoryPoolList{}
				pools.ListMeta.TotalCount = int64(testNumber)
				pools.Items = make(map[string]*domain.RepositoryPool)

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()

					pools.Items[id] = &domain.RepositoryPool{
						Name:               "twmStorage" + "-" + strconv.Itoa(i),
						HostID:             "/Volume" + strconv.Itoa(i),
						LocationVolume:     "/Volume" + strconv.Itoa(i),
						TotalSize:          0.0,
						HardLimit:          0.0,
						SoftLimit:          0.0,
						EnableLowerNotify:  true,
						LastNotifyTreshold: strconv.Itoa(100 * i),
					}
				}

				for id, pool := range pools.Items {
					err = etcd.RepositoryPool().Create(context.Background(), id, pool, metav1.CreateOptions{})
					assert.NoError(t, err)

				}

				return pools
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", repoPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantPools := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      KeyPrefix,
				RequestTimeout: 2,
			}, nil)
			assert.NoError(t, err)

			for repoid, wantPool := range wantPools.Items {
				pool, err := etcd.RepositoryPool().Get(context.Background(), repoid, metav1.GetOptions{})
				assert.NoError(t, err)

				assert.Equal(t, wantPool.Name, pool.Name)
				assert.Equal(t, wantPool.HostID, pool.HostID)
				assert.Equal(t, wantPool.LocationVolume, pool.LocationVolume)
				assert.Equal(t, wantPool.HardLimit, pool.HardLimit)
				assert.Equal(t, wantPool.SoftLimit, pool.SoftLimit)
				assert.Equal(t, wantPool.EnableLowerNotify, pool.EnableLowerNotify)
				assert.Equal(t, wantPool.LastNotifyTreshold, pool.LastNotifyTreshold)
			}

			defer tt.after(t)
		})
	}
}

func TestRepositoryPool_Delete(t *testing.T) {
	testNumber := 6
	tests := []struct {
		name   string
		before func(t *testing.T) domain.RepositoryPoolList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.RepositoryPoolList {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
					Endpoints:      []string{"http://127.0.0.1:2379"},
					Timeout:        5,
					Namespace:      KeyPrefix,
					RequestTimeout: 2,
				}, nil)
				assert.NoError(t, err)

				pools := domain.RepositoryPoolList{}
				pools.ListMeta.TotalCount = int64(testNumber)
				pools.Items = make(map[string]*domain.RepositoryPool)

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()

					pools.Items[id] = &domain.RepositoryPool{
						Name:               "twmStorage" + "-" + strconv.Itoa(i),
						HostID:             "/Volume" + strconv.Itoa(i),
						LocationVolume:     "/Volume" + strconv.Itoa(i),
						TotalSize:          0.0,
						HardLimit:          0.0,
						SoftLimit:          0.0,
						EnableLowerNotify:  true,
						LastNotifyTreshold: strconv.Itoa(100 * i),
					}
				}

				for id, pool := range pools.Items {
					err = etcd.RepositoryPool().Create(context.Background(), id, pool, metav1.CreateOptions{})
					assert.NoError(t, err)

				}

				return pools
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", repoPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantPools := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      KeyPrefix,
				RequestTimeout: 2,
			}, nil)
			assert.NoError(t, err)

			for del_id, _ := range wantPools.Items {
				err := etcd.RepositoryPool().Delete(context.Background(), del_id, metav1.DeleteOptions{})
				assert.NoError(t, err)

				wantdel := true
				pools, err := etcd.RepositoryPool().List(context.Background(), metav1.ListOptions{})
				assert.NoError(t, err)
				for id, _ := range pools.Items {
					if del_id == id {
						wantdel = false
						break
					}
				}
				assert.True(t, wantdel)

			}

			defer tt.after(t)
		})
	}
}

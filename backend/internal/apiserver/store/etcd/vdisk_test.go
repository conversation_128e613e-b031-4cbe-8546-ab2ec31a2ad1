package etcd

import (
	"context"
	"encoding/json"
	"os/exec"
	"testing"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	uuid "github.com/satori/go.uuid"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

var etcd, _ = GetEtcdFactoryOr(&options.EtcdOptions{
	Endpoints:      []string{"http://127.0.0.1:2379"},
	Timeout:        5,
	Namespace:      KeyPrefix,
	RequestTimeout: 2,
}, nil)

func rangeVDiskMode() []domain.TemplateDiskDriver {
	return []domain.TemplateDiskDriver{
		domain.TemplateDiskDriverSATA,
		domain.TemplateDiskDriverIDE,
		domain.TemplateDiskDriverSATA,
		domain.TemplateDiskDriverVirtio,
		domain.TemplateDiskDriverVirtio,
		domain.TemplateDiskDriverIDE,
	}
}

func TestVDisk_List(t *testing.T) {
	var testNumber = 6
	tests := []struct {
		name   string
		before func(t *testing.T) domain.VDiskList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.VDiskList {
				_ = exec.Command("/usr/bin/etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				vd := domain.VDiskList{}
				vd.ListMeta.TotalCount = int64(testNumber)
				vd.Items = make(map[string]*domain.VDisk)

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()

					vd.Items[id] = &domain.VDisk{
						DevLimit:       int64(0),
						DevReservation: int64(0),
						DevWeight:      int(3),
						Format:         int(2),
						GuestID:        "acb6c818-c6e5-4901-b434-d31ed6e256da",
						IOPSEnable:     false,
						IsDummy:        false,
						IsMetaDisk:     false,
						MinorID:        int(0),
						RepositoryID:   "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
						Size:           int64(32212254720),
						Type:           int(2),
						Unmap:          false,
						LUNUUID:        id,
						VDiskMode:      rangeVDiskMode()[i],
					}
				}

				for _, v := range vd.Items {
					jsonData, err := json.Marshal(v)
					assert.NoError(t, err)
					etcddir := virtualVDiskPrefix + v.LUNUUID
					err = exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
						"put", etcddir, string(jsonData)).Run()
					assert.NoError(t, err)
				}

				return vd
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", virtualVDiskPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wantVDisk := test.before(t)
			defer test.after(t)

			gotVDisk, err := etcd.VDisks().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)
			assert.Equal(t, wantVDisk.TotalCount, gotVDisk.TotalCount)
			assert.Equal(t, len(wantVDisk.Items), len(gotVDisk.Items))
			for id, wantVDisk := range wantVDisk.Items {
				gotVDisk, ok := gotVDisk.Items[id]
				assert.True(t, ok)
				assert.Equal(t, wantVDisk.DevLimit, gotVDisk.DevLimit)
				assert.Equal(t, wantVDisk.DevReservation, gotVDisk.DevReservation)
				assert.Equal(t, wantVDisk.DevWeight, gotVDisk.DevWeight)
				assert.Equal(t, wantVDisk.Format, gotVDisk.Format)
				assert.Equal(t, wantVDisk.GuestID, gotVDisk.GuestID)
				assert.Equal(t, wantVDisk.IOPSEnable, gotVDisk.IOPSEnable)
				assert.Equal(t, wantVDisk.IsDummy, gotVDisk.IsDummy)
				assert.Equal(t, wantVDisk.IsMetaDisk, gotVDisk.IsMetaDisk)
				assert.Equal(t, wantVDisk.MinorID, gotVDisk.MinorID)
				assert.Equal(t, wantVDisk.RepositoryID, gotVDisk.RepositoryID)
				assert.Equal(t, wantVDisk.Size, gotVDisk.Size)
				assert.Equal(t, wantVDisk.Type, gotVDisk.Type)
				assert.Equal(t, wantVDisk.Unmap, gotVDisk.Unmap)
				assert.Equal(t, wantVDisk.LUNUUID, gotVDisk.LUNUUID)
				assert.Equal(t, wantVDisk.VDiskMode, gotVDisk.VDiskMode)
			}
		})
	}
}

func TestVDisk_Create(t *testing.T) {
	var testNumber = 6
	tests := []struct {
		name   string
		before func(t *testing.T) domain.VDiskList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.VDiskList {
				_ = exec.Command("/usr/bin/etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				vd := domain.VDiskList{}
				vd.ListMeta.TotalCount = int64(testNumber)
				vd.Items = make(map[string]*domain.VDisk)

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()

					vd.Items[id] = &domain.VDisk{
						DevLimit:       int64(0),
						DevReservation: int64(0),
						DevWeight:      int(3),
						Format:         int(2),
						GuestID:        "acb6c818-c6e5-4901-b434-d31ed6e256da",
						IOPSEnable:     false,
						IsDummy:        false,
						IsMetaDisk:     false,
						MinorID:        int(0),
						RepositoryID:   "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
						Size:           int64(32212254720),
						Type:           int(2),
						Unmap:          false,
						LUNUUID:        id,
						VDiskMode:      rangeVDiskMode()[i],
					}
				}

				for _, v := range vd.Items {
					err := etcd.VDisks().Create(context.Background(), v.LUNUUID, v, metav1.CreateOptions{})
					assert.NoError(t, err)
				}

				return vd
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", virtualVDiskPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wantVDisk := test.before(t)
			defer test.after(t)

			gotVDisk, err := etcd.VDisks().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)
			assert.Equal(t, wantVDisk.TotalCount, gotVDisk.TotalCount)
			assert.Equal(t, len(wantVDisk.Items), len(gotVDisk.Items))
			for id, wantVDisk := range wantVDisk.Items {
				gotVDisk, ok := gotVDisk.Items[id]
				assert.True(t, ok)
				assert.Equal(t, wantVDisk.DevLimit, gotVDisk.DevLimit)
				assert.Equal(t, wantVDisk.DevReservation, gotVDisk.DevReservation)
				assert.Equal(t, wantVDisk.DevWeight, gotVDisk.DevWeight)
				assert.Equal(t, wantVDisk.Format, gotVDisk.Format)
				assert.Equal(t, wantVDisk.GuestID, gotVDisk.GuestID)
				assert.Equal(t, wantVDisk.IOPSEnable, gotVDisk.IOPSEnable)
				assert.Equal(t, wantVDisk.IsDummy, gotVDisk.IsDummy)
				assert.Equal(t, wantVDisk.IsMetaDisk, gotVDisk.IsMetaDisk)
				assert.Equal(t, wantVDisk.MinorID, gotVDisk.MinorID)
				assert.Equal(t, wantVDisk.RepositoryID, gotVDisk.RepositoryID)
				assert.Equal(t, wantVDisk.Size, gotVDisk.Size)
				assert.Equal(t, wantVDisk.Type, gotVDisk.Type)
				assert.Equal(t, wantVDisk.Unmap, gotVDisk.Unmap)
				assert.Equal(t, wantVDisk.LUNUUID, gotVDisk.LUNUUID)
				assert.Equal(t, wantVDisk.VDiskMode, gotVDisk.VDiskMode)
			}
		})
	}
}

func TestVDisk_Update(t *testing.T) {
	var testNumber = 6
	tests := []struct {
		name   string
		before func(t *testing.T) domain.VDiskList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.VDiskList {
				_ = exec.Command("/usr/bin/etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				vd := domain.VDiskList{}
				vd.ListMeta.TotalCount = int64(testNumber)
				vd.Items = make(map[string]*domain.VDisk)
				var thescendVDiskID string

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()
					if i == 1 {
						thescendVDiskID = id
					}
					vd.Items[id] = &domain.VDisk{
						DevLimit:       int64(0),
						DevReservation: int64(0),
						DevWeight:      int(3),
						Format:         int(2),
						GuestID:        "acb6c818-c6e5-4901-b434-d31ed6e256da",
						IOPSEnable:     false,
						IsDummy:        false,
						IsMetaDisk:     false,
						MinorID:        int(0),
						RepositoryID:   "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
						Size:           int64(32212254720),
						Type:           int(2),
						Unmap:          false,
						LUNUUID:        id,
						VDiskMode:      rangeVDiskMode()[i],
					}
				}

				for _, v := range vd.Items {
					err := etcd.VDisks().Create(context.Background(), v.LUNUUID, v, metav1.CreateOptions{})
					assert.NoError(t, err)
				}

				vd.Items[thescendVDiskID] = &domain.VDisk{
					DevLimit:       int64(100),
					DevReservation: int64(100),
					DevWeight:      int(100),
					Format:         int(2),
					GuestID:        "acb6c818-c6e5-4901-b434-d31ed6e256da",
					IOPSEnable:     false,
					IsDummy:        false,
					IsMetaDisk:     false,
					MinorID:        int(0),
					RepositoryID:   "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
					Size:           int64(60000000000),
					Type:           int(2),
					Unmap:          false,
					LUNUUID:        thescendVDiskID,
					VDiskMode:      domain.TemplateDiskDriverIDE,
				}

				err := etcd.VDisks().Update(context.Background(), vd.Items[thescendVDiskID].LUNUUID, vd.Items[thescendVDiskID], metav1.UpdateOptions{})
				assert.NoError(t, err)

				return vd
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", virtualVDiskPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wantVDisk := test.before(t)
			defer test.after(t)

			gotVDisk, err := etcd.VDisks().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)
			assert.Equal(t, wantVDisk.TotalCount, gotVDisk.TotalCount)
			assert.Equal(t, len(wantVDisk.Items), len(gotVDisk.Items))
			for id, wantVDisk := range wantVDisk.Items {
				gotVDisk, ok := gotVDisk.Items[id]
				assert.True(t, ok)
				assert.Equal(t, wantVDisk.DevLimit, gotVDisk.DevLimit)
				assert.Equal(t, wantVDisk.DevReservation, gotVDisk.DevReservation)
				assert.Equal(t, wantVDisk.DevWeight, gotVDisk.DevWeight)
				assert.Equal(t, wantVDisk.Format, gotVDisk.Format)
				assert.Equal(t, wantVDisk.GuestID, gotVDisk.GuestID)
				assert.Equal(t, wantVDisk.IOPSEnable, gotVDisk.IOPSEnable)
				assert.Equal(t, wantVDisk.IsDummy, gotVDisk.IsDummy)
				assert.Equal(t, wantVDisk.IsMetaDisk, gotVDisk.IsMetaDisk)
				assert.Equal(t, wantVDisk.MinorID, gotVDisk.MinorID)
				assert.Equal(t, wantVDisk.RepositoryID, gotVDisk.RepositoryID)
				assert.Equal(t, wantVDisk.Size, gotVDisk.Size)
				assert.Equal(t, wantVDisk.Type, gotVDisk.Type)
				assert.Equal(t, wantVDisk.Unmap, gotVDisk.Unmap)
				assert.Equal(t, wantVDisk.LUNUUID, gotVDisk.LUNUUID)
				assert.Equal(t, wantVDisk.VDiskMode, gotVDisk.VDiskMode)
			}
		})
	}
}

func TestVDisk_Get(t *testing.T) {
	var testNumber = 6
	var thethreeVDiskID string
	tests := []struct {
		name   string
		before func(t *testing.T) domain.VDiskList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.VDiskList {
				_ = exec.Command("/usr/bin/etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				vd := domain.VDiskList{}
				vd.ListMeta.TotalCount = int64(testNumber)
				vd.Items = make(map[string]*domain.VDisk)

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()
					if i == 2 {
						thethreeVDiskID = id
					}
					vd.Items[id] = &domain.VDisk{
						DevLimit:       int64(0),
						DevReservation: int64(0),
						DevWeight:      int(3),
						Format:         int(2),
						GuestID:        "acb6c818-c6e5-4901-b434-d31ed6e256da",
						IOPSEnable:     false,
						IsDummy:        false,
						IsMetaDisk:     false,
						MinorID:        int(0),
						RepositoryID:   "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
						Size:           int64(32212254720),
						Type:           int(2),
						Unmap:          false,
						LUNUUID:        id,
						VDiskMode:      rangeVDiskMode()[i],
					}
				}

				for _, v := range vd.Items {
					jsonData, err := json.Marshal(v)
					assert.NoError(t, err)
					etcddir := virtualVDiskPrefix + v.LUNUUID
					err = exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
						"put", etcddir, string(jsonData)).Run()
					assert.NoError(t, err)
				}

				return vd
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", virtualVDiskPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wantVDisk := test.before(t)
			defer test.after(t)

			gotVDisk, err := etcd.VDisks().Get(context.Background(), wantVDisk.Items[thethreeVDiskID].LUNUUID, metav1.GetOptions{})
			assert.NoError(t, err)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].LUNUUID, gotVDisk.LUNUUID)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].DevLimit, gotVDisk.DevLimit)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].DevReservation, gotVDisk.DevReservation)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].DevWeight, gotVDisk.DevWeight)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].Format, gotVDisk.Format)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].GuestID, gotVDisk.GuestID)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].IOPSEnable, gotVDisk.IOPSEnable)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].IsDummy, gotVDisk.IsDummy)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].IsMetaDisk, gotVDisk.IsMetaDisk)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].MinorID, gotVDisk.MinorID)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].RepositoryID, gotVDisk.RepositoryID)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].Size, gotVDisk.Size)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].Type, gotVDisk.Type)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].Unmap, gotVDisk.Unmap)
			assert.Equal(t, wantVDisk.Items[thethreeVDiskID].VDiskMode, gotVDisk.VDiskMode)
		})
	}
}

func TestVDisk_Delete(t *testing.T) {
	var testNumber = 3
	id1 := uuid.Must(uuid.NewV4()).String()
	id2 := uuid.Must(uuid.NewV4()).String()
	id3 := uuid.Must(uuid.NewV4()).String()

	tests := []struct {
		name   string
		before func(t *testing.T) domain.VDiskList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.VDiskList {
				_ = exec.Command("/usr/bin/etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				vd := domain.VDiskList{}
				vd.ListMeta.TotalCount = int64(testNumber)
				vd.Items = make(map[string]*domain.VDisk)

				vd.Items[id1] = &domain.VDisk{
					DevLimit:       int64(0),
					DevReservation: int64(0),
					DevWeight:      int(3),
					Format:         int(2),
					GuestID:        "acb6c818-c6e5-4901-b434-d31ed6e256da",
					IOPSEnable:     false,
					IsDummy:        false,
					IsMetaDisk:     false,
					MinorID:        int(0),
					RepositoryID:   "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
					Size:           int64(32212254720),
					Type:           int(2),
					Unmap:          false,
					LUNUUID:        id1,
					VDiskMode:      rangeVDiskMode()[0],
				}
				vd.Items[id2] = &domain.VDisk{
					DevLimit:       int64(0),
					DevReservation: int64(0),
					DevWeight:      int(3),
					Format:         int(2),
					GuestID:        "acb6c818-c6e5-4901-b434-d31ed6e256db",
					IOPSEnable:     false,
					IsDummy:        false,
					IsMetaDisk:     false,
					MinorID:        int(0),
					RepositoryID:   "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
					Size:           int64(50000000000),
					Type:           int(2),
					Unmap:          false,
					LUNUUID:        id2,
					VDiskMode:      rangeVDiskMode()[1],
				}
				vd.Items[id3] = &domain.VDisk{
					DevLimit:       int64(0),
					DevReservation: int64(0),
					DevWeight:      int(3),
					Format:         int(2),
					GuestID:        "acb6c818-c6e5-4901-b434-d31ed6e256dc",
					IOPSEnable:     false,
					IsDummy:        false,
					IsMetaDisk:     false,
					MinorID:        int(0),
					RepositoryID:   "3e3809fa-b48b-4e31-b7fc-f474fc2a9768",
					Size:           int64(60000000000),
					Type:           int(2),
					Unmap:          false,
					LUNUUID:        id3,
					VDiskMode:      rangeVDiskMode()[2],
				}

				for _, v := range vd.Items {
					jsonData, err := json.Marshal(v)
					assert.NoError(t, err)
					etcddir := virtualVDiskPrefix + v.LUNUUID
					err = exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
						"put", etcddir, string(jsonData)).Run()
					assert.NoError(t, err)
				}
				return vd
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", virtualVDiskPrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wantVDisk := test.before(t)
			defer test.after(t)

			err := etcd.VDisks().Delete(context.Background(), id2, metav1.DeleteOptions{})
			assert.NoError(t, err)

			gotVDisk, err := etcd.VDisks().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)
			assert.Equal(t, wantVDisk.TotalCount-1, gotVDisk.TotalCount)
			assert.Equal(t, len(wantVDisk.Items)-1, len(gotVDisk.Items))

			_, ok := gotVDisk.Items[id2]
			assert.False(t, ok)

			for _, v := range gotVDisk.Items {
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].LUNUUID, v.LUNUUID)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].DevLimit, v.DevLimit)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].DevReservation, v.DevReservation)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].DevWeight, v.DevWeight)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].Format, v.Format)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].GuestID, v.GuestID)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].IOPSEnable, v.IOPSEnable)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].IsDummy, v.IsDummy)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].IsMetaDisk, v.IsMetaDisk)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].MinorID, v.MinorID)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].RepositoryID, v.RepositoryID)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].Size, v.Size)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].Type, v.Type)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].Unmap, v.Unmap)
				assert.Equal(t, wantVDisk.Items[v.LUNUUID].VDiskMode, v.VDiskMode)
			}
		})
	}
}

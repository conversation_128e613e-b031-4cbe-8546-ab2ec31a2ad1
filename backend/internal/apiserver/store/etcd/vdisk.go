package etcd

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/component-base/pkg/util/jsonutil"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
)

var _ store.VDisk = (*vdisks)(nil)

type vdisks struct {
	ds *datastore
}

func (vdisk *vdisks) Create(ctx context.Context, vdiskUUID string, disk *domain.VDisk, opts metav1.CreateOptions) error {
	err := vdisk.ds.Put(ctx, vdisk.getVDiskKey(vdiskUUID), jsonutil.ToString(disk))
	if err != nil {
		log.Errorf("[collVDisk_Etcd] vdisk create failed - %v", err)
		return err
	}
	return nil
}

func (vdisk *vdisks) Update(ctx context.Context, vdiskUUID string, disk *domain.VDisk, opts metav1.UpdateOptions) error {
	err := vdisk.ds.Put(ctx, vdisk.getVDiskKey(vdiskUUID), jsonutil.ToString(disk))
	if err != nil {
		log.Errorf("[collVDisk_Etcd] vdisk update failed - %v", err)
		return err
	}
	return nil
}

func (vdisk *vdisks) Get(ctx context.Context, vdiskUUID string, opts metav1.GetOptions) (*domain.VDisk, error) {
	disk, err := vdisk.ds.Get(ctx, vdisk.getVDiskKey(vdiskUUID))
	if err != nil {
		log.Errorf("[collVDisk_Etcd] vdisk get failed - %v", err)
		return &domain.VDisk{}, err
	}

	var vd domain.VDisk
	if err := json.Unmarshal(disk, &vd); err != nil {
		log.Errorf("[collVDisk_Etcd] vdisk unmarshal failed - %v", err)
		return &domain.VDisk{}, err
	}
	return &vd, nil
}

func (vdisk *vdisks) Delete(ctx context.Context, vdiskUUID string, opts metav1.DeleteOptions) error {
	_, err := vdisk.ds.Delete(ctx, vdisk.getVDiskKey(vdiskUUID))
	if err != nil {
		log.Errorf("[collVDisk_Etcd] vdisk delete failed - %v", err)
		return err
	}
	return nil
}

func (vdisk *vdisks) List(ctx context.Context, opts metav1.ListOptions) (*domain.VDiskList, error) {
	kvs, err := vdisk.ds.List(ctx, vdisk.getVDiskKey(""))
	if err != nil {
		log.Errorf("[collVDisk_Etcd] vdisk list failed: %v", err)
		return nil, err
	}

	res := &domain.VDiskList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(kvs)),
		},
	}

	res.Items = make(map[string]*domain.VDisk)
	for _, kv := range kvs {
		var vd domain.VDisk
		if err := json.Unmarshal(kv.Val, &vd); err != nil {
			log.Errorf("[collVDisk_Etcd] vdisk unmarshal failed - %v", err)
			return nil, err
		}

		paths := vdisk.ds.SplitEtcdKeyPath(kv.Key)
		if len(paths) <= 0 {
			log.Info("[collVDisk_Etcd] failed to obtain the vdisk id - etcd path have problem")
			return nil, err
		}
		res.Items[paths[0]] = &vd
	}

	return res, nil
}

// 无序输出虚拟盘的UUID
func (vdisk *vdisks) ListUUID(ctx context.Context, opts metav1.ListOptions) ([]string, error) {
	kvs, err := vdisk.ds.List(ctx, vdisk.getVDiskKey(""))
	if err != nil {
		return nil, err
	}

	uuids := make([]string, 0, len(kvs))
	for _, kv := range kvs {
		uuids = append(uuids, vdisk.ds.SplitEtcdKeyPath(kv.Key)[0])
	}

	// 对 uuids 进行排序
	sort.Strings(uuids)

	return uuids, nil
}

func newVDisks(ds *datastore) *vdisks { return &vdisks{ds: ds} }

var virtualVDiskPrefix = domain.VDiskPrefix

func (vdisk *vdisks) getVDiskKey(diskId string) string {
	return fmt.Sprintf("%s%s", virtualVDiskPrefix, diskId)
}

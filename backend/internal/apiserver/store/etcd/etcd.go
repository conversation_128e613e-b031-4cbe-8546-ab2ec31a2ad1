// Package etcd
/**
* @Project : terravirtualmachine
* @File    : etcd_test.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/20 17:38
**/

package etcd

import (
	"context"
	"crypto/tls"
	"fmt"
	"path"
	"strings"
	"sync"
	"time"

	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
	"gitlab.local/golibrary/errors"
	"go.etcd.io/etcd/api/v3/mvccpb"
	clientv3 "go.etcd.io/etcd/client/v3"
	"google.golang.org/grpc"
)

var BackendEtcdStore = initEtcd()

func initEtcd() store.Factory {
	etcdStore, err := GetEtcdFactoryOr(&options.EtcdOptions{
		Endpoints:      []string{"http://127.0.0.1:2379"},
		Timeout:        5,
		Namespace:      KeyPrefix,
		RequestTimeout: 2,
	}, nil)
	if err != nil {
		panic(err)
	}
	return etcdStore
}

// Watcher defines a etcd watcher.
type Watcher struct {
	watcher clientv3.Watcher   // etcd 的 Watcher 实例，用于监听 etcd 键值对的变化
	cancel  context.CancelFunc // 用于取消监听操作的上下文取消函数
}

// datastore 存储 etcd 相关的数据和操作
type datastore struct {
	cli            *clientv3.Client // etcd 的客户端实例，用于与 etcd 服务通信
	requestTimeout time.Duration    // 请求 etcd 服务的超时时间
	leaseExpire    int              // 租约的超时时间（秒）

	leaseID            clientv3.LeaseID // etcd 租约的唯一标识符
	onKeepaliveFailure func()           // 租约保活失败时的回调函数
	leaseLiving        bool             // 租约是否处于存活状态

	watchers  map[string]*Watcher // 存储对不同 key 的 etcd 监听器，key 是监听的前缀
	namespace string              // etcd 存储的命名空间，可用于对 key 添加前缀
}

// Networks 返回一个 NetworkStore 接口实例，具体实现由 newNetworks 函数提供
func (ds *datastore) Networks() store.NetworkStore {
	return newNetworks(ds)
}

func (ds *datastore) Images() store.ImageStore {
	return newImage(ds)
}

func (ds *datastore) RepositoryPool() store.RepositoryPoolStore {
	return newRepositoryPool(ds)
}

func (ds *datastore) Host() store.HostStore {
	return newHost(ds)
}

func (ds *datastore) VMs() store.VMStore { return newVMs(ds) }

func (ds *datastore) VDisks() store.VDisk { return newVDisks(ds) }

func (ds *datastore) VNics() store.VNicStore {
	return newVNics(ds)
}

func (ds *datastore) Logs() store.Log { return newLogs(ds) }

// Close 关闭 etcd 存储的客户端连接
func (ds *datastore) Close() error {
	if ds.cli != nil {
		return ds.cli.Close()
	}
	return nil
}

// defaultOnKeepaliveFailure 是一个默认的租约保活失败的处理函数，会打印警告日志
func defaultOnKeepaliveFailure() {
	log.Warn("etcdStore keepalive failure")
}

var (
	// etcdFactory 存储 etcd 的存储工厂实例
	etcdFactory store.Factory
	// once 用于确保 GetEtcdFactoryOr 函数仅执行一次初始化操作
	once sync.Once
)

// GetEtcdFactoryOr 根据提供的 etcd 选项创建或获取 etcd 存储工厂，如果提供的选项为空且 etcdFactory 尚未创建，则返回错误
func GetEtcdFactoryOr(opt *options.EtcdOptions, onKeepaliveFailure func()) (store.Factory, error) {
	if opt == nil && etcdFactory == nil {
		return nil, errors.New("failed to get etcd store factory")
	}

	var err error
	once.Do(func() {
		var (
			tlsCfg *tls.Config      // etcd 的 TLS 配置
			cli    *clientv3.Client // etcd 的客户端实例
		)
		// 获取 etcd 的 TLS 配置
		tlsCfg, err = opt.GetEtcdTLSConfig()
		if err != nil {
			return
		}
		if opt.UseTLS && tlsCfg == nil {
			err = errors.New("enable etcdFactory tls but tls config is empty")
			return
		}
		ds := &datastore{}
		if onKeepaliveFailure == nil {
			onKeepaliveFailure = defaultOnKeepaliveFailure
		}
		ds.onKeepaliveFailure = onKeepaliveFailure
		// 创建 etcd 客户端实例，设置相关配置
		cli, err = clientv3.New(clientv3.Config{
			Endpoints:   opt.Endpoints,
			DialTimeout: time.Duration(opt.Timeout) * time.Second,
			Username:    opt.Username,
			Password:    opt.Password,
			TLS:         tlsCfg,
			DialOptions: []grpc.DialOption{
				grpc.WithBlock(),
			},
		})
		if err != nil {
			return
		}
		ds.cli = cli
		ds.requestTimeout = time.Duration(opt.RequestTimeout) * time.Second
		ds.leaseExpire = opt.LeaseExpire
		ds.watchers = make(map[string]*Watcher)
		ds.namespace = opt.Namespace

		// 启动 etcd 会话
		err = ds.startSession()
		if err != nil {
			if e := ds.cli.Close(); e != nil {
				log.Errorf("etcdStore client close failed: %s", e)
			}
			return
		}
		etcdFactory = ds
	})

	if etcdFactory == nil || err != nil {
		return nil, fmt.Errorf("failed to get etcd store fatory, etcdFactory: %+v, error: %w", etcdFactory, err)
	}
	return etcdFactory, nil
}

// startSession 启动 etcd 会话，包括创建租约和启动租约保活机制
func (ds *datastore) startSession() error {
	ctx := context.Background()
	// 请求一个租约
	resp, err := ds.cli.Grant(ctx, int64(ds.leaseExpire))
	if err != nil {
		return errors.Wrap(err, "creates new lease failed")
	}
	ds.leaseID = resp.ID
	// 启动租约的保活机制
	ch, err := ds.cli.KeepAlive(ctx, resp.ID)
	if err != nil {
		return errors.Wrapf(err, "keep alive failed, lease id: %d", ds.leaseID)
	}
	ds.leaseLiving = true
	// 监听租约保活状态
	go func() {
		for {
			if _, ok := <-ch; !ok {
				ds.leaseLiving = false
				log.Error("failed to keep alive session")
				if ds.onKeepaliveFailure != nil {
					ds.onKeepaliveFailure()
				}
				break
			}
		}
	}()
	return nil
}

// Client 返回 etcd 的客户端实例
func (ds *datastore) Client() *clientv3.Client {
	return ds.cli
}

// SessionLiving 检查租约是否处于存活状态
func (ds *datastore) SessionLiving() bool {
	return ds.leaseLiving
}

// RestartSession 尝试重新启动 etcd 会话，若租约处于存活状态则返回错误
func (ds *datastore) RestartSession() error {
	if ds.leaseLiving {
		return errors.New("session is living, can't restart")
	}
	return ds.startSession()
}

// getKey 为 key 添加命名空间前缀（如果存在）
func (ds *datastore) getKey(key string) string {
	if ds.namespace != "" {
		return fmt.Sprintf("%s%s", ds.namespace, key)
	}
	return key
}

func keyWithPrefix(prefix, key string) string {
	if strings.HasPrefix(key, prefix) {
		return key
	}
	return path.Join(prefix, key)
}

// Put 存储 key-val 对，不使用租约
func (ds *datastore) Put(ctx context.Context, key, val string) error {
	return ds.put(ctx, key, val, false)
}

// PutSession 存储 key-val 对，使用租约
func (ds *datastore) PutSession(ctx context.Context, key, val string) error {
	return ds.put(ctx, key, val, true)
}

// PutWithLease 存储 key-val 对，并关联一个具有指定 TTL 的租约
func (ds *datastore) PutWithLease(ctx context.Context, key, val string, ttlSeconds int64) error {
	resp, err := ds.grantLease(ctx, ttlSeconds)
	if err != nil {
		return fmt.Errorf("grant lease failed, err: %w", err)
	}
	nctx, cancel := context.WithTimeout(ctx, ds.requestTimeout)
	defer cancel()

	key = ds.getKey(key)
	leaseID := resp.ID
	opts := []clientv3.OpOption{
		clientv3.WithLease(leaseID),
	}
	if _, err := ds.cli.Put(nctx, key, val, opts...); err != nil {
		return errors.Wrap(err, "put key-val pair to etcd failed")
	}
	return nil
}

// put 存储 key-val 对，可以选择是否使用租约
func (ds *datastore) put(ctx context.Context, key, val string, session bool) error {
	nctx, cancel := context.WithTimeout(ctx, ds.requestTimeout)
	defer cancel()

	key = keyWithPrefix(ds.namespace, key)

	// if !strings.HasSuffix(key, "/") {
	// 	key += "/"
	// }

	if session {
		// 使用租约存储 key-val 对
		if _, err := ds.cli.Put(nctx, key, val, clientv3.WithLease(ds.leaseID)); err != nil {
			return errors.Wrap(err, "put key-val pair to etcd failed")
		}
		return nil
	}
	// 不使用租约存储
	if _, err := ds.cli.Put(nctx, key, val); err != nil {
		return errors.Wrap(err, "put key-val pair to etcd failed")
	}
	return nil
}

// grantLease 申请一个具有指定 TTL 的租约
func (ds *datastore) grantLease(ctx context.Context, ttlSeconds int64) (*clientv3.LeaseGrantResponse, error) {
	nctx, cancel := context.WithTimeout(ctx, ds.requestTimeout)
	defer cancel()
	resp, err := ds.cli.Grant(nctx, ttlSeconds)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// Get 从 etcd 获取 key 对应的 val，如果 key 不存在则返回错误
func (ds *datastore) Get(ctx context.Context, key string) ([]byte, error) {
	nctx, cancel := context.WithTimeout(ctx, ds.requestTimeout)
	defer cancel()

	key = keyWithPrefix(ds.namespace, key)
	resp, err := ds.cli.Get(nctx, key)
	if err != nil {
		return nil, errors.Wrap(err, "get key from etcd failed")
	}
	if len(resp.Kvs) == 0 {
		return nil, errors.New("key not found")
	}
	return resp.Kvs[0].Value, nil
}

// EtcdKeyVal 定义 etcd 存储的 key-val 对
type EtcdKeyVal struct {
	Key string
	Val []byte
}

// List 获取指定前缀的 key-val 对列表
// 返回的列表中的 key 不包含前缀，但是可能包含后缀，比如：
// prefix: /twm/live_cluster/hosts/
// key: /twm/live_cluster/hosts/1234567890
// 返回的 key: 1234567890
// prefix: /twm/live_cluster/hosts/
// key: /twm/live_cluster/hosts/1234567890/interfaces/0987654321
// 返回的 key: 1234567890/interfaces/0987654321
func (ds *datastore) List(ctx context.Context, prefix string) ([]EtcdKeyVal, error) {
	nctx, cancel := context.WithTimeout(ctx, ds.requestTimeout)
	defer cancel()

	prefix = keyWithPrefix(ds.namespace, prefix)

	// if !strings.HasSuffix(prefix, "/") {
	// 	prefix += "/"
	// }

	resp, err := ds.cli.KV.Get(nctx, prefix, clientv3.WithPrefix(), clientv3.WithSort(clientv3.SortByKey, clientv3.SortDescend))
	if err != nil {
		return nil, errors.Wrap(err, "get key from etcd failed")
	}
	res := make([]EtcdKeyVal, len(resp.Kvs))
	for i := 0; i < len(resp.Kvs); i++ {
		res[i] = EtcdKeyVal{
			Key: string(resp.Kvs[i].Key[len(prefix):]),
			Val: resp.Kvs[i].Value,
		}
	}
	return res, nil
}

// Cancel 取消 etcd 监听器
func (w *Watcher) Cancel() {
	w.watcher.Close()
	w.cancel()
}

func (ds *datastore) Watch(ctx context.Context,
	prefix string,
	onCreate store.CreateEventFunc,
	onModify store.ModifyEventFunc,
	onDelete store.DeleteEventFunc,
) error {
	if _, ok := ds.watchers[prefix]; ok {
		return fmt.Errorf("watch prefix %s already registered", prefix)
	}

	watcher := clientv3.NewWatcher(ds.cli)
	nctx, cancel := context.WithCancel(ctx)
	ds.watchers[prefix] = &Watcher{
		watcher: watcher,
		cancel:  cancel,
	}

	prefix = ds.getKey(prefix)

	// 开始监听，使用 WithPrefix 监听指定前缀的 key-val 对，WithPrevKV 可获取前一个 key-val 对
	rch := watcher.Watch(nctx, prefix, clientv3.WithPrefix(), clientv3.WithPrevKV())
	go func() {
		for wresp := range rch {
			for _, ev := range wresp.Events {
				key := ev.Kv.Key[len(prefix):]
				if ev.PrevKv == nil {
					onCreate(nctx, key, ev.Kv.Value)
				} else {
					switch ev.Type {
					case mvccpb.PUT:
						onModify(nctx, key, ev.PrevKv.Value, ev.Kv.Value)
					case mvccpb.DELETE:
						if onDelete != nil {
							onDelete(nctx, key)
						}
					}
				}
			}
		}
		log.Infof("stop watching %s", prefix)
	}()
	return nil
}

// UnWatch 取消对指定前缀的监听
func (ds *datastore) UnWatch(prefix string) {
	watcher, ok := ds.watchers[prefix]
	if ok {
		log.Debugf("unwatch %s", prefix)
		watcher.cancel()
		delete(ds.watchers, prefix)
	} else {
		log.Debugf("prefix %s not watched", prefix)
	}
}

// Delete 删除 etcd 中的 key-val 对，并返回删除前的值（如果存在）
func (ds *datastore) Delete(ctx context.Context, key string) ([]byte, error) {
	nctx, cancel := context.WithTimeout(ctx, ds.requestTimeout)
	defer cancel()
	key = keyWithPrefix(ds.namespace, key)

	// if !strings.HasSuffix(key, "/") {
	// 	key += "/"
	// }

	resp, err := ds.cli.Delete(nctx, key, clientv3.WithPrevKV())
	if err != nil {
		return nil, errors.Wrap(err, "delete key from etcd failed")
	}
	if resp.Deleted == 1 {
		return resp.PrevKvs[0].Value, nil
	}
	return nil, nil
}

func (ds *datastore) SplitEtcdKeyPath(etcdPath string) []string {
	paths := strings.Split(etcdPath, "/")
	return paths
}

// CountKeys 返回指定目录下的键总数
func (ds *datastore) CountKeys(ctx context.Context, prefix string) (int, error) {
	// 使用 WithPrefix 获取指定前缀的所有键
	resp, err := ds.cli.Get(ctx, prefix, clientv3.WithPrefix(), clientv3.WithKeysOnly())
	if err != nil {
		return 0, errors.Wrap(err, "failed to get keys from etcd")
	}

	// 返回键的数量
	return len(resp.Kvs), nil
}

package etcd

import (
	"context"
	"encoding/json"
	"fmt"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/component-base/pkg/util/jsonutil"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/golibrary/errors"
)

var _ store.VMStore = (*vms)(nil)

type vms struct {
	ds *datastore
}

func (vm *vms) ListStatus(ctx context.Context, opts metav1.ListOptions) ([]domain.StatusEntry, error) {
	panic("no finished")
}
func (vm *vms) List(ctx context.Context, opts metav1.ListOptions) (*domain.VMInstanceList, error) {
	kvs, err := vm.ds.List(ctx, domain.GuestEtcdPrefix)
	if err != nil {
		return nil, err
	}

	res := &domain.VMInstanceList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(kvs)),
		},
	}

	res.Items = make(map[string]*domain.VMInstance)
	for _, kv := range kvs {
		var tmpVM domain.VMInstance

		paths := vm.ds.SplitEtcdKeyPath(kv.Key)
		if err := json.Unmarshal(kv.Val, &tmpVM); err != nil {
			return nil, errors.Wrap(err, "unmarshal to VMInstance struct failed")
		}

		if len(paths) <= 0 {
			log.Info("failed to obtain the VMInstance id,etcd path have problem")
			return nil, errors.Wrap(err, "failed to obtain the VMInstance Id")
		}
		res.Items[paths[0]] = &tmpVM
	}

	return res, nil
}

func (vm *vms) Get(ctx context.Context, guestId string, opts metav1.GetOptions) (*domain.VMInstance, error) {
	by, err := vm.ds.Get(ctx, etcdKeyPath(guestId))
	if err != nil {
		return nil, err
	}

	res := &domain.VMInstance{}

	if err := json.Unmarshal(by, res); err != nil {
		return nil, errors.Wrap(err, "unmarshal to VMInstance struct failed")
	}

	return res, nil
}

func (vm *vms) Create(ctx context.Context, guestId string, ins *domain.VMInstance, opts metav1.CreateOptions) error {
	return vm.ds.Put(ctx, etcdKeyPath(guestId), jsonutil.ToString(ins))
}

func (vm *vms) Update(ctx context.Context, guestId string, ins *domain.VMInstance, opts metav1.CreateOptions) error {
	return vm.ds.Put(ctx, etcdKeyPath(guestId), jsonutil.ToString(ins))
}

func (vm *vms) Delete(ctx context.Context, guestId string, opts metav1.DeleteOptions) error {
	_, err := vm.ds.Delete(ctx, etcdKeyPath(guestId))
	return err
}

func newVMs(ds *datastore) *vms { return &vms{ds: ds} }

func etcdKeyPath(imgId string) string {
	return fmt.Sprintf(domain.GuestEtcdPrefix+"%v", imgId)
}

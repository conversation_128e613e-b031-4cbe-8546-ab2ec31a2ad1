package etcd

import (
	"context"
	"encoding/json"
	"os/exec"
	"strconv"
	"testing"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	uuid "github.com/satori/go.uuid"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

func makeTestImageHost() []string {
	host := make([]string, 0)

	host = append(host, []string{"333-222-111", "444-555-666", "555-666-777", "888-999-111"}...)
	return host
}

func TestImages_List(t *testing.T) {
	var testNumber = 6
	tests := []struct {
		name   string
		before func(t *testing.T) domain.ImageList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.ImageList {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				images := domain.ImageList{}
				images.ListMeta.TotalCount = int64(testNumber)
				images.Items = make(map[string]*domain.Image)

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()

					images.Items[id] = &domain.Image{
						Name:      "ubuntu 22.01" + "-" + strconv.Itoa(i),
						Type:      domain.ISO,
						RealFiles: "test_0.iso" + "-" + strconv.Itoa(i),
						Repos:     makeTestImageHost(),
						FileSize:  32135324,
					}
				}

				for id, img := range images.Items {
					jsonData, err := json.Marshal(img)
					assert.NoError(t, err)
					etcddir := domain.ImagePrefix + id
					err = exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
						"put", etcddir, string(jsonData)).Run()
					assert.NoError(t, err)

				}

				return images
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", domain.ImagePrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantImages := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      KeyPrefix,
				RequestTimeout: 2,
			}, nil)
			assert.NoError(t, err)

			images, err := etcd.Images().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)

			assert.Equal(t, wantImages.TotalCount, images.TotalCount)
			assert.Equal(t, len(wantImages.Items), len(images.Items))
			for id, wantImage := range wantImages.Items {
				img, ok := images.Items[id]

				assert.True(t, ok)
				assert.Equal(t, wantImage.RealFiles, img.RealFiles)
				assert.Equal(t, wantImage.Name, img.Name)
				assert.Equal(t, wantImage.Type, img.Type)
				assert.Equal(t, wantImage.Repos, img.Repos)
				assert.Equal(t, wantImage.FileSize, img.FileSize)
			}

			defer tt.after(t)
		})
	}
}

func TestImages_Create(t *testing.T) {
	var testNumber = 6
	tests := []struct {
		name   string
		before func(t *testing.T) domain.ImageList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.ImageList {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
					Endpoints:      []string{"http://127.0.0.1:2379"},
					Timeout:        5,
					Namespace:      KeyPrefix,
					RequestTimeout: 2,
				}, nil)
				assert.NoError(t, err)

				images := domain.ImageList{}
				images.ListMeta.TotalCount = int64(testNumber)
				images.Items = make(map[string]*domain.Image)

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()

					images.Items[id] = &domain.Image{
						Name:      "ubuntu 22.01" + "-" + strconv.Itoa(i),
						Type:      domain.ISO,
						RealFiles: "test_0.iso" + "-" + strconv.Itoa(i),
						Repos:     makeTestImageHost(),
						FileSize:  32135324,
					}
				}

				for id, img := range images.Items {
					err = etcd.Images().Create(context.Background(), id, img, metav1.CreateOptions{})
					assert.NoError(t, err)

				}

				return images
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", domain.ImagePrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantImages := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      KeyPrefix,
				RequestTimeout: 2,
			}, nil)
			assert.NoError(t, err)

			images, err := etcd.Images().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)

			assert.Equal(t, wantImages.TotalCount, images.TotalCount)
			assert.Equal(t, len(wantImages.Items), len(images.Items))
			for id, wantImage := range wantImages.Items {
				img, ok := images.Items[id]

				assert.True(t, ok)
				assert.Equal(t, wantImage.RealFiles, img.RealFiles)
				assert.Equal(t, wantImage.Name, img.Name)
				assert.Equal(t, wantImage.Type, img.Type)
				assert.Equal(t, wantImage.Repos, img.Repos)
				assert.Equal(t, wantImage.FileSize, img.FileSize)
			}

			// defer tt.after(t)
		})
	}
}

func TestImages_Delete(t *testing.T) {
	var testNumber = 6
	tests := []struct {
		name   string
		before func(t *testing.T) domain.ImageList
		after  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.ImageList {
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
					Endpoints:      []string{"http://127.0.0.1:2379"},
					Timeout:        5,
					Namespace:      KeyPrefix,
					RequestTimeout: 2,
				}, nil)
				assert.NoError(t, err)

				images := domain.ImageList{}
				images.ListMeta.TotalCount = int64(testNumber)
				images.Items = make(map[string]*domain.Image)

				for i := 0; i < testNumber; i++ {
					id := uuid.Must(uuid.NewV4()).String()

					images.Items[id] = &domain.Image{
						Name:      "ubuntu 22.01" + "-" + strconv.Itoa(i),
						Type:      domain.ISO,
						RealFiles: "test_0.iso" + "-" + strconv.Itoa(i),
						Repos:     makeTestImageHost(),
						FileSize:  32135324,
					}
				}

				for id, img := range images.Items {
					err = etcd.Images().Create(context.Background(), id, img, metav1.CreateOptions{})
					assert.NoError(t, err)

				}

				return images
			},
			after: func(t *testing.T) {
				// 清理测试数据
				err := exec.Command("/usr/bin/etcdctl", "--endpoints=http://127.0.0.1:2379",
					"del", domain.ImagePrefix, "--prefix").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantImages := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5,
				Namespace:      KeyPrefix,
				RequestTimeout: 2,
			}, nil)
			assert.NoError(t, err)

			for del_id, _ := range wantImages.Items {
				err := etcd.Images().Delete(context.Background(), del_id, metav1.DeleteOptions{})
				assert.NoError(t, err)

				wantdel := true
				images, err := etcd.Images().List(context.Background(), metav1.ListOptions{})
				assert.NoError(t, err)
				for id, _ := range images.Items {
					if del_id == id {
						wantdel = false
						break
					}
				}
				assert.True(t, wantdel)
			}

			// defer tt.after(t)
		})
	}
}

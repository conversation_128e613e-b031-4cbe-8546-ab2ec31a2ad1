package etcd

import (
	"context"
	"fmt"
	"os/exec"
	"testing"
	"time"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

func TestLog_Create(t *testing.T) {
	tests := []struct {
		name   string
		before func(t *testing.T) domain.LogList
		after  func(t *testing.T)
	}{
		{
			name: "test1_create_add_log",
			before: func(t *testing.T) domain.LogList {
				_ = exec.Command("/usr/bin/etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/", "--prefix").Run()

				testDataList := domain.LogList{}
				testDataList.Items = make(map[string]domain.LogEntry)

				for i := 0; i < 4; i++ {
					testDataList.Items[fmt.Sprintf("%d", i+1)] = domain.LogEntry{
						Message:  fmt.Sprintf("The virtual machine [test-%d] has been edited: [Virtual disks added]", i),
						Priority: 3,
						Time:     time.Now().Unix(),
					}
				}

				return testDataList
			},
			after: func(t *testing.T) {
				// 清理可能存在的脏数据
				_ = exec.Command("/usr/bin/etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", logPrefixAdd, "--prefix",
				).Run()
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wantData := test.before(t)
			defer test.after(t)

			for _, item := range wantData.Items {
				err := etcd.Logs().Create(context.Background(), &item, metav1.CreateOptions{})
				assert.NoError(t, err)
			}

			for i, item := range wantData.Items {
				actualData, err := etcd.Logs().GetAddLog(context.Background(), i, metav1.GetOptions{})
				assert.NoError(t, err)
				assert.Equal(t, item.Message, actualData.Message)
				assert.Equal(t, item.Priority, actualData.Priority)
			}
		})
	}
}

func TestLog_Clean(t *testing.T) {
	tests := []struct {
		name   string
		before func(t *testing.T) []domain.LogDel
		after  func(t *testing.T)
	}{
		{
			name: "test1_clean_log",
			before: func(t *testing.T) []domain.LogDel {
				_ = exec.Command("/usr/bin/etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "twm/live_cluster/", "--prefix",
				).Run()

				testDataList := domain.LogList{}
				testDataList.Items = make(map[string]domain.LogEntry)
				for i := 0; i < 4; i++ {
					testDataList.Items[fmt.Sprintf("%d", i+1)] = domain.LogEntry{
						Message:  fmt.Sprintf("The virtual machine [test-%d] has been edited: [Virtual disks added]", i),
						Priority: 3,
						Time:     time.Now().Unix(),
					}
					item := testDataList.Items[fmt.Sprintf("%d", i+1)]
					err := etcd.Logs().Create(context.Background(), &item, metav1.CreateOptions{})
					assert.NoError(t, err)
				}

				len, err := etcd.Logs().GetAddLen(context.Background(), metav1.GetOptions{})
				assert.NoError(t, err)

				return []domain.LogDel{
					{
						Start: -1,
						End:   len,
					},
				}
			},
			after: func(t *testing.T) {
				// 清理可能存在的脏数据
				_ = exec.Command("/usr/bin/etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", logPrefixDel, "--prefix",
				).Run()
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wantData := test.before(t)
			defer test.after(t)

			for _, item := range wantData {
				err := etcd.Logs().Clear(context.Background(), &item, metav1.DeleteOptions{})
				assert.NoError(t, err)

				actualData, err := etcd.Logs().GetDelLog(context.Background(), fmt.Sprintf("%d", 1), metav1.GetOptions{})
				assert.NoError(t, err)
				assert.Equal(t, item.Start, actualData.Start)
				assert.Equal(t, item.End, actualData.End)
			}
		})
	}

}

func TestLog_List(t *testing.T) {
	tests := []struct {
		name   string
		before func(t *testing.T) domain.LogList
		after  func(t *testing.T)
	}{
		{
			name: "test1_list_log",
			before: func(t *testing.T) domain.LogList {
				_ = exec.Command("/usr/bin/etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "twm/live_cluster/", "--prefix",
				).Run()

				testDataList := domain.LogList{}
				testDataList.Items = make(map[string]domain.LogEntry)
				for i := 0; i < 4; i++ {
					testDataList.Items[fmt.Sprintf("%d", i+1)] = domain.LogEntry{
						Message:  fmt.Sprintf("The virtual machine [test-%d] has been edited: [Virtual disks added]", i),
						Priority: 3,
						Time:     time.Now().Unix(),
					}
					item := testDataList.Items[fmt.Sprintf("%d", i+1)]
					err := etcd.Logs().Create(context.Background(), &item, metav1.CreateOptions{})
					assert.NoError(t, err)
				}

				return testDataList
			},

			after: func(t *testing.T) {
				// 清理可能存在的脏数据
				_ = exec.Command("/usr/bin/etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", logPrefixAdd, "--prefix",
				).Run()
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wantData := test.before(t)
			defer test.after(t)

			actualData, err := etcd.Logs().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)
			assert.Equal(t, len(wantData.Items), len(actualData.Items))

			for i, item := range wantData.Items {
				assert.Equal(t, item.Message, actualData.Items[i].Message)
				assert.Equal(t, item.Priority, actualData.Items[i].Priority)
			}
		})
	}
}

package etcd

import (
	"context"
	"fmt"
	"os/exec"
	"testing"

	"github.com/marmotedu/component-base/pkg/json"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

func TestVNic_List(t *testing.T) {
	tests := []struct {
		name            string
		before          func(t *testing.T) domain.VNicList
		after           func(t *testing.T)
		wanvtinterfaces domain.VNicList
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.VNicList {
				// 准备 /twm/live_cluster/virtual_interfaces/{virtual_interface_id} 测试数据
				// 清理
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interfaces/", "--prefix").Run()
				// 准备
				vIfaces := domain.VNicList{
					ListMeta: metav1.ListMeta{
						TotalCount: 3,
					},
					Items: []*domain.VNic{
						{
							VNicID:      "virtual_interface_id11",
							GuestID:     "guest_id",
							GuestName:   "guest_name111",
							MacAddress:  "00:00:00:00:00:11",
							NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
							NetworkName: "Default VM Network",
							VNicType:    1,
							PreferSRIOV: false,
							Running: map[string]*domain.VNicruningList{
								"9b5910c9-557c-42a7-b592-7996d606dcc0": {
									ListMeta: metav1.ListMeta{
										TotalCount: 1,
									},
									Items: []*domain.VNicruning{
										{
											InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
											InterfaceName: "ovs_eth0",
											OVSPort:       "viface111",
											UseVf:         false,
											VfPoolName:    "",
										},
									},
								},
							},
						},
						{
							VNicID:      "virtual_interface_id22",
							GuestID:     "guest_id",
							GuestName:   "guest_name22",
							MacAddress:  "00:00:00:00:00:22",
							NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
							NetworkName: "Default VM Network",
							VNicType:    1,
							PreferSRIOV: false,
							Running: map[string]*domain.VNicruningList{
								"9b5910c9-557c-42a7-b592-7996d606dcc0": {
									ListMeta: metav1.ListMeta{
										TotalCount: 1,
									},
									Items: []*domain.VNicruning{
										{
											InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
											InterfaceName: "ovs_eth0",
											OVSPort:       "viface222",
											UseVf:         false,
											VfPoolName:    "",
										},
									},
								},
							},
						},
						{
							VNicID:      "virtual_interface_id33",
							GuestID:     "guest_id",
							GuestName:   "guest_name33",
							MacAddress:  "00:00:00:00:00:33",
							NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
							NetworkName: "Default VM Network",
							VNicType:    1,
							PreferSRIOV: false,
							Running: map[string]*domain.VNicruningList{
								"9b5910c9-557c-42a7-b592-7996d606dcc0": {
									ListMeta: metav1.ListMeta{
										TotalCount: 1,
									},
									Items: []*domain.VNicruning{
										{
											InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
											InterfaceName: "ovs_eth0",
											OVSPort:       "viface333",
											UseVf:         false,
											VfPoolName:    "",
										},
									},
								},
							},
						},
					},
				}
				for _, vIface := range vIfaces.Items {
					jsonData, err := json.Marshal(vIface)
					assert.NoError(t, err)
					err = exec.Command("etcdctl",
						"--endpoints=http://127.0.0.1:2379",
						"put", fmt.Sprintf("/twm/live_cluster/virtual_interfaces/%s", vIface.VNicID), string(jsonData)).Run()
					assert.NoError(t, err)
				}
				return vIfaces
			},
			after: func(t *testing.T) {
				//清理 /twm/live_cluster/virtual_interfaces/{virtual_interface_id} 测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interfaces/", "--prefix").Run()
				assert.NoError(t, err)
			},
			wanvtinterfaces: domain.VNicList{
				ListMeta: metav1.ListMeta{
					TotalCount: 3,
				},
				Items: []*domain.VNic{
					{
						VNicID:      "virtual_interface_id11",
						GuestID:     "guest_id",
						GuestName:   "guest_name111",
						MacAddress:  "00:00:00:00:00:11",
						NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
						NetworkName: "Default VM Network",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"9b5910c9-557c-42a7-b592-7996d606dcc0": {
								ListMeta: metav1.ListMeta{
									TotalCount: 1,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
										InterfaceName: "ovs_eth0",
										OVSPort:       "viface111",
										UseVf:         false,
										VfPoolName:    "",
									},
								},
							},
						},
					},
					{
						VNicID:      "virtual_interface_id22",
						GuestID:     "guest_id",
						GuestName:   "guest_name22",
						MacAddress:  "00:00:00:00:00:22",
						NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
						NetworkName: "Default VM Network",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"9b5910c9-557c-42a7-b592-7996d606dcc0": {
								ListMeta: metav1.ListMeta{
									TotalCount: 1,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
										InterfaceName: "ovs_eth0",
										OVSPort:       "viface222",
										UseVf:         false,
										VfPoolName:    "",
									},
								},
							},
						},
					}, {
						VNicID:      "virtual_interface_id33",
						GuestID:     "guest_id",
						GuestName:   "guest_name33",
						MacAddress:  "00:00:00:00:00:33",
						NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
						NetworkName: "Default VM Network",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"9b5910c9-557c-42a7-b592-7996d606dcc0": {
								ListMeta: metav1.ListMeta{
									TotalCount: 1,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
										InterfaceName: "ovs_eth0",
										OVSPort:       "viface333",
										UseVf:         false,
										VfPoolName:    "",
									},
								},
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wanvtinterfaces := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5000,
				Namespace:      KeyPrefix,
				RequestTimeout: 2000,
			}, nil)
			assert.NoError(t, err)

			vinterfaces, err := etcd.VNics().List(context.Background(), "guest_id", metav1.ListOptions{})
			assert.NoError(t, err)

			assert.Equal(t, wanvtinterfaces.TotalCount, vinterfaces.TotalCount)
			assert.Equal(t, len(wanvtinterfaces.Items), len(vinterfaces.Items))

			for _, wantvinterface := range wanvtinterfaces.Items {
				wantVinterface := false
				for _, vinterface := range vinterfaces.Items {
					if wantvinterface.VNicID == vinterface.VNicID {

						assert.Equal(t, wantvinterface.GuestID, vinterface.GuestID)

						// 断言虚拟接口的基本信息
						assert.Equal(t, wantvinterface.GuestName, vinterface.GuestName)
						assert.Equal(t, wantvinterface.MacAddress, vinterface.MacAddress)
						assert.Equal(t, wantvinterface.NetworkID, vinterface.NetworkID)
						assert.Equal(t, wantvinterface.NetworkName, vinterface.NetworkName)
						assert.Equal(t, wantvinterface.VNicType, vinterface.VNicType)
						assert.Equal(t, wantvinterface.PreferSRIOV, vinterface.PreferSRIOV)

						//断言虚拟接口的运行信息
						assert.Equal(t, len(wantvinterface.Running), len(vinterface.Running))
						for hostID, wantruninginfolist := range wantvinterface.Running {
							assert.Equal(t, wantruninginfolist.TotalCount, vinterface.Running[hostID].TotalCount)
							for i := 0; i < len(wantruninginfolist.Items); i++ {
								assert.Equal(t, wantruninginfolist.Items[i].InterfaceID, vinterface.Running[hostID].Items[i].InterfaceID)
								assert.Equal(t, wantruninginfolist.Items[i].InterfaceName, vinterface.Running[hostID].Items[i].InterfaceName)
								assert.Equal(t, wantruninginfolist.Items[i].OVSPort, vinterface.Running[hostID].Items[i].OVSPort)
								assert.Equal(t, wantruninginfolist.Items[i].UseVf, vinterface.Running[hostID].Items[i].UseVf)
								assert.Equal(t, wantruninginfolist.Items[i].VfPoolName, vinterface.Running[hostID].Items[i].VfPoolName)
							}
						}

						wantVinterface = true
					}
				}
				assert.True(t, wantVinterface)
			} // end of range wanvtinterfaces.Items

			defer tt.after(t)
		})
	}
}

func TestVNic_Create(t *testing.T) {
	tests := []struct {
		name            string
		before          func(t *testing.T) domain.VNicList
		after           func(t *testing.T)
		wanvtinterfaces domain.VNicList
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.VNicList {
				// 准备 /twm/live_cluster/virtual_interfaces/{virtual_interface_id} 测试数据
				// 清理
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interfaces/", "--prefix").Run()
				// 准备
				vIfaces := domain.VNicList{
					ListMeta: metav1.ListMeta{
						TotalCount: 3,
					},
					Items: []*domain.VNic{
						{
							VNicID:      "virtual_interface_id11",
							GuestID:     "guest_id",
							GuestName:   "guest_name111",
							MacAddress:  "00:00:00:00:00:11",
							NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
							NetworkName: "Default VM Network",
							VNicType:    1,
							PreferSRIOV: false,
							Running: map[string]*domain.VNicruningList{
								"9b5910c9-557c-42a7-b592-7996d606dcc0": {
									ListMeta: metav1.ListMeta{
										TotalCount: 1,
									},
									Items: []*domain.VNicruning{
										{
											InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
											InterfaceName: "ovs_eth0",
											OVSPort:       "viface111",
											UseVf:         false,
											VfPoolName:    "",
										},
									},
								},
							},
						},
						{
							VNicID:      "virtual_interface_id22",
							GuestID:     "guest_id",
							GuestName:   "guest_name22",
							MacAddress:  "00:00:00:00:00:22",
							NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
							NetworkName: "Default VM Network",
							VNicType:    1,
							PreferSRIOV: false,
							Running: map[string]*domain.VNicruningList{
								"9b5910c9-557c-42a7-b592-7996d606dcc0": {
									ListMeta: metav1.ListMeta{
										TotalCount: 1,
									},
									Items: []*domain.VNicruning{
										{
											InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
											InterfaceName: "ovs_eth0",
											OVSPort:       "viface222",
											UseVf:         false,
											VfPoolName:    "",
										},
									},
								},
							},
						},
						{
							VNicID:      "virtual_interface_id33",
							GuestID:     "guest_id",
							GuestName:   "guest_name33",
							MacAddress:  "00:00:00:00:00:33",
							NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
							NetworkName: "Default VM Network",
							VNicType:    1,
							PreferSRIOV: false,
							Running: map[string]*domain.VNicruningList{
								"9b5910c9-557c-42a7-b592-7996d606dcc0": {
									ListMeta: metav1.ListMeta{
										TotalCount: 1,
									},
									Items: []*domain.VNicruning{
										{
											InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
											InterfaceName: "ovs_eth0",
											OVSPort:       "viface333",
											UseVf:         false,
											VfPoolName:    "",
										},
									},
								},
							},
						},
					},
				}
				// for _, vIface := range vIfaces.Items {
				// 	jsonData, err := json.Marshal(vIface)
				// 	assert.NoError(t, err)
				// 	err = exec.Command("etcdctl",
				// 		"--endpoints=http://127.0.0.1:2379",
				// 		"put", fmt.Sprintf("/twm/live_cluster/virtual_interfaces/%s", vIface.VNicID), string(jsonData)).Run()
				// 	assert.NoError(t, err)
				// }
				return vIfaces
			},
			after: func(t *testing.T) {
				//清理 /twm/live_cluster/virtual_interfaces/{virtual_interface_id} 测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interfaces/", "--prefix").Run()
				assert.NoError(t, err)
			},
			wanvtinterfaces: domain.VNicList{
				ListMeta: metav1.ListMeta{
					TotalCount: 3,
				},
				Items: []*domain.VNic{
					{
						VNicID:      "virtual_interface_id11",
						GuestID:     "guest_id",
						GuestName:   "guest_name111",
						MacAddress:  "00:00:00:00:00:11",
						NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
						NetworkName: "Default VM Network",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"9b5910c9-557c-42a7-b592-7996d606dcc0": {
								ListMeta: metav1.ListMeta{
									TotalCount: 1,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
										InterfaceName: "ovs_eth0",
										OVSPort:       "viface111",
										UseVf:         false,
										VfPoolName:    "",
									},
								},
							},
						},
					},
					{
						VNicID:      "virtual_interface_id22",
						GuestID:     "guest_id",
						GuestName:   "guest_name22",
						MacAddress:  "00:00:00:00:00:22",
						NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
						NetworkName: "Default VM Network",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"9b5910c9-557c-42a7-b592-7996d606dcc0": {
								ListMeta: metav1.ListMeta{
									TotalCount: 1,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
										InterfaceName: "ovs_eth0",
										OVSPort:       "viface222",
										UseVf:         false,
										VfPoolName:    "",
									},
								},
							},
						},
					}, {
						VNicID:      "virtual_interface_id33",
						GuestID:     "guest_id",
						GuestName:   "guest_name33",
						MacAddress:  "00:00:00:00:00:33",
						NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
						NetworkName: "Default VM Network",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"9b5910c9-557c-42a7-b592-7996d606dcc0": {
								ListMeta: metav1.ListMeta{
									TotalCount: 1,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
										InterfaceName: "ovs_eth0",
										OVSPort:       "viface333",
										UseVf:         false,
										VfPoolName:    "",
									},
								},
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wanvtinterfaces := tt.wanvtinterfaces

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5000,
				Namespace:      KeyPrefix,
				RequestTimeout: 2000,
			}, nil)
			assert.NoError(t, err)

			vifaces_param := tt.before(t)
			for _, viface := range vifaces_param.Items {
				err = etcd.VNics().Create(context.Background(), viface, metav1.CreateOptions{})
				assert.NoError(t, err)

			}

			vinterfaces, err := etcd.VNics().List(context.Background(), "guest_id", metav1.ListOptions{})
			assert.NoError(t, err)

			assert.Equal(t, wanvtinterfaces.TotalCount, vinterfaces.TotalCount)
			assert.Equal(t, len(wanvtinterfaces.Items), len(vinterfaces.Items))

			// 补充测试逻辑
			assert.Equal(t, tt.wanvtinterfaces.TotalCount, vinterfaces.TotalCount)
			assert.Equal(t, len(tt.wanvtinterfaces.Items), len(vinterfaces.Items))

			for _, wantvinterface := range tt.wanvtinterfaces.Items {
				wantVinterface := false
				for _, vinterface := range vinterfaces.Items {
					if wantvinterface.VNicID == vinterface.VNicID {

						assert.Equal(t, wantvinterface.GuestID, vinterface.GuestID)

						// 断言虚拟接口的基本信息
						assert.Equal(t, wantvinterface.GuestName, vinterface.GuestName)
						assert.Equal(t, wantvinterface.MacAddress, vinterface.MacAddress)
						assert.Equal(t, wantvinterface.NetworkID, vinterface.NetworkID)
						assert.Equal(t, wantvinterface.NetworkName, vinterface.NetworkName)
						assert.Equal(t, wantvinterface.VNicType, vinterface.VNicType)
						assert.Equal(t, wantvinterface.PreferSRIOV, vinterface.PreferSRIOV)

						//断言虚拟接口的运行信息
						assert.Equal(t, len(wantvinterface.Running), len(vinterface.Running))
						for hostID, wantruninginfolist := range wantvinterface.Running {
							assert.Equal(t, wantruninginfolist.TotalCount, vinterface.Running[hostID].TotalCount)
							for i := 0; i < len(wantruninginfolist.Items); i++ {
								assert.Equal(t, wantruninginfolist.Items[i].InterfaceID, vinterface.Running[hostID].Items[i].InterfaceID)
								assert.Equal(t, wantruninginfolist.Items[i].InterfaceName, vinterface.Running[hostID].Items[i].InterfaceName)
								assert.Equal(t, wantruninginfolist.Items[i].OVSPort, vinterface.Running[hostID].Items[i].OVSPort)
								assert.Equal(t, wantruninginfolist.Items[i].UseVf, vinterface.Running[hostID].Items[i].UseVf)
								assert.Equal(t, wantruninginfolist.Items[i].VfPoolName, vinterface.Running[hostID].Items[i].VfPoolName)
							}
						}

						wantVinterface = true
					}
				}
				assert.True(t, wantVinterface)
			} // end of range wanvtinterfaces.Items

			defer tt.after(t)
		})
	}
}

func TestVNic_Upate(t *testing.T) {
	tests := []struct {
		name             string
		createNetwork    func(t *testing.T)
		before           func(t *testing.T)
		after            func(t *testing.T)
		paramvinterfaces domain.VNicList
		wantvinterfaces  domain.VNicList
	}{
		{
			name: "test1",
			createNetwork: func(t *testing.T) {
				network1 := domain.Network{
					ObjectMeta: metav1.ObjectMeta{
						Name: "network1",
					},
					NetworkID: "network111",
					Type:      "external",
					VLANID:    100,
					Interfaces: map[string]*domain.InterfaceList{
						"host1": {
							ListMeta: metav1.ListMeta{
								TotalCount: 1,
							},
							Items: []*domain.Interface{
								{
									Checked:       true,
									HostID:        "host1",
									HostName:      "host1",
									InterfaceID:   "if1",
									InterfaceName: "ovs_eth0",
									IP:            []string{"***********"},
									Mask:          []string{"*************"},
									SpeedMbs:      100,
									Status:        domain.CONNECTED,
								},
							},
						},
					},
				}

				network2 := domain.Network{
					ObjectMeta: metav1.ObjectMeta{
						Name: "network2",
					},
					NetworkID: "network222",
					Type:      "external",
					VLANID:    100,
					Interfaces: map[string]*domain.InterfaceList{
						"host1": {
							ListMeta: metav1.ListMeta{
								TotalCount: 2,
							},
							Items: []*domain.Interface{
								{
									Checked:       true,
									HostID:        "host1",
									HostName:      "host1",
									InterfaceID:   "if1",
									InterfaceName: "ovs_eth0",
									IP:            []string{"***********"},
									Mask:          []string{"*************"},
									SpeedMbs:      100,
									Status:        domain.CONNECTED,
								},
								{
									Checked:       true,
									HostID:        "host1",
									HostName:      "host1",
									InterfaceID:   "if2",
									InterfaceName: "ovs_eth1",
									IP:            []string{"***********"},
									Mask:          []string{"*************"},
									SpeedMbs:      100,
									Status:        domain.CONNECTED,
								},
							},
						},
					},
				}
				jsonData, err := json.Marshal(network1)
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", fmt.Sprintf("/twm/live_cluster/networks/%s", network1.NetworkID), string(jsonData)).Run()
				assert.NoError(t, err)

				jsonData, err = json.Marshal(network2)
				assert.NoError(t, err)
				err = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"put", fmt.Sprintf("/twm/live_cluster/networks/%s", network2.NetworkID), string(jsonData)).Run()
				assert.NoError(t, err)
			},
			before: func(t *testing.T) {
				// 准备 /twm/live_cluster/virtual_interfaces/{virtual_interface_id} 测试数据
				// 清理
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interfaces/", "--prefix").Run()
				assert.NoError(t, err)
				// 准备
				vIfaces := domain.VNicList{
					ListMeta: metav1.ListMeta{
						TotalCount: 3,
					},
					Items: []*domain.VNic{
						{
							//测试加入网络的情况
							VNicID:      "virtual_interface_id11",
							GuestID:     "guest_id",
							GuestName:   "guest_name",
							MacAddress:  "00:00:00:00:00:11",
							NetworkID:   "",
							NetworkName: "",
							VNicType:    1,
							PreferSRIOV: false,
							Running: map[string]*domain.VNicruningList{
								"host1": {},
							},
						},
						{
							//测试删除网络的情况
							VNicID:      "virtual_interface_id22",
							GuestID:     "guest_id",
							GuestName:   "guest_name",
							MacAddress:  "00:00:00:00:00:22",
							NetworkID:   "network111",
							NetworkName: "network1",
							VNicType:    1,
							PreferSRIOV: false,
							Running: map[string]*domain.VNicruningList{
								"host1": {
									ListMeta: metav1.ListMeta{
										TotalCount: 1,
									},
									Items: []*domain.VNicruning{
										{
											InterfaceID:   "if1",
											InterfaceName: "ovs_eth0",
											OVSPort:       "tap000000000022",
											UseVf:         false,
											VfPoolName:    "",
										},
									},
								},
							},
						},
						{
							//测试更改网络的情况
							VNicID:      "virtual_interface_id33",
							GuestID:     "guest_id",
							GuestName:   "guest_name",
							MacAddress:  "00:00:00:00:00:22",
							NetworkID:   "network111",
							NetworkName: "network1",
							VNicType:    1,
							PreferSRIOV: false,
							Running: map[string]*domain.VNicruningList{
								"host1": {
									ListMeta: metav1.ListMeta{
										TotalCount: 1,
									},
									Items: []*domain.VNicruning{
										{
											InterfaceID:   "if1",
											InterfaceName: "ovs_eth0",
											OVSPort:       "tap000000000022",
											UseVf:         false,
											VfPoolName:    "",
										},
									},
								},
							},
						},
					},
				}
				//直接使用etcd命令插入键值，作为旧的虚拟网络信息列表
				for _, vIface := range vIfaces.Items {
					jsonData, err := json.Marshal(vIface)
					assert.NoError(t, err)
					err = exec.Command("etcdctl",
						"--endpoints=http://127.0.0.1:2379",
						"put", fmt.Sprintf("/twm/live_cluster/virtual_interfaces/%s", vIface.VNicID), string(jsonData)).Run()
					assert.NoError(t, err)
				}
			},
			after: func(t *testing.T) {
				//清理 /twm/live_cluster/virtual_interfaces/{virtual_interface_id} 测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interfaces/", "--prefix").Run()
				assert.NoError(t, err)
			},
			paramvinterfaces: domain.VNicList{
				ListMeta: metav1.ListMeta{
					TotalCount: 3,
				},
				Items: []*domain.VNic{
					{
						//测试加入网络的情况
						VNicID:      "virtual_interface_id11",
						GuestID:     "guest_id",
						GuestName:   "guest_name",
						MacAddress:  "00:00:00:00:00:11",
						NetworkID:   "network111",
						NetworkName: "network1",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"host1": {
								ListMeta: metav1.ListMeta{
									TotalCount: 1,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "if1",
										InterfaceName: "ovs_eth0",
										OVSPort:       "tap000000000011",
										UseVf:         false,
										VfPoolName:    "",
									},
								},
							},
						},
					},
					{
						//测试删除网络的情况
						VNicID:      "virtual_interface_id22",
						GuestID:     "guest_id",
						GuestName:   "guest_name",
						MacAddress:  "00:00:00:00:00:22",
						NetworkID:   "",
						NetworkName: "",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"host1": {},
						},
					},
					{
						//测试更改网络的情况
						VNicID:      "virtual_interface_id33",
						GuestID:     "guest_id",
						GuestName:   "guest_name",
						MacAddress:  "00:00:00:00:00:22",
						NetworkID:   "network222",
						NetworkName: "network2",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"host1": {
								ListMeta: metav1.ListMeta{
									TotalCount: 2,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "if1",
										InterfaceName: "ovs_eth0",
										OVSPort:       "tap000000000022",
										UseVf:         false,
										VfPoolName:    "",
									},
									{
										InterfaceID:   "if2",
										InterfaceName: "ovs_eth1",
										OVSPort:       "tap000000000022",
										UseVf:         false,
										VfPoolName:    "",
									},
								},
							},
						},
					},
				},
			},
			wantvinterfaces: domain.VNicList{
				ListMeta: metav1.ListMeta{
					TotalCount: 3,
				},
				Items: []*domain.VNic{
					{
						//测试加入网络的情况
						VNicID:      "virtual_interface_id11",
						GuestID:     "guest_id",
						GuestName:   "guest_name",
						MacAddress:  "00:00:00:00:00:11",
						NetworkID:   "network111",
						NetworkName: "network1",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"host1": {
								ListMeta: metav1.ListMeta{
									TotalCount: 1,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "if1",
										InterfaceName: "ovs_eth0",
										OVSPort:       "tap000000000011",
										UseVf:         false,
										VfPoolName:    "",
									},
								},
							},
						},
					},
					{
						//测试删除网络的情况
						VNicID:      "virtual_interface_id22",
						GuestID:     "guest_id",
						GuestName:   "guest_name",
						MacAddress:  "00:00:00:00:00:22",
						NetworkID:   "",
						NetworkName: "",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"host1": {},
						},
					},
					{
						//测试更改网络的情况
						VNicID:      "virtual_interface_id33",
						GuestID:     "guest_id",
						GuestName:   "guest_name",
						MacAddress:  "00:00:00:00:00:22",
						NetworkID:   "network222",
						NetworkName: "network2",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"host1": {
								ListMeta: metav1.ListMeta{
									TotalCount: 2,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "if1",
										InterfaceName: "ovs_eth0",
										OVSPort:       "tap000000000022",
										UseVf:         false,
										VfPoolName:    "",
									},
									{
										InterfaceID:   "if2",
										InterfaceName: "ovs_eth1",
										OVSPort:       "tap000000000022",
										UseVf:         false,
										VfPoolName:    "",
									},
								},
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			//创建网路
			tt.createNetwork(t)
			//创建旧键值
			tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5000,
				Namespace:      KeyPrefix,
				RequestTimeout: 2000,
			}, nil)
			assert.NoError(t, err)

			for _, vIface := range tt.paramvinterfaces.Items {
				err = etcd.VNics().Update(context.Background(), vIface, metav1.UpdateOptions{})
				assert.NoError(t, err)
			}

			vinterfaces, err := etcd.VNics().List(context.Background(), "guest_id", metav1.ListOptions{})
			assert.NoError(t, err)

			// 补充测试逻辑
			assert.Equal(t, tt.wantvinterfaces.TotalCount, vinterfaces.TotalCount)
			assert.Equal(t, len(tt.wantvinterfaces.Items), len(vinterfaces.Items))

			for _, wantvinterface := range tt.wantvinterfaces.Items {
				wantVinterface := false
				for _, vinterface := range vinterfaces.Items {
					if wantvinterface.VNicID == vinterface.VNicID {

						assert.Equal(t, wantvinterface.GuestID, vinterface.GuestID)

						// 断言虚拟接口的基本信息
						assert.Equal(t, wantvinterface.GuestName, vinterface.GuestName)
						assert.Equal(t, wantvinterface.MacAddress, vinterface.MacAddress)
						assert.Equal(t, wantvinterface.NetworkID, vinterface.NetworkID)
						assert.Equal(t, wantvinterface.NetworkName, vinterface.NetworkName)
						assert.Equal(t, wantvinterface.VNicType, vinterface.VNicType)
						assert.Equal(t, wantvinterface.PreferSRIOV, vinterface.PreferSRIOV)

						//断言虚拟接口的运行信息
						assert.Equal(t, len(wantvinterface.Running), len(vinterface.Running))
						for hostID, wantruninginfolist := range wantvinterface.Running {
							assert.Equal(t, wantruninginfolist.TotalCount, vinterface.Running[hostID].TotalCount)
							for i := 0; i < len(wantruninginfolist.Items); i++ {
								assert.Equal(t, wantruninginfolist.Items[i].InterfaceID, vinterface.Running[hostID].Items[i].InterfaceID)
								assert.Equal(t, wantruninginfolist.Items[i].InterfaceName, vinterface.Running[hostID].Items[i].InterfaceName)
								assert.Equal(t, wantruninginfolist.Items[i].OVSPort, vinterface.Running[hostID].Items[i].OVSPort)
								assert.Equal(t, wantruninginfolist.Items[i].UseVf, vinterface.Running[hostID].Items[i].UseVf)
								assert.Equal(t, wantruninginfolist.Items[i].VfPoolName, vinterface.Running[hostID].Items[i].VfPoolName)
							}
						}

						wantVinterface = true
					}
				}
				assert.True(t, wantVinterface)
			} // end of range wantvinterfaces.Items

			//defer tt.after(t)
		})
	}
}

func TestVNic_Delete(t *testing.T) {
	tests := []struct {
		name            string
		before          func(t *testing.T) domain.VNicList
		after           func(t *testing.T)
		wanvtinterfaces domain.VNicList
	}{
		{
			name: "test1",
			before: func(t *testing.T) domain.VNicList {
				// 准备 /twm/live_cluster/virtual_interfaces/{virtual_interface_id} 测试数据
				// 清理
				_ = exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interfaces/", "--prefix").Run()
				// 准备
				vIfaces := domain.VNicList{
					ListMeta: metav1.ListMeta{
						TotalCount: 3,
					},
					Items: []*domain.VNic{
						{
							VNicID:      "virtual_interface_id11",
							GuestID:     "guest_id",
							GuestName:   "guest_name111",
							MacAddress:  "00:00:00:00:00:11",
							NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
							NetworkName: "Default VM Network",
							VNicType:    1,
							PreferSRIOV: false,
							Running: map[string]*domain.VNicruningList{
								"9b5910c9-557c-42a7-b592-7996d606dcc0": {
									ListMeta: metav1.ListMeta{
										TotalCount: 1,
									},
									Items: []*domain.VNicruning{
										{
											InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
											InterfaceName: "ovs_eth0",
											OVSPort:       "viface111",
											UseVf:         false,
											VfPoolName:    "",
										},
									},
								},
							},
						},
						{
							VNicID:      "virtual_interface_id22",
							GuestID:     "guest_id",
							GuestName:   "guest_name22",
							MacAddress:  "00:00:00:00:00:22",
							NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
							NetworkName: "Default VM Network",
							VNicType:    1,
							PreferSRIOV: false,
							Running: map[string]*domain.VNicruningList{
								"9b5910c9-557c-42a7-b592-7996d606dcc0": {
									ListMeta: metav1.ListMeta{
										TotalCount: 1,
									},
									Items: []*domain.VNicruning{
										{
											InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
											InterfaceName: "ovs_eth0",
											OVSPort:       "viface222",
											UseVf:         false,
											VfPoolName:    "",
										},
									},
								},
							},
						},
						{
							VNicID:      "virtual_interface_id33",
							GuestID:     "guest_id",
							GuestName:   "guest_name33",
							MacAddress:  "00:00:00:00:00:33",
							NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
							NetworkName: "Default VM Network",
							VNicType:    1,
							PreferSRIOV: false,
							Running: map[string]*domain.VNicruningList{
								"9b5910c9-557c-42a7-b592-7996d606dcc0": {
									ListMeta: metav1.ListMeta{
										TotalCount: 1,
									},
									Items: []*domain.VNicruning{
										{
											InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
											InterfaceName: "ovs_eth0",
											OVSPort:       "viface333",
											UseVf:         false,
											VfPoolName:    "",
										},
									},
								},
							},
						},
					},
				}
				for _, vIface := range vIfaces.Items {
					jsonData, err := json.Marshal(vIface)
					assert.NoError(t, err)
					err = exec.Command("etcdctl",
						"--endpoints=http://127.0.0.1:2379",
						"put", fmt.Sprintf("/twm/live_cluster/virtual_interfaces/%s", vIface.VNicID), string(jsonData)).Run()
					assert.NoError(t, err)
				}
				return vIfaces
			},
			after: func(t *testing.T) {
				//清理 /twm/live_cluster/virtual_interfaces/{virtual_interface_id} 测试数据
				err := exec.Command("etcdctl",
					"--endpoints=http://127.0.0.1:2379",
					"del", "/twm/live_cluster/virtual_interfaces/", "--prefix").Run()
				assert.NoError(t, err)
			},
			wanvtinterfaces: domain.VNicList{
				ListMeta: metav1.ListMeta{
					TotalCount: 3,
				},
				Items: []*domain.VNic{
					{
						VNicID:      "virtual_interface_id11",
						GuestID:     "guest_id",
						GuestName:   "guest_name111",
						MacAddress:  "00:00:00:00:00:11",
						NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
						NetworkName: "Default VM Network",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"9b5910c9-557c-42a7-b592-7996d606dcc0": {
								ListMeta: metav1.ListMeta{
									TotalCount: 1,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
										InterfaceName: "ovs_eth0",
										OVSPort:       "viface111",
										UseVf:         false,
										VfPoolName:    "",
									},
								},
							},
						},
					},
					{
						VNicID:      "virtual_interface_id22",
						GuestID:     "guest_id",
						GuestName:   "guest_name22",
						MacAddress:  "00:00:00:00:00:22",
						NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
						NetworkName: "Default VM Network",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"9b5910c9-557c-42a7-b592-7996d606dcc0": {
								ListMeta: metav1.ListMeta{
									TotalCount: 1,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
										InterfaceName: "ovs_eth0",
										OVSPort:       "viface222",
										UseVf:         false,
										VfPoolName:    "",
									},
								},
							},
						},
					}, {
						VNicID:      "virtual_interface_id33",
						GuestID:     "guest_id",
						GuestName:   "guest_name33",
						MacAddress:  "00:00:00:00:00:33",
						NetworkID:   "bfffe844-3dee-46fd-a1cc-00d2cae7b767",
						NetworkName: "Default VM Network",
						VNicType:    1,
						PreferSRIOV: false,
						Running: map[string]*domain.VNicruningList{
							"9b5910c9-557c-42a7-b592-7996d606dcc0": {
								ListMeta: metav1.ListMeta{
									TotalCount: 1,
								},
								Items: []*domain.VNicruning{
									{
										InterfaceID:   "00000000-0000-0000-0000-9009d05e8271",
										InterfaceName: "ovs_eth0",
										OVSPort:       "viface333",
										UseVf:         false,
										VfPoolName:    "",
									},
								},
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			deleteParam := tt.before(t)

			etcd, err := GetEtcdFactoryOr(&options.EtcdOptions{
				Endpoints:      []string{"http://127.0.0.1:2379"},
				Timeout:        5000,
				Namespace:      KeyPrefix,
				RequestTimeout: 2000,
			}, nil)
			assert.NoError(t, err)

			for _, viface := range deleteParam.Items {
				err := etcd.VNics().Delete(context.Background(), viface, metav1.DeleteOptions{})
				assert.NoError(t, err)
			}

			vinterfaces, err := etcd.VNics().List(context.Background(), "guest_id", metav1.ListOptions{})
			assert.NoError(t, err)

			assert.Equal(t, vinterfaces.TotalCount, int64(0))
			assert.Equal(t, len(vinterfaces.Items), 0)

			defer tt.after(t)
		})
	}
}

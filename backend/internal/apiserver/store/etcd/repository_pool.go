package etcd

import (
	"context"
	"encoding/json"
	"fmt"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/component-base/pkg/util/jsonutil"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/golibrary/errors"
)

type RepositoryPool struct {
	ds *datastore
}

func (r *RepositoryPool) ListStatus(ctx context.Context, opts metav1.ListOptions) ([]domain.StatusEntry, error) {
	panic("no finished")
}

func (r *RepositoryPool) Create(ctx context.Context, repoId string, repo *domain.RepositoryPool, opts metav1.CreateOptions) error {
	return r.ds.Put(ctx, r.getRepoIdKey(repoId), jsonutil.ToString(repo))
}

func (r *RepositoryPool) Update(ctx context.Context, repoId string, repo *domain.RepositoryPool, opts metav1.UpdateOptions) error {
	return r.ds.Put(ctx, r.getRepoIdKey(repoId), jsonutil.ToString(repo))
}

// 根据存储空间uuid获取目标存储空间信息
func (r *RepositoryPool) Get(ctx context.Context, repoId string, opts metav1.GetOptions) (*domain.RepositoryPool, error) {
	resp, err := r.ds.Get(ctx, r.getRepoIdKey(repoId))
	if err != nil {
		return nil, err
	}

	var repo domain.RepositoryPool
	if err := json.Unmarshal(resp, &repo); err != nil {
		return nil, errors.Wrap(err, "unmarshal to Repository struct failed")
	}

	return &repo, nil
}

func (r *RepositoryPool) List(ctx context.Context, opts metav1.ListOptions) (*domain.RepositoryPoolList, error) {
	kvs, err := r.ds.List(ctx, repoPrefix)
	if err != nil {
		return nil, err
	}

	res := &domain.RepositoryPoolList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(kvs)),
		},
	}

	res.Items = make(map[string]*domain.RepositoryPool)
	for _, kv := range kvs {
		var pool domain.RepositoryPool

		paths := r.ds.SplitEtcdKeyPath(kv.Key)
		if err := json.Unmarshal(kv.Val, &pool); err != nil {
			return nil, errors.Wrap(err, "unmarshal to repositorypool struct failed")
		}

		if len(paths) <= 0 {
			log.Info("failed to obtain the repositorypool id,etcd path have problem")
			return nil, errors.Wrap(err, "failed to obtain the repositorypool Id")
		}
		res.Items[paths[0]] = &pool
	}

	return res, nil
}

func (r *RepositoryPool) Delete(ctx context.Context, repoId string, opts metav1.DeleteOptions) error {
	_, err := r.ds.Delete(ctx, r.getRepoIdKey(repoId))
	return err
}

func newRepositoryPool(ds *datastore) *RepositoryPool {
	return &RepositoryPool{ds: ds}
}

var repoPrefix = "/twm/live_cluster/repository/"

func (r *RepositoryPool) getRepoIdKey(repoId string) string {
	return fmt.Sprintf(repoPrefix+"%v", repoId)
}

// Package etcd
/**
* @Project : terravirtualmachine
* @File    : network.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/21 08:54
**/

package etcd

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/component-base/pkg/util/jsonutil"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/golibrary/errors"
)

var _ store.NetworkStore = (*networks)(nil)

type networks struct {
	ds *datastore
}

func (n *networks) Create(ctx context.Context, network *domain.Network, opts metav1.CreateOptions) error {
	return n.ds.Put(ctx, n.getNetworkKey(network.NetworkID), jsonutil.ToString(network))
}

func (n *networks) Update(ctx context.Context, network *domain.Network, opts metav1.UpdateOptions) error {
	return n.ds.Put(ctx, n.getNetworkKey(network.NetworkID), jsonutil.ToString(network))
}

func (n *networks) Delete(ctx context.Context, networkID string, opts metav1.DeleteOptions) (*domain.Network, error) {
	val, err := n.ds.Delete(ctx, n.getNetworkKey(networkID))
	if err != nil {
		return nil, err
	}
	var network domain.Network
	if err := json.Unmarshal(val, &network); err != nil {
		return nil, errors.Wrap(err, "unmarshal to Network struct failed")
	}
	return &network, nil
}

// Get 查询当前集群中的指定网络
// etcdctl get /twm/live_cluster/networks/{network_id}
func (n *networks) Get(ctx context.Context, networkID string, opts metav1.GetOptions) (*domain.Network, error) {
	resp, err := n.ds.Get(ctx, n.getNetworkKey(networkID))
	if err != nil {
		return nil, err
	}

	var network domain.Network
	if err := json.Unmarshal(resp, &network); err != nil {
		return nil, errors.Wrap(err, "unmarshal to Network struct failed")
	}

	return &network, nil
}

// GetInterface 查询当前集群中的指定主机的指定网络接口
// etcdctl get /twm/live_cluster/hosts/{host_id}/interfaces/{interface_id}
func (n *networks) GetInterface(ctx context.Context, hostID string, ifaceID string, opts metav1.GetOptions) (*domain.Interface, error) {
	resp, err := n.ds.Get(ctx, n.getNetworkHostKey(hostID)+n.getNetworkInterfaceKey(ifaceID))
	if err != nil {
		return nil, err
	}

	var iface domain.Interface
	if err := json.Unmarshal(resp, &iface); err != nil {
		return nil, errors.Wrap(err, "unmarshal to Interface struct failed")
	}

	return &iface, nil
}

// List 查询当前集群中的网络列表
// etcdctl get /twm/live_cluster/networks/ --prefix
func (n *networks) List(ctx context.Context, opts metav1.ListOptions) (*domain.NetworkList, error) {
	kvs, err := n.ds.List(ctx, n.getNetworkKey(""))
	if err != nil {
		return nil, err
	}

	res := &domain.NetworkList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(kvs)),
		},
	}

	for _, kv := range kvs {
		var network domain.Network
		if err := json.Unmarshal(kv.Val, &network); err != nil {
			return nil, errors.Wrap(err, "unmarshal to Network struct failed")
		}
		res.Items = append(res.Items, &network)
	}

	return res, nil
}

// ListHosts 查询当前集群中的主机列表
// etcdctl get /twm/live_cluster/hosts/ --prefix
func (n *networks) ListHosts(ctx context.Context, opts metav1.ListOptions) (*domain.NetworkHostList, error) {
	kvs, err := n.ds.List(ctx, n.getNetworkHostKey(""))
	if err != nil {
		return nil, err
	}

	res := &domain.NetworkHostList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(kvs)),
		},
	}

	// 这个版本没有集群
	// 使用一个默认Host "9b5910c9-557c-42a7-b592-7996d606dcc0"
	if len(kvs) == 0 {
		res.ListMeta.TotalCount = 1
		res.Items = append(res.Items, &domain.NetworkHost{
			HostID: "9b5910c9-557c-42a7-b592-7996d606dcc0",
		})
		return res, nil
	}

	for _, kv := range kvs {
		// 查找第一个斜杠的位置
		slashIndex1 := strings.Index(kv.Key, "/")
		var hostID string
		if slashIndex1 == -1 {
			// 如果没有斜杠，整个 kv.Key 就是第一个层级
			hostID = kv.Key
		} else {
			// 去掉第一个斜杠
			tmpStr := kv.Key[slashIndex1+1:]
			//查找第二个斜杠
			slashIndex2 := strings.Index(tmpStr, "/")
			if slashIndex2 == -1 {
				hostID = tmpStr
			} else {
				hostID = tmpStr[:slashIndex2]
			}
		}

		res.Items = append(res.Items, &domain.NetworkHost{
			HostID: hostID,
		})
	}

	return res, nil
}

// ListInterfaces 查询当前集群中的指定主机的网络接口列表
// etcdctl get /twm/live_cluster/hosts/{host_id}/interfaces/ --prefix
func (n *networks) ListInterfaces(ctx context.Context, hostID string, opts metav1.ListOptions) (*domain.InterfaceList, error) {
	kvs, err := n.ds.List(ctx, n.getNetworkHostKey(hostID)+n.getNetworkInterfaceKey(""))
	if err != nil {
		return nil, err
	}

	res := &domain.InterfaceList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(kvs)),
		},
	}

	for _, kv := range kvs {
		var iface domain.Interface
		if err := json.Unmarshal(kv.Val, &iface); err != nil {
			return nil, errors.Wrap(err, "unmarshal to Interface struct failed")
		}
		res.Items = append(res.Items, &iface)
	}

	return res, nil
}

// ListVNics 查询当前集群中的虚拟网络接口列表
// etcdctl get /twm/live_cluster/virtual_interfaces/ --prefix
func (n *networks) ListVNics(ctx context.Context, opts metav1.ListOptions) (*domain.VNicList, error) {
	kvs, err := n.ds.List(ctx, n.getNetworkVNicKey(""))
	if err != nil {
		return nil, err
	}

	res := &domain.VNicList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(kvs)),
		},
	}

	for _, kv := range kvs {
		var vIface domain.VNic
		if err := json.Unmarshal(kv.Val, &vIface); err != nil {
			return nil, errors.Wrap(err, "unmarshal to VNic struct failed")
		}
		res.Items = append(res.Items, &vIface)
	}

	return res, nil
}

func newNetworks(ds *datastore) *networks {
	return &networks{ds: ds}
}

var keyNetworks = "/networks/%v"               // /twm/live_cluster/networks/{network_id}
var keyNetworkHosts = "/hosts/%v"              // /twm/live_cluster/hosts/{host_id}
var keyNetworkInterfaces = "/nics/%v"          // /twm/live_cluster/host/{host_id}/interfaces/{interface_id}
var keyNetworkVNics = "/virtual_interfaces/%v" // /twm/live_cluster/virtual_interfaces/{virtual_interface_id}

func (n *networks) getNetworkKey(networkID string) string { return fmt.Sprintf(keyNetworks, networkID) }
func (n *networks) getNetworkHostKey(hostID string) string {
	return fmt.Sprintf(keyNetworkHosts, hostID)
}
func (n *networks) getNetworkInterfaceKey(interfaceID string) string {
	return fmt.Sprintf(keyNetworkInterfaces, interfaceID)
}
func (n *networks) getNetworkVNicKey(vIfaceID string) string {
	return fmt.Sprintf(keyNetworkVNics, vIfaceID)
}

package etcd

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/component-base/pkg/util/jsonutil"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/golibrary/errors"
)

func (l *logs) GetAddLog(ctx context.Context, idx string, opts metav1.GetOptions) (*domain.LogEntry, error) {
	log, err := l.ds.Get(ctx, l.getLogKeyAdd(idx))
	if err != nil {
		return nil, err
	}

	var logEntry domain.LogEntry
	if err := json.Unmarshal(log, &logEntry); err != nil {
		return nil, errors.Wrap(err, "unmarshal to LogEntry struct failed")
	}

	return &logEntry, nil
}

func (l *logs) GetDelLog(ctx context.Context, idx string, opts metav1.GetOptions) (*domain.LogDel, error) {
	log, err := l.ds.Get(ctx, l.getLogKeyDel(idx))
	if err != nil {
		return nil, err
	}

	var logDel []int
	if err := json.Unmarshal(log, &logDel); err != nil {
		return nil, errors.Wrap(err, "unmarshal to LogDel struct failed")
	}

	return &domain.LogDel{
		Start: logDel[0],
		End:   logDel[1],
	}, nil
}

func (l *logs) GetAddLen(ctx context.Context, opts metav1.GetOptions) (int, error) {
	return l.ds.CountKeys(ctx, l.getLogKeyAdd(""))
}

func (l *logs) GetDelLen(ctx context.Context, opts metav1.GetOptions) (int, error) {
	return l.ds.CountKeys(ctx, l.getLogKeyDel(""))
}

func (l *logs) Create(ctx context.Context, log *domain.LogEntry, opts metav1.CreateOptions) error {
	fmt.Println("add_idx: ", add_idx)
	err := l.ds.Put(ctx, l.getLogKeyAdd(strconv.Itoa(add_idx)), jsonutil.ToString(log))
	if err != nil {
		return err
	}

	seqNum, err := l.ds.CountKeys(ctx, l.getLogKeyAdd(""))

	if err != nil {
		return err
	}

	err = l.ds.Put(ctx, l.getLogKeyAdd("seq_num"), func() string {
		if seqNum == 0 || seqNum == 1 {
			return strconv.Itoa(seqNum + 1)
		}
		return strconv.Itoa(seqNum)
	}())
	if err != nil {
		return err
	}

	add_idx = seqNum

	return nil
}

func (l *logs) Clear(ctx context.Context, del *domain.LogDel, opts metav1.DeleteOptions) error {
	rangeData := []int{del.Start, del.End}
	err := l.ds.Put(ctx, l.getLogKeyDel(strconv.Itoa(del_idx)), jsonutil.ToString(rangeData))
	if err != nil {
		return err
	}

	seqNum, err := l.ds.CountKeys(ctx, l.getLogKeyDel(""))
	if err != nil {
		return err
	}

	err = l.ds.Put(ctx, l.getLogKeyDel("seq_num"), func() string {
		if seqNum == 0 {
			return "1"
		}
		return strconv.Itoa(seqNum)
	}())

	del_idx++

	return err
}

func (l *logs) List(ctx context.Context, opts metav1.ListOptions) (*domain.LogList, error) {
	logs, err := l.ds.List(ctx, l.getLogKeyAdd(""))
	if err != nil {
		return nil, err
	}

	res := &domain.LogList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(logs)),
		},
	}

	res.Items = make(map[string]domain.LogEntry)
	for _, kv := range logs {
		// 过滤掉键为 "seq_num" 的日志条目
		if strings.HasSuffix(string(kv.Key), "seq_num") {
			continue
		}

		var log domain.LogEntry
		if err := json.Unmarshal(kv.Val, &log); err != nil {
			return nil, errors.Wrap(err, "unmarshal to LogEntry struct failed")
		}
		res.Items[string(kv.Key)] = log
	}
	res.ListMeta.TotalCount = int64(len(res.Items))
	return res, nil
}

type logs struct {
	ds *datastore
}

var _ store.Log = (*logs)(nil)

func newLogs(ds *datastore) *logs { return &logs{ds: ds} }

var logPrefix = "/twm/live_cluster/log/"
var logPrefixAdd = "/twm/live_cluster/log/add/"
var logPrefixDel = "/twm/live_cluster/log/del/"

var add_idx int = 1
var del_idx int = 1

func GetLogKey() string {
	return logPrefix
}

func (l *logs) getLogKeyAdd(idx string) string {
	return fmt.Sprintf("%s%s", logPrefixAdd, idx)
}

func (l *logs) getLogKeyDel(idx string) string {
	return fmt.Sprintf("%s%s", logPrefixDel, idx)
}

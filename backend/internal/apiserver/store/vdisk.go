package store

import (
	"context"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

type VDisk interface {
	Get(ctx context.Context, vdiskUUID string, opts metav1.GetOptions) (*domain.VDisk, error)
	Create(ctx context.Context, vdiskUUID string, disk *domain.VDisk, opts metav1.CreateOptions) error
	Update(ctx context.Context, vdiskUUID string, disk *domain.VDisk, opts metav1.UpdateOptions) error
	Delete(ctx context.Context, vdiskUUID string, opts metav1.DeleteOptions) error
	List(ctx context.Context, opts metav1.ListOptions) (*domain.VDiskList, error)
	ListUUID(ctx context.Context, opts metav1.ListOptions) ([]string, error)
}

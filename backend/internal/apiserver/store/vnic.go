package store

import (
	"context"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

type VNicStore interface {
	List(ctx context.Context, GuestID string, opts metav1.ListOptions) (*domain.VNicList, error)
	Get(ctx context.Context, vInterfaceID string, opts metav1.GetOptions) (*domain.VNic, error)
	Update(ctx context.Context, vInterface *domain.VNic, opts metav1.UpdateOptions) error
	Delete(ctx context.Context, vInterface *domain.VNic, opts metav1.DeleteOptions) error
	Create(ctx context.Context, vInterface *domain.VNic, opts metav1.CreateOptions) error
	//CreateList(ctx context.Context, vInterfaces *domain.VNicList, opts metav1.CreateOptions) error
}

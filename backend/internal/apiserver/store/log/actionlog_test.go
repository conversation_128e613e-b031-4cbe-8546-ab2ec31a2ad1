package log

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestInfo(t *testing.T) {

	// 1. prepare a file for testing
	dir := t.TempDir()

	// 2. create a logger
	l, err := NewActionLog(dir, "test")
	require.NoError(t, err)
	// 2.1 setting the global writer so that we don't have to pass the writer to every function
	SetDefaultWriter(l.AsWriter())

	// 3. write some logs
	Info("any category", Translation{
		Section: "any-section",
		Key:     "any-key",
	})
	Info("any category", Translation{
		Section: "any-section",
		Key:     "any-key",
	})

	// 4. process the request comes front-end
	items, err := l.AsRequestProcessor().DoRequest(RequestEntity{
		GetRequest: &GetRequestEntity{},
		Type:       RequestTypeGet,
	})
	require.NoError(t, err)
	require.Len(t, items, 2)

}

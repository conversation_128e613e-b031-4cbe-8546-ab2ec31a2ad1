package log

import (
	"context"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	dto "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/service/v1/dto"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/pkg/code"
	"gitlab.local/golibrary/errors"
)

type LogSrv interface {
	List(ctx context.Context, opts metav1.ListOptions) (*dto.LogResponseDto, error)
	Clear(ctx context.Context, opts metav1.ListOptions) error
	Export(ctx *gin.Context, opts metav1.ListOptions) error
}

func (l *logDto) List(ctx context.Context, opts metav1.ListOptions) (*dto.LogResponseDto, error) {
	addList, err := l.store.Logs().List(ctx, opts)
	if err != nil {
		log.L(ctx).Errorf("list logs failed: %v", err)
		return nil, errors.WithCode(code.ErrListLogs, err.Error())
	}

	// 获取上次清除日志的范围
	rangeLen, err := l.store.Logs().GetDelLen(ctx, metav1.GetOptions{})
	if err != nil {
		log.L(ctx).Errorf("get del log len failed: %v", err)
		return nil, errors.WithCode(code.ErrListLogs, err.Error())
	}

	rangeClean, err := l.store.Logs().GetDelLog(ctx, strconv.Itoa(rangeLen-1), metav1.GetOptions{})
	if err != nil {
		log.L(ctx).Errorf("get del log failed: %v", err)
		return nil, errors.WithCode(code.ErrListLogs, err.Error())
	}

	// 获取清除后的的日志
	var showList = dto.LogResponseDto{
		Logs:  make([]dto.LogEntryDto, 0),
		Total: 0,
	}
	for idx, log := range addList.Items {
		idxInt, _ := strconv.Atoi(idx)
		if idxInt > rangeClean.Start && idxInt < rangeClean.End {
			continue
		}
		showList.Logs = append(showList.Logs, dto.LogEntryDto{
			Event: log.Message,
			Level: log.Priority.ToString(),
			Time:  formatTime(log.Time),
		})
		showList.Total++
	}
	return &showList, nil
}

func (l *logDto) Clear(ctx context.Context, opts metav1.ListOptions) error {
	rangeLen, err := l.store.Logs().GetAddLen(ctx, metav1.GetOptions{})
	if err != nil {
		log.L(ctx).Errorf("get add log len failed: %v", err)
		return errors.WithCode(code.ErrListLogs, err.Error())
	}
	clearRange := domain.LogDel{
		Start: -1,
		End:   rangeLen,
	}
	err = l.store.Logs().Clear(ctx, &clearRange, metav1.DeleteOptions{})
	if err != nil {
		log.L(ctx).Errorf("clear log failed: %v", err)
		return errors.WithCode(code.ErrListLogs, err.Error())
	}

	err = l.store.Logs().Create(ctx, &domain.LogEntry{
		Message:  domain.ClearLogMessage,
		Priority: domain.LogLevelInfo,
		Time:     time.Now().Unix(),
	}, metav1.CreateOptions{})
	if err != nil {
		log.L(ctx).Errorf("create log failed: %v", err)
		return errors.WithCode(code.ErrListLogs, err.Error())
	}

	return nil
}

func (l *logDto) Export(ctx *gin.Context, opts metav1.ListOptions) error {
	// logs, err := l.store.Logs().List(ctx, opts)
	// if err != nil {
	// 	log.L(ctx).Errorf("list logs failed: %v", err)
	// 	return errors.WithCode(code.ErrListLogs, err.Error())
	// }

	// // add header
	// header := make([]string, 0)
	// header = append(header,
	// 	"Level",
	// 	"Time",
	// 	"Event",
	// )

	// _ = excelctl.ExportCsv(ctx, "VMs_logs", header, func(writer *csv.Writer) error {
	// 	for _, log := range logs.Items {
	// 		writer.Write([]string{
	// 			log.Priority.ToString(),
	// 			formatTime(log.Time),
	// 			log.Message,
	// 		})
	// 	}
	// 	return nil
	// })

	return nil
}

func (l *logDto) Search(ctx context.Context, opts metav1.ListOptions) (*dto.LogResponseDto, error) {
	return nil, nil
}

func formatTime(t int64) string {
	tm := time.Unix(t, 0)
	return tm.Format("2006/01/02 15:04:05")
}

// var _ LogSrv = (*logDto)(nil)

// type logDto struct {
// 	store store.Factory
// }

// func newLogDto(srv *service) *logDto {
// 	return &logDto{
// 		store: srv.store,
// 	}
// }

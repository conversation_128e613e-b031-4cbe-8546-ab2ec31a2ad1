package log

import (
	"context"
	"encoding/json"
	"time"

	"github.com/samber/mo"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	elog "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store/etcd"
	"gitlab.local/golibrary/actionlog/store"
	clientv3 "go.etcd.io/etcd/client/v3"
)

type Level string

const (
	levelInfo Level = "Info"
	levelErr  Level = "Error"
	levelWarn Level = "Warning"
)

type TrPair struct {
	Variable string `json:"variable"`
	Value    string `json:"value"`
}

type Translation struct {
	Section string   `json:"section"`
	Key     string   `json:"key"`
	TrPairs []TrPair `json:"tr_pairs"`
}

type LogEntityCommonField struct {
	Id       int64     `gorm:"column:id;autoIncrement;primaryKey" json:"id"`
	Level    Level     `gorm:"column:level"            json:"level"`
	Time     time.Time `gorm:"column:time"             json:"time"`
	Category string    `gorm:"column:category"         json:"category"`
	User     string    `gorm:"column:user"             json:"user"`
}

type Entity struct {
	LogEntityCommonField
	Tr Translation `json:"translation"`
}

type VEntity struct {
	LogEntityCommonField
	Event string `json:"event"`
}

type DaoEntity struct {
	LogEntityCommonField
	Tr string `gorm:"column:translation" json:"translation"`
}

func (DaoEntity) TableName() string {
	return "log"
}

func (DaoEntity) IdName() string {
	return "id"
}

func (Self DaoEntity) IdValue() int64 {
	return Self.Id
}

type CountGetter interface {
	Count() (int64, error)
}

type ByOptionGetter interface {
	Get(o Option) mo.Result[[]Entity]
}

type Deleter interface {
	Del() error
	DelByCategory(category string) error
}

type CategoryCount struct {
	Name  string `json:"name"`
	Count int64  `json:"count"`
}

type CategoryGetter interface {
	DistinctCategories() ([]CategoryCount, error)
}

type Store struct {
	etcdStore store.GetEtcdFactoryOr()
}

type Creator interface {
	Create(entity Entity) (int64, error)
}

func newStore(db *gorm.DB) (*Store, error) {
	curdService, err := terdbcurd.New[DaoEntity](db)
	if err != nil {
		return nil, err
	}
	return &Store{curdService: curdService}, nil
}

func (Self *Store) toEtcdEntity(entity Entity) (*domain.LogEntity, error) {
	
}

func (Self *Store) toEntity(daoItem DaoEntity) Entity {
	var entity Entity
	entity.LogEntityCommonField = daoItem.LogEntityCommonField
	_ = json.Unmarshal([]byte(daoItem.Tr), &entity.Tr)
	return entity
}

func (Self *Store) toManyEntities(daoItems []DaoEntity) []Entity {
	result := make([]Entity, len(daoItems))
	for i, daoItem := range daoItems {
		result[i] = Self.toEntity(daoItem)
	}
	return result
}

func (Self *Store) Create(entity Entity) (int64, error) {
	return Self.curdService.Create(Self.toDaoEntity(entity))
}

func (Self *Store) Del() error {
	return Self.curdService.Del()
}

func (Self *Store) DelByCategory(category string) error {
	return Self.curdService.Del(terdbcurd.Where("category = ?", category))
}

func (Self *Store) DelOldestRows(n int) error {
	return Self.curdService.Model().Exec("DELETE FROM log WHERE id IN (SELECT id FROM log ORDER BY time LIMIT ?)", n).Error
}

func (Self *Store) DelTimeBefore(time time.Time) error {
	return Self.curdService.Del(terdbcurd.Where("time < ?", time))
}

func (Self *Store) Count() (int64, error) {
	var count int64
	err := Self.curdService.Model().Count(&count).Error
	return count, err
}

func (Self *Store) DistinctCategories() ([]CategoryCount, error) {
	result := make([]CategoryCount, 0)
	err := Self.curdService.Model().Raw("select category as 'name',COUNT(*) as 'count' from log group by category").Scan(&result).Error
	return result, err
}

type Option struct {
	DescOrder bool    `json:"desc_order"`
	Limit     int     `json:"limit"`
	Offset    int     `json:"offset"`
	Category  *string `json:"category"`
}

func (s *Store) Get(o Option) mo.Result[[]Entity] {
    baseKey := elog.LogPrefix
    conditions := []clientv3.OpOption{clientv3.WithPrefix()}

    // 设置排序方式
    sortOrder := clientv3.SortAscend
    if o.DescOrder {
        sortOrder = clientv3.SortDescend
    }
    conditions = append(conditions, clientv3.WithSort(clientv3.SortByKey, sortOrder))

    var (
        collected  []domain.LogEntry
        nextKey    = ""
        remaining  = o.Offset + o.Limit
    )

    for remaining > 0 {
        currentOpts := append(conditions, clientv3.WithLimit(int64(remaining)))
        if nextKey != "" {
            currentOpts = append(currentOpts, clientv3.WithFromKey())
        }

        // 获取日志数据
        var logList *domain.LogList
        var err error
        if o.Category == nil || *o.Category == "" {
            logList, err = s.etcdStore.Logs().GetByOpOption(context.Background(), baseKey, currentOpts)
        } else {
            logList, err = s.etcdStore.Logs().GetByCategory(context.Background(), baseKey, *o.Category, currentOpts)
        }

        if err != nil {
            return mo.Err[[]Entity](err)
        }

        // 处理排序和分页
        entries := s.extractAndSortEntries(logList, o.DescOrder, len(collected) == 0)
        collected = append(collected, entries...)

        batchSize := len(entries)
        remaining -= batchSize

        // 判断是否需要继续获取
        if batchSize == 0 || remaining <= 0 {
            break
        }

        // 准备下一批的起始key
        lastKey := s.getLastKey(logList, o.DescOrder)
        nextKey = string(append(lastKey, 0))
    }

    // 应用分页并返回结果
    entities := s.applyPagination(collected, o.Offset, o.Limit)
    return mo.Ok(entities)
}

// 辅助方法保持原有逻辑，但优化实现
func (s *Store) extractAndSortEntries(logList *domain.LogList, descOrder, isFirstBatch bool) []domain.LogEntry {
    entries := make([]domain.LogEntry, 0, len(logList.Items))
    for _, entry := range logList.Items {
        entries = append(entries, entry)
    }

    if isFirstBatch && descOrder {
        // 优化反转实现
        for i, j := 0, len(entries)-1; i < j; i, j = i+1, j-1 {
            entries[i], entries[j] = entries[j], entries[i]
        }
    }
    return entries
}

func (s *Store) applyPagination(entries []domain.LogEntry, offset, limit int) []Entity {
    if offset >= len(entries) {
        return []Entity{}
    }

    end := offset + limit
    if end > len(entries) {
        end = len(entries)
    }
    return s.convertToEntities(entries[offset:end])
}

func (s *Store) getLastKey(logList *domain.LogList, descOrder bool) []byte {
    if len(logList.Items) == 0 {
        return []byte{}
    }

    keys := make([]string, 0, len(logList.Items))
    for k := range logList.KeyIndexMap {
        keys = append(keys, k)
    }

    sort.Strings(keys)
    if descOrder {
        return []byte(keys[0]) // 降序时取第一个key
    }
    return []byte(keys[len(keys)-1]) // 升序时取最后一个key
}

func (s *Store) convertToEntities(logEntries []domain.LogEntry) []Entity {
    entities := make([]Entity, 0, len(logEntries))
    for _, log := range logEntries {
        entity := Entity{
            LogEntityCommonField: LogEntityCommonField{
                Level:    Level(log.Priority.ToString()),
                Category: log.Category,
                Time:     log.Time,
                User:     log.Username,
            },
            Tr: Translation{
                Section: log.Section,
                Key:     log.Key,
                TrPairs: make([]domain.TrPair, 0, len(log.TrPairs)),
            },
        }

        for _, trPair := range log.TrPairs {
            entity.Tr.TrPairs = append(entity.Tr.TrPairs, domain.TrPair{
                Variable: trPair.Variable,
                Value:    trPair.Value,
            })
        }

        entities = append(entities, entity)
    }
    return entities
}
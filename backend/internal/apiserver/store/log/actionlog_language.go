package log

import (
	"gitlab.local/golibrary/language"
)

type LangService struct {
	appName       string
	langeGetter   *language.Lang
	tosLang       language.TranslateGetter
	languageCache map[string]language.LangSection
}

func newLangService(langeGetter *language.Lang, appName string) *LangService {
	tosLang := language.NewTosLang(langeGetter)
	return &LangService{
		langeGetter:   langeGetter,
		tosLang:       tosLang,
		appName:       appName,
		languageCache: make(map[string]language.LangSection, 50),
	}
}

func (Self *LangService) GetTranslate(section string, key string) string {
	languageKey := Self.appName + Self.langeGetter.Language
	appLang, ok := Self.languageCache[languageKey]
	if !ok {
		appLang = Self.langeGetter.InitLang(Self.appName, "").LngRes.Lng
		Self.languageCache[languageKey] = appLang
	}
	s := appLang.GetTranslate(section, key)
	if s == "" {
		s = Self.tosLang.GetTranslate(section, key)
	}
	return s
}

package log

import (
	"os/user"
	"time"

	"github.com/gogf/gf/os/glog"
)

type Writer struct {
	logCreator     Creator
	defaultSection string
}

func (Self *Writer) getCurUser() string {
	user, err := user.Current()
	if err != nil {
		return ""
	}
	return user.Username
}

func (Self *Writer) log(level Level, category string, tr Translation, user ...string) {
	if len(tr.TrPairs) == 0 {
		tr.TrPairs = make([]TrPair, 0)
	}
	if tr.Section == "" {
		tr.Section = Self.defaultSection
	}

	item := Entity{
		LogEntityCommonField: LogEntityCommonField{
			Level:    level,
			Time:     time.Now(),
			Category: category,
		},
		Tr: tr,
	}
	if len(user) > 0 {
		item.User = user[0]
	} else {
		item.User = Self.getCurUser()
	}
	_, err := Self.logCreator.Create(item)
	if err != nil {
		glog.Info(err)
	}
}

func (Self *Writer) Info(category string, tr Translation, user ...string) {
	Self.log(levelInfo, category, tr, user...)
}

func (Self *Writer) Warn(category string, tr Translation, user ...string) {
	Self.log(levelWarn, category, tr, user...)
}
func (Self *Writer) Error(category string, tr Translation, user ...string) {
	Self.log(levelErr, category, tr, user...)
}

// belows are global functions
var (
	defaultWriter *Writer
)

func SetDefaultWriter(logger *Writer) {
	defaultWriter = logger
}

func log(level Level, category string, tr Translation, user ...string) {
	if defaultWriter == nil {
		return
	}
	defaultWriter.log(level, category, tr, user...)
}

func Info(category string, tr Translation, user ...string) {
	log(levelInfo, category, tr, user...)
}

func Warn(category string, tr Translation, user ...string) {
	log(levelWarn, category, tr, user...)
}
func Error(category string, tr Translation, user ...string) {
	log(levelErr, category, tr, user...)
}

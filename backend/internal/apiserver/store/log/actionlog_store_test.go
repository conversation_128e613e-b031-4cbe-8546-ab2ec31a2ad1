package log

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func TestPagingReader_Get(t *testing.T) {

	now := time.Now()
	itemsToCreate := []Entity{
		{
			LogEntityCommonField: LogEntityCommonField{
				Time:     now.Add(time.Second * 1),
				Category: "1",
			},
		},
		{
			LogEntityCommonField: LogEntityCommonField{
				Time:     now.Add(time.Second * 2),
				Category: "2",
			},
		},
		{
			LogEntityCommonField: LogEntityCommonField{
				Time:     now.Add(time.Second * 3),
				Category: "3",
			},
		},
	}

	db, err := gorm.Open(sqlite.Open("file::memory:?cache=private"))
	require.NoError(t, err)

	r, err := newStore(db)
	require.NoError(t, err)
	for _, item := range itemsToCreate {
		_, err = r.Create(item)
		require.NoError(t, err)
	}

	// should return all and in desc order
	{
		items := r.Get(Option{
			DescOrder: true,
			Limit:     4,
		}).MustGet()
		require.Len(t, items, 3)
	}

	// should return 2 and in asc order
	{
		items1 := r.Get(Option{
			DescOrder: false,
			Limit:     2,
		}).MustGet()
		require.Len(t, items1, 2)
		require.True(t, items1[0].Time.Before(items1[1].Time))
	}

	// should return 2 and with time limit
	{
		items := r.Get(Option{
			DescOrder: true,
			Limit:     4,
		}).MustGet()
		require.Len(t, items, 3)
	}

	// should return category 1
	{
		category := "2"
		items := r.Get(Option{
			Category: &category,
		}).MustGet()
		require.Len(t, items, 1)
		require.Equal(t, category, items[0].Category)
	}
}

func TestStore_DelOldestRows(t *testing.T) {
	now := time.Now()
	itemsToCreate := []Entity{
		{
			LogEntityCommonField: LogEntityCommonField{
				Time:     now.Add(time.Second * 1),
				Category: "1",
			},
		},
		{
			LogEntityCommonField: LogEntityCommonField{
				Time:     now.Add(time.Second * 2),
				Category: "2",
			},
		},
		{
			LogEntityCommonField: LogEntityCommonField{
				Time:     now.Add(time.Second * 3),
				Category: "3",
			},
		},
	}

	db, err := gorm.Open(sqlite.Open("file::memory:?cache=private"))
	require.NoError(t, err)
	r, err := newStore(db)
	require.NoError(t, err)

	for _, item := range itemsToCreate {
		_, err = r.Create(item)
		require.NoError(t, err)
	}
	require.NoError(t, r.DelOldestRows(2))
	items := r.Get(Option{}).MustGet()
	require.Len(t, items, 1)
}

func TestStore_DistinctCategories(t *testing.T) {

	itemsToCreate := []Entity{
		{
			LogEntityCommonField: LogEntityCommonField{
				Category: "1",
			},
		},
		{
			LogEntityCommonField: LogEntityCommonField{
				Category: "2",
			},
		},
		{
			LogEntityCommonField: LogEntityCommonField{
				Category: "3",
			},
		},
		{
			LogEntityCommonField: LogEntityCommonField{
				Category: "3",
			},
		},
	}

	db, err := gorm.Open(sqlite.Open("file::memory:?cache=private"))
	require.NoError(t, err)
	r, err := newStore(db)
	require.NoError(t, err)

	for _, item := range itemsToCreate {
		_, err = r.Create(item)
		require.NoError(t, err)
	}

	categories, err := r.DistinctCategories()
	require.NoError(t, err)
	require.Equal(t, []CategoryCount{
		{
			Name:  "1",
			Count: 1,
		},
		{
			Name:  "2",
			Count: 1,
		},
		{
			Name:  "3",
			Count: 2,
		},
	}, categories)
}

func TestStore_Del(t *testing.T) {
	itemsToCreate := []Entity{
		{
			LogEntityCommonField: LogEntityCommonField{
				Category: "1",
			},
		},
		{
			LogEntityCommonField: LogEntityCommonField{
				Category: "2",
			},
		},
		{
			LogEntityCommonField: LogEntityCommonField{
				Category: "3",
			},
		},
		{
			LogEntityCommonField: LogEntityCommonField{
				Category: "3",
			},
		},
	}

	db, err := gorm.Open(sqlite.Open("file::memory:?cache=private"))
	require.NoError(t, err)
	r, err := newStore(db)
	require.NoError(t, err)

	for _, item := range itemsToCreate {
		_, err = r.Create(item)
		require.NoError(t, err)
	}

	require.NoError(t, r.Del())
	items := r.Get(Option{}).MustGet()
	require.Len(t, items, 0)
}

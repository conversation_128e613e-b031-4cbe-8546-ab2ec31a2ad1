package log

import (
	"fmt"
	"strings"

	"github.com/repeale/fp-go"
	"github.com/samber/mo"
	"gitlab.local/golibrary/language"
)

type TranslationReader struct {
	lang   language.TranslateGetter
	getter ByOptionGetter
}

type Getter interface {
	Get(o Option) mo.Result[[]VEntity]
}

func newTranslationReader(getter ByOptionGetter, lang language.TranslateGetter) *TranslationReader {
	return &TranslationReader{getter: getter, lang: lang}
}

func (Self *TranslationReader) Get(o Option) mo.Result[[]VEntity] {

	items := Self.getter.Get(o)
	if items.IsError() {
		return mo.Err[[]VEntity](items.Error())
	}

	mapTranslate := func(item Entity) VEntity {
		msgToFormatted := Self.lang.GetTranslate(item.Tr.Section, item.Tr.Key)
		msgToFormatted = fp.Reduce(func(acc string, trPair TrPair) string {
			return strings.Replace(acc, fmt.Sprintf("{{%s}}", trPair.Variable), trPair.Value, -1)
		}, msgToFormatted)(item.Tr.TrPairs)
		return VEntity{LogEntityCommonField: item.LogEntityCommonField, Event: msgToFormatted}
	}

	return mo.Ok(fp.Map(mapTranslate)(items.MustGet()))
}

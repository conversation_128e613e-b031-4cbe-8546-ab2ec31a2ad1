package log

import (
	"testing"
	"time"

	"github.com/spf13/afero"
	"github.com/stretchr/testify/require"
	"github.com/undefinedlabs/go-mpatch"
	_ "github.com/undefinedlabs/go-mpatch"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func TestCleaner_GetCfg(t *testing.T) {

	// create a store
	db, err := gorm.Open(sqlite.Open("file::memory:?cache=private"))
	require.NoError(t, err)
	store, err := newStore(db)
	require.NoError(t, err)

	obj, err := newCleaner(afero.NewMemMapFs(), "/file", store)
	require.NoError(t, err)

	// should return default cfg
	cfg, err := obj.GetCfg()
	require.NoError(t, err)

	require.Equal(t, defaultMaxDaysOfLogs, cfg.MaxDaysOfLogs)
	require.Equal(t, defaultMaxNumOfLogs, cfg.MaxNumOfLogs)
}

func TestCleaner_SetCfg(t *testing.T) {
	// create a store
	db, err := gorm.Open(sqlite.Open("file::memory:?cache=private"))
	require.NoError(t, err)
	store, err := newStore(db)
	require.NoError(t, err)

	obj, err := newCleaner(afero.NewMemMapFs(), "/file", store)
	require.NoError(t, err)

	// set cfg
	cfgToSet := CleanCfg{
		MaxDaysOfLogs:        3,
		MaxNumOfLogs:         10,
		EnabledMaxNumOfLogs:  true,
		EnabledMaxDaysOfLogs: true,
	}
	require.NoError(t, obj.SetCfg(cfgToSet))

	// should return cfg the was set
	cfgRet, err := obj.GetCfg()
	require.NoError(t, err)
	require.Equal(t, cfgToSet, cfgRet)

	// the MaxNumOfLogs should take effect
	for i := 0; i < 100; i++ {
		_, err := obj.Create(Entity{
			LogEntityCommonField: LogEntityCommonField{
				Time: time.Now(),
			},
		})
		require.NoError(t, err)
	}
	require.Len(t, store.Get(Option{}).MustGet(), cfgToSet.MaxNumOfLogs)

	// the MaxDaysOfLogs should take effect
	futureTime := time.Now().AddDate(0, 0, cfgToSet.MaxDaysOfLogs+1)
	p, err := mpatch.PatchMethod(time.Now, func() time.Time {
		return futureTime
	})
	defer func() { _ = p.Unpatch() }()
	require.NoError(t, err)

	// create another log which should be using futureTime
	id, err := obj.Create(Entity{
		LogEntityCommonField: LogEntityCommonField{
			Time: time.Now(),
		},
	})
	require.NoError(t, err)

	itemsAfterCleanup := store.Get(Option{}).MustGet()
	require.Len(t, itemsAfterCleanup, 1)
	require.Equal(t, id, itemsAfterCleanup[0].Id)

}

func TestCleaner_Create(t *testing.T) {
	// create a store
	db, err := gorm.Open(sqlite.Open("file::memory:?cache=private"))
	require.NoError(t, err)
	store, err := newStore(db)
	require.NoError(t, err)

	obj, err := newCleaner(afero.NewMemMapFs(), "/file", store)
	require.NoError(t, err)

	// create a log
	logToCreate := Entity{
		LogEntityCommonField: LogEntityCommonField{
			Time:     time.Now(),
			Category: "1",
		},
	}
	id, err := obj.Create(logToCreate)
	require.NoError(t, err)

	// the log should be in the store
	items := store.Get(Option{}).MustGet()
	require.Len(t, items, 1)

	// the id of the log should be the same as the one returned by the store
	require.Equal(t, id, items[0].Id)
}

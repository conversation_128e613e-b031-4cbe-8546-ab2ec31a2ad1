package log

import (
	"bytes"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/gogf/gf/errors/gerror"
	"github.com/gogf/gf/os/glog"
	"github.com/repeale/fp-go"
	"github.com/samber/mo"
	"gitlab.local/golibrary/resp"
	"gitlab.local/golibrary/utils"
)

type RequestType string

const (
	RequestTypeGet               RequestType = "get"
	RequestTypeExport            RequestType = "export"
	RequestTypeDel               RequestType = "delete"
	RequestTypeSetCleanupCfg     RequestType = "set_cleanup_cfg"
	RequestTypeGetCleanupCfg     RequestType = "get_cleanup_cfg"
	RequestTypeGetLogTotalNumber RequestType = "get_log_total_number"
)

type SetCleanupRequestEntity struct {
	CleanupCfg CleanCfg `json:"cleanup_cfg"`
}

type GetRequestEntity struct {
	Option
	StrToSearch string `json:"str_to_search"`
}

type DelRequestEntity struct {
	Category string `json:"category"`
}

type ExportRequestEntity struct {
	Option
}

type RequestEntity struct {
	DelRequest        *DelRequestEntity        `json:"del_request"`
	GetRequest        *GetRequestEntity        `json:"get_request"`
	ExportRequest     *ExportRequestEntity     `json:"export_request"`
	SetCleanupRequest *SetCleanupRequestEntity `json:"set_cleanup_request"`
	Type              RequestType              `json:"type"`
}

func (Self RequestEntity) Validate() error {
	if Self.Type == RequestTypeGet && Self.GetRequest == nil {
		return fmt.Errorf("GetRequest is required")
	}

	if Self.Type == RequestTypeExport && Self.ExportRequest == nil {
		return fmt.Errorf("ExportRequest is required")
	}

	if Self.Type == RequestTypeDel && Self.DelRequest == nil {
		return fmt.Errorf("DelRequest is required")
	}
	if Self.Type == RequestTypeSetCleanupCfg && Self.SetCleanupRequest == nil {
		return fmt.Errorf("SetCleanupRequest is required")
	}
	return nil
}

type RequestStore struct {
	CountGetter
	Getter
	Deleter
	CategoryGetter
}

type RequestProcessor struct {
	cleaner *Cleaner
	appName string
	store   RequestStore
}

func (Self *RequestProcessor) doGet(r GetRequestEntity) (any, error) {
	r.StrToSearch = strings.ToLower(r.StrToSearch)
	filterStr := func(item VEntity) bool {
		return strings.Contains(strings.ToLower(item.Event), r.StrToSearch)
	}

	storeOpt := r.Option
	if r.StrToSearch != "" {
		// trying to get all items from store if search string is not empty
		storeOpt.Limit = 0
		storeOpt.Offset = 0
		// if search string is not empty, we return maximum 5000 items
		r.Limit = 5000
	}
	return Self.store.Get(storeOpt).FlatMap(func(items []VEntity) mo.Result[[]VEntity] {
		if r.StrToSearch != "" {
			items = fp.Filter(filterStr)(items)
		}
		if len(items) > r.Limit {
			items = items[:r.Limit]
		}
		return mo.Ok(items)
	}).Get()
}

func (Self *RequestProcessor) doExport(r ExportRequestEntity) (any, error) {

	ents, err := Self.store.Get(r.Option).Get()
	if err != nil {
		return nil, err
	}

	csvLineCh := make(chan []string, len(ents))
	go func() {
		for _, ent := range ents {
			csvLineCh <- []string{string(ent.LogEntityCommonField.Level),
				ent.Time.Local().Format("2006-01-02 15:04:05"),
				ent.Category,
				ent.User,
				ent.Event}
		}
		close(csvLineCh)
	}()

	w := bytes.Buffer{}
	slowBuf := utils.NewSlowReader(&w)
	fileR := io.NopCloser(slowBuf)
	go func() {
		if err := utils.WriteCsvAsync(&w, []string{"level", "time", "category", "user", "event"}, csvLineCh); err != nil {
			glog.Error(err)
		}
		slowBuf.Eof()
	}()

	now := time.Now()
	return resp.NewFileMemResp(fileR, fmt.Sprintf("%s_log_%04d%02d%02d%02d%02d.csv",
		Self.appName,
		now.Year(),
		now.Month(),
		now.Day(),
		now.Hour(),
		now.Minute())), nil
}

func (Self *RequestProcessor) doDel(r DelRequestEntity) (any, error) {
	if r.Category == "" {
		return nil, Self.store.Del()
	}
	return nil, Self.store.DelByCategory(r.Category)
}

func (Self *RequestProcessor) doRequest(r RequestEntity) (any, error) {
	if err := r.Validate(); err != nil {
		return nil, err
	}

	// do search
	if r.Type == RequestTypeGet {
		return Self.doGet(*r.GetRequest)
	}

	// do export
	if r.Type == RequestTypeExport {
		return Self.doExport(*r.ExportRequest)
	}

	// do delete
	if r.Type == RequestTypeDel {
		return Self.doDel(*r.DelRequest)
	}

	// do set cleanup cfg
	if r.Type == RequestTypeSetCleanupCfg {
		return nil, Self.cleaner.SetCfg(r.SetCleanupRequest.CleanupCfg)
	}

	// do set get cleanup cfg
	if r.Type == RequestTypeGetCleanupCfg {
		return Self.cleaner.GetCfg()
	}

	// do get total number
	if r.Type == RequestTypeGetLogTotalNumber {
		var resp struct {
			Count      int64           `json:"count"`
			Categories []CategoryCount `json:"categories"`
		}
		count, err := Self.store.Count()
		if err != nil {
			return nil, err
		}
		resp.Count = count
		resp.Categories, err = Self.store.DistinctCategories()
		return resp, err
	}

	return nil, nil
}

func (Self *RequestProcessor) Do(data []byte) (any, error) {
	r, err := utils.UnmarshalJsonGeneric[RequestEntity](data)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to unmarshal data")
	}
	return Self.doRequest(r)
}

func (Self *RequestProcessor) DoRequest(r RequestEntity) (any, error) {
	return Self.doRequest(r)
}

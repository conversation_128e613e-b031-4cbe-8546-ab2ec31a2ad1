package log

import (
	"sync"
	"time"

	"github.com/gogf/gf/os/glog"
	"github.com/spf13/afero"
	"gitlab.local/golibrary/utils"
)

type CleanCfg struct {
	EnabledMaxNumOfLogs  bool `json:"enabled_max_num_of_logs"`
	MaxNumOfLogs         int  `json:"max_num_of_logs"`
	EnabledMaxDaysOfLogs bool `json:"enabled_max_days_of_logs"`
	MaxDaysOfLogs        int  `json:"max_days_of_logs"`
}

const (
	defaultMaxNumOfLogs  = 10000
	defaultMaxDaysOfLogs = 30
)

type Cleaner struct {
	// on disk cfg
	cfgFile      string
	fs           afero.Fs
	cfg          CleanCfg
	lastCleanDay int
	totalNum     int
	store        *Store

	// lock
	lock sync.Locker
}

func newCleaner(fs afero.Fs, cfgFile string, store *Store) (*Cleaner, error) {
	obj := &Cleaner{
		fs:      fs,
		cfgFile: cfgFile,
		store:   store,
		lock:    &sync.Mutex{},
	}
	if err := obj.init(); err != nil {
		return nil, err
	}
	return obj, nil
}

func (Self *Cleaner) init() error {

	//
	ok, _ := afero.Exists(Self.fs, Self.cfgFile)
	if !ok {
		if err := Self.SetCfg(CleanCfg{
			MaxNumOfLogs:         defaultMaxNumOfLogs,
			MaxDaysOfLogs:        defaultMaxDaysOfLogs,
			EnabledMaxDaysOfLogs: true,
			EnabledMaxNumOfLogs:  true}); err != nil {
			return err
		}
	}

	// init number of rows in database
	n, err := Self.store.Count()
	if err != nil {
		return err
	}
	Self.totalNum = int(n)

	// init cfg
	Self.cfg, err = Self.GetCfg()
	if err != nil {
		return err
	}

	Self.onMaxNumOfLogs()
	Self.onMaxDaysOfLogs()

	// ok path
	return nil
}

func (Self *Cleaner) onMaxDaysOfLogs() {
	if !Self.cfg.EnabledMaxDaysOfLogs {
		return
	}
	now := time.Now()
	if now.Day() != Self.lastCleanDay {
		if err := Self.store.DelTimeBefore(now.AddDate(0, 0, -(Self.cfg.MaxDaysOfLogs))); err != nil {
			glog.Error(err)
		}
		Self.lastCleanDay = now.Day()
	}
}

func (Self *Cleaner) onMaxNumOfLogs() {

	if !Self.cfg.EnabledMaxNumOfLogs {
		return
	}

	if Self.totalNum > Self.cfg.MaxNumOfLogs {
		if err := Self.store.DelOldestRows(Self.totalNum - Self.cfg.MaxNumOfLogs); err != nil {
			glog.Error(err)
			return
		}
		count, _ := Self.store.Count()
		Self.totalNum = int(count)
	}
}

func (Self *Cleaner) SetCfg(cfg CleanCfg) error {
	if err := utils.SafeWriteJsonFile(Self.fs, Self.cfgFile, cfg); err != nil {
		return err
	}
	Self.cfg = cfg
	return nil
}

func (Self *Cleaner) GetCfg() (CleanCfg, error) {
	return utils.UnmarshalJsonFile2[CleanCfg](Self.fs, Self.cfgFile)
}

func (Self *Cleaner) Create(entity Entity) (int64, error) {
	Self.lock.Lock()
	defer Self.lock.Unlock()
	id, err := Self.store.Create(entity)
	if err != nil {
		return 0, err
	}
	Self.totalNum += 1
	Self.onMaxNumOfLogs()
	Self.onMaxDaysOfLogs()
	return id, nil
}

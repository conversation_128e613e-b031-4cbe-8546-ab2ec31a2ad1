package log

import (
	"path/filepath"

	"github.com/spf13/afero"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/golibrary/language"
)

type ActionLog struct {
	writer       *Writer
	reqProcessor *RequestProcessor
	etcdStore    store.Factory
}

func NewActionLog(dir string, appName string) (*ActionLog, error) {

	// create a store
	store, err := newStore(db)
	if err != nil {
		return nil, err
	}

	// the background log cleaner
	cleaner, err := newCleaner(afero.NewOsFs(), filepath.Join(dir, appName+".cfg"), store)
	if err != nil {
		return nil, err
	}
	r := newTranslationReader(store, newLangService(language.New(), appName))
	return &ActionLog{
		writer: &Writer{logCreator: cleaner, defaultSection: appName},
		reqProcessor: &RequestProcessor{appName: appName, store: RequestStore{
			Getter:         r,
			Deleter:        store,
			CountGetter:    store,
			CategoryGetter: store,
		}, cleaner: cleaner},
	}, nil
}

func (Self *ActionLog) AsWriter() *Writer {
	return Self.writer
}

func (Self *ActionLog) AsRequestProcessor() *RequestProcessor {
	return Self.reqProcessor
}

var _ LogSrv = (*logDto)(nil)

type logDto struct {
	store store.Factory
}

func newLogDto(srv *service) *logDto {
	return &logDto{
		store: srv.store,
	}
}

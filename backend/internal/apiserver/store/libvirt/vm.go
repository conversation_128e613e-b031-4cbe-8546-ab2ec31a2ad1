package libvirt

import (
	"context"
	"crypto/rand"
	"encoding/xml"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"github.com/marmotedu/iam/pkg/log"

	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"libvirt.org/go/libvirt"

	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

var _ store.VMLibvirtStore = (*vms)(nil)

type vms struct {
	pool *ConnPool
}

// interface
type virDomainInterfaceMAC struct {
	Address string `xml:"address,attr"`
}
type virDomainInterfaceModel struct {
	Type string `xml:"type,attr"`
}

// type virDomainInterfaceSource struct {
// 	Bridge  string `xml:"bridge,attr,omitempty"`
// 	Network string `xml:"network,attr,omitempty"`
// }

// type virDomainInterfaceLimit struct {
// 	Average uint `xml:"average,attr,omitempty"`
// 	Peak    uint `xml:"peak,attr,omitempty"`
// 	Burst   uint `xml:"burst,attr,omitempty"`
// }

// type virDomainInterfaceBandwidth struct {
// 	Inbound  *virDomainInterfaceLimit `xml:"inbound,omitempty"`
// 	Outbound *virDomainInterfaceLimit `xml:"outbound,omitempty"`
// }

type virDomainInterfaceTarget struct {
	Device  string `xml:"dev,attr,omitempty"`
	Managed string `xml:"managed,attr,omitempty"`
	// "no" :  libvirt 不会自动管理这个 tap 设备，它需要 手动创建和管理。
}

type virDomainInterfaceElement struct {
	XMLName xml.Name                  `xml:"interface,omitempty"`
	Type    string                    `xml:"type,attr,omitempty"`
	MAC     *virDomainInterfaceMAC    `xml:"mac,omitempty"`
	Alias   *virDomainAlias           `xml:"alias,attr,omitempty"`
	Model   *virDomainInterfaceModel  `xml:"model,omitempty"`
	Target  *virDomainInterfaceTarget `xml:"target,omitempty"`
	// Source  virDomainInterfaceSource  `xml:"source,omitempty"`

	// 网络限流
	// Bandwidth *virDomainInterfaceBandwidth `xml:"bandwidth,omitempty"`
	// ip限制
	// Filter    *virNwfilterRef
}

// disk
type virDomainDiskSourceHost struct {
	Name string `xml:"name,attr"`
	Port uint   `xml:"port,attr"`
}

type virDomainDiskSource struct {
	File     string                   `xml:"file,attr,omitempty"`
	Protocol string                   `xml:"protocol,attr,omitempty"`
	Name     string                   `xml:"name,attr,omitempty"`
	Pool     string                   `xml:"pool,attr,omitempty"`
	Volume   string                   `xml:"volume,attr,omitempty"`
	Device   string                   `xml:"dev,attr,omitempty"`
	Host     *virDomainDiskSourceHost `xml:"host,omitempty"`
}

type virDomainDiskTarget struct {
	Device string `xml:"dev,attr,omitempty"`
	Bus    string `xml:"bus,attr,omitempty"`
}

type virDomainDiskDriver struct {
	Name string `xml:"name,attr"`
	Type string `xml:"type,attr,omitempty"`
}

type virDomainDiskTune struct {
	ReadBytePerSecond   uint `xml:"read_bytes_sec,omitempty"`
	WriteBytePerSecond  uint `xml:"write_bytes_sec,omitempty"`
	ReadIOPerSecond     int  `xml:"read_iops_sec,omitempty"`
	WriteIOPerSecond    int  `xml:"write_iops_sec,omitempty"`
	ReadIOPerSecondMax  int  `xml:"read_iops_sec_max,omitempty"`
	WriteIOPerSecondMax int  `xml:"write_iops_sec_max,omitempty"`
}

type virDomainDiskBoot struct {
	Order int `xml:"order,attr"`
}

type virDomainDiskElement struct {
	XMLName      xml.Name             `xml:"disk,omitempty"`
	Type         string               `xml:"type,attr,omitempty"`
	Device       string               `xml:"device,attr,omitempty"`
	BackingStore string               `xml:"backingStore,omitempty"`
	Driver       virDomainDiskDriver  `xml:"driver,omitempty"`
	Source       *virDomainDiskSource `xml:"source,omitempty"`
	Target       virDomainDiskTarget  `xml:"target,omitempty"`
	ReadOnly     *bool                `xml:"readonly,omitempty"`
	IoTune       *virDomainDiskTune   `xml:"iotune,omitempty"`
	Boot         *virDomainDiskBoot   `xml:"boot"`
	// Alias        *virDomainAlias      `xml:"alias,attr,omitempty"`
	// BootOrder    virDomainDiskBootOrder `xml:"boot,omitempty"`
}

// domain

type virDomainBootDevice struct {
	Device string `xml:"dev,attr"`
}

type virDomainOSType struct {
	Name    string `xml:",innerxml"`
	Arch    string `xml:"arch,attr"`
	Machine string `xml:"machine,attr"`
}

type virDomainOSElement struct {
	Type      virDomainOSType       `xml:"type"`
	BootOrder []virDomainBootDevice `xml:"boot,omitempty"`
	//todo:bootmenu/bootloader/kernal/initrd
}

type virDomainCpuTopology struct {
	Sockets uint `xml:"sockets,attr"`
	Cores   uint `xml:"cores,attr"`
	Threads uint `xml:"threads,attr"`
	Dies    uint `xml:"dies,attr,omitempty"`
}

type virDomainCpuModel struct {
	Model    string `xml:",innerxml"`
	Fallback string `xml:"fallback,attr,omitempty"`
}

type virDomainCpuFeature struct {
	Policy string `xml:"policy,attr,omitempty"`
	Name   string `xml:"name,attr"`
}

type virDomainCpuElement struct {
	Mode       string                `xml:"mode,attr,omitempty"`
	Match      string                `xml:"match,attr,omitempty"`
	Check      string                `xml:"check,attr,omitempty"`
	Migratable string                `xml:"migratable,attr,omitempty"`
	Model      []virDomainCpuModel   `xml:"model,omitempty"`
	Features   []virDomainCpuFeature `xml:"feature,omitempty"`
	Topology   virDomainCpuTopology  `xml:"topology"`
}

type virDomainFeaturePAE struct {
	XMLName xml.Name `xml:"pae,omitempty"`
}

type virDomainFeatureACPI struct {
	XMLName xml.Name `xml:"acpi,omitempty"`
}

type virDomainFeatureAPIC struct {
	XMLName xml.Name `xml:"apic,omitempty"`
	State   string   `xml:"state,attr"`
}

type virDomainFeatureHAP struct {
	XMLName xml.Name `xml:"hap,omitempty"`
	State   string   `xml:"state,attr"`
}

type virDomainFeatureSpinlocks struct {
	XMLName xml.Name `xml:"spinlocks,omitempty"`
	State   string   `xml:"state,attr,omitempty"`
	Retries uint     `xml:"retries,attr,omitempty"`
}

type virDomainFeatureHypervRelaxed struct {
	State string `xml:"state,attr"`
}

type virDomainFeatureHypervVapic struct {
	State string `xml:"state,attr"`
}

type virDomainFeatureHyperv struct {
	XMLName   xml.Name                      `xml:"hyperv,omitempty"`
	Relaxed   virDomainFeatureHypervRelaxed `xml:"relaxed,omitempty"`
	Vapic     virDomainFeatureHypervVapic   `xml:"vapic,omitempty"`
	SpinLocks *virDomainFeatureSpinlocks
}

type virDomainFeatureElement struct {
	PAE    *virDomainFeaturePAE
	ACPI   *virDomainFeatureACPI
	APIC   *virDomainFeatureAPIC
	HAP    *virDomainFeatureHAP
	Hyperv *virDomainFeatureHyperv
}

type virDomainClockTimer struct {
	Name       string `xml:"name,attr,omitempty"`
	TickPolicy string `xml:"tickpolicy,attr,omitempty"`
	Track      string `xml:"track,attr,omitempty"`
	Present    string `xml:"present,attr,omitempty"`
}

type virDomainClockElement struct {
	Offset     string                `xml:"offset,attr,omitempty"`
	Adjustment uint                  `xml:"adjustment,attr,omitempty"`
	Basis      string                `xml:"basis,attr,omitempty"`
	Timer      []virDomainClockTimer `xml:"timer,omitempty"`
}

type virDomainControllerElement struct {
	Type  string `xml:"type,attr,omitempty"`
	Index string `xml:"index,attr,omitempty"`
	Model string `xml:"model,attr,omitempty"`
}

type virDomainSerialTargetModel struct {
	Name string `xml:"name,attr"`
}

type virDomainSource struct {
	Path string `xml:"path,attr,omitempty"`
}

type virDomainSerialTarget struct {
	Type  string                      `xml:"type,attr"`
	Port  uint                        `xml:"port,attr"`
	Model *virDomainSerialTargetModel `xml:"model,attr,omitempty"`
}

type virDomainSerialElement struct {
	Type   string                 `xml:"type,attr,omitempty"`
	Source *virDomainSource       `xml:"source,omitempty"`
	Target *virDomainSerialTarget `xml:"target,omitempty"`
	Alias  *virDomainAlias        `xml:"alias,attr,omitempty"`
}

type virDomainAlias struct {
	Name string `xml:"name,attr,omitempty"`
}

type virDomainConsoleTarget struct {
	Type string `xml:"type,attr"`
	Port int    `xml:"port,attr"`
}

type virDomainConsoleElement struct {
	XMLName xml.Name                `xml:"console,omitempty"`
	Type    string                  `xml:"type,attr,omitempty"`
	TTY     string                  `xml:"tty,attr,omitempty"`
	Source  *virDomainSource        `xml:"source,omitempty"`
	Target  *virDomainConsoleTarget `xml:"target,omitempty"`
	Alias   *virDomainAlias         `xml:"alias,omitempty"`
}

type virDomainChannelTarget struct {
	Type  string `xml:"type,attr,omitempty"`
	Name  string `xml:"name,attr,omitempty"`
	State string `xml:"state,attr,omitempty"`
}

type virDomainChannelSource struct {
	Mode string `xml:"mode,attr,omitempty"`
	Path string `xml:"path,attr,omitempty"`
}

type virDomainChannel struct {
	Type   string                 `xml:"type,attr,omitempty"`
	Target virDomainChannelTarget `xml:"target,omitempty"`
	Source virDomainChannelSource `xml:"source,omitempty"`
	Alias  *virDomainAlias        `xml:"alias,omitempty"`
}

type virDomainInput struct {
	Type  string          `xml:"type,attr,omitempty"`
	Bus   string          `xml:"bus,attr,omitempty"`
	Alias *virDomainAlias `xml:"alias,omitempty"`
}

type virDomainGraphicsListen struct {
	Type    string `xml:"type,attr,omitempty"`
	Address string `xml:"address,attr,omitempty"`
}

type virDomainGraphicsElement struct {
	XMLName    xml.Name                 `xml:"graphics,omitempty"`
	Type       string                   `xml:"type,attr,omitempty"`
	Port       int                      `xml:"port,attr,omitempty"`
	AutoPort   string                   `xml:"autoport,attr,omitempty"`
	ListenAttr string                   `xml:"listen,attr,omitempty"`
	Listen     *virDomainGraphicsListen `xml:"listen,omitempty"`
}

type virVideoModelAcceleration struct {
	Accel2d string `xml:"accel2d,attr,omitempty"`
}

type virVideoModel struct {
	Type         string                    `xml:"type,attr,omitempty"`
	VRAM         uint                      `xml:"vram,attr,omitempty"`
	Heads        uint                      `xml:"heads,attr,omitempty"`
	Primary      string                    `xml:"primary,attr,omitempty"`
	Acceleration virVideoModelAcceleration `xml:"primary,omitempty"`
}

type virVideoDriver struct {
	Name string `xml:"name,attr,omitempty"`
}

type virVideoElement struct {
	Model  virVideoModel   `xml:"model,omitempty"`
	Driver *virVideoDriver `xml:"driver,omitempty"`
	Alias  *virDomainAlias `xml:"alias,omitempty"`
}

type virDomainMemoryStats struct {
	Period uint `xml:"period,attr,omitempty"`
}

type virDomainMemoryBalloon struct {
	Model string               `xml:"model,attr,omitempty"`
	Stats virDomainMemoryStats `xml:"stats,omitempty"`
	Alias *virDomainAlias      `xml:"alias,omitempty"`
}

type virDomainHostDevSourceVendor struct {
	Id uint `xml:"id,attr,omitempty"`
}

type virDomainHostDevSourceProduct struct {
	Id uint `xml:"id,attr,omitempty"`
}

type virDomainHostDevSourceAddress struct {
	Bus     uint `xml:"bus,attr,omitempty"`
	Deviece uint `xml:"device,attr,omitempty"`
}

type virDomainHostdevSource struct {
	Protocol string                        `xml:"protocol,attr,omitempty"`
	Wwpn     string                        `xml:"wwpn,attr,omitempty"`
	Vendor   virDomainHostDevSourceVendor  `xml:"vendor,omitempty"`
	Product  virDomainHostDevSourceProduct `xml:"product,omitempty"`
	Address  virDomainHostDevSourceAddress `xml:"address,omitempty"`
}

type virDomainHostdevBoot struct {
	Order int `xml:"order,attr"`
}

type virDomainHostdev struct {
	XMLName xml.Name               `xml:"hostdev"`
	Managed string                 `xml:"managed,attr,omitempty"`
	Type    string                 `xml:"type,attr,omitempty"`
	Mode    string                 `xml:"mode,attr,omitempty"`
	Source  virDomainHostdevSource `xml:"source,omitempty"`
	// Boot    virDomainHostdevBoot   `xml:"boot,omitempty"`
}

type virDomainDevicesElement struct {
	Emulator      string                       `xml:"emulator,omitempty"`
	Disks         []virDomainDiskElement       `xml:"disk,omitempty"`
	Interface     []virDomainInterfaceElement  `xml:"interface,omitempty"`
	Graphics      virDomainGraphicsElement     `xml:"graphics,omitempty"`
	Controller    []virDomainControllerElement `xml:"controller,omitempty"`
	Input         []virDomainInput             `xml:"input,omitempty"`
	Hostdevs      []virDomainHostdev           `xml:"hostdev,omitempty"` // USB
	Serial        *virDomainSerialElement      `xml:"serial,omitempty"`
	Console       virDomainConsoleElement      `xml:"console,omitempty"`
	MemoryBalloon virDomainMemoryBalloon       `xml:"memballoon,omitempty"`
	Channel       virDomainChannel             `xml:"channel,omitempty"`
	Video         virVideoElement              `xml:"video,omitempty"`
}

type virDomainQEMUCommandlineArg struct {
	Value string `xml:"value,attr"`
}

type virDomainQEMUCommandline struct {
	XMLName xml.Name                      `xml:"qemu:commandline"`
	Args    []virDomainQEMUCommandlineArg `xml:"qemu:arg"`
}

type virDomainSuspendToDisk struct {
	Enabled string `xml:"enabled,attr,omitempty"`
}

type virDomainSuspendToMem struct {
	Enabled string `xml:"enabled,attr,omitempty"`
}

type virDomainPowerElement struct {
	Disk virDomainSuspendToDisk `xml:"suspend-to-disk"`
	Mem  virDomainSuspendToMem  `xml:"suspend-to-mem"`
}

type virDomainCPUTuneDefine struct {
	Shares uint `xml:"shares"`
	Period uint `xml:"period"`
	Quota  uint `xml:"quota"`
}

type virDomainDefine struct {
	XMLName     xml.Name                `xml:"domain"`
	Type        string                  `xml:"type,attr"`
	XmlnsQemu   string                  `xml:"xmlns:qemu,attr,omitempty"`
	Title       string                  `xml:"title,omitempty"`
	Name        string                  `xml:"name"`
	UUID        string                  `xml:"uuid,omitempty"`
	Memory      uint                    `xml:"memory"` //Default in KiB
	VCpu        uint                    `xml:"vcpu"`
	CPUTune     virDomainCPUTuneDefine  `xml:"cputune,omitempty"`
	OS          virDomainOSElement      `xml:"os"`
	CPU         virDomainCpuElement     `xml:"cpu"`
	Devices     virDomainDevicesElement `xml:"devices,omitempty"`
	OnPowerOff  string                  `xml:"on_poweroff,omitempty"`
	OnReboot    string                  `xml:"on_reboot,omitempty"`
	OnCrash     string                  `xml:"on_Crash,omitempty"`
	PowerManage virDomainPowerElement   `xml:"pm,omitempty"`
	Features    virDomainFeatureElement `xml:"features,omitempty"`
	Clock       virDomainClockElement   `xml:"clock,omitempty"`

	QEMUCommandline *virDomainQEMUCommandline
}

const (
	DiskTypeNetwork        = "network"
	DiskTypeBlock          = "block"
	DiskTypeFile           = "file"
	DiskTypeVolume         = "volume"
	DeviceCDROM            = "cdrom"
	DeviceDisk             = "disk"
	DriverNameQEMU         = "qemu"
	DriverTypeRaw          = "raw"
	DriverTypeQCOW2        = "qcow2"
	StartDeviceCharacter   = 0x61 //'a'
	DevicePrefixIDE        = "hd"
	DevicePrefixSCSISata   = "sd"
	DevicePrefixVirtio     = "vd"
	DiskBusIDE             = "ide"
	DiskBusSATA            = "sata"
	DiskBusVirtio          = "virtio"
	ProtocolHTTPS          = "https"
	NetworkModelRTL8139    = "rtl8139"
	NetworkModelE1000      = "e1000"
	NetworkModelVIRTIO     = "virtio"
	DisplayDriverVGA       = "vga"
	DisplayDriverCirrus    = "cirrus"
	DisplayDriverQXL       = "qxl"
	DisplayDriverVirtIO    = "virtio"
	DisplayDriverNone      = "none"
	RemoteControlVNC       = "vnc"
	RemoteControlSPICE     = "spice"
	DevModeSubSystem       = "subsystem"
	DevTypeScsiHost        = "scsi_host"
	DevTypeUSB             = "usb"
	DevProtocol            = "vhost"
	USBModelXHCI           = "nec-xhci"
	USBModelNone           = ""
	TabletBusNone          = ""
	TabletBusVIRTIO        = "virtio"
	TabletBusUSB           = "usb"
	InputTablet            = "tablet"
	DefaultControllerIndex = 1
	ListenAllAddress       = "0.0.0.0"
	ListenTypeAddress      = "address"
	NwfilterActionAccept   = "accept"
	NwfilterActionDrop     = "drop"
	NwfilterDirectionIn    = "in"
	NwfilterDirectionOut   = "out"
	NwfilterDirectionInOut = "inout"
	NwfilterPrefix         = "nano-nwfilter-"
	InterfaceTypeBridge    = "bridge"
	OSMachineQ35           = "q35"
	InterfaceTypeEthernet  = "ethernet"

	PCIController          = "pci"
	USBController          = "usb"
	VirtioSCSIController   = "scsi"
	IDEController          = "ide"
	VirtioSerialController = "virtio-serial"
	VirtioSCSIModel        = "virtio-scsi"
	PCIControllerModel     = "pci-root"
	PCIeControllerModel    = "pcie-root"

	BootDeviceCDROM    = "cdrom"
	BootDeviceHardDisk = "hd"
)

func devOffsetIDE(currentDomain *virDomainDefine) string {
	ch := make([]bool, 2*26)
	for _, disk := range currentDomain.Devices.Disks {
		if disk.Target.Bus != DiskBusIDE {
			continue
		}

		DevCh := []byte(disk.Target.Device)
		ch[DevCh[len(DevCh)-1]-StartDeviceCharacter] = true
	}

	ret := ""
	for idx, use := range ch {
		if !use {
			ret = fmt.Sprintf("%s%c", DevicePrefixIDE, StartDeviceCharacter+idx)
			break
		}
	}

	return ret
}

func devOffsetSata(currentDomain *virDomainDefine) string {
	ch := make([]bool, 2*26)
	for _, disk := range currentDomain.Devices.Disks {
		if disk.Target.Bus != DiskBusSATA {
			continue
		}

		DevCh := []byte(disk.Target.Device)
		ch[DevCh[len(DevCh)-1]-StartDeviceCharacter] = true
	}

	ret := ""
	for idx, use := range ch {
		if !use {
			ret = fmt.Sprintf("%s%c", DevicePrefixSCSISata, StartDeviceCharacter+idx)
			break
		}
	}

	return ret
}

func devOffsetVirtIO(currentDomain *virDomainDefine) string {
	ch := make([]bool, 2*26)
	for _, disk := range currentDomain.Devices.Disks {
		if disk.Target.Bus != DiskBusVirtio {
			continue
		}

		DevCh := []byte(disk.Target.Device)
		ch[DevCh[len(DevCh)-1]-StartDeviceCharacter] = true
	}

	ret := ""
	for idx, use := range ch {
		if !use {
			ret = fmt.Sprintf("%s%c", DevicePrefixVirtio, StartDeviceCharacter+idx)
			break
		}
	}

	return ret
}

func newVMs(pool *ConnPool) *vms {
	return &vms{pool: pool}
}

// Create 创建虚拟机
func (v *vms) CreateGuestInstance(ctx context.Context, config *domain.GuestConfig) (err error) {
	var virDomain *libvirt.Domain
	defer func() {
		if nil != err {
			if nil != virDomain {
				_ = virDomain.Undefine()
			}
		}
	}()
	var xmlData []byte
	var domainDefine virDomainDefine

	conn, err := v.pool.Get()
	if domainDefine, err = v.createDefine(config); err != nil {
		err = fmt.Errorf("create domain define for instance '%s' fail: %s", config.Name, err.Error())
		return
	}

	if xmlData, err = xml.MarshalIndent(domainDefine, "", " "); err != nil {
		err = fmt.Errorf("generete domain define for instance '%s' fail: %s", config.Name, err.Error())
		return
	}
	log.Infof("debug: failed domain define\n%s", string(xmlData))
	virDomain, err = conn.DomainDefineXML(string(xmlData))
	if err != nil {
		log.Infof("debug: failed domain define\n%s\n%s\n", string(xmlData), err.Error())
		err = fmt.Errorf("create domain for instance '%s' fail: %s", config.Name, err.Error())
		return
	}
	if config.AutoStart == 1 {
		if err = virDomain.SetAutostart(true); err != nil {
			err = fmt.Errorf("enable auto start for instance '%s' fail: %s", config.Name, err.Error())
			return
		}
	}
	config.Created = true
	return nil
}

func (v *vms) createDefine(config *domain.GuestConfig) (define virDomainDefine, err error) {
	const (
		cpuModeCustom      = "custom"
		cpuModePassThrough = "host-passthrough"
		cpuMatchExact      = "exact"
		cpuMatchMinimum    = "minimum"
		cpuCheckPartial    = "none"
		cpuCheckMigratable = "on"
		cpuCheckFull       = "full"
		modelFallbackAllow = "allow"
		modelCore2Duo      = "core2duo"
		cpuFeatureMonitor  = "monitor"
		// policy: disable, require, optional, force, forbid
		cpuFeaturePolicyDisable  = "disable"
		cpuFeaturePolicyRequire  = "require"
		cpuFeaturePolicyOptional = "optional"
		cpuFeaturePolicyForce    = "force"
		cpuFeaturePolicyForbid   = "forbid"
	)
	const (
		modelIntelWestmere = "Westmere"
		modelAMDOpteronG5  = "Opteron_G5"
		modelDefault       = modelIntelWestmere
	)

	define.Initial()
	define.Name = config.Name
	define.UUID = config.ID
	define.Memory = config.Memory << 10
	define.VCpu = config.Cores
	define.OS.Type.Machine = config.OSMachine

	if config.CPUPassThru {
		define.CPU.Mode = cpuModePassThrough
		define.CPU.Check = cpuCheckPartial
		define.CPU.Migratable = cpuCheckMigratable
	} else {
		define.CPU.Mode = cpuModeCustom
		define.CPU.Match = cpuMatchExact
		define.CPU.Check = cpuCheckFull
		define.CPU.Model = []virDomainCpuModel{
			{
				Model:    modelDefault,
				Fallback: cpuFeaturePolicyForbid,
			},
		}

		// CPU features
		var require = []string{
			"vme",
			"arat",
			"pclmuldq",
			"x2apic",
			"hypervisor",
		}
		var features = make([]virDomainCpuFeature, 0)
		for _, feature := range require {
			features = append(features, virDomainCpuFeature{
				Name:   feature,
				Policy: cpuFeaturePolicyRequire,
			})
		}
		define.CPU.Features = features
	}

	//cpu
	if err = setCPUPriority(&define, config.CPUPriority); err != nil {
		err = fmt.Errorf("set CPU prioirity fail: %s", err.Error())
		return
	}

	if err = define.CPU.Topology.SetCpuTopology(config.Cores); err != nil {
		err = fmt.Errorf("set CPU topology fail: %s", err.Error())
		return
	}

	// bootFirst := BootDeviceCDROM
	// bootSecond := BootDeviceHardDisk

	// if config.OSBoot == BootDeviceHardDisk {
	// 	bootFirst = BootDeviceHardDisk
	// 	bootSecond = BootDeviceCDROM
	// }

	// define.OS.BootOrder = []virDomainBootDevice{
	// 	{Device: bootFirst},
	// 	{Device: bootSecond},
	// }

	if config.Template == nil {
		err = fmt.Errorf("config's template is empty, create instance fail")
		return
	}

	// disk
	if err = define.SetLocalVolumes(config.VDisks, config.OSMachine, config.BootImages); err != nil {
		err = fmt.Errorf("set local volumes fail: %s", err.Error())
		return
	}
	// if err = define.SetLocalVolumes(config.Template.Disk, config.StoragePool, config.StorageVolumes, config.OSMachine, config.BootImages,
	// 	config.ReadSpeed, config.ReadIOPS, config.WriteSpeed, config.WriteIOPS); err != nil {
	// 	err = fmt.Errorf("set local volumes fail: %s", err.Error())
	// 	return
	// }

	// net
	if err = define.SetPlainNetwork(config.VNics); err != nil {
		err = fmt.Errorf("set plain network fail: %s", err.Error())
		return
	}

	// usb
	if err = define.SetLocalUSB(config.StorageUsb); err != nil {
		err = fmt.Errorf("set loacl usb fail: %s", err.Error())
		return
	}

	define.SetVideoDriver(config.Template.Display)
	if err = define.SetRemoteControl(config.Template.Control, config.MonitorPort, config.MonitorSecret); err != nil {
		err = fmt.Errorf("set remote control fail: %s", err.Error())
		return
	}
	//tablet
	if config.Template.Tablet != TabletBusNone {
		define.Devices.Input = append(define.Devices.Input, virDomainInput{InputTablet, config.Template.Tablet, &virDomainAlias{""}})
	}

	if config.Template.USB != USBModelNone {
		define.Devices.Controller = append(define.Devices.Controller, virDomainControllerElement{
			Type:  USBController,
			Model: config.Template.USB})
	}

	if config.OSMachine == OSMachineQ35 {
		define.Devices.Controller = append(define.Devices.Controller, virDomainControllerElement{
			Type:  PCIController,
			Model: PCIeControllerModel})
	} else {
		define.Devices.Controller = append(define.Devices.Controller, virDomainControllerElement{
			Type:  PCIController,
			Model: PCIControllerModel})

		define.Devices.Controller = append(define.Devices.Controller, virDomainControllerElement{
			Type: IDEController})
	}
	return
}

func (define *virDomainDefine) SetRemoteControl(display string, port uint, secret string) error {
	define.Devices.Graphics.Port = int(port)
	define.Devices.Graphics.Type = display
	define.Devices.Graphics.Listen = &virDomainGraphicsListen{ListenTypeAddress, ListenAllAddress}
	return nil
}

func (define *virDomainDefine) SetVideoDriver(model string) {
	define.Devices.Video.Model.Type = model
}

func (define *virDomainDefine) Initial() {
	const (
		KVMInstanceType = "kvm"
		DefaultOSName   = "hvm"
		DefaultOSArch   = "x86_64"
		Xmlns           = "http://libvirt.org/schemas/domain/qemu/1.0"
		// DefaultOSMachine             = "pc"
		// DefaultOSMachineQ35          = "q35"
		DefaultQEMUEmulator          = "/usr/local/sbin/qemu-system-x86_64"
		DestroyInstance              = "destroy"
		RestartInstance              = "restart"
		DefaultPowerEnabled          = "no"
		DefaultClockBasis            = "utc"
		DefaultClockOffset           = "variable"
		ClockTimerRtc                = "rtc"
		ClockTimerRtcTickPolicy      = "catchup"
		ClockTimerRtcTrack           = "guest"
		ClockTimerPit                = "pit"
		ClockTimerPitTickPolicy      = "delay"
		ClockTimerHpet               = "hpet"
		ClockTimerHpetPresent        = "no"
		ClockTimerHypervClock        = "hypervclock"
		ClockTimerHypervClockPresent = "yes"
		MemoryBalloonModel           = "virtio"
		MemoryBalloonPeriod          = 2
		FeaturesHAPState             = "on"
		FeaturesHypervRelaxed        = "on"
		FeaturesHypervVapic          = "on"
		FeaturesSpinLocksState       = "on"
		FeaturesSpinLocksRetries     = 8191
		ChannelType                  = "unix"
		ChannelTargetType            = "virtio"
		ChannelTargetName            = "org.qemu.guest_agent.0"
	)

	define.Type = KVMInstanceType
	define.OS.Type.Name = DefaultOSName
	define.OS.Type.Arch = DefaultOSArch
	// define.OS.Type.Machine = DefaultOSMachine
	//define.OS.Type.Machine = DefaultOSMachineQ35
	define.Devices.Emulator = DefaultQEMUEmulator
	define.XmlnsQemu = Xmlns
	define.OnPowerOff = DestroyInstance
	define.OnReboot = RestartInstance
	define.OnCrash = RestartInstance

	define.PowerManage.Disk.Enabled = DefaultPowerEnabled
	define.PowerManage.Mem.Enabled = DefaultPowerEnabled

	define.Features.PAE = &virDomainFeaturePAE{}
	define.Features.ACPI = &virDomainFeatureACPI{}
	define.Features.APIC = &virDomainFeatureAPIC{}
	define.Features.HAP = &virDomainFeatureHAP{State: FeaturesHAPState}
	define.Features.Hyperv = &virDomainFeatureHyperv{Relaxed: virDomainFeatureHypervRelaxed{State: FeaturesHypervRelaxed},
		Vapic:     virDomainFeatureHypervVapic{State: FeaturesHypervVapic},
		SpinLocks: &virDomainFeatureSpinlocks{State: FeaturesSpinLocksState, Retries: FeaturesSpinLocksRetries},
	}

	define.Devices.MemoryBalloon.Model = MemoryBalloonModel
	define.Devices.MemoryBalloon.Stats.Period = MemoryBalloonPeriod

	define.Devices.Channel.Type = ChannelType
	define.Devices.Channel.Target = virDomainChannelTarget{ChannelTargetType, ChannelTargetName, ""}

	define.Devices.Controller = []virDomainControllerElement{
		virDomainControllerElement{Type: VirtioSerialController},
		virDomainControllerElement{Type: VirtioSCSIController, Model: VirtioSCSIModel},
	}

	define.Clock = virDomainClockElement{Offset: DefaultClockOffset, Adjustment: 0, Basis: DefaultClockBasis, Timer: []virDomainClockTimer{
		{Name: ClockTimerRtc, TickPolicy: ClockTimerRtcTickPolicy, Track: ClockTimerRtcTrack},
		{Name: ClockTimerPit, TickPolicy: ClockTimerPitTickPolicy},
		{Name: ClockTimerHpet, Present: ClockTimerHpetPresent},
		{Name: ClockTimerHypervClock, Present: ClockTimerHypervClockPresent},
	}}
}

func (define *virDomainDefine) SetLocalVolumes(vdisks []domain.GuestStoreVDisk, OSMachine string, bootImages []string) error {

	// {
	// 	//empty ide cdrom
	// 	var devName = fmt.Sprintf("%s%c", DevicePrefixIDE, StartDeviceCharacter+IDEOffsetCDROM)
	// 	var cdromElement = virDomainDiskElement{Type: DiskTypeBlock, Device: DeviceCDROM, Driver: virDomainDiskDriver{DriverNameQEMU, DriverTypeRaw},
	// 		Target: virDomainDiskTarget{devName, DiskBusIDE}, ReadOnly: &readyOnly}
	// 	define.Devices.Disks = append(define.Devices.Disks, cdromElement)
	// 	order += 1
	// }
	var devName string
	var bootOrder = 0
	var readyOnly = true
	for _, vdisk := range vdisks {
		if OSMachine == OSMachineQ35 && vdisk.DevBus == DiskBusIDE {
			log.Info("IDE controllers are unsupported for this machine type")
			return fmt.Errorf("IDE controllers are unsupported for this machine type")
		}

		switch vdisk.DevBus {
		case DiskBusIDE:
			devName = devOffsetIDE(define)
		case DiskBusSATA:
			devName = devOffsetSata(define)
		case DiskBusVirtio:
			devName = devOffsetVirtIO(define)
		}

		var ioTune *virDomainDiskTune
		if 0 != vdisk.WriteSpeed || 0 != vdisk.ReadSpeed ||
			0 != vdisk.WriteIOPS || 0 != vdisk.ReadIOPS ||
			0 != vdisk.ReadIOPSMax || 0 != vdisk.WriteIOPSMax {
			var limit = virDomainDiskTune{}
			limit.ReadBytePerSecond = uint(vdisk.ReadSpeed)
			limit.ReadIOPerSecond = int(vdisk.ReadIOPS)
			limit.WriteBytePerSecond = uint(vdisk.WriteSpeed)
			limit.WriteIOPerSecond = int(vdisk.WriteIOPS)
			limit.ReadIOPerSecondMax = int(vdisk.ReadIOPSMax)
			limit.WriteIOPerSecondMax = int(vdisk.WriteIOPSMax)
			ioTune = &limit
		}

		// sata
		// ide
		if vdisk.DevBus == DiskBusSATA ||
			vdisk.DevBus == DiskBusIDE {
			var source = virDomainDiskSource{Device: vdisk.Device}
			var diskElement = virDomainDiskElement{Type: DiskTypeBlock, Device: DeviceDisk,
				Driver: virDomainDiskDriver{DriverNameQEMU, DriverTypeRaw},
				Target: virDomainDiskTarget{devName, vdisk.DevBus},
				Boot:   &virDomainDiskBoot{Order: vdisk.Idx},
				Source: &source}
			// 速率控制
			if ioTune != nil {
				diskElement.IoTune = ioTune
			}
			define.Devices.Disks = append(define.Devices.Disks, diskElement)
		} else if vdisk.DevBus == DiskBusVirtio {
			// virtio scsi
			var hostdev = virDomainHostdev{Mode: DevModeSubSystem, Type: DevTypeScsiHost, Managed: "no",
				Source: virDomainHostdevSource{Protocol: DevProtocol, Wwpn: vdisk.NAA}}
			// Boot:   virDomainHostdevBoot{Order: vdisk.Idx}}

			define.Devices.Hostdevs = append(define.Devices.Hostdevs, hostdev)
			// // <qemu:arg value='vhost-scsi-pci,virtqueue_size=128,
			// //cmd_per_lun=128,wwpn=naa.106666da-2ccc-4bd3-a0d7-1fc615e13da4,addr=0xb,
			// //id=vdisk_106666da-2ccc-4bd3-a0d7-1fc615e13da4,set_driverok=off,num_queues=1,max_sectors=16384,boot_tpgt=1,bootindex=2'/>
			// // value := fmt.Sprintf("vhost-scsi-pci,virtqueue_size=128,virtqueue_size=128,cmd_per_lun=128,wwpn=%s,addr=%s,id=vdisk_%s,num_queues=1,max_sectors=16384,boot_tpgt=1,bootindex=%s",
			// // 	vdisk.NAA, "0xa", "106666da-2ccc-4bd3-a0d7-1fc615e13da4", "1")
			// value := fmt.Sprintf("vhost-scsi-pci,wwpn=%s", vdisk.NAA)
			// define.QEMUCommandline = &virDomainQEMUCommandline{Args: []virDomainQEMUCommandlineArg{
			// 	virDomainQEMUCommandlineArg{"-device"},
			// 	virDomainQEMUCommandlineArg{value},
			// }}
		}

		if vdisk.Idx >= bootOrder {
			bootOrder = vdisk.Idx + 1
		}
	}

	for _, img := range bootImages {
		if img == "" {
			continue
		}

		var ciDevice string
		var bus string
		if OSMachine != OSMachineQ35 {
			ciDevice = devOffsetIDE(define)
			bus = DiskBusIDE
		} else {
			ciDevice = devOffsetSata(define)
			bus = DiskBusSATA
		}
		var isoSource = virDomainDiskSource{File: img}
		var ciElement = virDomainDiskElement{Type: DiskTypeFile, Device: DeviceCDROM,
			Driver: virDomainDiskDriver{DriverNameQEMU, DriverTypeRaw},
			Target: virDomainDiskTarget{ciDevice, bus},
			Boot:   &virDomainDiskBoot{Order: bootOrder},
			Source: &isoSource, ReadOnly: &readyOnly}
		define.Devices.Disks = append(define.Devices.Disks, ciElement)
		bootOrder += 1
	}

	return nil
}

func (define *virDomainDefine) SetLocalUSB(usbs []string) error {
	for _, usb := range usbs {
		if !strings.Contains(usb, ":") {
			return fmt.Errorf("%s lead :, format error", usb)
		}
		usbIds := strings.Split(usb, ":")

		if len(usbIds) < 4 {
			return fmt.Errorf("%s lead :, format error", usb)
		}

		var Bus, Device, vendorId, productId uint64
		var err error

		if Bus, err = strconv.ParseUint(usbIds[0], 16, 4); err != nil {
			return fmt.Errorf("%s lead :, format error", usb)
		}
		if Device, err = strconv.ParseUint(usbIds[1], 16, 4); err != nil {
			return fmt.Errorf("%s lead :, format error", usb)
		}
		if vendorId, err = strconv.ParseUint(usbIds[2], 16, 16); err != nil {
			return fmt.Errorf("%s lead :, format error", usb)
		}
		if productId, err = strconv.ParseUint(usbIds[3], 16, 16); err != nil {
			return fmt.Errorf("%s lead :, format error", usb)
		}

		var vendor = virDomainHostDevSourceVendor{Id: uint(vendorId)}
		var product = virDomainHostDevSourceProduct{Id: uint(productId)}

		var hostdevUsb = virDomainHostdev{Mode: DevModeSubSystem, Type: DevTypeUSB, Managed: "no",
			Source: virDomainHostdevSource{Vendor: vendor, Product: product, Address: virDomainHostDevSourceAddress{Bus: uint(Bus), Deviece: uint(Device)}}}
		define.Devices.Hostdevs = append(define.Devices.Hostdevs, hostdevUsb)
	}
	return nil
}

func (define *virDomainDefine) generateMacAddress() (string, error) {
	const (
		BufferSize = 3
		MacPrefix  = "00:16:3e"
	)
	buf := make([]byte, BufferSize)
	_, err := rand.Read(buf)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s:%02x:%02x:%02x", MacPrefix, buf[0], buf[1], buf[2]), nil
}

func (define *virDomainDefine) SetPlainNetwork(ifaces []domain.GuestNetworkInterface) (err error) {
	define.Devices.Interface = make([]virDomainInterfaceElement, 0, len(ifaces))

	for _, iface := range ifaces {
		if iface.HardwareAddress == "" {
			iface.HardwareAddress, err = define.generateMacAddress()
			if err != nil {
				return err
			}
		}

		var i = virDomainInterfaceElement{}
		i.Type = InterfaceTypeEthernet
		i.MAC = &virDomainInterfaceMAC{iface.HardwareAddress}
		i.Target = &virDomainInterfaceTarget{Device: iface.NetworkSource, Managed: "no"}
		i.Model = &virDomainInterfaceModel{iface.NetBus}

		define.Devices.Interface = append(define.Devices.Interface, i)
	}

	// ip过滤策略
	// i.Filter = &virNwfilterRef{Filter: filterName}
	//网络限流
	// if 0 != receiveSpeed || 0 != sendSpeed {
	// 	var bandWidth = virDomainInterfaceBandwidth{}
	// 	if 0 != receiveSpeed {
	// 		var limit = virDomainInterfaceLimit{}
	// 		limit.Average = uint(receiveSpeed)
	// 		limit.Peak = uint(receiveSpeed >> 9)
	// 		limit.Burst = limit.Average
	// 		bandWidth.Inbound = &limit
	// 	}
	// 	if 0 != sendSpeed {
	// 		var limit = virDomainInterfaceLimit{}
	// 		limit.Average = uint(sendSpeed)
	// 		limit.Peak = uint(sendSpeed >> 9)
	// 		limit.Burst = limit.Average
	// 		bandWidth.Outbound = &limit
	// 	}

	// 	// i.Bandwidth = &bandWidth
	// }

	return nil
}

func (topology *virDomainCpuTopology) SetCpuTopology(cores uint) error {
	// const (
	// 	//SplitThreshold = 4
	// 	ThreadPerCore = 2
	// 	MaxCores      = 1 << 5
	// 	MaxSockets    = 1 << 3
	// )
	// if (totalThreads > 1) && (0 != (totalThreads % 2)) {
	// 	return fmt.Errorf("even core number ( %d ) is not allowed", totalThreads)
	// }
	// var threads, cores, sockets uint
	// if totalThreads < ThreadPerCore {
	// 	threads = 1
	// 	sockets = 1
	// 	cores = totalThreads
	// } else {
	// 	threads = ThreadPerCore
	// 	cores = totalThreads / threads
	// 	sockets = 1
	// 	if cores > MaxCores {
	// 		for sockets = 2; sockets < MaxSockets+1; sockets = sockets << 1 {
	// 			cores = (totalThreads / threads) / sockets
	// 			if cores <= MaxCores {
	// 				break
	// 			}
	// 		}
	// 		if cores > MaxCores {
	// 			return fmt.Errorf("no proper cpu topology fit total threads %d", totalThreads)
	// 		}
	// 	}
	// }
	topology.Threads = 1
	topology.Cores = cores
	topology.Sockets = 1
	topology.Dies = 1
	return nil
}

func setCPUPriority(guest *virDomainDefine, priority domain.PriorityEnum) (err error) {
	const (
		periodPerSecond  = 1000000
		quotaPerSecond   = 1000000
		highShares       = 2000
		highMediumShares = 1500
		mediumShares     = 1000
		lowMediumShares  = 750
		lowShares        = 500
	)
	guest.CPUTune.Period = periodPerSecond
	switch priority {
	case domain.PriorityHigh:
		guest.CPUTune.Shares = highShares
		guest.CPUTune.Quota = quotaPerSecond
		break
	case domain.PriorityHighMedium:
		guest.CPUTune.Shares = highMediumShares
		guest.CPUTune.Quota = quotaPerSecond / 2
		break
	case domain.PriorityMedium:
		guest.CPUTune.Shares = mediumShares
		guest.CPUTune.Quota = quotaPerSecond / 4
		break
	case domain.PriorityLowMedium:
		guest.CPUTune.Shares = lowMediumShares
		guest.CPUTune.Quota = quotaPerSecond / 6
		break
	case domain.PriorityLow:
		guest.CPUTune.Shares = lowShares
		guest.CPUTune.Quota = quotaPerSecond / 8
		break
	default:
		return fmt.Errorf("invalid CPU priority %d", priority)
	}
	return nil
}

// hasErrorCode 检查错误是否包含特定的错误码
func hasErrorCode(err error, expectedErrCode libvirt.ErrorNumber) bool {
	var libvirtError libvirt.Error
	ok := errors.As(err, &libvirtError)
	if ok {
		return errors.Is(libvirtError.Code, expectedErrCode)
	}
	return false
}

func (v *vms) IsInstanceRunning(ctx context.Context, id string) (bool, error) {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return false, err
	}
	return virDomain.IsActive()
}

func (v *vms) StartInstance(ctx context.Context, id string) error {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return err
	}
	isRunning, err := virDomain.IsActive()
	if err != nil {
		return err
	}
	if isRunning {
		return fmt.Errorf("instance '%s' already started", id)
	}
	return virDomain.Create()
}

func (v *vms) StopInstance(ctx context.Context, id string, reboot, force bool) error {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return err
	}
	isRunning, err := virDomain.IsActive()
	if err != nil {
		return err
	}
	if !isRunning {
		return fmt.Errorf("instance '%s' already stopped", id)
	}
	//todo: check & unload cdrom before restart
	if reboot {
		if force {
			return virDomain.Reset(0)
		} else {
			return virDomain.Reboot(libvirt.DOMAIN_REBOOT_DEFAULT)
		}
	} else {
		if force {
			return virDomain.Destroy()
		} else {
			return virDomain.Shutdown()
		}
	}
}

func (v *vms) DeleteInstance(ctx context.Context, id string) (err error) {
	var virDomain *libvirt.Domain
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err = conn.LookupDomainByUUIDString(id)
	if err != nil {
		return
	}

	var currentDomain virDomainDefine
	xmlDesc, err := virDomain.GetXMLDesc(0)
	if err != nil {
		return err
	}

	if err = xml.Unmarshal([]byte(xmlDesc), &currentDomain); err != nil {
		return err
	}

	var running bool
	running, err = virDomain.IsActive()
	if err != nil {
		return
	}
	if running {
		return fmt.Errorf("instance '%s' is running", id)
	}

	return virDomain.Undefine()
}

func (v *vms) ModifyGuestName(ctx context.Context, uuid, newName string) (err error) {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(uuid)
	if err != nil {
		return err
	}
	isRunning, err := virDomain.IsActive()
	if err != nil {
		return err
	}
	if isRunning {
		return fmt.Errorf("instance '%s' is still running", uuid)
	}
	var currentName string
	currentName, err = virDomain.GetName()
	if err != nil {
		return
	}
	if currentName == newName {
		return errors.New("no need to change")
	}
	return virDomain.Rename(newName, 0)
}

func (v *vms) ModifyCPUTopology(ctx context.Context, id string, core uint, immediate bool) (err error) {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	const (
		TopologyFormat = "<topology sockets='%d' dies='%d' cores='%d' threads='%d'/>"
		VCPUFormat     = "<vcpu placement='static'>%d</vcpu>"
	)
	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return err
	}

	var currentDomain virDomainDefine
	xmlDesc, err := virDomain.GetXMLDesc(0)
	if err != nil {
		return err
	}

	if err = xml.Unmarshal([]byte(xmlDesc), &currentDomain); err != nil {
		log.Infof("%s", err.Error())
		return err
	}

	var previousDefine = fmt.Sprintf(TopologyFormat, currentDomain.CPU.Topology.Sockets, currentDomain.CPU.Topology.Dies,
		currentDomain.CPU.Topology.Cores, currentDomain.CPU.Topology.Threads)
	var newTopology = virDomainCpuTopology{}
	if err = newTopology.SetCpuTopology(core); err != nil {
		return
	}
	var replaceData = fmt.Sprintf(TopologyFormat, newTopology.Sockets, newTopology.Dies,
		newTopology.Cores, newTopology.Threads)
	var previousDefineVCPU = fmt.Sprintf(VCPUFormat, currentDomain.VCpu)
	var replaceDataVCPU = fmt.Sprintf(VCPUFormat, core)

	var modifiedData = strings.Replace(strings.Replace(xmlDesc, previousDefine, replaceData, 1), previousDefineVCPU, replaceDataVCPU, 1)

	if _, err = conn.DomainDefineXML(modifiedData); err != nil {
		return err
	}
	log.Info("2")
	return nil
}

func (v *vms) ModifyMemory(ctx context.Context, id string, memory uint, immediate bool) (err error) {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return err
	}

	var memoryInMiB = uint64(memory)
	maxMemory, err := virDomain.GetMaxMemory()
	if err != nil {
		return err
	}

	if memoryInMiB > maxMemory {
		virDomain.SetMaxMemory(uint64(maxMemory))
		if err = virDomain.SetMemoryFlags(memoryInMiB, libvirt.DOMAIN_MEM_CONFIG|libvirt.DOMAIN_MEM_MAXIMUM); err != nil {
			return
		}
	}

	// 修改memory大小
	virDomain.SetMaxMemory(uint64(memoryInMiB))
	// 修改currentMemory大小
	if err = virDomain.SetMemoryFlags(memoryInMiB, libvirt.DOMAIN_MEM_CONFIG); err != nil {
		return
	}
	return nil
}

func (v *vms) ModifyAutoStart(guestID string, enable bool) (err error) {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	var virDomain *libvirt.Domain
	if virDomain, err = conn.LookupDomainByUUIDString(guestID); err != nil {
		err = fmt.Errorf("get guest fail: %s", err.Error())
		return
	}
	var current bool
	if current, err = virDomain.GetAutostart(); err != nil {
		err = fmt.Errorf("check auto start status fail: %s", err.Error())
		return
	}
	if current == enable {
		if current {
			err = fmt.Errorf("auto start of guest '%s' already enabled", guestID)
		} else {
			err = fmt.Errorf("auto start of guest '%s' already disabled", guestID)
		}
		return
	}
	if err = virDomain.SetAutostart(enable); err != nil {
		err = fmt.Errorf("set auto start fail: %s", err.Error())
		return
	}
	return
}

func removeSerialConsole(xml string) string {
	newXML := xml
	newXML = replaceString(newXML, `<serial\b[^>]*>.*?</console>`, "")
	return newXML
}

func replaceString(original, pattern, replacement string) string {
	re := regexp.MustCompile(pattern)
	return re.ReplaceAllString(original, replacement)
}

func (v *vms) ModifySerialConsole(ctx context.Context, guestID string, enable bool) (err error) {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	var virDomain *libvirt.Domain
	if virDomain, err = conn.LookupDomainByUUIDString(guestID); err != nil {
		err = fmt.Errorf("get guest fail: %s", err.Error())
		return
	}

	var xmlContent string
	xmlContent, err = virDomain.GetXMLDesc(0)
	if err != nil {
		err = fmt.Errorf("get xml guest fail,error: %s", err.Error())
		return
	}

	xmlContent = removeSerialConsole(xmlContent)
	log.Infof("%s", xmlContent)
	_, err = conn.DomainDefineXML(xmlContent)
	if err != nil {
		err = fmt.Errorf("define fail: %s, content: %s", err.Error(), xmlContent)
	}
	return
}

func (v *vms) SetCPUThreshold(ctx context.Context, guestID string, priority domain.PriorityEnum) (err error) {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(guestID)
	if err != nil {
		return err
	}
	var xmlContent string
	xmlContent, err = virDomain.GetXMLDesc(0)
	if err != nil {
		return
	}
	var newDefine virDomainDefine
	if err = xml.Unmarshal([]byte(xmlContent), &newDefine); err != nil {
		return
	}
	if err = setCPUPriority(&newDefine, priority); err != nil {
		return
	}
	data, err := xml.MarshalIndent(newDefine, "", " ")
	if err != nil {
		return err
	}
	_, err = conn.DomainDefineXML(string(data))
	if err != nil {
		err = fmt.Errorf("define fail: %s, content: %s", err.Error(), string(data))
	}
	return
}

func (v *vms) EjectMediaISO(ctx context.Context, id string, bootImages []string) (err error) {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	// 解析
	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return err
	}
	var currentDomain virDomainDefine
	xmlDesc, err := virDomain.GetXMLDesc(0)
	if err != nil {
		return err
	}
	if err = xml.Unmarshal([]byte(xmlDesc), &currentDomain); err != nil {
		log.Infof("%s", err.Error())
		return err
	}

	isRunning, err := virDomain.IsActive()
	if err != nil {
		return err
	}

	// 判断要卸载的iso文件
	imgs := make(map[string]string)
	for _, i := range bootImages {
		imgs[i] = ""
	}

	//empty ide cdrom
	var readyOnly = true
	var emptyDriver = virDomainDiskElement{Type: DiskTypeBlock, Device: DeviceCDROM, Driver: virDomainDiskDriver{DriverNameQEMU, DriverTypeRaw},
		Target: virDomainDiskTarget{}, ReadOnly: &readyOnly}

	for idx, disk := range currentDomain.Devices.Disks {
		if _, ok := imgs[disk.Source.File]; ok {
			emptyDriver.Target.Device = disk.Target.Device
			emptyDriver.Target.Bus = disk.Target.Bus

			if isRunning {
				// 用于热卸载
				var deviceWithoutMedia string
				if data, err := xml.MarshalIndent(emptyDriver, "", " "); err != nil {
					log.Infof("%s eject media fail,err: %s", id, err.Error())
					return err
				} else {
					deviceWithoutMedia = string(data)
				}

				if err := virDomain.UpdateDeviceFlags(deviceWithoutMedia, libvirt.DOMAIN_DEVICE_MODIFY_LIVE); err != nil {
					log.Infof("%s", deviceWithoutMedia)
					log.Infof("%s eject media fail,err: %s", id, err.Error())
					return err
				}
				continue
			}

			//冷卸载
			currentDomain.Devices.Disks[idx] = emptyDriver
		}
	}

	if isRunning {
		return nil
	}

	// 冷卸载
	if xmldata, err := xml.MarshalIndent(currentDomain, "", ""); err != nil {
		log.Infof("%s eject media fail", id)
		return err
	} else {
		if _, err := conn.DomainDefineXML(string(xmldata)); err != nil {
			log.Infof("%s eject media fail", id)
			return err
		}
	}

	return nil
}

func (v *vms) InsertMediaISO(ctx context.Context, id string, osMachine string, bootImages []string) (err error) {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	// 解析
	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return err
	}
	var currentDomain virDomainDefine
	xmlDesc, err := virDomain.GetXMLDesc(0)
	if err != nil {
		return err
	}
	if err = xml.Unmarshal([]byte(xmlDesc), &currentDomain); err != nil {
		log.Infof("%s", err.Error())
		return err
	}

	isRunning, err := virDomain.IsActive()
	if err != nil {
		return err
	}

	var ciDevice string
	var bus string
	var deviceWithoutMedia string
	var readyOnly = true
	for _, img := range bootImages {
		loadIndex := -1
		for idx, disk := range currentDomain.Devices.Disks {
			if disk.Device == "cdrom" {
				if disk.Source == nil || disk.Source.File == "" {
					loadIndex = idx
					continue
				}
			}
		}

		if loadIndex == -1 {
			if osMachine != OSMachineQ35 {
				ciDevice = devOffsetIDE(&currentDomain)
				bus = DiskBusIDE
			} else {
				ciDevice = devOffsetSata(&currentDomain)
				bus = DiskBusSATA
			}
		} else {
			ciDevice = currentDomain.Devices.Disks[loadIndex].Target.Device
			bus = currentDomain.Devices.Disks[loadIndex].Target.Bus
		}

		var isoSource = virDomainDiskSource{File: img}
		var ciElement = virDomainDiskElement{Type: DiskTypeFile, Device: DeviceCDROM, Driver: virDomainDiskDriver{DriverNameQEMU, DriverTypeRaw},
			Target: virDomainDiskTarget{ciDevice, bus}, Source: &isoSource, ReadOnly: &readyOnly}

		// 热加载
		if isRunning {
			if data, err := xml.MarshalIndent(ciElement, "", " "); err != nil {
				log.Infof("%s insert media fail,err: %s", id, err.Error())
				return err
			} else {
				deviceWithoutMedia = string(data)
			}
			if err := virDomain.UpdateDeviceFlags(deviceWithoutMedia, libvirt.DOMAIN_DEVICE_MODIFY_LIVE); err != nil {
				log.Infof("%s insert media fail,err: %s", id, err.Error())
				return err
			}
			if loadIndex != -1 {
				currentDomain.Devices.Disks[loadIndex].Source.File = img
			}
			continue
		}

		// 冷加载
		if loadIndex != -1 {
			currentDomain.Devices.Disks[loadIndex] = ciElement
		} else {
			currentDomain.Devices.Disks = append(currentDomain.Devices.Disks, ciElement)
		}

		if xmldata, err := xml.MarshalIndent(currentDomain, "", ""); err != nil {
			log.Infof("%s insert media fail,err: %s", id, err.Error())
			return err
		} else {
			if _, err := conn.DomainDefineXML(string(xmldata)); err != nil {
				log.Infof("%s insert media fail,err: %s", id, err.Error())
				return err
			}
		}
	}

	return nil
}

func (v *vms) Exists(id string) bool {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	_, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return false
	}
	return true
}

func (v *vms) GetInstanceStatus(id string) (ins domain.GuestConfig, err error) {
	panic("no finish")
}

func (v *vms) ModifyVideoCard(id string, videoCard string) error {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return err
	}

	isRunning, err := virDomain.IsActive()
	if err != nil {
		return err
	}
	if isRunning {
		return fmt.Errorf("instance '%s' running", id)
	}

	var currentDomain virDomainDefine
	xmlDesc, err := virDomain.GetXMLDesc(0)
	if err != nil {
		return err
	}

	if err = xml.Unmarshal([]byte(xmlDesc), &currentDomain); err != nil {
		log.Infof("%s", err.Error())
		return err
	}

	currentDomain.Devices.Video.Model.Type = videoCard
	if xmldata, err := xml.MarshalIndent(currentDomain, "", ""); err != nil {
		log.Infof("%s modify video card to %s fail", id, videoCard)
		return err
	} else {
		if _, err := conn.DomainDefineXML(string(xmldata)); err != nil {
			log.Infof("%s modify video card to %s fail", id, videoCard)
			return err
		}
	}
	return nil
}

func (v *vms) ModifyMachineType(ctx context.Context, config domain.GuestConfig) error {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(config.ID)
	if err != nil {
		return err
	}

	isRunning, err := virDomain.IsActive()
	if err != nil {
		return err
	}
	if isRunning {
		return fmt.Errorf("instance '%s' running", config.ID)
	}

	// 修改机器类型，只能重新创建
	if err := v.CreateGuestInstance(ctx, &config); err != nil {
		log.Infof("%s modify machine type fail,err: %s", config.ID, err.Error())
		return err
	}
	return nil
}

func (v *vms) ModifyOSBoot(ctx context.Context, id string, osboot string) error {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return err
	}

	isRunning, err := virDomain.IsActive()
	if err != nil {
		return err
	}
	if isRunning {
		return fmt.Errorf("instance '%s' running", id)
	}

	var currentDomain virDomainDefine
	xmlDesc, err := virDomain.GetXMLDesc(0)
	if err != nil {
		return err
	}

	if err = xml.Unmarshal([]byte(xmlDesc), &currentDomain); err != nil {
		log.Infof("%s", err.Error())
		return err
	}

	// bootFirst := BootDeviceCDROM
	// bootSecond := BootDeviceHardDisk

	// if osboot == BootDeviceHardDisk {
	// 	bootFirst = BootDeviceHardDisk
	// 	bootSecond = BootDeviceCDROM
	// }

	// currentDomain.OS.BootOrder = []virDomainBootDevice{
	// 	{Device: bootFirst},
	// 	{Device: bootSecond},
	// }
	if xmldata, err := xml.MarshalIndent(currentDomain, "", ""); err != nil {
		log.Infof("%s modify os boot order fail,err: %s", id, err.Error())
		return err
	} else {
		if _, err := conn.DomainDefineXML(string(xmldata)); err != nil {
			log.Infof("%s modify os boot order fail,err: %s", id, err.Error())
			return err
		}
	}
	return nil
}

func (v *vms) ModifyNetInterface(ctx context.Context, id string, ifaces []domain.GuestNetworkInterface) error {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return err
	}

	isRunning, err := virDomain.IsActive()
	if err != nil {
		return err
	}
	if isRunning {
		return fmt.Errorf("instance '%s' stopped", id)
	}

	var currentDomain virDomainDefine
	xmlDesc, err := virDomain.GetXMLDesc(0)
	if err != nil {
		return err
	}

	if err = xml.Unmarshal([]byte(xmlDesc), &currentDomain); err != nil {
		log.Infof("%s", err.Error())
		return err
	}

	currentDomain.Devices.Interface = currentDomain.Devices.Interface[:0]
	for _, iface := range ifaces {
		var i = virDomainInterfaceElement{}
		i.Type = InterfaceTypeEthernet
		i.MAC = &virDomainInterfaceMAC{iface.HardwareAddress}
		i.Target = &virDomainInterfaceTarget{Device: iface.NetworkSource, Managed: "no"}
		i.Model = &virDomainInterfaceModel{iface.NetBus}

		currentDomain.Devices.Interface = append(currentDomain.Devices.Interface, i)
	}

	if xmldata, err := xml.MarshalIndent(currentDomain, "", ""); err != nil {
		log.Infof("%s modify mac to fail", id)
		return err
	} else {
		if _, err := conn.DomainDefineXML(string(xmldata)); err != nil {
			log.Infof("%s modify mac to fail", id)
			return err
		}
	}

	return nil
}

func (v *vms) SetDiskThreshold(guestID string, writeSpeed, writeIOPS, readSpeed, readIOPS uint64) (err error) {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(guestID)
	if err != nil {
		return err
	}
	var currentDomain virDomainDefine
	xmlDesc, err := virDomain.GetXMLDesc(0)
	if err != nil {
		return err
	}
	if err = xml.Unmarshal([]byte(xmlDesc), &currentDomain); err != nil {
		return err
	}
	var activated bool
	if activated, err = virDomain.IsActive(); err != nil {
		return err
	}
	if activated {
		err = fmt.Errorf("instance %s ('%s') is still running", currentDomain.Name, guestID)
		return
	}
	//var affectFlag = libvirt.DOMAIN_AFFECT_CONFIG | libvirt.DOMAIN_AFFECT_LIVE
	//deprecated: unsupported configuration: the block I/O throttling group parameter is not supported with this QEMU binary
	//var affectFlag = libvirt.DOMAIN_AFFECT_CONFIG
	//var parameters libvirt.DomainBlockIoTuneParameters
	//parameters.WriteIopsSec = writeIOPS
	//parameters.WriteIopsSecSet = true
	//parameters.ReadIopsSec = readIOPS
	//parameters.ReadIopsSecSet = true
	//parameters.GroupNameSet = true
	//parameters.GroupName = "default"
	//
	//for _, dev := range currentDomain.Devices.Disks {
	//	if nil != dev.ReadOnly {
	//		//ignore readonly device
	//		continue
	//	}
	//	if err = virDomain.SetBlockIoTune(dev.Target.Device, &parameters, affectFlag); err != nil {
	//		err = fmt.Errorf("set block io tune fail for dev '%s' : %s", dev.Target.Device, err.Error())
	//		return
	//	}
	//}
	//return nil

	var impactFlag = libvirt.DOMAIN_DEVICE_MODIFY_CONFIG

	for _, dev := range currentDomain.Devices.Disks {
		if nil != dev.ReadOnly {
			//ignore readonly device
			continue
		}
		//static configure
		var tune = dev.IoTune
		if tune == nil {
			tune = &virDomainDiskTune{}
		}
		tune.WriteIOPerSecond = int(writeIOPS)
		tune.ReadIOPerSecond = int(readIOPS)
		tune.WriteBytePerSecond = uint(writeSpeed)
		tune.ReadBytePerSecond = uint(readSpeed)

		dev.IoTune = tune
		data, err := xml.MarshalIndent(dev, "", " ")
		if err != nil {
			return err
		}
		if err = virDomain.UpdateDeviceFlags(string(data), impactFlag); err != nil {
			err = fmt.Errorf("update device fail: %s, content: %s", err.Error(), string(data))
			return err
		}
	}
	return nil
}

func (v *vms) UpdateDomain(guestID string, writeSpeed, writeIOPS, readSpeed, readIOPS uint64) (err error) {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(guestID)
	if err != nil {
		return err
	}

	var currentDomain virDomainDefine
	xmlDesc, err := virDomain.GetXMLDesc(0)
	if err != nil {
		return err
	}
	if err = xml.Unmarshal([]byte(xmlDesc), &currentDomain); err != nil {
		return err
	}
	var activated bool
	if activated, err = virDomain.IsActive(); err != nil {
		return err
	}
	if activated {
		err = fmt.Errorf("instance %s ('%s') is still running", currentDomain.Name, guestID)
		return
	}
	//var affectFlag = libvirt.DOMAIN_AFFECT_CONFIG | libvirt.DOMAIN_AFFECT_LIVE
	//deprecated: unsupported configuration: the block I/O throttling group parameter is not supported with this QEMU binary
	//var affectFlag = libvirt.DOMAIN_AFFECT_CONFIG
	//var parameters libvirt.DomainBlockIoTuneParameters
	//parameters.WriteIopsSec = writeIOPS
	//parameters.WriteIopsSecSet = true
	//parameters.ReadIopsSec = readIOPS
	//parameters.ReadIopsSecSet = true
	//parameters.GroupNameSet = true
	//parameters.GroupName = "default"
	//
	//for _, dev := range currentDomain.Devices.Disks {
	//	if nil != dev.ReadOnly {
	//		//ignore readonly device
	//		continue
	//	}
	//	if err = virDomain.SetBlockIoTune(dev.Target.Device, &parameters, affectFlag); err != nil {
	//		err = fmt.Errorf("set block io tune fail for dev '%s' : %s", dev.Target.Device, err.Error())
	//		return
	//	}
	//}
	//return nil

	var impactFlag = libvirt.DOMAIN_DEVICE_MODIFY_CONFIG

	for _, dev := range currentDomain.Devices.Disks {
		if nil != dev.ReadOnly {
			//ignore readonly device
			continue
		}
		//static configure
		var tune = dev.IoTune
		if tune == nil {
			tune = &virDomainDiskTune{}
		}
		tune.WriteIOPerSecond = int(writeIOPS)
		tune.ReadIOPerSecond = int(readIOPS)
		tune.WriteBytePerSecond = uint(writeSpeed)
		tune.ReadBytePerSecond = uint(readSpeed)

		dev.IoTune = tune
		data, err := xml.MarshalIndent(dev, "", " ")
		if err != nil {
			return err
		}
		if err = virDomain.UpdateDeviceFlags(string(data), impactFlag); err != nil {
			err = fmt.Errorf("update device fail: %s, content: %s", err.Error(), string(data))
			return err
		}
	}
	return nil
}

func (v *vms) ModifyVDisk(ctx context.Context, id string, vdisks []domain.GuestStoreVDisk) (err error) {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return err
	}

	var currentDomain virDomainDefine
	xmlDesc, err := virDomain.GetXMLDesc(0)
	if err != nil {
		return err
	}
	if err = xml.Unmarshal([]byte(xmlDesc), &currentDomain); err != nil {
		return err
	}

	type changeDisk struct {
		del    bool
		update bool
		add    bool

		optIndex int
		DevType  string
		vdisk    domain.GuestStoreVDisk
	}

	delDisk := make([]int, 0)
	delHostdev := make([]int, 0)
	updateDisks := make(map[string]*changeDisk)

	for _, vdisk := range vdisks {
		if vdisk.DevBus == DiskBusIDE ||
			vdisk.DevBus == DiskBusSATA {
			updateDisks[vdisk.Device] = &changeDisk{
				del:    false,
				update: false,
				add:    false,
				vdisk:  vdisk,
			}
		}

		if vdisk.DevBus == DiskBusVirtio {
			updateDisks[vdisk.NAA] = &changeDisk{
				del:    false,
				update: false,
				add:    false,
				vdisk:  vdisk,
			}
		}
	}

	for idx, vdisk := range currentDomain.Devices.Disks {
		if vdisk.Type != DiskTypeBlock {
			continue
		}

		if update, ok := updateDisks[vdisk.Source.Device]; !ok {
			updateDisks[vdisk.Source.Device] = &changeDisk{
				del:    true,
				update: false,
				add:    false,
				vdisk:  domain.GuestStoreVDisk{Device: vdisk.Source.Device, DevBus: vdisk.Target.Bus},
			}
			delDisk = append(delDisk, idx)
		} else if vdisk.Target.Bus == update.vdisk.DevBus {
			delete(updateDisks, vdisk.Source.Device)
			continue
		} else if vdisk.Target.Bus == DiskBusVirtio {
			update.del = true
			delDisk = append(delDisk, idx)
		} else {
			update.update = true
			update.optIndex = idx
			update.DevType = "disk"
		}
	}

	for idx, hostdev := range currentDomain.Devices.Hostdevs {
		if hostdev.Type != DevTypeScsiHost {
			continue
		}

		if update, ok := updateDisks[hostdev.Source.Wwpn]; !ok {
			updateDisks[hostdev.Source.Wwpn] = &changeDisk{
				del:    true,
				update: false,
				add:    false,
				vdisk:  domain.GuestStoreVDisk{NAA: hostdev.Source.Wwpn},
			}
			delHostdev = append(delHostdev, idx)
		} else if update.vdisk.DevBus != DiskBusVirtio {
			update.del = true
			delHostdev = append(delHostdev, idx)
		} else {
			delete(updateDisks, hostdev.Source.Wwpn)
		}
	}

	for _, updata := range updateDisks {
		updata.add = !updata.del && !updata.update
	}

	isRunning, err := virDomain.IsActive()
	if err != nil {
		return err
	}

	var devName string
	var updateVDisk string
	for _, updata := range updateDisks {
		vdisk := updata.vdisk

		switch vdisk.DevBus {
		case DiskBusIDE:
			devName = devOffsetIDE(&currentDomain)
		case DiskBusSATA:
			devName = devOffsetSata(&currentDomain)
		case DiskBusVirtio:
			devName = devOffsetVirtIO(&currentDomain)
		}

		var ioTune *virDomainDiskTune
		if 0 != vdisk.WriteSpeed || 0 != vdisk.ReadSpeed ||
			0 != vdisk.WriteIOPS || 0 != vdisk.ReadIOPS ||
			0 != vdisk.ReadIOPSMax || 0 != vdisk.WriteIOPSMax {
			var limit = virDomainDiskTune{}
			limit.ReadBytePerSecond = uint(vdisk.ReadSpeed)
			limit.ReadIOPerSecond = int(vdisk.ReadIOPS)
			limit.WriteBytePerSecond = uint(vdisk.WriteSpeed)
			limit.WriteIOPerSecond = int(vdisk.WriteIOPS)
			limit.ReadIOPerSecondMax = int(vdisk.ReadIOPSMax)
			limit.WriteIOPerSecondMax = int(vdisk.WriteIOPSMax)
			ioTune = &limit
		}

		// sata
		// ide
		var diskData any
		var hostdev virDomainHostdev
		var diskElement virDomainDiskElement
		if vdisk.DevBus == DiskBusSATA ||
			vdisk.DevBus == DiskBusIDE {

			var source = virDomainDiskSource{Device: vdisk.Device}
			diskElement = virDomainDiskElement{Type: DiskTypeBlock, Device: DeviceDisk, Driver: virDomainDiskDriver{DriverNameQEMU, DriverTypeRaw},
				Target: virDomainDiskTarget{devName, vdisk.DevBus}, Source: &source}
			// 速率控制
			if ioTune != nil {
				diskElement.IoTune = ioTune
			}
			if updata.add {
				currentDomain.Devices.Disks = append(currentDomain.Devices.Disks, diskElement)
			}
			diskData = diskElement
		} else if vdisk.DevBus == DiskBusVirtio {
			// virtio scsi
			hostdev = virDomainHostdev{Mode: DevModeSubSystem, Type: DevTypeScsiHost, Managed: "no",
				Source: virDomainHostdevSource{Protocol: DevProtocol, Wwpn: vdisk.NAA}}
			if updata.add {
				currentDomain.Devices.Hostdevs = append(currentDomain.Devices.Hostdevs, hostdev)
			}
			diskData = hostdev
		}

		// 热加载
		if isRunning && updata.add {
			if data, err := xml.MarshalIndent(diskData, "", " "); err != nil {
				log.Infof("%s eject media fail,err: %s", id, err.Error())
				return err
			} else {
				updateVDisk = string(data)
			}

			if err := virDomain.AttachDeviceFlags(updateVDisk, libvirt.DOMAIN_DEVICE_MODIFY_LIVE|libvirt.DOMAIN_DEVICE_MODIFY_CONFIG); err != nil {
				log.Infof("guest: %s, attach device %s,err: %s", id, updateVDisk, err.Error())
			}
		}

		// 冷更新
		if !isRunning && updata.update {
			switch updata.DevType {
			case "disk":
				currentDomain.Devices.Disks[updata.optIndex] = diskElement

			case "hostdev":
				currentDomain.Devices.Hostdevs[updata.optIndex] = hostdev
			}
		}
	}

	if !isRunning {
		// 冷删除
		newDisks := make([]virDomainDiskElement, 0)
		newHostDev := make([]virDomainHostdev, 0)
		for idx, disk := range currentDomain.Devices.Disks {
			del := false
			for _, delIndx := range delDisk {
				if idx == delIndx {
					del = true
					break
				}
			}
			if !del {
				newDisks = append(newDisks, disk)
			}
		}
		currentDomain.Devices.Disks = newDisks

		for idx, hostdev := range currentDomain.Devices.Hostdevs {
			del := false
			for _, delIndx := range delHostdev {
				if idx == delIndx {
					del = true
					break
				}
			}
			if !del {
				newHostDev = append(newHostDev, hostdev)
			}
		}
		currentDomain.Devices.Hostdevs = newHostDev

		if xmldata, err := xml.MarshalIndent(currentDomain, "", ""); err != nil {
			log.Infof("%s", xmldata)
			log.Infof("%s modify devs to fail,err: %s", id, err.Error())
			return err
		} else {
			if _, err := conn.DomainDefineXML(string(xmldata)); err != nil {
				log.Infof("%s", xmldata)
				log.Infof("%s modify devs to fail,err: %s", id, err.Error())
				return err
			}
		}
	}

	return nil
}

func (v *vms) GraphicsLinkPort(ctx context.Context, id string) (int, error) {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return -1, err
	}

	var currentDomain virDomainDefine
	xmlDesc, err := virDomain.GetXMLDesc(0)
	if err != nil {
		return -1, err
	}
	if err = xml.Unmarshal([]byte(xmlDesc), &currentDomain); err != nil {
		return -1, err
	}

	return currentDomain.Devices.Graphics.Port, nil
}

func (v *vms) PauseInstance(ctx context.Context, id string) error {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return err
	}

	if err := virDomain.Suspend(); err != nil {
		return err
	}

	return nil
}

func (v *vms) UnpauseInstance(ctx context.Context, id string) error {
	conn, _ := v.pool.Get()
	defer v.pool.Put(conn)

	virDomain, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return err
	}

	if err := virDomain.Resume(); err != nil {
		return err
	}

	return nil
}

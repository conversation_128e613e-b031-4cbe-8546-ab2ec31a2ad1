package libvirt

import (
	"context"
	"fmt"
	"net"
	"os"
	"strings"
	"testing"

	"github.com/marmotedu/iam/pkg/log"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
	"libvirt.org/go/libvirt"
	"libvirt.org/go/libvirtxml"
)

// 开始测试前
// 请创建以下文件

// "/Volume1/kvmfile/test.qcow2"
// "/Volume1/kvmfile/ubuntu-22.04.5-desktop-amd64.iso"
// 网络接口： "tap0"

const (
	testURI             = "qemu:///system"
	testDomainName      = "test-vm"
	testDomainUUID      = "9c9bada8-45ba-475b-9640-ba1183bee290"
	testDiskPath        = "/Volume1/kvmfile/test.qcow2"
	testGuestName       = "test_vm"
	testGuestImageIso_0 = "/Volume1/kvmfile/ubuntu-22.04.5-desktop-amd64.iso"
	testGuestImageIso_1 = "/Volume1/kvmfile/ubuntu-22.04.5-desktop-amd64.iso"
	testNetworkSource   = "tap0"

	testVdiskSDC = "/dev/sdc"
	testVdiskSDD = "/dev/sdd"
	testVdiskSDE = "/dev/sde"
	testVdiskSDF = "/dev/sdf"
	testVdiskSDG = "/dev/sdg"
	testVdiskSDH = "/dev/sdh"
	testVdiskSDM = "/dev/sdm"

	testVdiskVirtIO = "naa.50014050d30d4764"
)

func getVMsLibvirtStore() store.LibvirtFactory {
	libvirtStore, _ := GetLibvirtFactoryOr(options.NewLibvirtOptions())
	return libvirtStore
}

func getLibvirtConn() (*libvirt.Connect, error) {
	return libvirt.NewConnect(testURI)
}

func defaultTemplate() *domain.GuestHardwareTemplate {
	defaultTemplate := domain.GuestHardwareTemplate{
		OperatingSystem: domain.TemplateOperatingSystem(domain.TemplateOperatingSystemLinux).ToString(),
		Display:         domain.TemplateDisplayDriver(domain.TemplateDisplayDriverVGA).ToString(),
		Control:         domain.TemplateRemoteControl(domain.TemplateRemoteControlVNC).ToString(),
		USB:             domain.TemplateUSBModel(domain.TemplateUSBModelPIIX3).ToString(),
		Tablet:          domain.TemplateTabletModel(domain.TemplateTabletModelUSB).ToString(),
		// Disk:            domain.TemplateDiskDriver(domain.TemplateDiskDriverVirtio).ToString(),
	}
	return &defaultTemplate
}

func checkNetworkInterface(intName string) bool {
	if interfaces, err := net.Interfaces(); err == nil {
		for _, iface := range interfaces {
			if iface.Name == intName {
				return true
			}
		}
	}
	return false
}

// 请确保环境中，存在以下文件
// "/Volume1/kvmfile/test.qcow2"
// "/Volume1/kvmfile/ubuntu-22.04.5-desktop-amd64.iso"
// 网络接口： "tap0"
func createGuestInstance(config *domain.GuestConfig) error {
	if _, err := os.Stat(testDiskPath); os.IsNotExist(err) {
		return err
	}

	if _, err := os.Stat(testGuestImageIso_0); os.IsNotExist(err) {
		return err
	}

	if _, err := os.Stat(testGuestImageIso_1); os.IsNotExist(err) {
		return err
	}

	if !checkNetworkInterface(testNetworkSource) {
		return fmt.Errorf("%s not define", testNetworkSource)
	}

	// 初始化基础配置
	config.Name = testGuestName
	config.Memory = 2 * 1024 * 1024 * 1024
	config.Cores = 2
	config.Template = defaultTemplate()
	config.ID = testDomainUUID
	config.OSBoot = "cdrom"

	osMachine := os.Getenv("OS_MACHINE")
	if osMachine == "" {
		osMachine = "pc"
	}
	config.OSMachine = osMachine

	// 虚拟盘
	config.VDisks = []domain.GuestStoreVDisk{
		{
			DevBus: domain.TemplateDiskDriver(domain.TemplateDiskDriverSATA).ToString(),
			Device: testVdiskSDD,
		},
		{
			DevBus: domain.TemplateDiskDriver(domain.TemplateDiskDriverVirtio).ToString(),
			NAA:    testVdiskVirtIO,
		},
	}
	// if config.OSMachine != OSMachineQ35 {
	// 	config.VDisks = append(config.VDisks, domain.GuestStoreVDisk{
	// 		DevBus: domain.TemplateDiskDriver(domain.TemplateDiskDriverIDE).ToString(),
	// 		Device: testVdiskSDC,
	// 	})
	// }
	// 启动映像
	config.BootImages = []string{testGuestImageIso_0, testGuestImageIso_1}

	// 网卡
	config.VNics = []domain.GuestNetworkInterface{
		domain.GuestNetworkInterface{
			NetworkSource:   testNetworkSource,
			HardwareAddress: "",
			NetBus:          domain.TemplateNetworkModel(domain.TemplateNetworkModelVirtIO).ToString(),
		},
	}

	// 清除环境
	deleteInstance(*config)

	err := getVMsLibvirtStore().VMs().CreateGuestInstance(context.Background(), config)
	return err
}

func deleteInstance(config domain.GuestConfig) {
	manager := getVMsLibvirtStore()
	if err := manager.VMs().StopInstance(context.Background(), config.ID, false, true); err != nil {
		log.Infof("%s", err.Error())
	}

	if err := manager.VMs().DeleteInstance(context.Background(), config.ID); err != nil {
		log.Infof("%s", err.Error())
	}
}

func lookupDomainByUUIDString(id string) (*libvirtxml.Domain, error) {
	conn, _ := getLibvirtConn()
	ins, err := conn.LookupDomainByUUIDString(id)
	if err != nil {
		return nil, err
	}

	xmlData, err := ins.GetXMLDesc(0)
	if err != nil {
		return nil, err
	}

	readConfig := libvirtxml.Domain{}
	readConfig.Unmarshal(xmlData)

	return &readConfig, err
}

func enableInstance(config domain.GuestConfig) (bool, error) {
	getVMsLibvirtStore().VMs().StartInstance(context.Background(), config.ID)
	return getVMsLibvirtStore().VMs().IsInstanceRunning(context.Background(), config.ID)
}

// 移除虚拟机
// PASS
func TestVms_ModifyInstanceDelete(t *testing.T) {
	var current domain.GuestConfig

	// 创建测试虚拟机
	if err := createGuestInstance(&current); err != nil {
		t.Fatal(err)
	}

	// 移除虚拟机
	deleteInstance(current)

	domain, _ := lookupDomainByUUIDString(current.ID)
	if domain != nil {
		t.Fatal()
	}
}

// 启动虚拟机
func TestVms_StartInstance(t *testing.T) {
	var current domain.GuestConfig
	// 创建测试虚拟机
	if err := createGuestInstance(&current); err != nil {
		t.Fatal(err)
	}

	// 启动虚拟机
	isRunning, err := enableInstance(current)
	if !isRunning {
		t.Fatal(err)
	}
}

// 修改虚拟机的名称 不可热修改
// PASS
func TestVms_ModifyInstanceName(t *testing.T) {
	var current domain.GuestConfig
	var testName = "test"
	// 创建测试虚拟机
	if err := createGuestInstance(&current); err != nil {
		t.Fatal(err)
	}

	// 修改虚拟机名称
	getVMsLibvirtStore().VMs().ModifyGuestName(context.Background(), current.ID, testName)

	if readConfig, err := lookupDomainByUUIDString(current.ID); err == nil {
		// 检查CPU、Mem是否修改成功
		assert.Equal(t, readConfig.Name, testName)
	} else {
		t.Fatal(err)
	}
}

// 测试修改CPU 内存大小 不可热插拔
// Pass
func TestVms_ModifyCpuMem(t *testing.T) {
	var current domain.GuestConfig

	// 创建测试虚拟机
	if err := createGuestInstance(&current); err != nil {
		t.Fatal(err)
	}

	// 修改CPU、内存大小
	// 修小
	cores := uint(1)
	memory := uint(2 * 1024 * 1024)

	getVMsLibvirtStore().VMs().ModifyCPUTopology(context.Background(), current.ID, cores, true)
	getVMsLibvirtStore().VMs().ModifyMemory(context.Background(), current.ID, memory, true)

	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		// 检查CPU、Mem是否修改成功
		assert.Equal(t, readConfig.VCPU.Value, cores)
		assert.Equal(t, readConfig.Memory.Value, memory>>10)
		assert.Equal(t, readConfig.CurrentMemory.Value, memory>>10)
	} else {
		t.Fatal(err)
	}

	// 增大
	cores = 3
	memory = 1024 * 1024 * 1024
	getVMsLibvirtStore().VMs().ModifyCPUTopology(context.Background(), current.ID, cores, true)
	getVMsLibvirtStore().VMs().ModifyMemory(context.Background(), current.ID, memory, true)

	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		// 检查CPU、Mem是否修改成功
		assert.Equal(t, readConfig.VCPU.Value, cores)
		assert.Equal(t, readConfig.Memory.Value, memory>>10)
		assert.Equal(t, readConfig.CurrentMemory.Value, memory>>10)
	} else {
		t.Fatal(err)
	}
}

// 测试CPU直通 --- 不可热插拔
// 通过
func TestVms_CPUPassThru(t *testing.T) {
	// 创建虚拟机
	var config domain.GuestConfig

	// 启用CPU兼容模式
	config.CPUPassThru = false

	// 创建测试虚拟机
	if err := createGuestInstance(&config); err != nil {
		t.Fatal(err)
	}

	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		// CPU兼容模式是否配置正确
		assert.Equal(t, readConfig.CPU.Mode, "custom")
		assert.Equal(t, readConfig.CPU.Check, "full")
		assert.Equal(t, readConfig.CPU.Match, "exact")
		assert.Equal(t, readConfig.CPU.Model.Fallback, "forbid")
		assert.Equal(t, readConfig.CPU.Model.Value, "Westmere")

		names := []string{"vme", "arat", "pclmuldq", "x2apic", "hypervisor"}
		for idx, Feature := range readConfig.CPU.Features {
			assert.Equal(t, Feature.Policy, "require")
			assert.Equal(t, Feature.Name, names[idx])
		}
	} else {
		t.Fatal(err)
	}

	// 启动虚拟机
	isRunning, err := enableInstance(config)
	if !isRunning {
		t.Fatal(err)
	}

	// 切换到CPU直通模式
	config.CPUPassThru = true
	if err := createGuestInstance(&config); err != nil {
		t.Fatal(err)
	}

	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		// CPU直通模式是否配置正确
		assert.Equal(t, readConfig.CPU.Mode, "host-passthrough")
		assert.Equal(t, readConfig.CPU.Check, "none")
		assert.Equal(t, readConfig.CPU.Migratable, "on")
	} else {
		t.Fatal(err)
	}

	// 启动虚拟机
	isRunning, err = enableInstance(config)
	if !isRunning {
		t.Fatal(err)
	}
}

// 视频卡   测试切换视频卡，不可热插拔
// PASS
func TestVms_Video(t *testing.T) {
	// 创建虚拟机
	var config domain.GuestConfig

	// 创建测试虚拟机
	if err := createGuestInstance(&config); err != nil {
		t.Fatal(err)
	}
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		// 视频卡 vga
		for _, video := range readConfig.Devices.Videos {
			assert.Equal(t, video.Model.Type, "vga")
		}
	} else {
		t.Fatal(err)
	}

	getVMsLibvirtStore().VMs().ModifyVideoCard(config.ID, domain.TemplateDisplayDriver(domain.TemplateDisplayDriverVmvga).ToString())
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		// 视频卡 vmvga
		for _, video := range readConfig.Devices.Videos {
			assert.Equal(t, video.Model.Type, "vmvga")
		}
	} else {
		t.Fatal(err)
	}

	getVMsLibvirtStore().VMs().ModifyVideoCard(config.ID, domain.TemplateDisplayDriver(domain.TemplateDisplayDriverCirrus).ToString())
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		// 视频卡 cirrus
		for _, video := range readConfig.Devices.Videos {
			assert.Equal(t, video.Model.Type, "cirrus")
		}
	} else {
		t.Fatal(err)
	}
}

// 机器类型 不可热插拔
// PASS
func TestVms_ModifyMachineType(t *testing.T) {
	// 创建虚拟机
	var config domain.GuestConfig

	// 创建测试虚拟机
	if err := createGuestInstance(&config); err != nil {
		t.Fatal(err)
	}
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		// 机器类型 PC
		assert.True(t, strings.Contains(readConfig.OS.Type.Machine, "pc"))
	} else {
		t.Fatal(err)
	}

	config.OSMachine = "q35"
	getVMsLibvirtStore().VMs().ModifyMachineType(context.Background(), config)
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		// 机器类型 PC
		assert.True(t, strings.Contains(readConfig.OS.Type.Machine, "q35"))
	} else {
		t.Fatal(err)
	}

	if isRunning, _ := enableInstance(config); !isRunning {
		t.Logf("start fail")
	}
}

// 虚拟机优先级 Qcos 不可热插拔
// PASS
func TestVms_ModifyMachineCPUPriority(t *testing.T) {
	// 创建虚拟机
	var config domain.GuestConfig

	// 创建测试虚拟机
	if err := createGuestInstance(&config); err != nil {
		t.Fatal(err)
	}

	getVMsLibvirtStore().VMs().SetCPUThreshold(context.Background(), config.ID, domain.PriorityHigh)
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		assert.Equal(t, readConfig.CPUTune.Shares.Value, uint(2000))
		assert.Equal(t, readConfig.CPUTune.Quota.Value, int64(1000000))
	} else {
		t.Fatal(err)
	}

	getVMsLibvirtStore().VMs().SetCPUThreshold(context.Background(), config.ID, domain.PriorityHighMedium)
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		assert.Equal(t, readConfig.CPUTune.Shares.Value, uint(1500))
		assert.Equal(t, readConfig.CPUTune.Quota.Value, int64(1000000/2))
	} else {
		t.Fatal(err)
	}

	getVMsLibvirtStore().VMs().SetCPUThreshold(context.Background(), config.ID, domain.PriorityMedium)
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		assert.Equal(t, readConfig.CPUTune.Shares.Value, uint(1000))
		assert.Equal(t, readConfig.CPUTune.Quota.Value, int64(1000000/4))
	} else {
		t.Fatal(err)
	}

	getVMsLibvirtStore().VMs().SetCPUThreshold(context.Background(), config.ID, domain.PriorityLowMedium)
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		assert.Equal(t, readConfig.CPUTune.Shares.Value, uint(750))
		assert.Equal(t, readConfig.CPUTune.Quota.Value, int64(1000000/6))
	} else {
		t.Fatal(err)
	}

	getVMsLibvirtStore().VMs().SetCPUThreshold(context.Background(), config.ID, domain.PriorityLow)
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		assert.Equal(t, readConfig.CPUTune.Shares.Value, uint(500))
		assert.Equal(t, readConfig.CPUTune.Quota.Value, int64(1000000/8))
	} else {
		t.Fatal(err)
	}
}

// 修改网卡MAC地址、修改网卡型号 不可热修改
// 新增网卡、删除网卡 不可热插拔
// PASS
func TestVms_ModifyMachineMAC(t *testing.T) {
	// 创建虚拟机
	var config domain.GuestConfig

	// 创建测试虚拟机
	if err := createGuestInstance(&config); err != nil {
		t.Fatal(err)
	}

	// 修改MAC地址、网卡型号
	// 新增网卡
	ifaces := []domain.GuestNetworkInterface{
		{
			NetworkSource:   "tap1",
			HardwareAddress: "02:11:32:23:ca:00",
			NetBus:          domain.TemplateNetworkModel(domain.TemplateNetworkModelE1000).ToString(),
		},
		{
			NetworkSource:   "tap2",
			HardwareAddress: "02:11:32:23:ca:00",
			NetBus:          domain.TemplateNetworkModel(domain.TemplateNetworkModelVirtIO).ToString(),
		},
		{
			NetworkSource:   "tap3",
			HardwareAddress: "02:11:32:23:ca:00",
			NetBus:          domain.TemplateNetworkModel(domain.TemplateNetworkModelRTL18139).ToString(),
		},
		{
			NetworkSource:   "tap4",
			HardwareAddress: "02:11:32:23:ca:00",
			NetBus:          domain.TemplateNetworkModel(domain.TemplateNetworkModelRTL18139).ToString(),
		},
		{
			NetworkSource:   "tap5",
			HardwareAddress: "02:11:32:23:ca:00",
			NetBus:          domain.TemplateNetworkModel(domain.TemplateNetworkModelVirtIO).ToString(),
		},
	}
	getVMsLibvirtStore().VMs().ModifyNetInterface(context.Background(), config.ID, ifaces)

	// 验证是否修改成功
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		assert.Equal(t, len(readConfig.Devices.Interfaces), len(ifaces))

		for _, iface := range readConfig.Devices.Interfaces {
			for _, modify := range ifaces {
				if iface.Target.Dev == modify.NetworkSource {
					assert.Equal(t, iface.MAC.Address, modify.HardwareAddress)
					assert.Equal(t, iface.Model.Type, modify.NetBus)
				}
			}
		}
	} else {
		t.Fatal(err)
	}

	// 删除网卡
	sub := 0
	if len(ifaces)-4 >= 0 {
		sub = len(ifaces) - 4
	}

	ifaces = ifaces[:sub]
	getVMsLibvirtStore().VMs().ModifyNetInterface(context.Background(), config.ID, ifaces)

	// 验证是否修改成功
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		assert.Equal(t, len(readConfig.Devices.Interfaces), len(ifaces))

		for _, iface := range readConfig.Devices.Interfaces {
			for _, modify := range ifaces {
				if iface.Target.Dev == modify.NetworkSource {
					assert.Equal(t, iface.MAC.Address, modify.HardwareAddress)
					assert.Equal(t, iface.Model.Type, modify.NetBus)
				}
			}
		}
	} else {
		t.Fatal(err)
	}

	// if running, err := enableInstance(config); !running {
	// 	t.Fatal(err)
	// }
}

// 加载、卸载ISO文件，cdrom 可热修改
// PASS
func TestVms_LoadOrUnloadISO(t *testing.T) {
	// 创建虚拟机
	var config domain.GuestConfig

	// 创建测试虚拟机
	if err := createGuestInstance(&config); err != nil {
		t.Fatal(err)
	}

	// 运行虚拟机
	if running, err := enableInstance(config); !running {
		t.Fatal(err)
	}

	// 热卸载ISO
	getVMsLibvirtStore().VMs().EjectMediaISO(context.Background(), config.ID, []string{testGuestImageIso_0, testGuestImageIso_1})
	// 验证是否热卸载成功
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		for _, disk := range readConfig.Devices.Disks {
			if disk.Device == "cdrom" {
				assert.True(t, disk.Source.File.File == "")
			}
		}
	} else {
		t.Fatal(err)
	}

	// 热加载ISO
	getVMsLibvirtStore().VMs().InsertMediaISO(context.Background(), config.ID, config.OSMachine, []string{testGuestImageIso_0, testGuestImageIso_1})
	// 验证是否热加载成功
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		for _, disk := range readConfig.Devices.Disks {
			if disk.Device == "cdrom" {
				found := false
				if disk.Source.File.File == testGuestImageIso_0 {
					log.Infof("cdrom %s", testGuestImageIso_0)
					found = true
				}
				if disk.Source.File.File == testGuestImageIso_1 {
					log.Infof("cdrom %s", testGuestImageIso_1)
					found = true
				}

				assert.True(t, found)
			}
		}
	} else {
		t.Fatal(err)
	}

	// 冷卸载ISO
	getVMsLibvirtStore().VMs().StopInstance(context.Background(), config.ID, false, true)
	getVMsLibvirtStore().VMs().EjectMediaISO(context.Background(), config.ID, []string{testGuestImageIso_0, testGuestImageIso_1})
	// 验证是否冷卸载成功
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		for _, disk := range readConfig.Devices.Disks {
			if disk.Device == "cdrom" {
				assert.True(t, disk.Source.File.File == "")
			}
		}
	} else {
		t.Fatal(err)
	}

	// 冷加载ISO
	getVMsLibvirtStore().VMs().InsertMediaISO(context.Background(), config.ID, config.OSMachine, []string{testGuestImageIso_0, testGuestImageIso_1})
	// 验证是否冷加载成功
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		for _, disk := range readConfig.Devices.Disks {
			if disk.Device == "cdrom" {
				found := false
				if disk.Source.File.File == testGuestImageIso_0 {
					log.Infof("cdrom %s", testGuestImageIso_0)
					found = true
				}
				if disk.Source.File.File == testGuestImageIso_1 {
					log.Infof("cdrom %s", testGuestImageIso_1)
					found = true
				}

				assert.True(t, found)
			}
		}
	} else {
		t.Fatal(err)
	}

	// 运行虚拟机
	if running, err := enableInstance(config); !running {
		t.Fatal(err)
	}
}

// 修改自动启动， 可热修改
// PASS
func TestVms_AutoRun(t *testing.T) {
	// 创建虚拟机
	var config domain.GuestConfig

	// 创建测试虚拟机
	if err := createGuestInstance(&config); err != nil {
		t.Fatal(err)
	}
	getVMsLibvirtStore().VMs().ModifyAutoStart(config.ID, false)

	// 测试开启自启动
	if err := getVMsLibvirtStore().VMs().ModifyAutoStart(config.ID, true); err != nil {
		t.Fatal(err)
	}

	// 测试关闭自启动
	if err := getVMsLibvirtStore().VMs().ModifyAutoStart(config.ID, false); err != nil {
		t.Fatal(err)
	}
}

// 修改虚拟机启动位置， 不可热修改
// PASS
func TestVms_ModifyOSBoot(t *testing.T) {
	// 创建虚拟机
	var config domain.GuestConfig

	// 创建测试虚拟机
	if err := createGuestInstance(&config); err != nil {
		t.Fatal(err)
	}

	getVMsLibvirtStore().VMs().ModifyOSBoot(context.Background(), config.ID, "hd")
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		assert.Equal(t, readConfig.OS.BootDevices[0].Dev, "hd")
		assert.Equal(t, readConfig.OS.BootDevices[1].Dev, "cdrom")
	} else {
		t.Fatal(err)
	}

	getVMsLibvirtStore().VMs().ModifyOSBoot(context.Background(), config.ID, "cdrom")
	// 验证开启串口
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		assert.Equal(t, readConfig.OS.BootDevices[0].Dev, "cdrom")
		assert.Equal(t, readConfig.OS.BootDevices[1].Dev, "hd")
	} else {
		t.Fatal(err)
	}

	// 运行虚拟机
	if running, err := enableInstance(config); !running {
		t.Fatal(err)
	}
}

// 新增、删除虚拟盘  可热新增virtio， 不可热移除
func TestVms_ModifyVDisk(t *testing.T) {
	// // 创建虚拟机
	var config domain.GuestConfig

	// 创建测试虚拟机
	if err := createGuestInstance(&config); err != nil {
		t.Fatal(err)
	}

	// 冷删除 IDE, SATA
	vdisks := []domain.GuestStoreVDisk{
		{
			DevBus: domain.TemplateDiskDriver(domain.TemplateDiskDriverVirtio).ToString(),
			NAA:    testVdiskVirtIO,
		},
	}
	getVMsLibvirtStore().VMs().ModifyVDisk(context.Background(), config.ID, vdisks)
	// 验证是否删除
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		for _, olddisk := range readConfig.Devices.Disks {
			if olddisk.Driver.Type != "block" {
				continue
			}

			for _, newdisk := range vdisks {
				assert.NotEqual(t, olddisk.Device, newdisk.Device)
				assert.NotEqual(t, olddisk.Target.Bus, newdisk.DevBus)
			}
		}
	} else {
		t.Fatal(err)
	}

	// 创建测试虚拟机
	config = domain.GuestConfig{}
	if err := createGuestInstance(&config); err != nil {
		t.Fatal(err)
	}
	// 冷更新 IDE, SATA
	vdisks_2 := []domain.GuestStoreVDisk{
		{
			DevBus: domain.TemplateDiskDriver(domain.TemplateDiskDriverIDE).ToString(),
			Device: testVdiskSDD,
			NAA:    testVdiskVirtIO,
		},
		{
			DevBus: domain.TemplateDiskDriver(domain.TemplateDiskDriverVirtio).ToString(),
			Device: testVdiskSDD,
			NAA:    testVdiskVirtIO,
		},
	}

	getVMsLibvirtStore().VMs().ModifyVDisk(context.Background(), config.ID, vdisks_2)
	// 验证更新
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		cnt := 0
		for _, olddisk := range readConfig.Devices.Disks {
			if olddisk.Source == nil || olddisk.Source.Block == nil {
				continue
			}

			found := false
			for _, newdisk := range vdisks_2 {
				if newdisk.Device == olddisk.Source.Block.Dev &&
					(newdisk.DevBus == DiskBusIDE ||
						newdisk.DevBus == DiskBusSATA) {
					found = true
					cnt += 1
				}
			}
			assert.True(t, found)
		}

		for _, oldhostdev := range readConfig.Devices.Hostdevs {
			if oldhostdev.SubsysSCSIHost == nil {
				continue
			}

			found := false
			for _, newdisk := range vdisks_2 {
				if newdisk.NAA == oldhostdev.SubsysSCSIHost.Source.WWPN &&
					newdisk.DevBus == DiskBusVirtio {
					found = true
					cnt += 1
				}
			}
			assert.True(t, found)
		}
		assert.Equal(t, cnt, len(vdisks_2))
	} else {
		t.Fatal(err)
	}

	// 冷添加 SATA, IDE
	vdisks_2 = append(vdisks_2, []domain.GuestStoreVDisk{
		{
			DevBus: domain.TemplateDiskDriver(domain.TemplateDiskDriverSATA).ToString(),
			Device: testVdiskSDE,
			NAA:    "naa.5001405dab2b2279",
		},
		{
			DevBus: domain.TemplateDiskDriver(domain.TemplateDiskDriverIDE).ToString(),
			Device: testVdiskSDF,
			NAA:    "naa.5001405dab2b2279",
		},
		{
			DevBus: domain.TemplateDiskDriver(domain.TemplateDiskDriverSATA).ToString(),
			Device: testVdiskSDG,
			NAA:    "naa.5001405dab2b2279",
		},
		{
			DevBus: domain.TemplateDiskDriver(domain.TemplateDiskDriverVirtio).ToString(),
			Device: testVdiskSDG,
			NAA:    "naa.5001405dab2b2279", // "naa.5001405dab2b2279",
		},
	}...)
	getVMsLibvirtStore().VMs().ModifyVDisk(context.Background(), config.ID, vdisks_2)
	// 验证添加
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		cnt := 0
		for _, olddisk := range readConfig.Devices.Disks {
			if olddisk.Source == nil || olddisk.Source.Block == nil {
				continue
			}

			found := false
			for _, newdisk := range vdisks_2 {
				if newdisk.Device == olddisk.Source.Block.Dev &&
					(newdisk.DevBus == DiskBusIDE ||
						newdisk.DevBus == DiskBusSATA) {
					found = true
					cnt += 1
				}
			}
			assert.True(t, found)
		}

		for _, oldhostdev := range readConfig.Devices.Hostdevs {
			if oldhostdev.SubsysSCSIHost == nil {
				continue
			}

			found := false
			for _, newdisk := range vdisks_2 {
				if newdisk.NAA == oldhostdev.SubsysSCSIHost.Source.WWPN &&
					newdisk.DevBus == DiskBusVirtio {
					found = true
					cnt += 1
				}
			}
			assert.True(t, found)
		}
		assert.Equal(t, cnt, len(vdisks_2))
	} else {
		t.Fatal(err)
	}

	// 冷删除 SATA, IDE，VirtIo
	vdisks_2 = append(vdisks_2[:1], vdisks_2[1:3]...)
	getVMsLibvirtStore().VMs().ModifyVDisk(context.Background(), config.ID, vdisks_2)
	// 验证添加
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		cnt := 0
		for _, olddisk := range readConfig.Devices.Disks {
			if olddisk.Source == nil || olddisk.Source.Block == nil {
				continue
			}

			found := false
			for _, newdisk := range vdisks_2 {
				if newdisk.Device == olddisk.Source.Block.Dev &&
					(newdisk.DevBus == DiskBusIDE ||
						newdisk.DevBus == DiskBusSATA) {
					found = true
					cnt += 1
				}
			}
			assert.True(t, found)
		}

		for _, oldhostdev := range readConfig.Devices.Hostdevs {
			if oldhostdev.SubsysSCSIHost == nil {
				continue
			}

			found := false
			for _, newdisk := range vdisks_2 {
				if newdisk.NAA == oldhostdev.SubsysSCSIHost.Source.WWPN &&
					newdisk.DevBus == DiskBusVirtio {
					found = true
					cnt += 1
				}
			}
			assert.True(t, found)
		}
		assert.Equal(t, cnt, len(vdisks_2))
	} else {
		t.Fatal(err)
	}

	// 热添加 virtio
	isRunning, err := enableInstance(config)
	if !isRunning {
		t.Fatal(err)
	}

	vdisks_2 = append(vdisks_2, []domain.GuestStoreVDisk{
		{
			DevBus: domain.TemplateDiskDriver(domain.TemplateDiskDriverVirtio).ToString(),
			Device: testVdiskSDG,
			NAA:    "naa.500140544072d766",
		},
	}...)
	getVMsLibvirtStore().VMs().ModifyVDisk(context.Background(), config.ID, vdisks_2)
	// 验证添加
	if readConfig, err := lookupDomainByUUIDString(testDomainUUID); err == nil {
		cnt := 0
		for _, olddisk := range readConfig.Devices.Disks {
			if olddisk.Source == nil || olddisk.Source.Block == nil {
				continue
			}

			found := false
			for _, newdisk := range vdisks_2 {
				if newdisk.Device == olddisk.Source.Block.Dev &&
					(newdisk.DevBus == DiskBusIDE ||
						newdisk.DevBus == DiskBusSATA) {
					found = true
					cnt += 1
				}
			}
			assert.True(t, found)
		}

		for _, oldhostdev := range readConfig.Devices.Hostdevs {
			if oldhostdev.SubsysSCSIHost == nil {
				continue
			}

			found := false
			for _, newdisk := range vdisks_2 {
				if newdisk.NAA == oldhostdev.SubsysSCSIHost.Source.WWPN &&
					newdisk.DevBus == DiskBusVirtio {
					found = true
					cnt += 1
				}
			}
			assert.True(t, found)
		}
		assert.Equal(t, cnt, len(vdisks_2))
	} else {
		t.Fatal(err)
	}
}

// 控制虚拟盘的IO读写 可热更改
func TestVms_ModifyVDiskIOTune(t *testing.T) {
	// 创建虚拟机
	var config domain.GuestConfig

	// 创建测试虚拟机
	if err := createGuestInstance(&config); err != nil {
		t.Fatal(err)
	}

	// IOTune
}

// 修改USB控制器版本 不可热修改
// 卸载、加载USB设备， 可热操作
// 添加USB设备， 可热操作
// 启动关闭串行端口， 不可热修改
// 修改硬盘布局 可热修改 考虑可能需要vnc配合 后续再弄
// 修改固件 不可热修改 最后阶段再弄

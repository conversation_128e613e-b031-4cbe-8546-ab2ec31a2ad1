package libvirt

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestNewConnPool(t *testing.T) {
	tests := []struct {
		name    string
		config  *Config
		wantErr bool
	}{
		{
			name: "valid config",
			config: &Config{
				URI:            "qemu:///system",
				MaxIdle:        5,
				MaxActive:      10,
				IdleTimeout:    time.Minute,
				ConnectTimeout: time.Second * 10,
			},
			wantErr: false,
		},
		{
			name: "invalid idle connections",
			config: &Config{
				URI:            "qemu:///system",
				MaxIdle:        0,
				MaxActive:      10,
				IdleTimeout:    time.Minute,
				ConnectTimeout: time.Second * 10,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pool, err := NewConnPool(tt.config)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, pool)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, pool)
				assert.Equal(t, tt.config.MaxIdle, len(pool.idle))
				err = pool.Close()
				assert.NoError(t, err)
			}
		})
	}
}

func TestConnPool_GetPut(t *testing.T) {
	pool, err := NewConnPool(&Config{
		URI:            "qemu:///system",
		MaxIdle:        2,
		MaxActive:      3,
		IdleTimeout:    time.Minute,
		ConnectTimeout: time.Second * 10,
	})
	assert.NoError(t, err)
	defer pool.Close()

	// Test Get
	conn, err := pool.Get()
	assert.NoError(t, err)
	assert.NotNil(t, conn)
	assert.Equal(t, 1, pool.active)
	assert.Equal(t, 1, len(pool.idle))

	// Test Put
	err = pool.Put(conn)
	assert.NoError(t, err)
	assert.Equal(t, 0, pool.active)
	assert.Equal(t, 2, len(pool.idle))
}

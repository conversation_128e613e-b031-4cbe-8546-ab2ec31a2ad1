package libvirt

import (
	"fmt"
	"sync"
	"time"

	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

var BackendLibvirtStore = initLibvirt()

func initLibvirt() store.LibvirtFactory {
	libvirtStore, err := GetLibvirtFactoryOr(options.NewLibvirtOptions())
	if err != nil {
		panic(err)
	}
	return libvirtStore
}

type datastore struct {
	pool *ConnPool
}

var (
	libvirtFactory store.LibvirtFactory
	once           sync.Once
)

// GetLibvirtFactoryOr 创建或获取libvirt工厂实例
func GetLibvirtFactoryOr(opts *options.LibvirtOptions) (store.LibvirtFactory, error) {
	if opts == nil && libvirtFactory == nil {
		return nil, fmt.Errorf("failed to get libvirt factory")
	}

	var err error
	once.Do(func() {
		config := &Config{
			URI:            opts.URI,
			Username:       opts.Username,
			Password:       opts.Password,
			MaxIdle:        opts.MaxIdle,
			MaxActive:      opts.MaxActive,
			IdleTimeout:    time.Duration(opts.IdleTimeout) * time.Second,
			ConnectTimeout: time.Duration(opts.ConnectTimeout) * time.Second,
		}

		var pool *ConnPool
		pool, err = NewConnPool(config)
		if err != nil {
			return
		}

		libvirtFactory = &datastore{pool: pool}
	})

	if libvirtFactory == nil || err != nil {
		return nil, fmt.Errorf("failed to create libvirt factory, error: %v", err)
	}

	return libvirtFactory, nil
}

// VMs 返回虚拟机操作接口
func (ds *datastore) VMs() store.VMLibvirtStore {
	return newVMs(ds.pool)
}

// Close 关闭工厂
func (ds *datastore) Close() error {
	if ds.pool != nil {
		return ds.pool.Close()
	}
	return nil
}

package libvirt

import (
	"fmt"
	"github.com/marmotedu/iam/pkg/log"
	"sync"
	"time"

	"libvirt.org/go/libvirt"
)

type ConnPool struct {
	mu sync.RWMutex

	// 连接池配置
	config *Config

	// 空闲连接池
	idle []*libvirt.Connect

	// 活跃连接数
	active int

	// 关闭标志
	closed bool
}

type Config struct {
	// Libvirt URI
	URI string

	// 认证信息
	Username string
	Password string

	// 连接池大小
	MaxIdle   int // 最大空闲连接数
	MaxActive int // 最大活跃连接数

	// 连接超时设置
	IdleTimeout    time.Duration // 空闲连接超时时间
	ConnectTimeout time.Duration // 连接超时时间
}

// NewConnPool 创建新的连接池
func NewConnPool(config *Config) (*ConnPool, error) {
	if config.MaxIdle <= 0 || config.MaxActive <= 0 {
		return nil, fmt.Errorf("invalid pool config")
	}

	pool := &ConnPool{
		config: config,
		idle:   make([]*libvirt.Connect, 0, config.MaxIdle),
	}

	// 预创建连接
	for i := 0; i < config.MaxIdle; i++ {
		conn, err := pool.createConn()
		if err != nil {
			return nil, err
		}
		pool.idle = append(pool.idle, conn)
	}

	// 启动空闲连接清理
	go pool.cleanupIdleConns()

	return pool, nil
}

// Get 获取连接
func (p *ConnPool) Get() (*libvirt.Connect, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		return nil, fmt.Errorf("connection pool is closed")
	}

	// 优先使用空闲连接
	if len(p.idle) > 0 {
		conn := p.idle[len(p.idle)-1]
		p.idle = p.idle[:len(p.idle)-1]
		p.active++
		return conn, nil
	}

	// 检查是否达到最大连接数
	if p.active >= p.config.MaxActive {
		return nil, fmt.Errorf("too many connections")
	}

	// 创建新连接
	conn, err := p.createConn()
	if err != nil {
		return nil, err
	}
	p.active++
	return conn, nil
}

// Put 释放连接
func (p *ConnPool) Put(conn *libvirt.Connect) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		_, err := conn.Close()
		return err
	}

	// 检查连接是否可用
	if alive, err := conn.IsAlive(); err != nil || !alive {
		p.active--
		if i, err2 := conn.Close(); err2 != nil {
			log.Warnf("error closing connection: errno:%d, err:%v", i, err2)
			return err2
		}
		return err
	}

	// 放回空闲池
	if len(p.idle) < p.config.MaxIdle {
		p.idle = append(p.idle, conn)
	} else {
		if i, err := conn.Close(); err != nil {
			log.Warnf("error closing connection: errno:%d, err:%v", i, err)
		}
	}
	p.active--
	return nil
}

// Close 关闭连接池
func (p *ConnPool) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		return nil
	}
	p.closed = true

	// 关闭所有连接
	for _, conn := range p.idle {
		if _, err := conn.Close(); err != nil {
			log.Warnf("error closing connection pool: %v", err)
		}
	}
	p.idle = nil
	p.active = 0

	return nil
}

// 创建新的libvirt连接
func (p *ConnPool) createConn() (*libvirt.Connect, error) {
	auth := &libvirt.ConnectAuth{
		CredType: []libvirt.ConnectCredentialType{
			libvirt.CRED_AUTHNAME,
			libvirt.CRED_PASSPHRASE,
		},
		Callback: func(cred []*libvirt.ConnectCredential) {
			for _, c := range cred {
				switch c.Type {
				case libvirt.CRED_AUTHNAME:
					c.Result = p.config.Username
					c.ResultLen = len(c.Result)
				case libvirt.CRED_PASSPHRASE:
					c.Result = p.config.Password
					c.ResultLen = len(c.Result)
				}
			}
		},
	}

	conn, err := libvirt.NewConnectWithAuth(p.config.URI, auth, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to libvirt: %v", err)
	}

	return conn, nil
}

// 清理空闲连接
func (p *ConnPool) cleanupIdleConns() {
	ticker := time.NewTicker(p.config.IdleTimeout)
	defer ticker.Stop()

	for range ticker.C {
		p.mu.Lock()
		if p.closed {
			p.mu.Unlock()
			return
		}

		// 检查并关闭超时的空闲连接
		var remaining []*libvirt.Connect
		for _, conn := range p.idle {
			if alive, _ := conn.IsAlive(); !alive {
				i, err := conn.Close()
				if err != nil {
					log.Warnf("error cleanup Idle Connections: errno:%d, err:%v", i, err)
				}
				continue
			}
			remaining = append(remaining, conn)
		}
		p.idle = remaining
		p.mu.Unlock()
	}
}

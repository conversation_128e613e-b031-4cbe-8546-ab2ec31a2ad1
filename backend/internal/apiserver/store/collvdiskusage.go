package store

import (
	"context"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
)

type CollVDiskUsageStore interface {
	Get(ctx context.Context, guestUUID string, opts metav1.GetOptions) (*domain.VDiskSpec, error)                  //获取虚拟机下所有虚拟盘信息
	GetVDisk(ctx context.Context, guestUUID, vdiskUUID string, opts metav1.GetOptions) (*domain.VDiskUsage, error) //获取虚拟机下指定虚拟盘信息
	List(ctx context.Context, opts metav1.ListOptions) (*domain.VDiskList, error)                                  //获取当前主机下所有虚拟盘信息
	Delete(ctx context.Context, guestUUID string, opts metav1.DeleteOptions) error                                 //默认删除虚拟机下所有虚拟盘
	DeleteVDisk(ctx context.Context, guestUUID, vdiskUUID string, opts metav1.DeleteOptions) error                 //指定删除目标虚拟机下的虚拟盘
}

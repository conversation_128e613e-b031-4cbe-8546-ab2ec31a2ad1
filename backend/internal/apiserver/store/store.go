// Package store
/**
* @Project : terravirtualmachine
* @File    : store.go
* @IDE     : GoLand
* <AUTHOR> <EMAIL>
* @Date    : 2025/1/20 17:36
**/

package store

import "context"

var client Factory

// Factory defines the vmm-apiserver store interface.
type Factory interface {
	Networks() NetworkStore
	Images() ImageStore
	RepositoryPool() RepositoryPoolStore
	Host() HostStore
	VMs() VMStore
	VDisks() VDisk
	VNics() VNicStore
	// VMs() VMStore
	Logs() Log
	Close() error
}

type RedisFactory interface {
	CollMonitoring() CollMonitoringStore
	CollNetwork() CollNetworkStore
	CollVMUsage() CollVMUsageStore
	CollVolume() CollVolumeStore
	CollVDiskUsage() CollVDiskUsageStore
}

// LibvirtFactory 定义libvirt工厂接口
type LibvirtFactory interface {
	VMs() VMLibvirtStore
	Close() error
}

// Client return the store client instance.
func Client() Factory {
	return client
}

// SetClient set the vmm-apiserver store client.
func SetClient(c Factory) {
	client = c
}

// CreateEventFunc defines etcd create event function handler.
type CreateEventFunc func(ctx context.Context, key, val []byte)

// ModifyEventFunc defines etcd update event function handler.
type ModifyEventFunc func(ctx context.Context, key, oldVal, val []byte)

// DeleteEventFunc defines etcd delete event function handler.
type DeleteEventFunc func(ctx context.Context, key []byte)

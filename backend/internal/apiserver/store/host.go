package store

import (
	"context"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

type HostNetIface interface {
	IfaceCreate(ctx context.Context, HostId string, ifaceId string, iface *domain.HostNetIface, opts metav1.CreateOptions) error
	IfaceDelete(ctx context.Context, hostID string, ifaceId string, opts metav1.DeleteOptions) error
	IfaceGet(ctx context.Context, hostId string, ifaceId string, opts metav1.GetOptions) (*domain.HostNetIface, error)
	ListIfaceOfHost(ctx context.Context, hostId string, opts metav1.ListOptions) (*domain.HostNetIfaceList, error)
}

type HostStore interface {
	HostNetIface
	Create(ctx context.Context, hostId string, host *domain.Host, opts metav1.CreateOptions) error
	List(ctx context.Context, opts metav1.ListOptions) (*domain.HostList, error)
	Get(ctx context.Context, HostId string, opts metav1.GetOptions) (*domain.Host, error)
	Delete(ctx context.Context, HostId string, opts metav1.DeleteOptions) error
	ListStatus(ctx context.Context, opts metav1.ListOptions) ([]domain.StatusEntry, error)
}

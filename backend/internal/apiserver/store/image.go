package store

import (
	"context"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

type ImageStore interface {
	Create(ctx context.Context, imageId string, image *domain.Image, opts metav1.CreateOptions) error
	Update(ctx context.Context, imageId string, image *domain.Image, opts metav1.UpdateOptions) error
	List(ctx context.Context, opts metav1.ListOptions) (*domain.ImageList, error)
	Delete(ctx context.Context, imageId string, opts metav1.DeleteOptions) error
	Get(ctx context.Context, imageId string, opts metav1.GetOptions) (*domain.Image, error)
}

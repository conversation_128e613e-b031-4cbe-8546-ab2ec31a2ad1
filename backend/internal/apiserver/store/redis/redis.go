package redis

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/marmotedu/iam/pkg/log"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
	"gitlab.local/golibrary/errors"
)

var BackendRedisStore = initRedis()

func initRedis() store.RedisFactory {
	redisStore, err := GetRedisFactoryOr(options.NewRedisOptions())
	if err != nil {
		panic(err)
	}
	return redisStore
}

// ModifyEventFunc 定义 Redis 更新事件处理函数
type ModifyEventFunc func(ctx context.Context, key string, val map[string]string)

// DeleteEventFunc 定义 Redis 删除事件处理函数
type DeleteEventFunc func(ctx context.Context, key string)

// RedisStore 实现 Redis 存储
type RedisStore struct {
	client         *redis.Client
	requestTimeout time.Duration
	namespace      string
	watchers       map[string]context.CancelFunc
	mu             sync.Mutex
}

func (rs *RedisStore) CollMonitoring() store.CollMonitoringStore {
	return newMonitoring(rs)
}

func (rs *RedisStore) CollNetwork() store.CollNetworkStore {
	return newCollNetworks(rs)
}

func (rs *RedisStore) CollVMUsage() store.CollVMUsageStore {
	return newCollVMUsage(rs)
}

func (rs *RedisStore) CollVolume() store.CollVolumeStore {
	return newCollVolume(rs)
}

func (rs *RedisStore) CollVDiskUsage() store.CollVDiskUsageStore {
	return newCollVDiskUsage(rs)
}

var (
	redisFactory store.RedisFactory
	once         sync.Once
)

type RedisKeyVal struct {
	Key string
	Val []byte
}

// GetRedisFactoryOr 创建或获取 Redis 工厂实例
func GetRedisFactoryOr(opt *options.RedisOptions) (store.RedisFactory, error) {
	if opt == nil && redisFactory == nil {
		return nil, errors.New("failed to get redis store factory")
	}

	var err error
	once.Do(func() {
		client := redis.NewClient(&redis.Options{
			Addr:     opt.Address,
			Username: opt.Username,
			Password: opt.Password,
			DB:       opt.DB,
		})

		// 确保 requestTimeout 被正确赋值
		if opt.RequestTimeout <= 0 {
			opt.RequestTimeout = 10 // 设置默认超时时间
		}
		redisFactory = &RedisStore{
			client:         client,
			requestTimeout: time.Duration(opt.RequestTimeout) * time.Second,
			namespace:      opt.Namespace,
			watchers:       make(map[string]context.CancelFunc),
		}
	})

	if redisFactory == nil || err != nil {
		return nil, errors.Wrapf(err, "failed to get redis store factory, factory: %+v", redisFactory)
	}
	return redisFactory, nil
}

// getKey 添加命名空间前缀
func (r *RedisStore) getKey(key string) string {
	if r.namespace == "" {
		return key
	}

	// 定义命名空间前缀
	nsPrefix := r.namespace
	// 检查是否已包含前缀
	if strings.HasPrefix(key, nsPrefix) {
		return key
	}
	return nsPrefix + "/" + key
}

// HSet 存储哈希表字段值
func (r *RedisStore) HSet(ctx context.Context, key string, fields map[string]interface{}) error {
	nctx, cancel := context.WithTimeout(ctx, r.requestTimeout)
	defer cancel()

	fullKey := r.getKey(key)
	args := make([]interface{}, 0, len(fields)*2)
	for field, value := range fields {
		args = append(args, field, value)
	}

	if err := r.client.HSet(nctx, fullKey, args...).Err(); err != nil {
		return errors.Wrapf(err, "failed to hset key: %s", key)
	}
	return nil
}

// HGetALL 获取HASH键值
func (r *RedisStore) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	nctx, cancel := context.WithTimeout(ctx, r.requestTimeout)
	defer cancel()

	fullKey := r.getKey(key)
	//fmt.Println("fullkey = ", fullKey)
	result, err := r.client.HGetAll(nctx, fullKey).Result()
	if err != nil {
		return nil, errors.Wrapf(err, "hgetall failed for key %s", key)
	}

	if len(result) == 0 {
		return nil, errors.New("key not found")
	}
	return result, nil
}

// 新增 GetHashField 方法用于获取单个字段
func (r *RedisStore) GetHashField(ctx context.Context, key, field string) (string, error) {
	nctx, cancel := context.WithTimeout(ctx, r.requestTimeout)
	defer cancel()

	fullKey := r.getKey(key)
	val, err := r.client.HGet(nctx, fullKey, field).Result()
	if err == redis.Nil {
		return "", errors.New("field not found")
	}
	return val, errors.Wrapf(err, "failed to get field %s", field)
}

// Watch 监听键变化
func (r *RedisStore) Watch(ctx context.Context, prefix string, onModify ModifyEventFunc, onDelete DeleteEventFunc) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.watchers[prefix]; exists {
		return fmt.Errorf("prefix %q is already being watched", prefix)
	}

	var err error
	// 配置Redis通知
	if err = r.client.ConfigSet(ctx, "notify-keyspace-events", "KEAh").Err(); err != nil {
		return fmt.Errorf("failed to configure keyspace events: %w", err)
	}

	watchCtx, cancel := context.WithCancel(ctx)
	defer func() {
		if err != nil {
			cancel()
		}
	}()

	pattern := r.getKey(prefix) + "*"
	pubsub := r.client.PSubscribe(watchCtx, "__keyspace@*__:"+pattern)

	// 使用缓冲通道处理取消
	done := make(chan struct{})
	r.watchers[prefix] = func() {
		cancel()
		<-done
	}

	go func() {
		defer pubsub.Close()
		defer close(done)
		defer delete(r.watchers, prefix)

		for {
			select {
			case <-watchCtx.Done():
				log.L(ctx).Infof("Stopping watcher for prefix: %s", prefix)
				return
			default:
				msg, err := pubsub.ReceiveMessage(watchCtx)
				// 格式化输出消息结构
				// fmt.Printf("Message Details:\n"+
				// 	"Channel: %s\n"+
				// 	"Pattern: %s\n"+
				// 	"Payload: %s\n"+
				// 	"PayloadSlice: %v\n",
				// 	msg.Channel,
				// 	msg.Pattern,
				// 	msg.Payload,
				// 	msg.PayloadSlice)

				if err != nil {
					if !errors.Is(err, context.Canceled) {
						log.L(ctx).Errorf("Error receiving message: %v", err)
					}
					return
				}

				// 解析键名
				key, found := strings.CutPrefix(msg.Channel, "__keyspace@0__:")
				if !found {
					log.L(ctx).Errorf("Unexpected channel format: %s", msg.Channel)
					continue
				}

				// 处理事件类型
				eventType := strings.ToLower(strings.TrimSpace(msg.Payload))
				switch eventType {
				case "hset", "hmset", "hsetnx":
					if val, err := r.HGetAll(watchCtx, key); err == nil {
						onModify(watchCtx, key, val)
					}
				case "del", "expired", "rename_from":
					onDelete(watchCtx, key)
				default:
					log.L(ctx).Infof(": %s", eventType)
				}
			}
		}
	}()

	return nil
}

// UnWatch 取消监听
func (r *RedisStore) UnWatch(prefix string) {
	r.mu.Lock()
	defer r.mu.Unlock()

	if cancel, exists := r.watchers[prefix]; exists {
		cancel()
		delete(r.watchers, prefix)
	}
}

// List 列出键值对
func (r *RedisStore) List(ctx context.Context, prefix string) ([]RedisKeyVal, error) {
	nctx, cancel := context.WithTimeout(ctx, r.requestTimeout)
	defer cancel()

	var cursor uint64
	var keys []string
	fullPrefix := r.getKey(prefix)

	result := make([]RedisKeyVal, 0)
	for {
		var err error
		keys, cursor, err = r.client.Scan(nctx, cursor, fullPrefix+"*", 100).Result()
		if err != nil {
			return nil, errors.Wrap(err, "scan failed")
		}

		for _, key := range keys {
			val, err := r.client.Get(nctx, key).Bytes()
			if err != nil {
				continue
			}
			result = append(result, RedisKeyVal{
				Key: key[len(r.namespace):],
				Val: val,
			})
		}

		if cursor == 0 {
			break
		}
	}
	return result, nil
}

// Delete 删除键
func (r *RedisStore) Delete(ctx context.Context, key string) (map[string]string, error) {
	nctx, cancel := context.WithTimeout(ctx, r.requestTimeout)
	defer cancel()

	fullKey := r.getKey(key)
	val, err := r.client.HGetAll(nctx, fullKey).Result()
	if err != nil {
		return nil, errors.Wrap(err, "get before delete failed")
	}

	if err := r.client.Del(nctx, fullKey).Err(); err != nil {
		return nil, errors.Wrap(err, "delete failed")
	}
	return val, nil
}

// DeleteField 删除哈希表中的指定字段
func (r *RedisStore) DeleteField(ctx context.Context, key string, fields ...string) error {
	nctx, cancel := context.WithTimeout(ctx, r.requestTimeout)
	defer cancel()

	fullKey := r.getKey(key)
	result, err := r.client.HDel(nctx, fullKey, fields...).Result()
	if err != nil {
		return errors.Wrapf(err, "failed to delete fields from key: %s", key)
	}

	if result == 0 {
		return fmt.Errorf("fields not found in key: %s", key)
	}
	return nil
}

// Close 关闭连接
func (r *RedisStore) Close() error {
	return r.client.Close()
}

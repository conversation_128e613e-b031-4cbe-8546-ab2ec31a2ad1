package redis

import (
	"context"
	"encoding/json"
	"os/exec"
	"testing"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
)

func TestCollVMUsage_List(t *testing.T) {
	tests := []struct {
		name    string
		prepare func(t *testing.T) map[string]string
		cleanup func(t *testing.T)
	}{
		{
			name: "guset usage",
			prepare: func(t *testing.T) map[string]string {
				// 清理Redis测试数据
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()

				// 准备测试数据
				testData := map[string]string{
					"ubuntu2204-guest": `{
						"disk": [
							{
								"vdisk_id": "ubuntu2204-guest-vda",
								"read_iops": 8,
								"write_iops": 410,
								"read_throughput": 46.4,
								"write_throughput": 228392.8,
								"read_avg_latency": 0,
								"write_avg_latency": 0,
								"total_iops": 419,
								"total_throughput": 228439.2,
								"total_io_latency": 0
							},
							{
								"vdisk_id": "ubuntu2204-guest-sda",
								"read_iops": 0,
								"write_iops": 0,
								"read_throughput": 0.0,
								"write_throughput": 0.0,
								"read_avg_latency": 0,
								"write_avg_latency": 0,
								"total_iops": 0,
								"total_throughput": 0.0,
								"total_io_latency": 0
							}
						],
						"network": [
							{
								"device": "vnet2",
								"rx_bytes": 0.0,
								"tx_bytes": 0.0,
								"rx_kbps": 2.0099609375,
								"tx_kbps": 0.567578125
							}
						],
						"vcpu_usage": 29.0,
						"memory_usage": 6.273746490478516
					}`,
				}

				// 写入Redis
				for vmID, data := range testData {
					err := exec.Command("redis-cli", "-n", "0", "HSET", "collectd/vm_usage", vmID, data).Run()
					assert.NoError(t, err)
				}
				return testData
			},
			cleanup: func(t *testing.T) {
				err := exec.Command("redis-cli", "-n", "0", "DEL", "collectd/vm_usage").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantData := tt.prepare(t)
			defer tt.cleanup(t)

			// 初始化Redis存储
			store, err := GetRedisFactoryOr(getTestMonOptions())
			assert.NoError(t, err)

			// 执行测试
			got, err := store.CollVMUsage().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)
			assert.NotNil(t, got)

			assert.Equal(t, len(wantData), len(got.Items))
			for vmID, want := range wantData {
				var wantVMUsage domain.VMUsage
				err := json.Unmarshal([]byte(want), &wantVMUsage)
				assert.NoError(t, err)

				gotVMUsage, exists := got.Items[vmID]
				assert.True(t, exists, "guest %s not found", vmID)

				assert.Equal(t, wantVMUsage.MemoryUsage, gotVMUsage.MemoryUsage, "memory usage not match")
				assert.Equal(t, wantVMUsage.Disk, gotVMUsage.Disk, "disk usage not match")
				assert.Equal(t, wantVMUsage.Network, gotVMUsage.Network, "network usage not match")
				assert.Equal(t, wantVMUsage.VCPUUsage, gotVMUsage.VCPUUsage, "vcpu usage not match")
			}

		})
	}
}

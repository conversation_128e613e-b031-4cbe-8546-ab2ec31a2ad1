package redis

import (
	"context"
	"os"
	"os/exec"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

const (
	testNamespace = "/test"
)

func TestMain(m *testing.M) {
	// 清理测试数据
	_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
	os.Exit(m.Run())
}

func getTestRedisOptions() *options.RedisOptions {
	return &options.RedisOptions{
		Address:        "localhost:6379",
		DB:             0,
		Namespace:      testNamespace,
		RequestTimeout: 2,
	}
}

func TestGetRedisFactoryOr(t *testing.T) {
	tests := []struct {
		name    string
		opts    *options.RedisOptions
		wantErr bool
	}{
		{
			name:    "valid connection",
			opts:    getTestRedisOptions(),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			redis, err := GetRedisFactoryOr(tt.opts)
			assert.NoError(t, err)
			assert.NotNil(t, redis)
		})
	}
}

func TestRedisStore_HGetAll(t *testing.T) {
	redis, _ := GetRedisFactoryOr(getTestRedisOptions())
	rs := redis.(*RedisStore)

	// 清理测试数据
	_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()

	tests := []struct {
		name     string
		key      string
		setup    func(t *testing.T)
		expected map[string]string
	}{
		{
			name: "basic hash",
			key:  "/system_utilization",
			setup: func(t *testing.T) {
				err := exec.Command("redis-cli", "hset", "/test:/system_utilization",
					"cpu", "{ \"user\": 277662.0, \"system\": 401423.0, \"wait\": 15239.0, \"nice\": 3854.0, \"interrupt\": 0.0, \"softirq\": 56705.0, \"steal\": 0.0, \"idle\": 77874013.0 }",
					"time", "{ \"current_time\": \"1739753800.58\", \"uptime\": 900000.0 }").Run()
				assert.NoError(t, err)
			},
			expected: map[string]string{
				"cpu":  "{ \"user\": 277662.0, \"system\": 401423.0, \"wait\": 15239.0, \"nice\": 3854.0, \"interrupt\": 0.0, \"softirq\": 56705.0, \"steal\": 0.0, \"idle\": 77874013.0 }",
				"time": "{ \"current_time\": \"1739753800.58\", \"uptime\": 900000.0 }",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setup(t)
			defer exec.Command("redis-cli", "del", tt.key).Run()

			val, err := rs.HGetAll(context.Background(), tt.key)
			assert.NoError(t, err)
			assert.Equal(t, tt.expected, val)
		})
	}
}

func TestRedisStore_Watch(t *testing.T) {
	redis, _ := GetRedisFactoryOr(getTestRedisOptions())
	rs := redis.(*RedisStore)

	// 清理测试数据
	_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()

	// 合法使用示例
	fields := map[string]interface{}{
		"username":   "john_doe",         // string
		"age":        30,                 // int
		"balance":    99.99,              // float64
		"is_admin":   true,               // bool
		"updated_at": time.Now(),         // time.Time
		"metadata":   []byte{0x01, 0x02}, // []byte
	}

	t.Run("基本键监听", func(t *testing.T) {
		eventCh := make(chan string, 3)
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()
		// 启动监听
		err := rs.Watch(ctx, "/watch_test",
			func(ctx context.Context, key string, val map[string]string) {
				eventCh <- "update" + key
			},
			func(ctx context.Context, key string) {
				eventCh <- "delete" + key
			},
		)
		assert.NoError(t, err)
		// 触发事件
		_ = rs.HSet(ctx, "/watch_test/system_utilization", fields)
		_, _ = rs.Delete(ctx, "/watch_test/system_utilization")

		// 验证事件
		assert.Eventually(t, func() bool {
			return len(eventCh) == 2
		}, 3*time.Second, 100*time.Millisecond)

		events := []string{
			<-eventCh,
			<-eventCh,
		}
		assert.Contains(t, events, "update/test:/watch_test/system_utilization")
		assert.Contains(t, events, "delete/test:/watch_test/system_utilization")
	})

	t.Run("重复监听检测", func(t *testing.T) {
		err := rs.Watch(context.Background(), "/duplicate", nil, nil)
		assert.NoError(t, err)

		err = rs.Watch(context.Background(), "/duplicate", nil, nil)
		assert.ErrorContains(t, err, "prefix \"/duplicate\" is already being watched")
	})

	t.Run("事件数据验证", func(t *testing.T) {
		ready := make(chan struct{})
		dataCh := make(chan map[string]string, 1)

		// 启动监听协程
		go func() {
			assert.NoError(t, rs.Watch(context.Background(), "/data_check",
				func(_ context.Context, key string, val map[string]string) {
					select {
					case dataCh <- val:
					default:
					}
				}, nil))
			close(ready) // 通知监听就绪
		}()

		// 等待监听就绪
		<-ready
		time.Sleep(100 * time.Millisecond) // 确保订阅生效

		// 执行测试操作
		assert.NoError(t, rs.HSet(context.Background(), "/data_check/key",
			map[string]interface{}{"field": "value"}))

		// 验证结果
		select {
		case val := <-dataCh:
			assert.Equal(t, map[string]string{"field": "value"}, val)
		case <-time.After(3 * time.Second):
			t.Fatal("未收到事件")
		}
	})
}

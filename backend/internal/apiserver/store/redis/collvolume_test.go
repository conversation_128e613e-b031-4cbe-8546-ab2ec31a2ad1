package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os/exec"
	"testing"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
)

func TestCollVolume(t *testing.T) {
	tests := []struct {
		name    string
		prepare func(t *testing.T) map[string]string
		cleanup func(t *testing.T)
	}{
		{
			name: "volume",
			prepare: func(t *testing.T) map[string]string {
				// 清理Redis测试数据
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()

				// 准备测试数据
				testData := map[string]string{
					"VM Storage 1": `{"volume_path":"Volume1","size_free_byte":1073741824,"size_used_byte":1073741824,"percent_free":100,"percent_used":0}`,
					"VM Storage 2": `{"volume_path":"Volume2","size_free_byte":1073741824,"size_used_byte":1073741824,"percent_free":80,"percent_used":20}`,
					"VM Storage 3": `{"volume_path":"Volume3","size_free_byte":1073741824,"size_used_byte":1073741824,"percent_free":60,"percent_used":40}`,
				}

				for key, data := range testData {
					err := exec.Command("redis-cli", "-n", "0", "HSET", "collectd/volume", key, data).Run()
					assert.NoError(t, err)
				}

				return testData
			},
			cleanup: func(t *testing.T) {
				// 清理Redis测试数据
				err := exec.Command("redis-cli", "-n", "0", "DEL", "collectd/volume").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wantData := test.prepare(t)
			defer test.cleanup(t)

			store, err := GetRedisFactoryOr(getTestMonOptions())
			assert.NoError(t, err)

			got, err := store.CollVolume().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)
			assert.NotNil(t, got)

			// 将返回的结构体转换为 map[string]string
			storageMap, err := convertToMap(got)
			if err != nil {
				log.Fatalf("Failed to convert to map: %v", err)
			}
			assert.Equal(t, len(wantData), len(storageMap))

			for key, want := range wantData {
				got, exists := storageMap[key]
				assert.True(t, exists, "key %s not found", key)
				assert.Equal(t, want, got)
			}
		})
	}
}

func convertToMap(storageState *domain.StorageState) (map[string]string, error) {
	result := make(map[string]string)

	for key, storageInfo := range storageState.Storage {
		// 将 StorageInfo 转换为 JSON 字符串
		jsonData, err := json.Marshal(storageInfo)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal StorageInfo: %v", err)
		}
		result[key] = string(jsonData)
	}

	return result, nil
}

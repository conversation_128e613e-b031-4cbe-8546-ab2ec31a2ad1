package redis

import (
	"context"
	"encoding/json"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
)

var _ store.CollNetworkStore = (*collnetworks)(nil)

type collnetworks struct {
	store *RedisStore
}

func newCollNetworks(s *RedisStore) *collnetworks {
	return &collnetworks{store: s}
}
func (cn *collnetworks) Get(ctx context.Context, filed string, opts metav1.GetOptions) (string, error) {
	data, err := cn.store.GetHashField(ctx, collectdNetworkKey, filed)
	if err != nil {
		log.L(ctx).Warnf("[collNetwork_Redis] network HGetField failed - %v", err)
		return "", err
	}
	return data, nil
}

func (cn *collnetworks) List(ctx context.Context, opts metav1.ListOptions) (*domain.NetworkState, error) {
	// 获取原始数据 map[string]string
	rawMap, err := cn.store.HGetAll(ctx, collectdNetworkKey)
	if err != nil {
		log.L(ctx).Errorf("[collNetwork_Redis] network HGetAll failed - %v", err)
		return &domain.NetworkState{}, err
	}

	networkState := &domain.NetworkState{
		Interfaces: make(map[string]*domain.NetworkInterface, len(rawMap)),
	}

	// 直接遍历 map
	for ifaceName, jsonData := range rawMap {
		var ni *domain.NetworkInterface
		if err := json.Unmarshal([]byte(jsonData), &ni); err != nil {
			log.L(ctx).Errorf("[collNetwork_Redis] networks %s data unmarshal failed - %v", ifaceName, err)
			continue
		}
		networkState.TotalCount++
		networkState.Interfaces[ifaceName] = ni
	}

	return networkState, nil
}

const collectdNetworkKey = "network"

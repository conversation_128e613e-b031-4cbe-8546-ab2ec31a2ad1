package redis

import (
	"context"
	"encoding/json"
	"os/exec"
	"testing"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"

	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
)

func TestCollNetworks_List(t *testing.T) {
	tests := []struct {
		name    string
		prepare func(t *testing.T) map[string]string
		cleanup func(t *testing.T)
	}{
		{
			name: "完整接口数据解析",
			prepare: func(t *testing.T) map[string]string {
				// 清理Redis测试数据
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()

				// 准备测试数据
				testData := map[string]string{
					"br0":    `{"ip":"************","mask":"*********","speed":1000,"status":"connected","type":"ether","use_dhcp":true}`,
					"virbr0": `{"ip":"*************","mask":"*********","speed":-1,"status":"disconnected","type":"ether","use_dhcp":false}`,
					"lo":     `{"ip":"127.0.0.1","mask":"*************","speed":-1,"status":"disconnected","type":"ether","use_dhcp":false}`,
					"ens33":  `{"ip":"","mask":"","speed":1000,"status":"connected","type":"ether","use_dhcp":false}`,
				}

				// 写入Redis
				for iface, data := range testData {
					err := exec.Command("redis-cli", "-n", "0", "HSET", "collectd/network", iface, data).Run()
					assert.NoError(t, err)
				}
				return testData
			},
			cleanup: func(t *testing.T) {
				err := exec.Command("redis-cli", "-n", "0", "DEL", "collectd/network").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantData := tt.prepare(t)
			defer tt.cleanup(t)

			// 初始化Redis存储
			store, err := GetRedisFactoryOr(getTestMonOptions())
			assert.NoError(t, err)

			// 执行测试
			got, err := store.CollNetwork().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)
			assert.NotNil(t, got)

			assert.Equal(t, len(wantData), len(got.Interfaces))

			// 详细字段验证
			for ifaceName, jsonStr := range wantData {
				var wantNI domain.NetworkInterface
				err := json.Unmarshal([]byte(jsonStr), &wantNI)
				assert.NoError(t, err)

				gotNI, exists := got.Interfaces[ifaceName]
				assert.True(t, exists, "接口 %s 不存在", ifaceName)

				// 逐字段比对
				assert.Equal(t, wantNI.IP, gotNI.IP, "IP地址不匹配")
				assert.Equal(t, wantNI.Mask, gotNI.Mask, "子网掩码不匹配")
				assert.Equal(t, wantNI.Speed, gotNI.Speed, "速率不匹配")
				assert.Equal(t, wantNI.Status, gotNI.Status, "状态不匹配")
				assert.Equal(t, wantNI.Type, gotNI.Type, "类型不匹配")
				assert.Equal(t, wantNI.UseDHCP, gotNI.UseDHCP, "DHCP状态不匹配")
			}
		})
	}
}

func TestCollNetworks_Get(t *testing.T) {
	tests := []struct {
		name    string
		prepare func(t *testing.T) map[string]string
		cleanup func(t *testing.T)
	}{
		{
			name: "完整接口数据解析",
			prepare: func(t *testing.T) map[string]string {
				// 清理Redis测试数据
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()

				// 准备测试数据
				testData := map[string]string{
					"br0":    `{"ip":"************","mask":"*********","speed":1000,"status":"connected","type":"ether","use_dhcp":true}`,
					"virbr0": `{"ip":"*************","mask":"*********","speed":-1,"status":"disconnected","type":"ether","use_dhcp":false}`,
					"lo":     `{"ip":"127.0.0.1","mask":"*************","speed":-1,"status":"disconnected","type":"ether","use_dhcp":false}`,
					"ens33":  `{"ip":"","mask":"","speed":1000,"status":"connected","type":"ether","use_dhcp":false}`,
				}

				// 写入Redis
				for iface, data := range testData {
					err := exec.Command("redis-cli", "-n", "0", "HSET", "collectd/network", iface, data).Run()
					assert.NoError(t, err)
				}
				return testData
			},
			cleanup: func(t *testing.T) {
				err := exec.Command("redis-cli", "-n", "0", "DEL", "collectd/network").Run()
				assert.NoError(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantData := tt.prepare(t)
			defer tt.cleanup(t)

			// 初始化Redis存储
			store, err := GetRedisFactoryOr(getTestMonOptions())
			assert.NoError(t, err)

			// 执行测试
			got, err := store.CollNetwork().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)
			assert.NotNil(t, got)

			assert.Equal(t, len(wantData), len(got.Interfaces))

			// 详细字段验证
			for ifaceName, jsonStr := range wantData {
				var wantNI domain.NetworkInterface
				err := json.Unmarshal([]byte(jsonStr), &wantNI)
				assert.NoError(t, err)

				gotNI, exists := got.Interfaces[ifaceName]
				assert.True(t, exists, "接口 %s 不存在", ifaceName)

				// 逐字段比对
				assert.Equal(t, wantNI.IP, gotNI.IP, "IP地址不匹配")
				assert.Equal(t, wantNI.Mask, gotNI.Mask, "子网掩码不匹配")
				assert.Equal(t, wantNI.Speed, gotNI.Speed, "速率不匹配")
				assert.Equal(t, wantNI.Status, gotNI.Status, "状态不匹配")
				assert.Equal(t, wantNI.Type, gotNI.Type, "类型不匹配")
				assert.Equal(t, wantNI.UseDHCP, gotNI.UseDHCP, "DHCP状态不匹配")
			}
		})
	}
}

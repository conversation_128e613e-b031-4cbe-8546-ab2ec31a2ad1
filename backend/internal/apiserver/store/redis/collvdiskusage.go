package redis

import (
	"context"
	"encoding/json"
	"fmt"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
)

func (c *collvdiskusage) Get(ctx context.Context, guestUUID string, opts metav1.GetOptions) (*domain.VDiskSpec, error) {
	rawData, err := c.store.GetHashField(ctx, collectdKey, guestUUID)
	if err != nil {
		log.Errorf("[collVdiskUsage_Redis] vdiskUsage get failed - %v", err)
		return &domain.VDiskSpec{}, err
	}

	var vdiskSpec domain.VDiskSpec
	fmt.Println(rawData)
	if err := json.Unmarshal([]byte(rawData), &vdiskSpec); err != nil {
		log.Errorf("[collVdiskUsage_Redis] vdiskUsage unmarshal failed - %v", err)
		return &domain.VDiskSpec{}, err
	}

	return &vdiskSpec, nil
}

func (c *collvdiskusage) GetVDisk(ctx context.Context, guestUUID, vdiskUUID string, opts metav1.GetOptions) (*domain.VDiskUsage, error) {
	rawData, err := c.store.GetHashField(ctx, collectdKey, guestUUID)
	if err != nil {
		log.Errorf("[collVdiskUsage_Redis] vdiskUsage get failed - %v", err)
		return &domain.VDiskUsage{}, err
	}

	// 反序列化到 VDiskSpec 结构
	var spec domain.VDiskSpec
	if err := json.Unmarshal([]byte(rawData), &spec); err != nil {
		log.Errorf("[collVdiskUsage_Redis] vdiskUsage unmarshal failed - %v", err)
		return &domain.VDiskUsage{}, err
	}

	// 查找指定虚拟盘
	disk, exists := spec.Disks[vdiskUUID]
	if !exists {
		log.Errorf("[collVdiskUsage_Redis] vdisk not found - %s for guest %s", vdiskUUID, guestUUID)
		return &domain.VDiskUsage{}, err
	}

	return disk, nil
}

func (c *collvdiskusage) List(ctx context.Context, opts metav1.ListOptions) (*domain.VDiskList, error) {
	rawMap, err := c.store.HGetAll(ctx, collectdKey)
	if err != nil {
		log.Errorf("[collVdiskUsage_Redis] vdiskUsage list failed - %v", err)
		return defaultVDiskSpec(), err
	}

	vdiskList := &domain.VDiskList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(rawMap)),
		},
		Items: make(map[string]*domain.VDiskSpec, len(rawMap)),
	}
	for k, v := range rawMap {
		vdiskSpec := &domain.VDiskSpec{}
		if err := json.Unmarshal([]byte(v), vdiskSpec); err != nil {
			log.Errorf("[collVdiskUsage_Redis] vdiskUsage unmarshal failed - %v", err)
			return defaultVDiskSpec(), err
		}
		vdiskList.Items[k] = vdiskSpec
	}

	return vdiskList, nil
}

func (c *collvdiskusage) Delete(ctx context.Context, guestUUID string, opts metav1.DeleteOptions) error {
	return c.store.DeleteField(ctx, collectdKey, guestUUID)
}

func (c *collvdiskusage) DeleteVDisk(ctx context.Context, guestUUID, vdiskUUID string, opts metav1.DeleteOptions) error {
	rawData, err := c.store.GetHashField(ctx, collectdKey, guestUUID)
	if err != nil {
		log.Errorf("[collVdiskUsage_Redis] vdiskUsage get failed - %v", err)
		return err
	}

	// 解析现有数据
	var spec domain.VDiskSpec
	if err := json.Unmarshal([]byte(rawData), &spec); err != nil {
		log.Errorf("[collVdiskUsage_Redis] vdiskUsage unmarshal failed - %v", err)
		return err
	}

	// 检查目标磁盘是否存在
	disk, exists := spec.Disks[vdiskUUID]
	if !exists {
		log.Errorf("[collVdiskUsage_Redis] vdisk not found - %s", vdiskUUID)
		return err
	}

	// 更新总量
	spec.TotalUsed -= disk.Used
	spec.TotalSize -= disk.Size

	// 删除目标磁盘
	delete(spec.Disks, vdiskUUID)
	// 重新序列化数据
	updatedData, err := json.Marshal(spec)
	if err != nil {
		log.Errorf("[collVdiskUsage_Redis] vdiskUsage marshal failed - %v", err)
		return err
	}

	// 更新 Redis 数据
	if err := c.store.HSet(ctx, collectdKey, map[string]interface{}{guestUUID: string(updatedData)}); err != nil {
		log.Errorf("[collVdiskUsage_Redis] vdiskUsage update failed - %v", err)
		return err
	}

	return nil
}

// 返回默认的 VDiskSpec
func defaultVDiskSpec() *domain.VDiskList {
	return &domain.VDiskList{
		ListMeta: metav1.ListMeta{
			TotalCount: 0,
		},
		Items: make(map[string]*domain.VDiskSpec),
	}
}

type collvdiskusage struct {
	store *RedisStore
}

func newCollVDiskUsage(s *RedisStore) *collvdiskusage {
	return &collvdiskusage{store: s}
}

const collectdKey = "vdisk_usage"

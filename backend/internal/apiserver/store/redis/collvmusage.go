package redis

import (
	"context"
	"encoding/json"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
)

func (c *collvmusage) Get(ctx context.Context, vmUUID string, opts metav1.GetOptions) (*domain.VMUsage, error) {
	rawData, err := c.store.GetHashField(ctx, collectdVMUsageKey, vmUUID)
	if err != nil {
		log.L(ctx).Errorf("[collVMUsage_Redis] vmUsage get failed - %v", err)
		return &domain.VMUsage{}, nil
	}

	var vmUsage domain.VMUsage
	if err := json.Unmarshal([]byte(rawData), &vmUsage); err != nil {
		log.L(ctx).Errorf("[collVMUsage_Redis] vmUsage unmarshal failed - %v", err)
		return &domain.VMUsage{}, nil
	}

	return &vmUsage, nil
}

func (c *collvmusage) List(ctx context.Context, opts metav1.ListOptions) (*domain.VMUsageList, error) {
	rawMap, err := c.store.HGetAll(ctx, collectdVMUsageKey)
	if err != nil {
		log.L(ctx).Warnf("[collVMUsage_Redis] vmUsage get failed - %v", err)
		// 返回默认值的结构体
		return defaultVMUsageList(), nil
	}

	vmUsageList := &domain.VMUsageList{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(rawMap)),
		},
		Items: make(map[string]*domain.VMUsage, len(rawMap)),
	}

	for k, v := range rawMap {
		vmUsage := &domain.VMUsage{}
		if err := json.Unmarshal([]byte(v), vmUsage); err != nil {
			log.L(ctx).Errorf("[collVMUsage_Redis] vmUsage unmarshal failed - %v", err)
			return nil, err
		}

		vmUsageList.Items[k] = vmUsage
	}

	return vmUsageList, nil
}

// 返回默认的 VMUsageList
func defaultVMUsageList() *domain.VMUsageList {
	return &domain.VMUsageList{
		ListMeta: metav1.ListMeta{
			TotalCount: 0,
		},
		Items: make(map[string]*domain.VMUsage),
	}
}

const collectdVMUsageKey = "vm_usage"

var _ store.CollVMUsageStore = (*collvmusage)(nil)

type collvmusage struct {
	store *RedisStore
}

func newCollVMUsage(s *RedisStore) *collvmusage {
	return &collvmusage{store: s}
}

package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"os/exec"
	"testing"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
)

var redisD, _ = GetRedisFactoryOr(getTestMonOptions())
var rs = redisD.(*RedisStore)

var testData = domain.VDiskList{
	ListMeta: metav1.ListMeta{
		TotalCount: 2,
	},
	Items: map[string]*domain.VDiskSpec{
		// 第一个虚拟机
		"43c8f7fd-8e54-47a8-9ad5-ae305760e539": {
			Disks: map[string]*domain.VDiskUsage{
				"23a86dde-10bb-441e-a129-a5e6abab3b57": {
					Size: 59055800320,
					Used: 13322698752,
				},
				"87d389b3-8865-4a9d-beeb-95b48099be9a": {
					Size: 11811160064,
					Used: 0,
				},
				"e3a7888e-fce0-41a3-8f28-5384db54cf42": {
					Size: 10737418240,
					Used: 0,
				},
			},
			TotalSize: 81604378624,
			TotalUsed: 13322698752,
		},
		// 第二个虚拟机
		"7541cdc6-3a77-4165-ad20-4a2862fbd88c": {
			Disks: map[string]*domain.VDiskUsage{
				"7020c5c5-19df-42ab-96ea-bcbca2115935": {
					Size: 53687091200,
					Used: 0,
				},
				"b7fd34c2-40b3-4db0-b3e4-8e85241ec3d4": {
					Size: 64424509440,
					Used: 0,
				},
				"c5c1269a-2c9a-4161-a09e-1f97f0954ef6": {
					Size: 53687091200,
					Used: 0,
				},
				"f7c55467-a68c-4406-bcfc-20ad808d0fb4": {
					Size: 53687091200,
					Used: 0,
				},
			},
			TotalSize: ************,
			TotalUsed: 0,
		},
	},
}

func unmarshalVDiskList(t *testing.T, senddata *domain.VDiskList) error {
	// 创建符合 Redis 存储结构的 map
	redisData := make(map[string]interface{})
	for guestUUID, spec := range senddata.Items {
		// 序列化单个虚拟机的数据
		vmData := map[string]interface{}{
			"total_size": spec.TotalSize,
			"total_used": spec.TotalUsed,
		}
		// 添加磁盘数据
		for diskUUID, usage := range spec.Disks {
			vmData[diskUUID] = map[string]interface{}{
				"size": usage.Size,
				"used": usage.Used,
			}
		}

		// 序列化为 JSON 字符串
		jsonData, err := json.Marshal(vmData)
		if err != nil {
			t.Fatalf("JSON 序列化失败: %v", err)
		}
		redisData[guestUUID] = string(jsonData)
	}

	// 写入 Redis
	if err := rs.HSet(context.Background(), "vdisk_usage", redisData); err != nil {
		t.Fatalf("写入 Redis 失败: %v", err)
	}

	return nil
}

func TestCollvdiskusage_Get(t *testing.T) {

	targetUUID := "43c8f7fd-8e54-47a8-9ad5-ae305760e539"
	tests := []struct {
		name    string
		prepare func(t *testing.T) *domain.VDiskSpec
		cleanup func(t *testing.T)
	}{
		{
			name: "test",
			prepare: func(t *testing.T) *domain.VDiskSpec {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
				unmarshalVDiskList(t, &testData)
				return testData.Items[targetUUID]
			},
			cleanup: func(t *testing.T) {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
			},
		},
	}

	// 修改测试断言部分
	t.Run("test", func(t *testing.T) {
		wantData := tests[0].prepare(t) // 确保返回的不是 nil
		defer tests[0].cleanup(t)
		gotData, err := rs.CollVDiskUsage().Get(context.Background(), targetUUID, metav1.GetOptions{})

		// 添加空指针检查
		if !assert.NoError(t, err) {
			t.Fatal("获取数据失败")
		}
		if gotData == nil {
			t.Fatal("返回数据为 nil")
		}

		// 添加磁盘数据存在性检查
		if gotData.Disks == nil {
			t.Fatal("Disks 字段未初始化")
		}

		// 修改断言方式
		assert.Equal(t, wantData.TotalSize, gotData.TotalSize)
		assert.Equal(t, wantData.TotalUsed, gotData.TotalUsed)
		assert.Equal(t, len(wantData.Disks), len(gotData.Disks))

		// 深度比较每个磁盘数据
		for diskUUID, wantUsage := range wantData.Disks {
			gotUsage, exists := gotData.Disks[diskUUID]
			if !assert.True(t, exists, "缺少磁盘 "+diskUUID) {
				continue
			}
			assert.Equal(t, wantUsage.Size, gotUsage.Size)
			assert.Equal(t, wantUsage.Used, gotUsage.Used)
		}
	})

}

func TestCollvdiskusage_List(t *testing.T) {
	tests := []struct {
		name    string
		prepare func(t *testing.T) *domain.VDiskList
		cleanup func(t *testing.T)
	}{
		{
			name: "test list",
			prepare: func(t *testing.T) *domain.VDiskList {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
				unmarshalVDiskList(t, &testData)
				return &testData
			},
			cleanup: func(t *testing.T) {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantData := tt.prepare(t)
			defer tt.cleanup(t)
			gotData, err := rs.CollVDiskUsage().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)
			assert.Equal(t, wantData.Items, gotData.Items)
			assert.Equal(t, wantData.ListMeta.TotalCount, gotData.ListMeta.TotalCount)
			for k, v := range wantData.Items {
				assert.Equal(t, v.TotalSize, gotData.Items[k].TotalSize)
				assert.Equal(t, v.TotalUsed, gotData.Items[k].TotalUsed)
				assert.Equal(t, len(v.Disks), len(gotData.Items[k].Disks))
				for diskUUID, usage := range v.Disks {
					gotUsage, exists := gotData.Items[k].Disks[diskUUID]
					assert.True(t, exists)
					assert.Equal(t, usage.Size, gotUsage.Size)
					assert.Equal(t, usage.Used, gotUsage.Used)
				}
			}
		})
	}
}

func TestCollvdiskusage_GetVdisk(t *testing.T) {
	targetUUID := "43c8f7fd-8e54-47a8-9ad5-ae305760e539"
	targetDiskUUID := "23a86dde-10bb-441e-a129-a5e6abab3b57"
	tests := []struct {
		name    string
		prepare func(t *testing.T) *domain.VDiskUsage
		cleanup func(t *testing.T)
	}{
		{
			name: "test get vdisk",
			prepare: func(t *testing.T) *domain.VDiskUsage {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
				unmarshalVDiskList(t, &testData)
				return testData.Items[targetUUID].Disks[targetDiskUUID]
			},
			cleanup: func(t *testing.T) {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantData := tt.prepare(t)
			defer tt.cleanup(t)
			gotData, err := rs.CollVDiskUsage().GetVDisk(context.Background(), targetUUID, targetDiskUUID, metav1.GetOptions{})
			assert.NoError(t, err)
			assert.Equal(t, wantData.Size, gotData.Size)
			assert.Equal(t, wantData.Used, gotData.Used)
		})
	}
}

var testData2 = domain.VDiskList{
	ListMeta: metav1.ListMeta{
		TotalCount: 2,
	},
	Items: map[string]*domain.VDiskSpec{
		// 第一个虚拟机
		"43c8f7fd-8e54-47a8-9ad5-ae305760e539": {
			Disks: map[string]*domain.VDiskUsage{
				"87d389b3-8865-4a9d-beeb-95b48099be9a": {
					Size: 11811160064,
					Used: 0,
				},
				"e3a7888e-fce0-41a3-8f28-5384db54cf42": {
					Size: 10737418240,
					Used: 0,
				},
			},
			TotalSize: 22548578304,
			TotalUsed: 0,
		},
		// 第二个虚拟机
		"7541cdc6-3a77-4165-ad20-4a2862fbd88c": {
			Disks: map[string]*domain.VDiskUsage{
				"b7fd34c2-40b3-4db0-b3e4-8e85241ec3d4": {
					Size: 64424509440,
					Used: 0,
				},
				"c5c1269a-2c9a-4161-a09e-1f97f0954ef6": {
					Size: 53687091200,
					Used: 0,
				},
				"f7c55467-a68c-4406-bcfc-20ad808d0fb4": {
					Size: 53687091200,
					Used: 0,
				},
			},
			TotalSize: 171798691840,
			TotalUsed: 0,
		},
	},
}

func TestCollvdiskusage_DeleteVDisk(t *testing.T) {
	targetUUID1 := "43c8f7fd-8e54-47a8-9ad5-ae305760e539"
	targetDiskUUID1 := "23a86dde-10bb-441e-a129-a5e6abab3b57"
	targetUUID2 := "7541cdc6-3a77-4165-ad20-4a2862fbd88c"
	targetDiskUUID2 := "7020c5c5-19df-42ab-96ea-bcbca2115935"
	tests := []struct {
		name    string
		prepare func(t *testing.T) *domain.VDiskList
		cleanup func(t *testing.T)
	}{
		{
			name: "test delete vdisk",
			prepare: func(t *testing.T) *domain.VDiskList {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
				unmarshalVDiskList(t, &testData)
				return &testData2
			},
			cleanup: func(t *testing.T) {
				_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wantData := tt.prepare(t)
			defer tt.cleanup(t)

			err := rs.CollVDiskUsage().DeleteVDisk(context.Background(), targetUUID1, targetDiskUUID1, metav1.DeleteOptions{})
			assert.NoError(t, err)
			err = rs.CollVDiskUsage().DeleteVDisk(context.Background(), targetUUID2, targetDiskUUID2, metav1.DeleteOptions{})
			assert.NoError(t, err)

			gotData, err := rs.CollVDiskUsage().List(context.Background(), metav1.ListOptions{})
			assert.NoError(t, err)

			for uuid, v := range gotData.Items {
				fmt.Println("uuid:", uuid)
				assert.Equal(t, wantData.Items[uuid].TotalSize, v.TotalSize)
				assert.Equal(t, wantData.Items[uuid].TotalUsed, v.TotalUsed)
				assert.Equal(t, wantData.Items[uuid].Disks, v.Disks)
				for vu, d := range v.Disks {
					fmt.Println("vu:", vu)
					assert.Equal(t, wantData.Items[uuid].Disks[vu].Size, d.Size)
					assert.Equal(t, wantData.Items[uuid].Disks[vu].Used, d.Used)
				}
			}
		})
	}

}

package redis

import (
	"context"
	"encoding/json"
	"fmt"

	"strings"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
	"gitlab.local/golibrary/errors"
)

func (m *monitoring) Get(ctx context.Context, filed string, opts metav1.GetOptions) (string, error) {
	data, err := m.store.GetHashField(ctx, collectdSystemUtilizationKey, filed)

	if err != nil {
		log.L(ctx).Errorf("[collMonitoring_Redis] get failed - %v", err)
		return "", err
	}

	return data, err
}

func (m *monitoring) ListMap(ctx context.Context, opts metav1.ListOptions) (map[string]string, error) {
	mapData, err := m.store.HGetAll(ctx, collectdSystemUtilizationKey)

	if err != nil {
		log.L(ctx).Errorf("[collMonitoring_Redis] hgetall failed - %v", err)
		return nil, err
	}
	return mapData, err
}

// // 通用包装函数处理类型转换
// func wrapParser[T any](f func(map[string]string) (*T, error)) func(map[string]string) (interface{}, error) {
// 	return func(data map[string]string) (interface{}, error) {
// 		return f(data)
// 	}
// }

// var parsers = map[string]func(map[string]string) (interface{}, error){
// 	"system_utilization": wrapParser(parseSystemUtilization),
// 	"vdisk_utilization":  wrapParser(parseVdiskUtilization),
// }

func (m *monitoring) List(ctx context.Context, opts metav1.ListOptions) (*domain.SystemUtilization, error) {
	data, err := m.store.HGetAll(ctx, collectdSystemUtilizationKey)
	if err != nil {
		log.L(ctx).Errorf("redis monitoring list hgetall failed - %w", err.Error())
		return &domain.SystemUtilization{}, err
	}

	// parser, exists := parsers[collectdSystemUtilizationKey]
	// log.L(ctx).Debugf("exists = %v", exists)
	// if !exists {
	// 	log.L(ctx).Errorf("Redis Monitoring List unsupported key type: %s", err.Error())
	// 	return nil, err
	// }

	return parseSystemUtilization(data)
}

func parseSystemUtilization(data map[string]string) (*domain.SystemUtilization, error) {
	var util domain.SystemUtilization

	// 解析 CPU 数据
	if cpuStr, ok := data["cpu"]; ok {
		var rawData map[string]json.RawMessage
		if err := json.Unmarshal([]byte(cpuStr), &rawData); err != nil {
			return &domain.SystemUtilization{}, errors.Wrap(err, "unmarshal raw cpu data failed")
		}

		// 初始化 CPUs 字段
		cpuInfo := domain.CPUInfo{
			CPUs: make(map[string]domain.CPULoad),
		}

		// 遍历所有键值对，解析以 "cpu" 开头的键
		for key, value := range rawData {
			if key == "total_usage" {
				// 解析 total_usage 字段
				if err := json.Unmarshal(value, &cpuInfo.TotalUsage); err != nil {
					return &domain.SystemUtilization{}, errors.Wrap(err, "unmarshal total_usage failed")
				}
			} else if strings.HasPrefix(key, "cpu") {
				// 解析 cpu0、cpu1 等动态键值对
				var cpuLoad domain.CPULoad
				if err := json.Unmarshal(value, &cpuLoad); err != nil {
					return &domain.SystemUtilization{}, errors.Wrapf(err, "unmarshal cpu load for %s failed", key)
				}
				cpuInfo.CPUs[key] = cpuLoad
			}
		}

		util.CPU = cpuInfo
	}

	// 解析 Memory 数据
	if memStr, ok := data["memory"]; ok {
		if err := json.Unmarshal([]byte(memStr), &util.Memory); err != nil {
			return &domain.SystemUtilization{}, errors.Wrap(err, "unmarshal memory data failed")
		}
	}

	// 解析 Network 数据
	if netStr, ok := data["network"]; ok {
		if err := json.Unmarshal([]byte(netStr), &util.Network); err != nil {
			return &domain.SystemUtilization{}, errors.Wrap(err, "unmarshal network data failed")
		}
	}
	// 解析时间戳
	if timeStr, ok := data["time"]; ok {
		var timeData struct {
			CurrentTime string  `json:"current_time"`
			Uptime      float64 `json:"uptime"`
		}
		if err := json.Unmarshal([]byte(timeStr), &timeData); err != nil {
			return &domain.SystemUtilization{}, errors.Wrap(err, "unmarshal time data failed")
		}

		// 直接使用原始数据构建 TimeInfo
		util.Time = domain.TimeInfo{
			CurrentTime: timeData.CurrentTime,
			Uptime:      timeData.Uptime,
		}
	}

	return &util, nil
}

// // 虚拟磁盘解析器
// func parseVdiskUtilization(data map[string]string) (*domain.VdiskUtilization, error) {
// 	util := domain.VdiskUtilization{
// 		VirtualDisks: make(map[string]*domain.Stats),
// 	}

// 	for diskID, jsonData := range data {
// 		stats := new(domain.Stats)
// 		if err := json.Unmarshal([]byte(jsonData), &stats); err != nil {
// 			return nil, errors.Wrapf(err, "parse disk %s failed", diskID)
// 		}
// 		util.VirtualDisks[diskID] = stats
// 	}
// 	return &util, nil
// }

func (m *monitoring) Watch(ctx context.Context, cfg domain.WatchConfig, opts metav1.UpdateOptions) error {
	redisKeyPattern := fmt.Sprintf("%s:%s/*", cfg.Namespace, cfg.ResourceType)

	return m.store.Watch(ctx, redisKeyPattern,
		func(ctx context.Context, key string, val map[string]string) {
			resourceID := extractResourceID(key)
			cfg.Callbacks.OnUpdate(ctx, resourceID, val)
		},
		func(ctx context.Context, key string) {
			resourceID := extractResourceID(key)
			cfg.Callbacks.OnDelete(ctx, resourceID)
		},
	)
}

func extractResourceID(fullKey string) string {
	parts := strings.Split(fullKey, "/")
	if len(parts) > 0 {
		return parts[len(parts)-1]
	}
	return fullKey
}

var _ store.CollMonitoringStore = (*monitoring)(nil)

type monitoring struct {
	store *RedisStore
}

func newMonitoring(s *RedisStore) *monitoring {
	return &monitoring{store: s}
}

const collectdSystemUtilizationKey = "system_utilization"

package redis

import (
	"context"
	"encoding/json"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/marmotedu/iam/pkg/log"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/store"
)

func (c *collvolume) Get(ctx context.Context, volumeName string, opts metav1.GetOptions) (*domain.StorageInfo, error) {
	rawData, err := c.store.GetHashField(ctx, collectdVolumeKey, volumeName)
	if err != nil {
		log.L(ctx).Errorf("[collVolume_Redis] storage get failed - %v", err)
		return &domain.StorageInfo{}, nil
	}

	var storageInfo domain.StorageInfo
	if err := json.Unmarshal([]byte(rawData), &storageInfo); err != nil {
		log.L(ctx).Errorf("[collVolume_Redis] storage unmarshal failed - %v", err)
		return &domain.StorageInfo{}, nil
	}

	return &storageInfo, nil
}

func (c *collvolume) List(ctx context.Context, opts metav1.ListOptions) (*domain.StorageState, error) {
	rawMap, err := c.store.HGetAll(ctx, collectdVolumeKey)
	if err != nil {
		log.L(ctx).Errorf("[collVolume_Redis] storage get failed - %v", err)
		return defaultStorageState(), nil
	}

	storageState := &domain.StorageState{
		ListMeta: metav1.ListMeta{
			TotalCount: int64(len(rawMap)),
		},
		Storage: make(map[string]*domain.StorageInfo, len(rawMap)),
	}

	for k, v := range rawMap {
		storageInfo := &domain.StorageInfo{}
		if err := json.Unmarshal([]byte(v), storageInfo); err != nil {
			log.L(ctx).Errorf("[collVolume_Redis] storage unmarshal failed - %v", err)
			return defaultStorageState(), nil
		}

		storageState.Storage[k] = storageInfo
	}

	return storageState, nil
}

// 返回默认的 StorageState
func defaultStorageState() *domain.StorageState {
	return &domain.StorageState{
		ListMeta: metav1.ListMeta{
			TotalCount: 0,
		},
		Storage: make(map[string]*domain.StorageInfo),
	}
}

var _ store.CollVolumeStore = (*collvolume)(nil)

type collvolume struct {
	store *RedisStore
}

func newCollVolume(s *RedisStore) *collvolume {
	return &collvolume{store: s}
}

const collectdVolumeKey = "volume"

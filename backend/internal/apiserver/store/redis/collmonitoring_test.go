package redis

import (
	"context"
	"os/exec"
	"sync"
	"testing"
	"time"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
	"gitlab.local/TOS5_APP/terravirtualmachine/pkg/options"
)

// MockMonitoringStore 模拟存储层
type MockMonitoringStore struct {
	mock.Mock
}

func getTestMonOptions() *options.RedisOptions {
	return &options.RedisOptions{
		Address:        "localhost:6379",
		DB:             0,
		Namespace:      "collectd",
		RequestTimeout: 2,
	}
}

func (m *MockMonitoringStore) List(ctx context.Context) (*domain.SystemUtilization, error) {
	args := m.Called(ctx)
	return args.Get(0).(*domain.SystemUtilization), args.Error(1)
}

func (m *MockMonitoringStore) Watch(ctx context.Context, cfg domain.WatchConfig) error {
	args := m.Called(ctx, cfg)
	return args.Error(0)
}

func setupTestRedis(t *testing.T) *RedisStore {
	store, err := GetRedisFactoryOr(getTestMonOptions())
	if err != nil {
		t.Fatalf("connect to Redis failed: %v", err)
	}

	// 清空测试数据库
	if err := store.(*RedisStore).client.FlushDB(context.Background()).Err(); err != nil {
		t.Fatalf("flush test database failed: %v", err)
	}

	return store.(*RedisStore)
}

func TestMonitoringService_List(t *testing.T) {
	_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
	t.Run("use actual redis test List system utilization data", func(t *testing.T) {
		redis, err := GetRedisFactoryOr(getTestMonOptions())
		rs := redis.(*RedisStore)
		assert.NoError(t, err)

		var fields = map[string]interface{}{
			"network": "{ \"lo\": { \"rx\": 3008003090.0, \"tx\": 3008003090.0 }, \"ens33\": { \"rx\": 0.0, \"tx\": 0.0 }, \"br0\": { \"rx\": 0.0, \"tx\": 0.0 }, \"vnet0\": { \"rx\": 0.0, \"tx\": 0.0 }, \"virbr0\": { \"rx\": 0.0, \"tx\": 0.0 } }",
			"time":    "{ \"current_time\": \"1739753800.58\", \"uptime\": 793686.0 }",
			"memory":  "{ \"buffered\": 179576832.0, \"used\": 2257395712.0, \"slab_unrecl\": 217505792.0, \"cached\": 921776128.0, \"slab_recl\": 252084224.0, \"free\": 228151296.0, \"utilization\": \"83.18\" }",
			"cpu":     "{ \"total_usage\": \"83.18\", \"cpu0\": { \"user_load\": \"1.23\", \"system_load\": \"2.34\", \"other_load\": \"3.45\" }, \"cpu1\": { \"user_load\": \"4.56\", \"system_load\": \"5.67\", \"other_load\": \"6.78\"} }",
		}

		// 写入数据
		rs.HSet(context.Background(), "system_utilization", fields)

		expectedUtil := &domain.SystemUtilization{
			CPU: domain.CPUInfo{
				TotalUsage: "83.18",
				CPUs: map[string]domain.CPULoad{
					"cpu0": {UserLoad: "1.23", SystemLoad: "2.34", OtherLoad: "3.45"},
					"cpu1": {UserLoad: "4.56", SystemLoad: "5.67", OtherLoad: "6.78"},
				},
			},
			Memory: domain.MemoryInfo{
				Buffered:    179576832.0,
				Used:        2257395712.0,
				SlabUnrecl:  217505792.0,
				Cached:      921776128.0,
				SlabRecl:    252084224.0,
				Free:        228151296.0,
				Utilization: "83.18",
			},
			Network: domain.NetworSyskInfo{
				Interfaces: map[string]*domain.InterfaceStats{
					"lo":     {RX: 3008003090.0, TX: 3008003090.0},
					"ens33":  {RX: 0.0, TX: 0.0},
					"br0":    {RX: 0.0, TX: 0.0},
					"vnet0":  {RX: 0.0, TX: 0.0},
					"virbr0": {RX: 0.0, TX: 0.0},
				},
			},
			Time: domain.TimeInfo{
				CurrentTime: "1739753800.58",
				Uptime:      793686.0,
			},
		}

		// 执行测试
		util, err := redis.CollMonitoring().List(context.Background(), metav1.ListOptions{})

		// 验证结果
		assert.NoError(t, err)
		assert.Equal(t, expectedUtil, util)

	})
	_ = exec.Command("redis-cli", "-n", "0", "FLUSHDB").Run()
}

func TestMonitoringService_Get(t *testing.T) {
	t.Run("use actual redis test Get system utilization data", func(t *testing.T) {
		redis, err := GetRedisFactoryOr(getTestMonOptions())
		rs := redis.(*RedisStore)
		assert.NoError(t, err)

		var fields = map[string]interface{}{
			"network": "{ \"lo\": { \"rx\": 3008003090.0, \"tx\": 3008003090.0 }, \"ens33\": { \"rx\": 0.0, \"tx\": 0.0 }, \"br0\": { \"rx\": 0.0, \"tx\": 0.0 }, \"vnet0\": { \"rx\": 0.0, \"tx\": 0.0 }, \"virbr0\": { \"rx\": 0.0, \"tx\": 0.0 } }",
			"time":    "{ \"current_time\": \"1739753800.58\", \"uptime\": 793686.0 }",
			"memory":  "{ \"buffered\": 179576832.0, \"used\": 2257395712.0, \"slab_unrecl\": 217505792.0, \"cached\": 921776128.0, \"slab_recl\": 252084224.0, \"free\": 228151296.0 }",
			"cpu":     "{ \"user\": 277662.0, \"system\": 401423.0, \"wait\": 15239.0, \"nice\": 3854.0, \"interrupt\": 0.0, \"softirq\": 56705.0, \"steal\": 0.0, \"idle\": 77874013.0 }",
		}

		// 写入数据
		rs.HSet(context.Background(), "system_utilization", fields)

		// 确保测试完成后清理数据
		defer func() {
			_, err := rs.Delete(context.Background(), "system_utilization")
			assert.NoError(t, err)
		}()
		// 执行测试
		util, err := redis.CollMonitoring().Get(context.Background(), "network", metav1.GetOptions{})
		assert.NoError(t, err)
		assert.Equal(t, fields["network"], util)
	})
}

func TestMonitoring_Watch(t *testing.T) {
	t.Run("update event notification", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		store := setupTestRedis(t)
		var wg sync.WaitGroup
		wg.Add(1)

		fields3 := map[string]interface{}{
			"status": "up",
		}
		// 初始化数据
		err := store.HSet(ctx, "test:nodes/node-2", fields3)
		assert.NoError(t, err)

		cfg := domain.WatchConfig{
			Namespace:    "test",
			ResourceType: "nodes",
			Callbacks: domain.WatchCallbacks{
				OnUpdate: func(ctx context.Context, id string, new map[string]string) {
					defer wg.Done()
					assert.Equal(t, "node-2", id)
					assert.Equal(t, map[string]string{"status": "down"}, new)
				},
			},
		}

		go func() {
			if err := store.CollMonitoring().Watch(ctx, cfg, metav1.UpdateOptions{}); err != nil {
				t.Errorf("Watch failed: %v", err)
			}
		}()

		time.Sleep(500 * time.Millisecond)
		fields4 := map[string]interface{}{
			"status": "down",
		}
		err = store.HSet(ctx, "test:nodes/node-2", fields4)
		assert.NoError(t, err)

		wg.Wait()
	})

	t.Run("delete event notification", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		store := setupTestRedis(t)
		var wg sync.WaitGroup
		wg.Add(1)

		var fields = map[string]interface{}{
			"network": "{ \"lo\": { \"rx\": 3008003090.0, \"tx\": 3008003090.0 }, \"ens33\": { \"rx\": 0.0, \"tx\": 0.0 }, \"br0\": { \"rx\": 0.0, \"tx\": 0.0 }, \"vnet0\": { \"rx\": 0.0, \"tx\": 0.0 }, \"virbr0\": { \"rx\": 0.0, \"tx\": 0.0 } }",
			"time":    "{ \"current_time\": \"1739753800.58\", \"uptime\": 793686.0 }",
			"memory":  "{ \"buffered\": 179576832.0, \"used\": 2257395712.0, \"slab_unrecl\": 217505792.0, \"cached\": 921776128.0, \"slab_recl\": 252084224.0, \"free\": 228151296.0 }",
			"cpu":     "{ \"user\": 277662.0, \"system\": 401423.0, \"wait\": 15239.0, \"nice\": 3854.0, \"interrupt\": 0.0, \"softirq\": 56705.0, \"steal\": 0.0, \"idle\": 77874013.0 }",
		}
		// 初始化数据
		err := store.HSet(ctx, "test:nodes/node-3", fields)
		assert.NoError(t, err)

		cfg := domain.WatchConfig{
			Namespace:    "test",
			ResourceType: "nodes",
			Callbacks: domain.WatchCallbacks{
				OnDelete: func(ctx context.Context, id string) {
					defer wg.Done()
					assert.Equal(t, "node-3", id)
				},
			},
		}

		go func() {
			if err := store.CollMonitoring().Watch(ctx, cfg, metav1.UpdateOptions{}); err != nil {
				t.Errorf("Watch失败: %v", err)
			}
		}()

		time.Sleep(500 * time.Millisecond)
		_, err = store.Delete(ctx, "test:nodes/node-3")
		assert.NoError(t, err)

		wg.Wait()
	})
}

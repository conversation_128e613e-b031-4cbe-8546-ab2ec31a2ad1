package store

import (
	"context"

	metav1 "github.com/marmotedu/component-base/pkg/meta/v1"
	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1/collectd"
)

type CollMonitoringStore interface {
	Get(ctx context.Context, field string, opts metav1.GetOptions) (string, error)
	List(ctx context.Context, opts metav1.ListOptions) (*domain.SystemUtilization, error)
	ListMap(ctx context.Context, opts metav1.ListOptions) (map[string]string, error)
	Watch(ctx context.Context, cfg domain.WatchConfig, opts metav1.UpdateOptions) error
}

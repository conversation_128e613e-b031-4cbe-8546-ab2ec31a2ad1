package store

import (
	"context"

	domain "gitlab.local/TOS5_APP/terravirtualmachine/internal/apiserver/domain/v1"
)

// VMLibvirtStore 定义虚拟机操作接口
type VMLibvirtStore interface {
	// 基本操作
	CreateGuestInstance(ctx context.Context, config *domain.GuestConfig) (err error)
	DeleteInstance(ctx context.Context, id string) (err error)
	ModifyGuestName(ctx context.Context, uuid, newName string) (err error)
	ModifyCPUTopology(ctx context.Context, id string, core uint, immediate bool) (err error)
	ModifyMemory(ctx context.Context, id string, memory uint, immediate bool) (err error)
	ModifyAutoStart(guestID string, enable bool) (err error)
	SetCPUThreshold(ctx context.Context, guestID string, priority domain.PriorityEnum) (err error)
	EjectMediaISO(ctx context.Context, id string, bootImages []string) (err error)
	InsertMediaISO(ctx context.Context, id string, osMachine string, bootImages []string) (err error)
	GetInstanceStatus(id string) (ins domain.GuestConfig, err error)
	Exists(id string) bool
	ModifyVideoCard(id string, videoCard string) error
	ModifyMachineType(ctx context.Context, config domain.GuestConfig) error
	ModifyNetInterface(ctx context.Context, id string, ifaces []domain.GuestNetworkInterface) error
	SetDiskThreshold(guestID string, writeSpeed, writeIOPS, readSpeed, readIOPS uint64) (err error)
	ModifySerialConsole(ctx context.Context, guestID string, enable bool) (err error)
	ModifyOSBoot(ctx context.Context, id string, osboot string) error
	ModifyVDisk(ctx context.Context, id string, vdisks []domain.GuestStoreVDisk) (err error)
	// // 电源管理
	StartInstance(ctx context.Context, id string) error
	IsInstanceRunning(ctx context.Context, id string) (bool, error)
	StopInstance(ctx context.Context, id string, reboot, force bool) error
	PauseInstance(ctx context.Context, id string) error
	UnpauseInstance(ctx context.Context, id string) error

	GraphicsLinkPort(ctx context.Context, id string) (int, error)
}
